import { Component, OnInit, Inject, OnDestroy, EventEmitter, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { animate, style, transition, trigger } from '@angular/animations';
import { CatastroService } from '@app/services/catastro.service';

@Component({
  selector: 'app-map-dialog',
  templateUrl: './map-dialog.component.html',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-out', style({ opacity: 1 }))
      ])
    ])
  ]
})
export class MapDialogComponent implements OnInit, OnDestroy {
  // Evento para notificar cuando se cierra el diálogo
  @Output() dialogClosed = new EventEmitter<any>();

  // Tipo de mapa seleccionado
  mapType: 'leaflet' | 'mapbox';

  // Parámetros para el mapa
  queryParams: any;

  // Estado de carga
  loading = true;

  // Estado del tema
  isDarkTheme = false;

  // Estado del panel de edición (abierto por defecto)
  showEditPanel = true;

  // Estado del panel de hover (oculto por defecto)
  showHoverPanel = false;

  // Datos de dirección para editar
  editAddress: any = {
    provincia: '',
    municipio: '',
    via: '',
    numero: '',
    codigoPostal: '',
    bloque: '',
    escalera: '',
    planta: '',
    puerta: ''
  };

  // Datos para el catastro
  provinciasCatastro: any[] = [];
  municipiosCatastro: any[] = [];
  viasCatastro: any[] = [];

  // Estados de carga
  cargandoProvincias: boolean = false;
  cargandoMunicipios: boolean = false;
  cargandoVias: boolean = false;

  // Estados de visualización de dropdowns
  mostrarProvincias: boolean = false;
  mostrarMunicipios: boolean = false;
  mostrarVias: boolean = false;

  // Selecciones actuales
  selectedProvinciaCatastro: any = null;
  selectedMunicipioCatastro: any = null;
  selectedViaCatastro: any = null;

  constructor(
    public dialogRef: MatDialogRef<MapDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      mapType: 'leaflet' | 'mapbox',
      queryParams: any
    },
    private catastroService: CatastroService
  ) {
    this.mapType = data.mapType;
    this.queryParams = data.queryParams;
  }

  ngOnInit(): void {
    // Configurar el diálogo para ocupar toda la pantalla
    this.dialogRef.updateSize('100vw', '100vh');

    // Configurar el diálogo para que no se cierre al hacer clic fuera
    this.dialogRef.disableClose = true;

    // Detectar si el tema oscuro está activo
    this.isDarkTheme = document.body.classList.contains('dark-theme');

    // Añadir la clase dark-theme al contenedor del mapa si es necesario
    if (this.isDarkTheme) {
      const mapContainer = document.getElementById('map-dialog-container');
      if (mapContainer) {
        mapContainer.classList.add('dark-theme');
      }
    }

    // Asegurar que el panel de edición esté visible al inicio
    this.showEditPanel = true;

    // Inicializar los datos de dirección con los parámetros recibidos
    if (this.queryParams) {
      this.editAddress = {
        provincia: this.queryParams.provincia || '',
        municipio: this.queryParams.municipio || '',
        via: this.queryParams.via || '',
        numero: this.queryParams.numero || '',
        codigoPostal: this.queryParams.codigoPostal || '',
        bloque: this.queryParams.bloque || '',
        escalera: this.queryParams.escalera || '',
        planta: this.queryParams.planta || '',
        puerta: this.queryParams.puerta || ''
      };

      // Cargar datos del catastro si tenemos provincia
      if (this.editAddress.provincia) {
        this.cargarProvincias();

        // Si tenemos provincia y código de provincia, cargar municipios
        if (this.queryParams.codigoProvincia) {
          this.cargarMunicipios(this.queryParams.codigoProvincia);

          // Si tenemos municipio y código de municipio, cargar vías
          if (this.editAddress.municipio && this.queryParams.codigoMunicipio) {
            this.cargarVias(this.queryParams.codigoProvincia, this.queryParams.codigoMunicipio);
          }
        }
      }
    }

    // Ocultar el spinner después de un breve retraso
    // Esto da tiempo a que los componentes de mapa se inicialicen
    setTimeout(() => {
      this.loading = false;

      // Forzar un redimensionamiento de la ventana para que los mapas se rendericen correctamente
      window.dispatchEvent(new Event('resize'));

      // Forzar un segundo redimensionamiento después de un breve retraso
      // Esto es necesario para algunos navegadores y situaciones
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 500);
    }, 1000); // Aumentamos el tiempo para asegurar que los componentes estén listos
  }

  ngOnDestroy(): void {
    // Limpiar recursos si es necesario
  }

  /**
   * Alternar la visibilidad del panel de edición
   */
  toggleEditPanel(): void {
    this.showEditPanel = !this.showEditPanel;
  }



  /**
   * Maneja el evento de clic en el panel de edición
   * Solo cierra el panel si se hace clic en el botón de cerrar
   * @param event Evento de clic
   */
  onEditPanelClick(event: MouseEvent): void {
    // No cerramos el panel al hacer clic en él
    // Solo se cerrará cuando se haga clic en el botón de cerrar
    // o cuando se llame explícitamente a toggleEditPanel()

    // Detener la propagación del evento para evitar comportamientos inesperados
    event.stopPropagation();
  }

  /**
   * Determina si el clic fue en el borde del panel
   * @param event Evento de clic
   * @param element Elemento del panel
   * @returns true si el clic fue en el borde, false en caso contrario
   */
  private isClickOnPanelBorder(event: MouseEvent, element: HTMLElement): boolean {
    // Obtener las dimensiones y posición del panel
    const rect = element.getBoundingClientRect();

    // Coordenadas del clic relativas al panel
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Definir el ancho del borde (en píxeles)
    const borderWidth = 15; // Aumentamos el área de detección para facilitar el clic

    // Verificar si el clic fue en alguno de los bordes
    const isOnLeftBorder = x <= borderWidth;
    const isOnRightBorder = x >= rect.width - borderWidth;
    const isOnTopBorder = y <= borderWidth;
    const isOnBottomBorder = y >= rect.height - borderWidth;

    // Verificar si el clic fue en una esquina (donde se cruzan dos bordes)
    const isOnCorner = (isOnLeftBorder && isOnTopBorder) ||
                       (isOnLeftBorder && isOnBottomBorder) ||
                       (isOnRightBorder && isOnTopBorder) ||
                       (isOnRightBorder && isOnBottomBorder);

    // Si el clic fue en una esquina, aumentamos la precisión para evitar falsos positivos
    if (isOnCorner) {
      // Verificar si el clic está dentro del área de la esquina (un cuadrado de borderWidth x borderWidth)
      return (isOnLeftBorder && x <= borderWidth && (isOnTopBorder && y <= borderWidth || isOnBottomBorder && y >= rect.height - borderWidth)) ||
             (isOnRightBorder && x >= rect.width - borderWidth && (isOnTopBorder && y <= borderWidth || isOnBottomBorder && y >= rect.height - borderWidth));
    }

    // Devolver true si el clic fue en alguno de los bordes
    return isOnLeftBorder || isOnRightBorder || isOnTopBorder || isOnBottomBorder;
  }

  /**
   * Cancelar la edición y cerrar el panel
   */
  cancelEdit(): void {
    // Restaurar los valores originales
    if (this.queryParams) {
      this.editAddress = {
        provincia: this.queryParams.provincia || '',
        municipio: this.queryParams.municipio || '',
        via: this.queryParams.via || '',
        numero: this.queryParams.numero || '',
        codigoPostal: this.queryParams.codigoPostal || '',
        bloque: this.queryParams.bloque || '',
        escalera: this.queryParams.escalera || '',
        planta: this.queryParams.planta || '',
        puerta: this.queryParams.puerta || ''
      };
    }

    // Cerrar el panel
    this.showEditPanel = false;
  }

  /**
   * Aplicar los cambios y actualizar el mapa
   * Mantiene el panel abierto para permitir ediciones adicionales
   */
  applyEdit(): void {
    // Actualizar los parámetros de consulta
    this.queryParams = {
      ...this.queryParams,
      ...this.editAddress
    };

    // Actualizar el mapa según el tipo
    if (this.mapType === 'leaflet') {
      this.updateLeafletMap();
    } else if (this.mapType === 'mapbox') {
      this.updateMapboxMap();
    }

    // Mantener el panel abierto para permitir ediciones adicionales
    // this.showEditPanel = false; // Comentado para mantener el panel abierto

    // Mostrar un mensaje de confirmación (opcional)
    // Podríamos agregar un toast o un mensaje de confirmación aquí
  }

  /**
   * Actualizar el mapa Leaflet con los nuevos parámetros
   */
  private updateLeafletMap(): void {
    // Obtener la instancia del mapa Leaflet
    const leafletMap = (window as any).leafletMapInstance;
    if (!leafletMap) {
      console.error('No se pudo encontrar la instancia del mapa Leaflet');
      return;
    }

    // Obtener el componente de mapa Leaflet
    const leafletComponent = document.querySelector('app-mapa-tipificacion');
    if (!leafletComponent) {
      console.error('No se pudo encontrar el componente de mapa Leaflet');
      return;
    }

    // Intentar llamar al método buscarEnMapa del componente
    try {
      // Usar una técnica alternativa para acceder al componente
      // Crear un evento personalizado con los datos
      const event = new CustomEvent('update-map', {
        detail: this.queryParams,
        bubbles: true
      });

      // Disparar el evento en el componente
      leafletComponent.dispatchEvent(event);
    } catch (error) {
      // Error al actualizar el mapa
    }
  }

  /**
   * Actualizar el mapa Mapbox con los nuevos parámetros
   */
  private updateMapboxMap(): void {
    // Obtener la instancia del mapa Mapbox
    const mapboxMap = (window as any).mapboxMapInstance;
    if (!mapboxMap) {
      return;
    }

    // Obtener el componente de mapa Mapbox
    const mapboxComponent = document.querySelector('app-mapa-tipificacion-mapbox');
    if (!mapboxComponent) {
      return;
    }

    // Intentar llamar al método buscarEnMapa del componente
    try {
      // Usar una técnica alternativa para acceder al componente
      // Crear un evento personalizado con los datos
      const event = new CustomEvent('update-map', {
        detail: this.queryParams,
        bubbles: true
      });

      // Disparar el evento en el componente
      mapboxComponent.dispatchEvent(event);
    } catch (error) {
      // Error al actualizar el mapa
    }
  }

  /**
   * Carga las provincias del catastro
   */
  cargarProvincias(): void {
    this.cargandoProvincias = true;
    this.catastroService.getProvincias().subscribe({
      next: (response) => {
        if (response && response.d) {
          this.provinciasCatastro = response.d;

          // Buscar la provincia seleccionada
          if (this.editAddress.provincia && this.provinciasCatastro.length > 0) {
            // Verificar la estructura de los datos para evitar errores
            const firstProvincia = this.provinciasCatastro[0];
            const nombreProp = 'Nombre' in firstProvincia ? 'Nombre' :
                              'Denominacion' in firstProvincia ? 'Denominacion' : '';

            if (nombreProp && this.editAddress.provincia) {
              this.selectedProvinciaCatastro = this.provinciasCatastro.find(
                p => p[nombreProp] && p[nombreProp].toUpperCase() === this.editAddress.provincia.toUpperCase()
              );
            }
          }
        }
        this.cargandoProvincias = false;
      },
      error: () => {
        this.cargandoProvincias = false;
      }
    });
  }

  /**
   * Carga los municipios de la provincia seleccionada
   */
  cargarMunicipios(codigoProvincia: string): void {
    this.cargandoMunicipios = true;
    this.catastroService.getMunicipios(codigoProvincia).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.municipiosCatastro = response.d;

          // Buscar el municipio seleccionado
          if (this.editAddress.municipio && this.municipiosCatastro.length > 0) {
            // Verificar la estructura de los datos para evitar errores
            const firstMunicipio = this.municipiosCatastro[0];
            const nombreProp = 'Nombre' in firstMunicipio ? 'Nombre' :
                              'Denominacion' in firstMunicipio ? 'Denominacion' : '';

            if (nombreProp && this.editAddress.municipio) {
              this.selectedMunicipioCatastro = this.municipiosCatastro.find(
                m => m[nombreProp] && m[nombreProp].toUpperCase() === this.editAddress.municipio.toUpperCase()
              );


            }
          }
        }
        this.cargandoMunicipios = false;
      },
      error: () => {
        this.cargandoMunicipios = false;
      }
    });
  }

  /**
   * Carga las vías del municipio seleccionado
   */
  cargarVias(codigoProvincia: string, codigoMunicipio: string): void {
    this.cargandoVias = true;
    this.catastroService.getVias(codigoProvincia, codigoMunicipio).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.viasCatastro = response.d;

          // Buscar la vía seleccionada
          if (this.editAddress.via && this.viasCatastro.length > 0) {
            // Verificar la estructura de los datos para evitar errores
            const firstVia = this.viasCatastro[0];
            const nombreProp = 'Nombre' in firstVia ? 'Nombre' :
                              'DenominacionCompleta' in firstVia ? 'DenominacionCompleta' : '';

            if (nombreProp && this.editAddress.via) {
              this.selectedViaCatastro = this.viasCatastro.find(
                v => v[nombreProp] && v[nombreProp].toUpperCase() === this.editAddress.via.toUpperCase()
              );


            }
          }
        }
        this.cargandoVias = false;
      },
      error: () => {
        this.cargandoVias = false;
      }
    });
  }

  /**
   * Maneja el evento de focus en el campo de provincia
   */
  onProvinciaFocus(): void {
    // Cargar provincias al hacer focus (sin filtro)
    this.cargandoProvincias = true;
    this.catastroService.getProvincias().subscribe({
      next: (response) => {
        if (response && response.d) {
          this.provinciasCatastro = response.d;
        }
        this.cargandoProvincias = false;
      },
      error: () => {
        this.cargandoProvincias = false;
      }
    });

    // Mostrar el dropdown de provincias
    this.mostrarProvincias = true;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;
  }

  /**
   * Maneja el evento de input en el campo de provincia
   * Busca provincias que coincidan con lo que el usuario escribe
   */
  onProvinciaInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const filtro = inputElement.value.trim();

    // Buscar provincias que coincidan con el filtro
    this.cargandoProvincias = true;
    this.catastroService.getProvincias(filtro).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.provinciasCatastro = response.d;
          this.mostrarProvincias = true;
        }
        this.cargandoProvincias = false;
      },
      error: () => {
        this.cargandoProvincias = false;
      }
    });
  }

  /**
   * Maneja el evento de focus en el campo de municipio
   */
  onMunicipioFocus(): void {
    // Si tenemos provincia seleccionada
    if (this.selectedProvinciaCatastro) {
      // Obtener la propiedad de código correcta
      const codigoProp = 'Codigo' in this.selectedProvinciaCatastro ? 'Codigo' :
                        'CodigoINE' in this.selectedProvinciaCatastro ? 'CodigoINE' : '';

      if (codigoProp) {
        // Cargar municipios al hacer focus (sin filtro)
        this.cargandoMunicipios = true;
        this.catastroService.getMunicipios(this.selectedProvinciaCatastro[codigoProp]).subscribe({
          next: (response) => {
            if (response && response.d) {
              this.municipiosCatastro = response.d;
            }
            this.cargandoMunicipios = false;
          },
          error: () => {
            this.cargandoMunicipios = false;
          }
        });
      }

      // Mostrar el dropdown de municipios
      this.mostrarMunicipios = true;
    }

    // Ocultar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarVias = false;
  }

  /**
   * Maneja el evento de input en el campo de municipio
   * Busca municipios que coincidan con lo que el usuario escribe
   */
  onMunicipioInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const filtro = inputElement.value.trim();

    // Si tenemos provincia seleccionada
    if (this.selectedProvinciaCatastro) {
      // Obtener la propiedad de código correcta
      const codigoProp = 'Codigo' in this.selectedProvinciaCatastro ? 'Codigo' :
                        'CodigoINE' in this.selectedProvinciaCatastro ? 'CodigoINE' : '';

      if (codigoProp) {
        // Buscar municipios que coincidan con el filtro
        this.cargandoMunicipios = true;
        this.catastroService.getMunicipios(this.selectedProvinciaCatastro[codigoProp] ).subscribe({
          next: (response) => {
            if (response && response.d) {
              this.municipiosCatastro = response.d;
              this.mostrarMunicipios = true;
            }
            this.cargandoMunicipios = false;
          },
          error: () => {
            this.cargandoMunicipios = false;
          }
        });
      }
    }
  }

  /**
   * Maneja el evento de focus en el campo de vía
   */
  onViaFocus(): void {
    // Si tenemos provincia y municipio seleccionados
    if (this.selectedProvinciaCatastro && this.selectedMunicipioCatastro) {
      // Obtener las propiedades de código correctas
      const provCodigoProp = 'Codigo' in this.selectedProvinciaCatastro ? 'Codigo' :
                            'CodigoINE' in this.selectedProvinciaCatastro ? 'CodigoINE' : '';

      const munCodigoProp = 'Codigo' in this.selectedMunicipioCatastro ? 'Codigo' :
                           'CodigoINE' in this.selectedMunicipioCatastro ? 'CodigoINE' : '';

      if (provCodigoProp && munCodigoProp) {
        // Cargar vías al hacer focus (sin filtro)
        this.cargandoVias = true;
        this.catastroService.getVias(
          this.selectedProvinciaCatastro[provCodigoProp],
          this.selectedMunicipioCatastro[munCodigoProp]
        ).subscribe({
          next: (response) => {
            if (response && response.d) {
              this.viasCatastro = response.d;
            }
            this.cargandoVias = false;
          },
          error: () => {
            this.cargandoVias = false;
          }
        });
      }

      // Mostrar el dropdown de vías
      this.mostrarVias = true;
    }

    // Ocultar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
  }

  /**
   * Maneja el evento de input en el campo de vía
   * Busca vías que coincidan con lo que el usuario escribe
   */
  onViaInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const filtro = inputElement.value.trim();

    // Si tenemos provincia y municipio seleccionados
    if (this.selectedProvinciaCatastro && this.selectedMunicipioCatastro) {
      // Obtener las propiedades de código correctas
      const provCodigoProp = 'Codigo' in this.selectedProvinciaCatastro ? 'Codigo' :
                            'CodigoINE' in this.selectedProvinciaCatastro ? 'CodigoINE' : '';

      const munCodigoProp = 'Codigo' in this.selectedMunicipioCatastro ? 'Codigo' :
                           'CodigoINE' in this.selectedMunicipioCatastro ? 'CodigoINE' : '';

      if (provCodigoProp && munCodigoProp) {
        // Buscar vías que coincidan con el filtro
        this.cargandoVias = true;
        this.catastroService.getVias(
          this.selectedProvinciaCatastro[provCodigoProp],
          this.selectedMunicipioCatastro[munCodigoProp],
          filtro
        ).subscribe({
          next: (response) => {
            if (response && response.d) {
              this.viasCatastro = response.d;
              this.mostrarVias = true;
            }
            this.cargandoVias = false;
          },
          error: () => {
            this.cargandoVias = false;
          }
        });
      }
    }
  }

  /**
   * Selecciona una provincia del dropdown
   */
  seleccionarProvincia(provincia: any): void {
    this.selectedProvinciaCatastro = provincia;

    // Usar la propiedad correcta según la estructura de datos
    const nombreProp = 'Nombre' in provincia ? 'Nombre' : 'Denominacion' in provincia ? 'Denominacion' : '';
    const codigoProp = 'Codigo' in provincia ? 'Codigo' : 'CodigoINE' in provincia ? 'CodigoINE' : '';

    if (nombreProp) {
      this.editAddress.provincia = provincia[nombreProp];
    }

    if (codigoProp) {
      this.queryParams.codigoProvincia = provincia[codigoProp];
    }

    // Limpiar municipio y vía
    this.editAddress.municipio = '';
    this.editAddress.via = '';
    this.selectedMunicipioCatastro = null;
    this.selectedViaCatastro = null;
    this.municipiosCatastro = [];
    this.viasCatastro = [];

    // Cargar municipios
    if (codigoProp) {
      this.cargarMunicipios(provincia[codigoProp]);
    }

    // Ocultar dropdown
    this.mostrarProvincias = false;
  }

  /**
   * Selecciona un municipio del dropdown
   */
  seleccionarMunicipio(municipio: any): void {
    this.selectedMunicipioCatastro = municipio;

    // Usar la propiedad correcta según la estructura de datos
    const nombreProp = 'Nombre' in municipio ? 'Nombre' : 'Denominacion' in municipio ? 'Denominacion' : '';
    const codigoProp = 'Codigo' in municipio ? 'Codigo' : 'CodigoINE' in municipio ? 'CodigoINE' : '';

    if (nombreProp) {
      this.editAddress.municipio = municipio[nombreProp];
    }

    if (codigoProp) {
      this.queryParams.codigoMunicipio = municipio[codigoProp];
    }

    // Limpiar vía
    this.editAddress.via = '';
    this.selectedViaCatastro = null;
    this.viasCatastro = [];

    // Cargar vías
    if (this.selectedProvinciaCatastro && codigoProp) {
      const provCodigoProp = 'Codigo' in this.selectedProvinciaCatastro ? 'Codigo' :
                            'CodigoINE' in this.selectedProvinciaCatastro ? 'CodigoINE' : '';

      if (provCodigoProp) {
        this.cargarVias(this.selectedProvinciaCatastro[provCodigoProp], municipio[codigoProp]);
      }
    }

    // Ocultar dropdown
    this.mostrarMunicipios = false;
  }

  /**
   * Selecciona una vía del dropdown
   */
  seleccionarVia(via: any): void {
    this.selectedViaCatastro = via;

    // Usar la propiedad correcta según la estructura de datos
    const nombreProp = 'Nombre' in via ? 'Nombre' : 'DenominacionCompleta' in via ? 'DenominacionCompleta' : '';

    if (nombreProp) {
      this.editAddress.via = via[nombreProp];
    }

    // Ocultar dropdown
    this.mostrarVias = false;
  }

  onClose(): void {
    // Intentar acceder a las instancias de los componentes de mapa para limpiarlos correctamente
    try {
      // Método simplificado para limpiar mapas sin usar __ngContext__
      // Esto evita errores en versiones más recientes de Angular

      // Para Leaflet, intentar acceder directamente al mapa global
      const leafletMap = (window as any).leafletMapInstance;
      if (leafletMap) {
        try {
          leafletMap.remove();
          (window as any).leafletMapInstance = null;
        } catch (e) {
          // Error al limpiar mapa
        }
      }

      // Para Mapbox, intentar acceder directamente al mapa global
      const mapboxMap = (window as any).mapboxMapInstance;
      if (mapboxMap) {
        try {
          mapboxMap.remove();
          (window as any).mapboxMapInstance = null;
        } catch (e) {
          // Error al limpiar mapa
        }
      }
    } catch (error) {
      // Error al intentar limpiar los mapas
    }

    // Limpiar los contenedores de mapa
    const mapContainers = ['dialog-map-container-leaflet', 'dialog-map-container-mapbox', 'dialog-map-leaflet', 'dialog-map-mapbox'];
    mapContainers.forEach(id => {
      const container = document.getElementById(id);
      if (container) {
        // Limpiar el contenido del contenedor
        container.innerHTML = '';
      }
    });

    // Forzar un redimensionamiento de la ventana para que los mapas se rendericen correctamente
    // cuando se vuelva a abrir el diálogo
    window.dispatchEvent(new Event('resize'));

    // Preparar los datos de dirección actualizados
    const addressData = {
      bloque: this.editAddress.bloque,
      escalera: this.editAddress.escalera,
      planta: this.editAddress.planta,
      puerta: this.editAddress.puerta,
      numero: this.editAddress.numero,
      codigoPostal: this.editAddress.codigoPostal,
      provincia: this.editAddress.provincia,
      municipio: this.editAddress.municipio,
      via: this.editAddress.via,
      mapType: this.mapType
    };

    // Emitir el evento de cierre del diálogo
    this.dialogClosed.emit(addressData);

    // Cerrar el diálogo y devolver los datos de dirección actualizados
    this.dialogRef.close(addressData);

    // Forzar un redimensionamiento adicional después de cerrar el diálogo
    // para asegurar que los componentes de la página principal se rendericen correctamente
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 300);
  }
}
