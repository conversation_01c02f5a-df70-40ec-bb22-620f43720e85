// src/app/models/cliente-con-usuario.dto.ts

export class ClienteConUsuarioDTO {
  public dni: string;
  public asesor: string;
  public fechaIngresado: any; // Puede ser string o array
  public numeroMovil: string;
  public coordinador: string;

  constructor(data: any) {
    this.dni = data.dni || '';
    this.asesor = data.asesor || '';
    this.numeroMovil = data.numeroMovil || '';
    this.coordinador = data.coordinador || '';

    // Mantener el formato original de fechaIngresado (array o string)
    // Esto es importante para poder usar la hora exacta en las consultas de detalle
    this.fechaIngresado = data.fechaIngresado || '';
  }

  /**
   * Obtiene la fecha de ingreso formateada para la URL de detalle
   * @returns Fecha en formato ISO (YYYY-MM-DDTHH:mm:ss.000) en zona horaria local
   */
  getFechaCreacionFormatted(): string {
    if (!this.fechaIngresado) return '';

    try {
      // Obtener el valor original de fechaIngresado

      // Si es un array [año, mes, día, hora, minuto, segundo]
      if (Array.isArray(this.fechaIngresado)) {
        if (this.fechaIngresado.length >= 3) {
          // Extraer componentes del array
          const year = this.fechaIngresado[0];
          const month = this.fechaIngresado[1]; // No restamos 1 porque usaremos directamente estos valores
          const day = this.fechaIngresado[2];
          const hours =
            this.fechaIngresado.length > 3 ? this.fechaIngresado[3] : 0;
          const minutes =
            this.fechaIngresado.length > 4 ? this.fechaIngresado[4] : 0;
          const seconds =
            this.fechaIngresado.length > 5 ? this.fechaIngresado[5] : 0;

          // Formatear directamente sin crear un objeto Date para evitar problemas de zona horaria
          const monthStr = String(month).padStart(2, '0');
          const dayStr = String(day).padStart(2, '0');
          const hoursStr = String(hours).padStart(2, '0');
          const minutesStr = String(minutes).padStart(2, '0');
          const secondsStr = String(seconds).padStart(2, '0');

          // Formatear milisegundos si están disponibles
          let millisStr = '000';
          if (this.fechaIngresado.length >= 7) {
            // Convertir nanosegundos a milisegundos (dividir por 1,000,000)
            const nanos = this.fechaIngresado[6];
            millisStr = String(Math.floor(nanos / 1000000)).padStart(3, '0');
          }

          // Crear la fecha formateada con milisegundos
          const formattedDate = `${year}-${monthStr}-${dayStr}T${hoursStr}:${minutesStr}:${secondsStr}.${millisStr}`;

          return formattedDate;
        } else {
          // Array de fecha incompleto
          return ''; // Array incompleto
        }
      } else if (typeof this.fechaIngresado === 'string') {
        // Procesar fecha como string

        // Verificar si el string tiene formato YYYY-MM-DD sin hora
        if (/^\d{4}-\d{2}-\d{2}$/.test(this.fechaIngresado)) {
          // Si es solo una fecha sin hora, añadir T19:00:00.000 directamente
          const formattedDate = `${this.fechaIngresado}T19:00:00.000`;

          return formattedDate;
        }

        // Si es un string con otro formato, intentar convertirlo a Date
        const date = new Date(this.fechaIngresado);

        // Verificar si la fecha es válida
        if (isNaN(date.getTime())) {
          // Fecha string inválida
          return '';
        }

        // Obtener componentes de fecha en zona horaria local
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        // Para evitar problemas de zona horaria, usar siempre 19:00:00.000 como hora
        const formattedDate = `${year}-${month}-${day}T19:00:00.000`;

        return formattedDate;
      } else {
        // Tipo de fecha no soportado
        return ''; // Tipo no soportado
      }
    } catch (error) {
      // Error al formatear fecha para URL
      return '';
    }
  }

  /**
   * Crea una instancia de ClienteConUsuarioDTO a partir de un objeto
   * @param data Datos del cliente
   * @returns Instancia de ClienteConUsuarioDTO
   */
  static fromJson(data: any): ClienteConUsuarioDTO {
    return new ClienteConUsuarioDTO(data);
  }

  /**
   * Crea un array de instancias de ClienteConUsuarioDTO a partir de un array de objetos
   * @param dataArray Array de datos de clientes
   * @returns Array de instancias de ClienteConUsuarioDTO
   */
  static fromJsonArray(dataArray: any[]): ClienteConUsuarioDTO[] {
    // Convertir array de clientes a objetos ClienteConUsuarioDTO
    return dataArray.map((data) => ClienteConUsuarioDTO.fromJson(data));
  }
}

// cliente-residencial.model.ts

export interface ClienteResidencial {
  id: number;
  campania?: string;
  nombresApellidos?: string;
  nifNie?: string;
  nacionalidad?: string;
  fechaNacimiento?: string; // Formato ISO (YYYY-MM-DD)
  genero?: string;
  correoElectronico?: string;
  cuentaBancaria?: string;
  permanencia?: string;
  direccion?: string;
  tipoFibra?: string;
  movilContacto: string;
  fijoCompania?: string;
  planActual?: string;
  codigoPostal?: string;
  provincia?: string;
  distrito?: string;
  ciudad?: string;
  tipoPlan?: string;
  icc?: string;
  movilesAPortar?: string[];
  // Para el usuario, puedes definir otra interfaz o usar 'any'
  usuario?: any;
  autorizaSeguros?: boolean;
  autorizaEnergias?: boolean;
  ventaRealizada?: boolean;
  deseaPromocionesLowi?: boolean;
  fechaCreacion: string; // Formato ISO (YYYY-MM-DDTHH:mm:ss)
  nombres?: string;
  observacion?: string;
  numeroAgente?: string;
  estadoLlamada?: string;
  observacionEstado?: string;
  titularDelServicio?: string;
  futbol?: string;
  velocidad?: string;
  tipoTecnologia?: string;
  numeroMoviles?: string;
  coordinador?: string;
  textoTranscription?: string;
  urlDriveTranscripcion?: string;
  notaAgenteComparadorIA?: number;
}
