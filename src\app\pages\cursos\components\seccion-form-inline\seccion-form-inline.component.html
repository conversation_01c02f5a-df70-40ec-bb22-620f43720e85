<div class="p-5 bg-white dark:bg-gray-800 rounded-lg shadow-sm" [ngClass]="{'dark-theme': isDarkTheme}">
  <!-- Encabezado del formulario -->
  <div class="flex items-center mb-4">
    <button type="button" (click)="onCancel()"
            class="p-1 mr-2 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 rounded-full">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
    </button>
    <h2 class="text-xl font-medium text-gray-800 dark:text-white">{{ title }}</h2>
  </div>

  <form [formGroup]="seccionForm" (ngSubmit)="onSubmit()" class="space-y-4">
    <!-- Título -->
    <div>
      <label for="titulo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Título <span class="text-red-500">*</span>
      </label>
      <input type="text" id="titulo" formControlName="titulo"
             class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
             placeholder="Ingrese el título de la sección">
      <div *ngIf="seccionForm.get('titulo')?.hasError('required') && seccionForm.get('titulo')?.touched"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título es obligatorio
      </div>
      <div *ngIf="seccionForm.get('titulo')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título no puede exceder los 100 caracteres
      </div>
    </div>

    <!-- Descripción -->
    <div>
      <label for="descripcion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Descripción
      </label>
      <textarea id="descripcion" formControlName="descripcion" rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                placeholder="Ingrese una descripción para la sección"></textarea>
      <div *ngIf="seccionForm.get('descripcion')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        La descripción no puede exceder los 500 caracteres
      </div>
    </div>

    <!-- Orden -->
    <div>
      <label for="orden" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Orden
      </label>
      <input type="number" id="orden" formControlName="orden"
             class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
             placeholder="Orden de la sección">
      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
        Si no se especifica, se asignará automáticamente
      </p>
    </div>

    <!-- Información del módulo -->
    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
      <div class="flex items-center">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Módulo:</span>
        <span class="text-sm italic text-gray-600 dark:text-gray-400">{{ modulo.titulo || modulo.nombre }}</span>
      </div>
    </div>

    <!-- Botones de acción -->
    <div class="flex justify-end pt-3 border-t border-gray-200 dark:border-gray-700">
      <button type="button" (click)="onCancel()" [disabled]="loading"
              class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm disabled:opacity-50">
        Cancelar
      </button>
      <button type="submit" [disabled]="seccionForm.invalid || loading"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed">
        <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>{{ isEditMode ? 'Actualizar' : 'Crear' }}</span>
      </button>
    </div>
  </form>
</div>
