<!-- src/app/pages/postulacion/postulacion.component.html -->
<div
  class="max-w-7xl mx-auto p-6 bg-white dark:bg-gray-800 shadow rounded-xl dark:shadow-gray-900"
>
  <h2 class="text-2xl font-semibold mb-6 text-gray-700 dark:text-gray-100">
    Listado de Postulaciones
  </h2>

  <!-- Filtros -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
    <div>
      <label
        class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-1"
        >Buscar por nombre</label
      >
      <input
        type="text"
        [(ngModel)]="filtroNombre"
        class="w-full px-3 py-2 border dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring focus:ring-blue-300 dark:focus:ring-blue-500 text-sm dark:bg-gray-700 dark:text-white"
      />
    </div>

    <!-- Fechas: Desde y Hasta -->
    <div>
      <label
        class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-1"
        >Desde</label
      >
      <input
        type="date"
        [(ngModel)]="filtroFechaDesde"
        class="w-full px-3 py-2 border dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring focus:ring-blue-300 dark:focus:ring-blue-500 text-sm dark:bg-gray-700 dark:text-white"
      />
    </div>

    <div>
      <label
        class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-1"
        >Hasta</label
      >
      <input
        type="date"
        [(ngModel)]="filtroFechaHasta"
        class="w-full px-3 py-2 border dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring focus:ring-blue-300 dark:focus:ring-blue-500 text-sm dark:bg-gray-700 dark:text-white"
      />
    </div>

    <div>
      <label
        class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-1"
        >Filtrar por sede</label
      >
      <select
        [(ngModel)]="filtroSede"
        class="w-full px-3 py-2 border dark:border-gray-600 rounded-lg shadow-sm text-sm focus:outline-none focus:ring focus:ring-blue-300 dark:focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
      >
        <option value="">-- Todas --</option>
        <option *ngFor="let sede of sedesUnicas" [value]="sede">
          {{ sede }}
        </option>
      </select>
    </div>

    <div>
      <label
        class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-1"
        >Filtrar por campaña</label
      >
      <select
        [(ngModel)]="filtroCampania"
        class="w-full px-3 py-2 border dark:border-gray-600 rounded-lg shadow-sm text-sm focus:outline-none focus:ring focus:ring-blue-300 dark:focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
      >
        <option value="">-- Todas --</option>
        <option *ngFor="let c of campaniasUnicas" [value]="c">{{ c }}</option>
      </select>
    </div>
  </div>

  <!-- Acciones -->
  <div class="flex justify-between items-center mb-4">
    <button
      (click)="descargarExcel()"
      class="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white text-sm px-4 py-2 rounded-lg flex items-center gap-2 shadow dark:shadow-gray-900"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5m0 0l5-5m-5 5V4"
        />
      </svg>
      Descargar Excel
    </button>

    <button
      (click)="filtrarPostulaciones()"
      class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white text-sm px-6 py-2 rounded-lg shadow dark:shadow-gray-900"
    >
      Filtrar
    </button>
  </div>

  <!-- Tabla -->
  <div
    class="overflow-auto rounded-xl border border-gray-200 dark:border-gray-700"
  >
    <table class="min-w-full bg-white dark:bg-gray-800 text-sm text-left">
      <thead
        class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 uppercase text-xs"
      >
        <tr>
          <th class="px-4 py-3 border-b dark:border-gray-600">Documento</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Apellidos</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Celular</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Campaña</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Sede</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Fecha</th>
          <th class="px-4 py-3 border-b dark:border-gray-600">Hora</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let row of postulacionesFiltradas"
          class="hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.documento }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.apellidos }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.celular }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.campania }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.sede }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.fecha_postulacion | date : "yyyy-MM-dd" }}
          </td>
          <td
            class="px-4 py-2 border-b dark:border-gray-600 dark:text-gray-200"
          >
            {{ row.hora_postulacion }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
