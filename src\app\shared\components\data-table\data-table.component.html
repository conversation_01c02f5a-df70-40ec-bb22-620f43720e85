<div class="data-table-container">
  <div class="data-table-header">
    <div class="data-table-title">
      <h2 *ngIf="title">{{title}}</h2>
      <p *ngIf="subtitle" class="subtitle">{{subtitle}}</p>
    </div>
    <div class="data-table-actions">
      <mat-form-field *ngIf="showSearch" appearance="outline" class="search-field" floatLabel="always">
        <mat-label>{{searchPlaceholder}}</mat-label>
        <input matInput [(ngModel)]="searchText" (keyup)="applyFilter($event)" (input)="onSearchChange($event)" placeholder="">
        <mat-icon matSuffix>search</mat-icon>
        <button *ngIf="searchText" matSuffix mat-icon-button aria-label="Limpiar" (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <div class="action-buttons">
        <button *ngIf="allowAdd" mat-raised-button color="primary" (click)="onAddClick()" class="add-button faq-add-button">
          <mat-icon>add</mat-icon> {{addButtonText}}
        </button>
        <button *ngIf="allowRefresh" mat-icon-button matTooltip="Refrescar" (click)="onRefreshClick()">
          <mat-icon>refresh</mat-icon>
        </button>
        <button *ngIf="allowExport" mat-icon-button matTooltip="Exportar" (click)="onExportClick()">
          <mat-icon>download</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <div class="data-table-content">
    <div class="loading-overlay" *ngIf="loading">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Cargando datos...</p>
    </div>

    <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z1 data-table">
      <!-- Dynamic columns -->
      <ng-container *ngFor="let column of columns" [matColumnDef]="column.property">
        <th mat-header-cell *matHeaderCellDef mat-sort-header [disabled]="!column.sortable">
          {{column.name}}
        </th>
        <td mat-cell *matCellDef="let row" [ngClass]="column.cellClass || ''" (click)="onRowClick(row)">
          <!-- Custom template if provided -->
          <ng-container *ngIf="column.template; else defaultCell">
            <ng-container *ngTemplateOutlet="column.template; context: {$implicit: row, column: column}"></ng-container>
          </ng-container>

          <!-- Default cell rendering -->
          <ng-template #defaultCell>
            <ng-container [ngSwitch]="column.type">
              <!-- Badge type -->
              <span *ngSwitchCase="'badge'" class="badge" [ngClass]="column.badgeClass ? column.badgeClass(row) : ''">
                {{row[column.property]}}
              </span>

              <!-- Date type -->
              <span *ngSwitchCase="'date'">
                {{row[column.property] | date: column.format || 'dd/MM/yyyy'}}
              </span>

              <!-- Boolean type -->
              <span *ngSwitchCase="'boolean'">
                <mat-icon *ngIf="row[column.property]" class="success-icon">check_circle</mat-icon>
                <mat-icon *ngIf="!row[column.property]" class="error-icon">cancel</mat-icon>
              </span>

              <!-- Link type -->
              <a *ngSwitchCase="'link'" [href]="row[column.property]" target="_blank" mat-button color="primary" (click)="$event.stopPropagation()">
                <mat-icon>{{column.icon || 'link'}}</mat-icon> {{column.linkText || 'Ver'}}
              </a>

              <!-- Image type -->
              <img *ngSwitchCase="'image'" [src]="row[column.property]" [alt]="column.alt || ''" class="table-image">

              <!-- Default type -->
              <span *ngSwitchDefault>{{row[column.property]}}</span>
            </ng-container>
          </ng-template>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Acciones</th>
        <td mat-cell *matCellDef="let row" class="actions-cell">
          <div class="action-buttons">
            <button *ngIf="allowEdit" mat-icon-button color="primary" matTooltip="Editar" (click)="onEditClick(row); $event.stopPropagation()">
              <mat-icon>edit</mat-icon>
            </button>
            <button *ngIf="allowDelete" mat-icon-button color="warn" matTooltip="Eliminar" (click)="onDeleteClick(row); $event.stopPropagation()">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </td>
      </ng-container>

      <!-- Header and Row Definitions -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="data-row"></tr>

      <!-- Empty State Row -->
      <tr class="empty-row" *matNoDataRow>
        <td [attr.colspan]="displayedColumns.length" class="empty-cell">
          <div class="empty-state" *ngIf="!loading && (!dataSource.data || dataSource.data.length === 0)">
            <mat-icon>info</mat-icon>
            <p>{{emptyStateText}}</p>
          </div>
          <div class="empty-state" *ngIf="!loading && dataSource.data && dataSource.data.length > 0">
            <mat-icon>search_off</mat-icon>
            <p>No se encontraron resultados para "{{searchText}}"</p>
          </div>
        </td>
      </tr>
    </table>

    <mat-paginator *ngIf="showPagination"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [length]="totalItems"
      showFirstLastButtons
      (page)="onPageChange($event)">
    </mat-paginator>
  </div>
</div>
