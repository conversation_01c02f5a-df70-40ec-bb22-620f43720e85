// src/app/pages/faq/faq.module.ts
import {
  NgModule,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

/* ComponenteS */
import { FaqListComponent } from './components/faq-list/faq-list.component';
import { CardV1Component } from './components/card-v1';
import { ModalFormComponent } from './components/modal-form/modal-form.component';
import { FaqDialogComponent } from './components/faq-dialog/faq-dialog.component';
import { FaqRespuestasComponent } from './components/faq-respuestas/faq-respuestas.component';

/* Angular‑Material que usa el formulario */
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatSnackBarModule } from '@angular/material/snack-bar';

/* ngx‑bootstrap */
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { ModalModule } from 'ngx-bootstrap/modal';
import { FaqRoutingModule } from './FaqRoutingModule';
import { HighlightPipe } from './pipes/highlight.pipe';
import { ReplaceAttributePipe } from './pipes/replace.pipe';
// Importamos directamente el pipe ArrayToDate

import { PlaceholderCardComponent } from './pipes/placeholder-card.component';
import { FormUploadComponent } from './components/form-upload/form-upload.component';
import { DateArrayPipe } from './pipes/date-array.pipe';

/* Shared Modules */
import { PopupsModule } from '@app/shared/popups';

/* NgRx */
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { faqReducer } from './store/faq.reducer';
import { FaqEffects } from './store/faq.effects';

@NgModule({
  declarations: [
    FaqListComponent,
    CardV1Component,
    ModalFormComponent,
    FaqDialogComponent,
    HighlightPipe,
    ReplaceAttributePipe,
    PlaceholderCardComponent,
    FormUploadComponent,
    DateArrayPipe,
    FaqRespuestasComponent, // 👈 nuevo componente para respuestas múltiples
  ],
  schemas: [
    // Esto permite usar elementos personalizados y atributos desconocidos
    // Útil durante el desarrollo para evitar errores de compilación
    CUSTOM_ELEMENTS_SCHEMA,
    NO_ERRORS_SCHEMA,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,

    /* material */
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatIconModule,
    MatSelectModule,
    MatButtonModule,
    MatDialogModule,
    MatListModule,
    MatSnackBarModule,

    /* ngx‑bootstrap */
    PaginationModule.forRoot(),
    AccordionModule.forRoot(),
    BsDropdownModule.forRoot(),
    ModalModule.forRoot(),

    /* Shared Modules */
    PopupsModule,

    /* NgRx */
    StoreModule.forFeature('faq', faqReducer),
    EffectsModule.forFeature([FaqEffects]),

    FaqRoutingModule,
  ],
})
export class FaqModule {}
