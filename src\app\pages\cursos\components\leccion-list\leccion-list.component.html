<div class="p-2">
  <div class="flex items-center mb-2">
    <button class="text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 p-1 rounded mr-1" (click)="onBack()">
      <mat-icon class="text-sm">arrow_back</mat-icon>
    </button>
    <h3 *ngIf="modulo" class="text-sm font-medium text-gray-800 dark:text-white flex-grow truncate">
      <span class="text-indigo-600 dark:text-indigo-400">Lecciones:</span> {{ modulo.titulo || modulo.nombre }}
    </h3>
    <h3 *ngIf="seccion" class="text-sm font-medium text-gray-800 dark:text-white flex-grow truncate">
      <span class="text-indigo-600 dark:text-indigo-400">Lecciones:</span> {{ seccion.titulo }}
    </h3>
    <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editLeccion.emit(null)">
      <mat-icon class="mr-1 text-xs">add</mat-icon> Nueva
    </button>
  </div>

  <div class="relative min-h-[50px]">
    <app-spinner *ngIf="loading"></app-spinner>

    <div *ngIf="error" class="flex items-center text-red-600 dark:text-red-400 text-xs mb-2">
      <mat-icon class="mr-1 text-xs">error</mat-icon>
      <span>{{ error }}</span>
    </div>

    <div *ngIf="!loading && !error && lecciones.length === 0" class="flex flex-col items-center py-3 text-center">
      <mat-icon class="text-indigo-500 dark:text-indigo-400 mb-1">video_library</mat-icon>
      <p *ngIf="modulo" class="text-xs text-gray-600 dark:text-gray-400 mb-2">No hay lecciones disponibles</p>
      <p *ngIf="seccion" class="text-xs text-gray-600 dark:text-gray-400 mb-2">No hay lecciones disponibles</p>
      <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editLeccion.emit(null)">
        <mat-icon class="mr-1 text-xs">add</mat-icon> Crear Lección
      </button>
    </div>

    <div cdkDropList (cdkDropListDropped)="onDrop($event)" class="space-y-2" *ngIf="lecciones.length > 0">
      <div *ngFor="let leccion of lecciones" class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm relative p-2 cursor-move" cdkDrag>
        <div class="absolute top-1 right-1 text-gray-400 dark:text-gray-500" cdkDragHandle>
          <mat-icon class="text-sm">drag_indicator</mat-icon>
        </div>

        <div class="flex items-start mb-1">
          <div class="flex-shrink-0 mr-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-full p-1">
            <mat-icon class="text-indigo-600 dark:text-indigo-400 text-sm">
              {{ leccion.tipoLeccion === TipoLeccion.VIDEO ? 'videocam' :
                 leccion.tipoLeccion === TipoLeccion.CUESTIONARIO ? 'quiz' :
                 leccion.tipoLeccion === TipoLeccion.PDF ? 'picture_as_pdf' : 'video_library' }}
            </mat-icon>
          </div>
          <div class="flex-grow">
            <div class="flex items-center">
              <h4 class="text-sm font-medium text-gray-800 dark:text-white mr-2">{{ leccion.titulo || leccion.nombre }}</h4>
              <div class="ml-auto text-xs px-1.5 py-0.5 rounded-full"
                   [ngClass]="{'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': leccion.estado === 'A',
                              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': leccion.estado === 'I'}">
                {{ leccion.estado === 'A' ? 'Activo' : 'Inactivo' }}
              </div>
            </div>
            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <span class="mr-2">Orden: {{ leccion.orden }}</span>
              <span *ngIf="leccion.duracion" class="flex items-center">
                <mat-icon class="mr-0.5 text-xs">timer</mat-icon> {{ getDuracionFormateada(leccion.duracion) }}
              </span>
            </div>
          </div>
        </div>

        <p class="text-xs text-gray-600 dark:text-gray-400 mb-1 line-clamp-1">{{ leccion.descripcion }}</p>

        <div class="flex flex-wrap gap-1 mb-1">
          <div *ngIf="leccion.tipoLeccion === TipoLeccion.VIDEO && leccion.videoUrl"
               class="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-1.5 py-0.5 rounded flex items-center">
            <mat-icon class="mr-0.5 text-xs">videocam</mat-icon>
            <span>Video</span>
          </div>

          <div *ngIf="leccion.tipoLeccion === TipoLeccion.CUESTIONARIO"
               class="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-1.5 py-0.5 rounded flex items-center">
            <mat-icon class="mr-0.5 text-xs">quiz</mat-icon>
            <span>Cuestionario</span>
          </div>

          <div *ngIf="leccion.tipoLeccion === TipoLeccion.PDF && leccion.pdfUrl"
               class="text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-1.5 py-0.5 rounded flex items-center">
            <mat-icon class="mr-0.5 text-xs">picture_as_pdf</mat-icon>
            <span>PDF</span>
          </div>
        </div>

        <div class="flex justify-end space-x-1 border-t border-gray-100 dark:border-gray-700 pt-1">
          <button class="text-xs px-1.5 py-0.5 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20 rounded flex items-center" (click)="onViewLeccion(leccion)">
            <mat-icon class="mr-0.5 text-xs">visibility</mat-icon> Ver
          </button>
          <button class="text-xs px-1.5 py-0.5 text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20 rounded flex items-center" (click)="onEditLeccion(leccion)">
            <mat-icon class="mr-0.5 text-xs">edit</mat-icon> Editar
          </button>
          <button class="text-xs px-1.5 py-0.5 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded flex items-center" (click)="onDeleteLeccion(leccion.id)">
            <mat-icon class="mr-0.5 text-xs">delete</mat-icon> Eliminar
          </button>
          <button *ngIf="leccion.tipoLeccion === TipoLeccion.CUESTIONARIO"
                  class="text-xs px-1.5 py-0.5 text-purple-600 hover:bg-purple-50 dark:text-purple-400 dark:hover:bg-purple-900/20 rounded flex items-center"
                  (click)="gestionarCuestionario(leccion)">
            <mat-icon class="mr-0.5 text-xs">quiz</mat-icon> Cuestionario
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
