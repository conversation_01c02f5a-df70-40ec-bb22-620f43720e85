import { Action } from '@ngrx/store';
import { AnuncioCreateRequest, AnuncioResponse } from './save.models';

export enum Types {
  CREATE = '[Anuncio] Create: Start',
  CREATE_SUCCESS = '[Anuncio] Create: Success',
  CREATE_ERROR = '[Anuncio] Create: Error',

  READ = '[Anuncio] Read',
  READ_SUCCESS = '[Anuncio] Read:Success',
  READ_ERROR = '[Anuncio] Read:Error',

  UPDATE = '[Anuncio] Update: Start',
  UPDATE_SUCCESS = '[Anuncio] Update: Success',
  UPDATE_ERROR = '[Anuncio] Update: Error',

  DELETE = '[Anuncio] Delete: Start',
  DELETE_SUCCESS = '[Anuncio] Delete: Success',
  DELETE_ERROR = '[Anuncio] Delete: Error',

  READ_RECENT = '[Anuncios] Read Recent',
  READ_RECENT_SUCCESS = '[Anuncios] Read Recent Success',
  READ_RECENT_ERROR = '[Anuncios] Read Recent Error',
}

export class Read implements Action {
  readonly type = Types.READ;
  constructor(public page: number = 0) {}
}

export class ReadSuccess implements Action {
  readonly type = Types.READ_SUCCESS;
  constructor(
    public anuncios: AnuncioResponse[],
    public totalElements: number,
    public totalPages: number,
    public currentPage: number
  ) {}
}

export class ReadError implements Action {
  readonly type = Types.READ_ERROR;
  constructor(public error: string) {}
}

// Acción para leer recientes
export class ReadRecent implements Action {
  readonly type = Types.READ_RECENT;
  constructor(public page: number = 0) {}
}

export class ReadRecentSuccess implements Action {
  readonly type = Types.READ_RECENT_SUCCESS;
  constructor(
    public anuncios: AnuncioResponse[],
    public total: number,
    public pages: number,
    public page: number
  ) {}
}

export class ReadRecentError implements Action {
  readonly type = Types.READ_RECENT_ERROR;
  constructor(public error: string) {}
}

export class Create implements Action {
  readonly type = Types.CREATE;
  constructor(public anuncio: AnuncioCreateRequest) {}
}

export class CreateSuccess implements Action {
  readonly type = Types.CREATE_SUCCESS;
  constructor(public anuncio: AnuncioResponse) {}
}

export class CreateError implements Action {
  readonly type = Types.CREATE_ERROR;
  constructor(public error: string) {}
}

export class Delete implements Action {
  readonly type = Types.DELETE;
  constructor(public id: number) {}
}

export class DeleteSuccess implements Action {
  readonly type = Types.DELETE_SUCCESS;
  constructor() {}
}

export class DeleteError implements Action {
  readonly type = Types.DELETE_ERROR;
  constructor(public error: string) {}
}

export class Update implements Action {
  readonly type = Types.UPDATE;
  constructor(public id: number, public anuncio: AnuncioCreateRequest) {}
}

export class UpdateSuccess implements Action {
  readonly type = Types.UPDATE_SUCCESS;
  constructor(public anuncio: AnuncioResponse) {}
}

export class UpdateError implements Action {
  readonly type = Types.UPDATE_ERROR;
  constructor(public error: string) {}
}

export type All =
  | Read
  | ReadSuccess
  | ReadError
  | Create
  | CreateSuccess
  | CreateError
  | Update
  | UpdateSuccess
  | UpdateError
  | Delete
  | DeleteSuccess
  | DeleteError
  | ReadRecent
  | ReadRecentSuccess
  | ReadRecentError;
