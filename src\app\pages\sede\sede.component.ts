import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Sede, SedePaginadoResponse } from '@app/models/backend/sede/sede.model';
import { SedeService, SedePagination } from '@app/services/sede.service';
import { ThemeService } from '@app/services/theme.service';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { SedeDialogComponent } from './sede-dialog/sede-dialog.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-sede',
  templateUrl: './sede.component.html'
})
export class SedeComponent implements OnInit {
  // Columnas para la tabla
  displayedColumns: string[] = ['id', 'nombre', 'direccion', 'ciudad', 'provincia', 'telefono', 'email', 'estado', 'acciones'];

  // Datos para la tabla
  tableData = new MatTableDataSource<Sede>([]);
  tableTotalItems = 0;
  tableLoading = false;

  // Paginación
  currentPage = 0;
  pageSize = 10;
  pageSizeOptions = [5, 10, 25, 50];

  // Búsqueda
  searchText = '';

  // Tema
  isDarkTheme = false;

  // Suscripciones
  private subscriptions: Subscription[] = [];

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private sedeService: SedeService,
    private dialog: MatDialog,
    private themeService: ThemeService
  ) { }

  ngOnInit(): void {
    // Cargar sedes
    this.loadSedes();

    // Suscribirse al tema
    this.subscriptions.push(
      this.themeService.darkMode$.subscribe((isDark: boolean) => {
        this.isDarkTheme = isDark;
      })
    );
  }

  ngOnDestroy(): void {
    // Desuscribirse para evitar memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Cargar sedes con paginación
  loadSedes(): void {
    this.tableLoading = true;

    const pagination: SedePagination = {
      page: this.currentPage,
      perPage: this.pageSize,
      search: this.searchText,
      column: 'id',
      order: 'asc'
    };

    this.sedeService.getSedes(pagination).subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.tableData.data = response.data.sedes;
          this.tableTotalItems = response.data.totalItems;
          this.currentPage = response.data.currentPage;
        } else {
          this.tableData.data = [];
          this.tableTotalItems = 0;
        }
        this.tableLoading = false;
      },
      error: (error) => {
        console.error('Error al cargar sedes:', error);
        this.tableLoading = false;
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar las sedes. Por favor, intente nuevamente.'
        });
      }
    });
  }

  // Manejar cambio de página
  onPageChange(event: any): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSedes();
  }

  // Cambiar tamaño de página
  onPageSizeChange(): void {
    this.currentPage = 0;
    this.loadSedes();
  }

  // Ir a la primera página
  onFirstPage(): void {
    this.currentPage = 0;
    this.loadSedes();
  }

  // Ir a la página anterior
  onPreviousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.loadSedes();
    }
  }

  // Ir a la página siguiente
  onNextPage(): void {
    if (this.currentPage < this.getTotalPages() - 1) {
      this.currentPage++;
      this.loadSedes();
    }
  }

  // Ir a la última página
  onLastPage(): void {
    this.currentPage = this.getTotalPages() - 1;
    this.loadSedes();
  }

  // Calcular el número total de páginas
  getTotalPages(): number {
    return Math.ceil(this.tableTotalItems / this.pageSize) || 1;
  }

  // Buscar sedes
  onSearch(): void {
    this.currentPage = 0;
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.loadSedes();
  }

  // Limpiar búsqueda
  clearSearch(): void {
    this.searchText = '';
    this.onSearch();
  }

  // Abrir diálogo para crear sede
  openCreateDialog(): void {
    const dialogRef = this.dialog.open(SedeDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      disableClose: false,
      maxHeight: '90vh',
      panelClass: ['responsive-dialog', 'modern-modal', this.isDarkTheme ? 'dark-theme' : ''],
      data: { mode: 'create', sede: new Sede() }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSedes();
      }
    });
  }

  // Abrir diálogo para editar sede
  openEditDialog(sede: Sede): void {
    // Verificar que la sede tenga un ID válido
    if (!sede.id) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'No se puede editar una sede sin ID.'
      });
      return;
    }

    const dialogRef = this.dialog.open(SedeDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      disableClose: false,
      maxHeight: '90vh',
      panelClass: ['responsive-dialog', 'modern-modal', this.isDarkTheme ? 'dark-theme' : ''],
      data: { mode: 'edit', sede: {...sede} }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSedes();
      }
    });
  }

  // Eliminar sede
  deleteSede(sede: Sede): void {
    // Verificar que la sede tenga un ID válido
    if (!sede.id) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'No se puede eliminar una sede sin ID.'
      });
      return;
    }

    Swal.fire({
      title: '¿Está seguro?',
      text: `¿Desea eliminar la sede ${sede.nombre}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        this.sedeService.deleteSede(sede.id as number).subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              Swal.fire({
                icon: 'success',
                title: 'Eliminado',
                text: 'La sede ha sido eliminada correctamente.'
              });
              this.loadSedes();
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: response.msg || 'No se pudo eliminar la sede.'
              });
            }
          },
          error: (error) => {
            console.error('Error al eliminar sede:', error);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'No se pudo eliminar la sede. Por favor, intente nuevamente.'
            });
          }
        });
      }
    });
  }
}
