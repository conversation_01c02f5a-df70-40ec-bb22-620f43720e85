<div class="p-2">
  <div class="flex justify-between items-center mb-2">
    <h3 class="text-sm font-medium text-indigo-600 dark:text-indigo-400 flex items-center">
      <mat-icon class="mr-1 text-sm">folder</mat-icon>
      Secciones
    </h3>
    <button *ngIf="editable" class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="onCreateSeccion()" matTooltip="Crear nueva sección">
      <mat-icon class="mr-1 text-xs">add</mat-icon> Nueva
    </button>
  </div>

  <div *ngIf="loading" class="flex justify-center py-2">
    <mat-spinner diameter="30"></mat-spinner>
  </div>

  <div *ngIf="error && !loading" class="text-xs text-red-600 dark:text-red-400 p-2 flex items-center">
    <mat-icon class="mr-1 text-xs">error</mat-icon>
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!loading && !error && secciones.length === 0" class="flex flex-col items-center py-3 text-center">
    <mat-icon class="text-indigo-500 dark:text-indigo-400 mb-1">folder_open</mat-icon>
    <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">No hay secciones disponibles</p>
    <button *ngIf="editable" class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="onCreateSeccion()">
      <mat-icon class="mr-1 text-xs">add</mat-icon> Crear Sección
    </button>
  </div>

  <div cdkDropList (cdkDropListDropped)="onDrop($event)" [cdkDropListDisabled]="!editable" class="space-y-1.5">
    <div *ngFor="let seccion of secciones"
         class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-1.5 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
         cdkDrag [cdkDragDisabled]="!editable"
         (click)="onSeccionClick(seccion)">
      <div class="flex items-center">
        <div class="flex-grow">
          <div class="flex items-center">
            <span class="text-xs font-medium text-gray-500 dark:text-gray-400 mr-1">{{ seccion.orden }}.</span>
            <span class="text-sm font-medium text-gray-800 dark:text-white">{{ seccion.titulo }}</span>
          </div>
          <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-0.5">
            <span class="flex items-center mr-2">
              <mat-icon class="mr-0.5 text-xs">video_library</mat-icon>
              {{ getLeccionesCount(seccion) }} lecciones
            </span>
            <span *ngIf="isSeccionCompleted(seccion)" class="flex items-center text-green-600 dark:text-green-400">
              <mat-icon class="mr-0.5 text-xs">check_circle</mat-icon>
              Completado
            </span>
          </div>
        </div>
        <div *ngIf="editable" class="flex space-x-1">
          <button class="text-xs p-1 text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20 rounded"
                  (click)="onEditSeccion(seccion, $event)" matTooltip="Editar sección">
            <mat-icon class="text-xs">edit</mat-icon>
          </button>
          <button class="text-xs p-1 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded"
                  (click)="onDeleteSeccion(seccion.id, $event)" matTooltip="Eliminar sección">
            <mat-icon class="text-xs">delete</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
