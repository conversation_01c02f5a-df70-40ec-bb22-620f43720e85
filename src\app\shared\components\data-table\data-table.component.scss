.data-table-container {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 2rem;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

.data-table-header {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.data-table-title {
  h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1976d2;
  }

  .subtitle {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.data-table-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.search-field {
  width: 280px;

  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }

  ::ng-deep .mat-form-field-infix {
    padding: 0.5em 0;
    border-top: 0;
    display: flex;
    align-items: center;
  }

  ::ng-deep .mat-form-field-flex {
    height: 44px;
    align-items: center;
  }

  ::ng-deep .mat-form-field-suffix {
    align-self: center;
  }

  @media (max-width: 768px) {
    width: 100%;
  }
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  align-items: center;

  button[mat-raised-button] {
    height: 44px;
    line-height: 44px;
    padding: 0 20px;
    font-size: 15px;
    font-weight: 500;

    &.add-button {
      min-width: 150px;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    mat-icon {
      font-size: 20px;
      margin-right: 6px;
    }
  }

  button[mat-icon-button] {
    height: 40px;
    width: 40px;

    mat-icon {
      font-size: 18px;
    }
  }

  @media (max-width: 768px) {
    justify-content: flex-end;
    width: 100%;
  }
}

.data-table-content {
  position: relative;
  overflow-x: auto;
}

.data-table {
  width: 100%;
}

.mat-header-cell {
  background-color: #f1f3f5;
  color: #343a40;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 1rem;
  white-space: nowrap;
}

.mat-cell {
  padding: 0.875rem 1rem;
  font-size: 0.9rem;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
}

.data-row {
  transition: background-color 0.2s ease;
  cursor: default;

  &:hover {
    background-color: #f8f9fa;
  }
}

.actions-cell {
  white-space: nowrap;
  width: 1%;
}

.empty-cell {
  padding: 2rem !important;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;

  mat-icon {
    font-size: 2.5rem;
    color: #adb5bd;
    margin-bottom: 1rem;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  border-radius: 0.375rem;
  display: inline-block;
  text-align: center;
  white-space: nowrap;
}

.success-icon {
  color: #28a745;
}

.error-icon {
  color: #dc3545;
}

.table-image {
  max-width: 50px;
  max-height: 50px;
  border-radius: 4px;
}


:host-context(.dark-theme) {
  .data-table-container {
    background-color: #0d1a2b;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  }

  .data-table-header {
    border-bottom-color: rgba(255, 255, 255, 0.08);
  }

  .data-table-title h2,
  .data-table-title .subtitle {
    color: rgba(255, 255, 255, 0.9);
  }

  .mat-header-cell,
  .mat-cell {
    color: #ffffff;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .data-row:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .empty-state {
    color: rgba(255, 255, 255, 0.7);

    mat-icon {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}
