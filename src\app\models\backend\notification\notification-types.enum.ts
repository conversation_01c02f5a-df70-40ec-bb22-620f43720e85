/**
 * Tipos de notificación
 */
export enum NotificationType {
  SYSTEM = 'SYSTEM',           // Notificación del sistema
  USER_MESSAGE = 'USER_MESSAGE', // Mensaje directo de usuario a usuario
  BROADCAST = 'BROADCAST',     // Notificación para todos los usuarios
  ROLE_BASED = 'ROLE_BASED',   // Notificación para usuarios con un rol específico
  ALERT = 'ALERT',             // Alerta importante
  INFO = 'INFO'                // Información general
}

/**
 * Categorías para organizar en la UI
 */
export enum NotificationCategory {
  INBOX = 'INBOX',             // Bandeja de entrada
  GENERAL = 'GENERAL',         // General
  ARCHIVED = 'ARCHIVED'        // Archivado
}
