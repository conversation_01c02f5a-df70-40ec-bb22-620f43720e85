/**
 * Utilidades para el manejo de fechas y horas
 */
export class DateUtils {
  /**
   * Convierte un array de fecha [año, mes, día] a string en formato YYYY-MM-DD
   * @param dateArray Array con [año, mes, día]
   * @returns String en formato YYYY-MM-DD o string vacío si es inválido
   */
  static formatDateArrayToString(dateArray: any): string {
    if (!dateArray || !Array.isArray(dateArray) || dateArray.length < 3) {
      return '';
    }

    try {
      const year = dateArray[0];
      const month = dateArray[1].toString().padStart(2, '0'); // Asegurar formato de 2 dígitos
      const day = dateArray[2].toString().padStart(2, '0'); // Asegurar formato de 2 dígitos
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('Error al formatear array de fecha:', error);
      return '';
    }
  }

  /**
   * Convierte un array de hora [hora, minuto] a string en formato HH:MM
   * @param timeArray Array con [hora, minuto]
   * @returns String en formato HH:MM o string vacío si es inválido
   */
  static formatTimeArrayToString(timeArray: any): string {
    if (!timeArray || !Array.isArray(timeArray) || timeArray.length < 2) {
      return '00:00';
    }

    try {
      const hour = timeArray[0].toString().padStart(2, '0'); // Asegurar formato de 2 dígitos
      const minute = timeArray[1].toString().padStart(2, '0'); // Asegurar formato de 2 dígitos
      
      return `${hour}:${minute}`;
    } catch (error) {
      console.warn('Error al formatear array de hora:', error);
      return '00:00';
    }
  }

  /**
   * Convierte un array de fecha [año, mes, día] a un objeto Date
   * @param dateArray Array con [año, mes, día]
   * @returns Objeto Date o null si es inválido
   */
  static dateArrayToDate(dateArray: any): Date | null {
    if (!dateArray || !Array.isArray(dateArray) || dateArray.length < 3) {
      return null;
    }

    try {
      // En JavaScript los meses van de 0 a 11, por lo que restamos 1 al mes
      const year = dateArray[0];
      const month = dateArray[1] - 1;
      const day = dateArray[2];
      
      const date = new Date(year, month, day);
      
      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        return null;
      }
      
      return date;
    } catch (error) {
      console.warn('Error al convertir array de fecha a Date:', error);
      return null;
    }
  }

  /**
   * Convierte un array de fecha y hora a un objeto Date
   * @param dateArray Array con [año, mes, día]
   * @param timeArray Array con [hora, minuto]
   * @returns Objeto Date o null si es inválido
   */
  static dateTimeArrayToDate(dateArray: any, timeArray: any): Date | null {
    if (!dateArray || !Array.isArray(dateArray) || dateArray.length < 3) {
      return null;
    }

    try {
      // En JavaScript los meses van de 0 a 11, por lo que restamos 1 al mes
      const year = dateArray[0];
      const month = dateArray[1] - 1;
      const day = dateArray[2];
      
      // Valores predeterminados para hora y minuto
      let hour = 0;
      let minute = 0;
      
      // Si hay array de hora, usarlo
      if (timeArray && Array.isArray(timeArray) && timeArray.length >= 2) {
        hour = timeArray[0];
        minute = timeArray[1];
      }
      
      const date = new Date(year, month, day, hour, minute);
      
      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        return null;
      }
      
      return date;
    } catch (error) {
      console.warn('Error al convertir arrays de fecha y hora a Date:', error);
      return null;
    }
  }

  /**
   * Función auxiliar para formatear fechas de manera segura
   * @param dateValue Valor de fecha a formatear (puede ser array, string, Date, etc.)
   * @returns Fecha formateada como string ISO o string vacío si es inválida
   */
  static formatDateSafely(dateValue: any): string {
    if (!dateValue) return '';
    
    try {
      // Si es un array (formato del backend: [año, mes, día])
      if (Array.isArray(dateValue)) {
        // Verificar que tenga al menos año, mes y día
        if (dateValue.length >= 3) {
          // Ajustar el mes (en JavaScript los meses van de 0 a 11)
          const year = dateValue[0];
          const month = dateValue[1] - 1; // Restar 1 al mes
          const day = dateValue[2];
          
          const date = new Date(year, month, day);
          
          // Verificar si la fecha es válida
          if (isNaN(date.getTime())) {
            return '';
          }
          return date.toISOString().split('T')[0]; // Solo la parte de la fecha
        }
        return ''; // Array incompleto
      }
      
      // Si ya es un string, verificar si es una fecha válida
      if (typeof dateValue === 'string') {
        // Si ya tiene formato YYYY-MM-DD, devolverlo tal cual
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
          return dateValue;
        }
        
        // Intentar convertir el string a fecha
        const date = new Date(dateValue);
        // Verificar si la fecha es válida
        if (isNaN(date.getTime())) {
          return '';
        }
        return date.toISOString().split('T')[0]; // Solo la parte de la fecha
      }
      
      // Si es un objeto Date, convertirlo a string ISO
      if (dateValue instanceof Date) {
        // Verificar si la fecha es válida
        if (isNaN(dateValue.getTime())) {
          return '';
        }
        return dateValue.toISOString().split('T')[0]; // Solo la parte de la fecha
      }
      
      // Si es otro tipo (número, etc.), intentar convertirlo a fecha
      const date = new Date(dateValue);
      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toISOString().split('T')[0]; // Solo la parte de la fecha
    } catch (error) {
      // Si hay cualquier error, devolver string vacío
      console.warn('Error al formatear fecha:', error);
      return '';
    }
  }
}
