import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { WebSocketService } from '@app/services/websocket/WebSocketService';

@Component({
  selector: 'app-websocket-status',
  templateUrl: './websocket-status.component.html'
})
export class WebsocketStatusComponent implements OnInit, OnDestroy {
  isConnected = false;
  private subscription: Subscription | null = null;

  constructor(private webSocketService: WebSocketService) { }

  ngOnInit(): void {
    this.subscription = this.webSocketService.getConnectionStatus().subscribe(
      connected => {
        this.isConnected = connected;
      }
    );
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  reconnect(): void {
    // Intentar reconectar el WebSocket
    this.webSocketService.connect();

    // Solicitar el estado de los usuarios después de la conexión
    setTimeout(() => {
      if (this.webSocketService.isConnected()) {
        this.webSocketService.requestUsersStatus();
      }
    }, 1000);
  }
}
