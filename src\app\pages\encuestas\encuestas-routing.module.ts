import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { EncuestasPageComponent } from './pages/encuestas-page/encuestas-page.component';
import { EncuestaDetalleComponent } from './components/encuesta-detalle/encuesta-detalle.component';
import { ResponderEncuestaComponent } from './components/responder-encuesta/responder-encuesta.component';
import { ResultadosEncuestaComponent } from './components/resultados-encuesta/resultados-encuesta.component';

const routes: Routes = [
  {
    path: '',
    component: EncuestasPageComponent
  },
  {
    path: 'detalle/:id',
    component: EncuestaDetalleComponent
  },
  {
    path: 'responder/:id',
    component: ResponderEncuestaComponent
  },
  {
    path: 'resultados/:id',
    component: ResultadosEncuestaComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EncuestasRoutingModule { }
