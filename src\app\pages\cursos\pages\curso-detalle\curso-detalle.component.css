/* Estilos específicos que no se pueden lograr con Tailwind */

/* Estilos para el reproductor de video */
.video-player:focus {
  outline: none;
}

/* Personalización de controles para tema oscuro */
.dark .video-player::-webkit-media-controls-panel {
  background-color: rgba(45, 45, 45, 0.7);
}

.dark .video-player::-webkit-media-controls-play-button,
.dark .video-player::-webkit-media-controls-volume-slider,
.dark .video-player::-webkit-media-controls-timeline {
  filter: brightness(1.2);
}

/* Animación de spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Estilos para limitar líneas de texto */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
