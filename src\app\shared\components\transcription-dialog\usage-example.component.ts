import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './transcription-dialog.component';

@Component({
  selector: 'app-usage-example',
  template: `
    <div class="container p-6">
      <h2 class="text-2xl font-bold mb-6">Ejemplos de Uso del Modal de Transcripción</h2>
      
      <div class="examples-grid">
        
        <!-- Ejemplo 1: Botón básico -->
        <div class="example-card">
          <h3>1. Transcripción Básica</h3>
          <p>Abre el modal para subir un archivo de audio</p>
          <button 
            type="button"
            mat-raised-button 
            color="primary" 
            (click)="openBasicTranscription()">
            <mat-icon>audiotrack</mat-icon>
            Transcribir Audio
          </button>
        </div>

        <!-- Ejemplo 2: Con datos de cliente -->
        <div class="example-card">
          <h3>2. Con Datos de Cliente</h3>
          <p>Transcripción asociada a un cliente específico</p>
          <button 
            type="button"
            mat-raised-button 
            color="accent" 
            (click)="openClientTranscription()">
            <mat-icon>person</mat-icon>
            Transcribir para Cliente
          </button>
        </div>

        <!-- Ejemplo 3: Botón flotante -->
        <div class="example-card">
          <h3>3. Botón Flotante</h3>
          <p>Botón flotante para acceso rápido</p>
          <button 
            type="button"
            mat-fab 
            color="primary" 
            (click)="openBasicTranscription()"
            matTooltip="Transcribir Audio">
            <mat-icon>mic</mat-icon>
          </button>
        </div>

        <!-- Ejemplo 4: En menú -->
        <div class="example-card">
          <h3>4. Opción de Menú</h3>
          <p>Como opción en un menú desplegable</p>
          <button mat-button [matMenuTriggerFor]="menu">
            <mat-icon>more_vert</mat-icon>
            Opciones
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="openBasicTranscription()">
              <mat-icon>record_voice_over</mat-icon>
              <span>Transcribir Audio</span>
            </button>
            <button mat-menu-item>
              <mat-icon>download</mat-icon>
              <span>Descargar</span>
            </button>
          </mat-menu>
        </div>
      </div>

      <!-- Información de uso -->
      <div class="info-section">
        <h3>📋 Cómo Usar:</h3>
        <ol>
          <li>Haz clic en cualquier botón de arriba</li>
          <li>Se abrirá el modal de transcripción</li>
          <li>Arrastra y suelta un archivo de audio o haz clic para seleccionar</li>
          <li>Configura las opciones de transcripción</li>
          <li>Haz clic en "Iniciar Transcripción"</li>
          <li>Espera a que se complete el proceso</li>
        </ol>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .examples-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .example-card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      background: white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .example-card h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 18px;
    }
    
    .example-card p {
      margin: 0 0 15px 0;
      color: #666;
      font-size: 14px;
    }
    
    .example-card button {
      width: 100%;
    }
    
    .example-card:nth-child(3) button {
      width: auto;
      margin: 0 auto;
      display: block;
    }
    
    .info-section {
      background: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #2196F3;
    }
    
    .info-section h3 {
      margin: 0 0 15px 0;
      color: #333;
    }
    
    .info-section ol {
      margin: 0;
      padding-left: 20px;
    }
    
    .info-section li {
      margin-bottom: 8px;
      color: #555;
    }
  `]
})
export class UsageExampleComponent {

  constructor(private dialog: MatDialog) {}

  /**
   * Abre el modal de transcripción básico
   */
  openBasicTranscription(): void {
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true
    };

    this.openTranscriptionDialog(dialogData);
  }

  /**
   * Abre el modal de transcripción con datos de cliente
   */
  openClientTranscription(): void {
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true,
      cliente: {
        nombres: 'María José',
        apellidos: 'González López'
      },
      numeroMovil: '987654321'
    };

    this.openTranscriptionDialog(dialogData);
  }

  /**
   * Método común para abrir el modal
   */
  private openTranscriptionDialog(data: TranscriptionDialogData): void {
    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '95vw',
      maxWidth: '1000px',
      height: '95vh',
      maxHeight: '900px',
      disableClose: false,
      data: data
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Transcripción completada:', result);
        // Aquí puedes manejar el resultado
        this.handleTranscriptionSuccess(result);
      } else if (result === false) {
        console.log('❌ Transcripción cancelada');
      }
    });
  }

  /**
   * Maneja el resultado exitoso de la transcripción
   */
  private handleTranscriptionSuccess(result: any): void {
    // Aquí puedes hacer lo que necesites con el resultado
    console.log('Texto transcrito:', result.transcription);
    console.log('Estado de ánimo:', result.mood);
    console.log('Duración:', result.duration);
    
    // Ejemplo: mostrar una notificación
    alert(`¡Transcripción completada!\n\nTexto: ${result.transcription?.substring(0, 100)}...`);
  }
}
