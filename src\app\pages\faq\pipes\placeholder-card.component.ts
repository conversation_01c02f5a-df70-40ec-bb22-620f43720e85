// src/app/pages/faq/components/placeholder-card/placeholder-card.component.ts
import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-placeholder-card',
  template: `
    <ng-container *ngFor="let _ of [].constructor(rows)">
      <div class="placeholder-glow my-2">
        <span class="placeholder col-12 d-block" style="height:3rem"></span>
      </div>
    </ng-container>
  `,
})
export class PlaceholderCardComponent { @Input() rows = 5; }
