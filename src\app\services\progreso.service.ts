import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  ProgresoUsuario,
  ProgresoLeccion,
  ProgresoCreateRequest,
  ProgresoUpdateRequest,
  CompletarLeccionRequest
} from '@app/models/backend/curso/progreso.model';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ProgresoService {
  private baseUrl = environment.url + 'api/progreso';
  private progresoSubject = new BehaviorSubject<ProgresoUsuario | null>(null);
  public progreso$ = this.progresoSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene el progreso de un usuario en un curso
   */
  getProgresoByCursoAndUsuario(cursoId: number, usuarioId: number): Observable<GenericResponse<ProgresoUsuario>> {
    return this.http.get<GenericResponse<ProgresoUsuario>>(`${this.baseUrl}/curso/${cursoId}/usuario/${usuarioId}`).pipe(
      map(response => {
        if (response.data) {
          this.progresoSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene todos los progresos de un usuario
   */
  getProgresosByUsuario(usuarioId: number): Observable<GenericResponse<ProgresoUsuario[]>> {
    return this.http.get<GenericResponse<ProgresoUsuario[]>>(`${this.baseUrl}/usuario/${usuarioId}`);
  }

  /**
   * Crea un nuevo registro de progreso
   */
  createProgreso(progreso: ProgresoCreateRequest): Observable<GenericResponse<ProgresoUsuario>> {
    return this.http.post<GenericResponse<ProgresoUsuario>>(this.baseUrl, progreso).pipe(
      map(response => {
        if (response.data) {
          this.progresoSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Actualiza un registro de progreso
   */
  updateProgreso(id: number, progreso: ProgresoUpdateRequest): Observable<GenericResponse<ProgresoUsuario>> {
    return this.http.put<GenericResponse<ProgresoUsuario>>(`${this.baseUrl}/${id}`, progreso).pipe(
      map(response => {
        if (response.data) {
          this.progresoSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Marca una lección como completada
   */
  completarLeccion(request: CompletarLeccionRequest): Observable<GenericResponse<ProgresoLeccion>> {
    // Convertir el formato de la solicitud al formato que espera el backend
    const backendRequest = {
      leccionId: request.leccionId,
      usuarioId: request.usuarioId,
      completado: request.completado !== undefined ? request.completado : true,
      segundosVistos: request.tiempoVisto || 0,
      porcentajeCompletado: 100, // Por defecto, si se marca como completada, el porcentaje es 100%
      ultimaPosicion: 0 // Por defecto, la última posición es 0
    };

    return this.http.post<GenericResponse<ProgresoLeccion>>(`${this.baseUrl}/leccion/completar`, backendRequest);
  }

  /**
   * Marca una lección como vista (actualiza el progreso)
   */
  marcarLeccionComoVista(leccionId: number, usuarioId: number, tiempoVisto: number = 0): Observable<GenericResponse<ProgresoLeccion>> {
    const request: CompletarLeccionRequest = {
      leccionId: leccionId,
      usuarioId: usuarioId,
      completado: true,
      tiempoVisto: tiempoVisto
    };
    return this.completarLeccion(request);
  }

  /**
   * Desmarca una lección como completada
   */
  desmarcarLeccionComoCompletada(leccionId: number, usuarioId: number): Observable<GenericResponse<ProgresoLeccion>> {
    const request: CompletarLeccionRequest = {
      leccionId: leccionId,
      usuarioId: usuarioId,
      completado: false,
      tiempoVisto: 0
    };
    return this.completarLeccion(request);
  }

  /**
   * Obtiene las lecciones completadas de un usuario en un curso
   */
  getLeccionesCompletadas(cursoId: number, usuarioId: number): Observable<GenericResponse<ProgresoLeccion[]>> {
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/curso/${cursoId}/usuario/${usuarioId}/lecciones`)
      .pipe(
        map(response => {
          if (response.rpta === 1 && response.data) {
            // Convertir la respuesta del backend al formato que espera el frontend
            const progresos: ProgresoLeccion[] = response.data.map((item: any) => ({
              id: item.id,
              usuario: null,
              usuarioId: item.usuarioId,
              leccion: null,
              leccionId: item.leccionId,
              completado: item.completado,
              fechaCompletado: item.fechaCreacion,
              tiempoVisto: item.segundosVistos
            }));

            return {
              rpta: 1,
              msg: 'Lecciones completadas obtenidas correctamente',
              data: progresos
            };
          }
          return {
            rpta: response.rpta,
            msg: response.msg,
            data: []
          };
        })
      );
  }

  /**
   * Obtiene los IDs de las lecciones completadas de un usuario en un curso
   */
  getLeccionesCompletadasIds(cursoId: number, usuarioId: number): Observable<GenericResponse<number[]>> {
    return this.getLeccionesCompletadas(cursoId, usuarioId).pipe(
      map(response => {
        if (response.rpta === 1 && response.data) {
          const ids = response.data
            .filter(progreso => progreso.completado)
            .map(progreso => progreso.leccionId || (progreso.leccion ? progreso.leccion.id : 0))
            .filter(id => id > 0);

          return {
            rpta: 1,
            msg: 'IDs de lecciones completadas obtenidos correctamente',
            data: ids
          };
        }
        return {
          rpta: response.rpta,
          msg: response.msg,
          data: []
        };
      })
    );
  }

  /**
   * Verifica si una lección está completada por un usuario
   */
  isLeccionCompletada(leccionId: number, usuarioId: number): Observable<GenericResponse<boolean>> {
    return this.http.get<GenericResponse<boolean>>(`${this.baseUrl}/leccion/${leccionId}/usuario/${usuarioId}/completada`);
  }

  /**
   * Obtiene un resumen del progreso de un usuario en un curso
   * @param cursoId ID del curso
   * @param usuarioId ID del usuario
   * @returns Observable con el resumen del progreso
   */
  getResumenProgresoCurso(cursoId: number, usuarioId: number): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/resumen/curso/${cursoId}/usuario/${usuarioId}`);
  }
}
