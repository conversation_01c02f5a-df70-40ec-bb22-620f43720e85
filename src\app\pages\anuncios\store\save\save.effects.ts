import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationService } from '@app/services';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import * as fromUser from '@app/store/user';
import { catchError, delay, map, switchMap, tap } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import * as fromActions from './save.actions';
import { AnuncioCreateRequest, AnuncioResponse } from './save.models';
import { environment } from 'environments/environment';

type Action = fromActions.All;

@Injectable()
export class SaveEffects {
  private user: any = null;

  constructor(
    private actions: Actions,
    private httpClient: HttpClient,
    private router: Router,
    private notification: NotificationService,
    private store: Store
  ) {
    // Subscribe to user state
    this.store
      .pipe(select(fromUser.getUser))
      .subscribe((user) => (this.user = user));
  }

  // Efecto para crear un nuevo anuncio
  create$ = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.CREATE),
      map((action: fromActions.Create) => action.anuncio),
      switchMap((anuncio: AnuncioCreateRequest) => {
        console.log('SaveEffects: Enviando solicitud de creación de anuncio:', anuncio);

        return this.httpClient
          .post<AnuncioResponse>(`${environment.url}api/anuncios`, anuncio)
          .pipe(
            map((anuncioCreado: AnuncioResponse) => {
              console.log('SaveEffects: Anuncio creado exitosamente:', anuncioCreado);
              this.notification.success('Anuncio creado exitosamente');
              return new fromActions.CreateSuccess(anuncioCreado);
            }),
            catchError((error) => {
              console.error('SaveEffects: Error al crear anuncio:', error);
              this.notification.error('Error al crear anuncio: ' + error.message);
              return of(new fromActions.CreateError(error.message));
            })
          );
      })
    )
  );

  // Efecto para actualizar un anuncio existente
  update$ = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.UPDATE),
      map((action: fromActions.Update) => ({
        id: action.id,
        anuncio: action.anuncio
      })),
      switchMap(({ id, anuncio }) => {
        console.log('SaveEffects: Enviando solicitud de actualización de anuncio:', id, anuncio);

        return this.httpClient
          .post<AnuncioResponse>(`${environment.url}api/anuncios/update/${id}`, anuncio)
          .pipe(
            map((anuncioActualizado: AnuncioResponse) => {
              console.log('SaveEffects: Anuncio actualizado exitosamente:', anuncioActualizado);
              this.notification.success('Anuncio actualizado exitosamente');
              return new fromActions.UpdateSuccess(anuncioActualizado);
            }),
            catchError((error) => {
              console.error('SaveEffects: Error al actualizar anuncio:', error);
              this.notification.error('Error al actualizar anuncio: ' + error.message);
              return of(new fromActions.UpdateError(error.message));
            })
          );
      })
    )
  );

  // Efecto para eliminar un anuncio
  delete$ = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.DELETE),
      map((action: fromActions.Delete) => action.id),
      switchMap((id: number) => {
        console.log('SaveEffects: Enviando solicitud de eliminación de anuncio:', id);

        return this.httpClient
          .delete<any>(`${environment.url}api/anuncios/${id}`)
          .pipe(
            map(() => {
              console.log('SaveEffects: Anuncio eliminado exitosamente');
              this.notification.success('Anuncio eliminado exitosamente');
              return new fromActions.DeleteSuccess();
            }),
            catchError((error) => {
              console.error('SaveEffects: Error al eliminar anuncio:', error);
              this.notification.error('Error al eliminar anuncio: ' + error.message);
              return of(new fromActions.DeleteError(error.message));
            })
          );
      })
    )
  );
}