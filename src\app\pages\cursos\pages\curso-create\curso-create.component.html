<div class="p-4 sm:p-6 dark:bg-gray-900 min-h-screen" [ngClass]="{'dark-theme': isDarkTheme}">
  <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <!-- Encabezado -->
    <div class="px-6 py-4 bg-indigo-600 dark:bg-indigo-800">
      <h1 class="text-xl font-bold text-white">Crear Nuevo Curso</h1>
      <p class="text-indigo-100 text-sm mt-1">Complete el formulario para crear un nuevo curso</p>
    </div>

    <!-- Contenido del formulario -->
    <div class="p-6">
      <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-8">
        <!-- Información básica del curso -->
        <div class="space-y-4">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
            Información Básica
          </h2>

          <div>
            <label for="nombre" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Nombre del Curso <span class="text-red-500">*</span>
            </label>
            <input type="text" id="nombre" formControlName="nombre"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                   placeholder="Ingrese el nombre del curso">
            <div *ngIf="form.get('nombre')?.hasError('required') && form.get('nombre')?.touched"
                 class="mt-1 text-sm text-red-600 dark:text-red-400">
              El nombre del curso es obligatorio
            </div>
            <div *ngIf="form.get('nombre')?.hasError('maxlength')"
                 class="mt-1 text-sm text-red-600 dark:text-red-400">
              El nombre no debe exceder los 100 caracteres
            </div>
          </div>

          <div>
            <label for="descripcion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Descripción
            </label>
            <textarea id="descripcion" formControlName="descripcion" rows="6"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Ingrese una descripción del curso"></textarea>
            <div *ngIf="form.get('descripcion')?.hasError('maxlength')"
                 class="mt-1 text-sm text-red-600 dark:text-red-400">
              La descripción no debe exceder los 500 caracteres
            </div>
          </div>
        </div>

        <!-- Fechas del curso -->
        <div class="space-y-4">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
            Fechas del Curso
          </h2>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label for="fechaInicio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Fecha de Inicio
              </label>
              <input type="date" id="fechaInicio" formControlName="fechaInicio"
                     class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
            </div>

            <div>
              <label for="fechaFin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Fecha de Fin
              </label>
              <input type="date" id="fechaFin" formControlName="fechaFin"
                     class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
            </div>
          </div>
        </div>

        <!-- Video introductorio del curso -->
        <div class="space-y-4">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
            Video Introductorio del Curso
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Sube un video introductorio para presentar el curso. Este video se mostrará en la página principal del curso.
          </p>

          <div class="space-y-6">
            <div class="flex flex-wrap items-center gap-4">
              <button type="button" (click)="openFilesUpload()"
                      class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                Subir desde Firebase
              </button>

              <span class="text-gray-500 dark:text-gray-400">o</span>

              <div>
                <button type="button" (click)="fileInput.click()"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-900/50 border border-transparent rounded-md shadow-sm focus:outline-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                  </svg>
                  Seleccionar archivo
                </button>
                <input #fileInput type="file" accept="video/*" (change)="onFileSelected($event)" class="hidden">
              </div>
            </div>

            <!-- Vista previa del video -->
            <div *ngIf="videoPreviewUrl || form.get('videoUrl')?.value" class="space-y-4">
              <h3 class="text-md font-medium text-gray-800 dark:text-gray-200">
                Vista previa del video introductorio
              </h3>

              <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <app-curso-video-player
                  [videoUrl]="videoPreviewUrl || form.get('videoUrl')?.value"
                  [controls]="true">
                </app-curso-video-player>
              </div>

              <div class="flex flex-wrap gap-3">
                <button *ngIf="videoPreviewUrl" type="button" (click)="removeSelectedVideo()"
                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md focus:outline-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                  Eliminar video seleccionado
                </button>

                <button *ngIf="form.get('videoUrl')?.value" type="button" (click)="form.patchValue({videoUrl: ''})"
                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md focus:outline-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                  Eliminar video de Firebase
                </button>
              </div>
            </div>
          </div>

          <div class="flex items-start p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-md border border-blue-200 dark:border-blue-800 text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
            <span>Después de crear el curso, podrás añadir módulos y lecciones con sus propios videos y subtítulos.</span>
          </div>
        </div>

        <!-- Mensaje de error -->
        <div *ngIf="error" class="flex items-center p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md border border-red-200 dark:border-red-800">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Botones de acción -->
        <div class="flex justify-end pt-4 mt-6 border-t border-gray-200 dark:border-gray-700">
          <button type="button" (click)="onCancel()"
                  class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm">
            Cancelar
          </button>
          <button type="submit" [disabled]="form.invalid || loading"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed">
            <svg *ngIf="!loading" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ !loading ? 'Guardar Curso' : 'Guardando...' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
