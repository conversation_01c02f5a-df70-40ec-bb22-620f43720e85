import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PostulacionService {
  //private apiUrl = 'https://back-landing-midas.onrender.com/api/listar-postulacion';
  private apiUrl = 'https://apisozarusac.com/LandingPage/listar-postulacion/';

  constructor(private http: HttpClient) {}

  getPostulaciones(): Observable<any> {
    return this.http.get<any>(this.apiUrl);
  }
}
