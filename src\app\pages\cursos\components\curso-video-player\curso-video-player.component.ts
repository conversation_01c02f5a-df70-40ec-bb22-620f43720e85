import { Component, Input, OnInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Component({
  selector: 'app-curso-video-player',
  templateUrl: './curso-video-player.component.html',
  styleUrls: ['./curso-video-player.component.scss']
})
export class CursoVideoPlayerComponent implements OnInit, OnDestroy {
  @Input() videoUrl: string = '';
  @Input() autoplay: boolean = false;
  @Input() controls: boolean = true;
  @Input() title: string = '';
  @Input() subtitlesUrl: string | null = '';
  @Input() showSubtitles: boolean = true;

  @ViewChild('videoPlayer') videoPlayerRef!: ElementRef<HTMLVideoElement>;
  @ViewChild('subtitlesTrack') subtitlesTrackRef!: ElementRef<HTMLTrackElement>;

  loading: boolean = true;
  error: boolean = false;
  errorMessage: string = '';
  subtitlesLoaded: boolean = false;
  subtitlesError: boolean = false;

  constructor(private http: HttpClient) { }

  ngOnInit(): void {
    // Verificar que la URL del video sea válida
    if (this.videoUrl && !this.videoUrl.startsWith('http://') && !this.videoUrl.startsWith('https://') && !this.videoUrl.startsWith('blob:')) {
      console.warn('URL de video inválida:', this.videoUrl);
      this.error = true;
      this.errorMessage = 'URL de video inválida. Debe comenzar con http://, https:// o blob:';
      return;
    }

    // Si se proporciona una URL de subtítulos en formato .txt, convertirla a VTT
    if (this.subtitlesUrl && typeof this.subtitlesUrl === 'string' && this.subtitlesUrl.endsWith('.txt')) {
      this.convertTxtToVtt();
    }
  }

  ngOnDestroy(): void {
    // Detener el video cuando se destruye el componente
    if (this.videoPlayerRef?.nativeElement) {
      this.videoPlayerRef.nativeElement.pause();
    }
  }

  onVideoLoad(): void {
    this.loading = false;
  }

  onVideoError(event: any): void {
    this.loading = false;
    this.error = true;
    this.errorMessage = 'Error al cargar el video. Por favor, inténtelo de nuevo más tarde.';
    console.error('Error al cargar el video:', event);
  }

  onSubtitlesLoad(): void {
    this.subtitlesLoaded = true;
    console.log('Subtítulos cargados correctamente');
  }

  onSubtitlesError(event: any): void {
    this.subtitlesError = true;
    console.error('Error al cargar los subtítulos:', event);
  }

  /**
   * Convierte un archivo de subtítulos en formato .txt a formato WebVTT
   * El formato esperado del .txt es:
   * 00:00:10,000 --> 00:00:15,000
   * Texto del subtítulo
   *
   * 00:00:16,000 --> 00:00:20,000
   * Otro texto de subtítulo
   */
  private convertTxtToVtt(): void {
    if (!this.subtitlesUrl || typeof this.subtitlesUrl !== 'string') {
      return;
    }

    this.http.get(this.subtitlesUrl, { responseType: 'text' })
      .pipe(
        catchError(error => {
          console.error('Error al cargar el archivo de subtítulos:', error);
          this.subtitlesError = true;
          return of('');
        })
      )
      .subscribe(text => {
        if (text) {
          // Convertir el formato de texto a WebVTT
          const vttContent = this.formatTextToVtt(text);

          // Crear un Blob con el contenido VTT
          const blob = new Blob([vttContent], { type: 'text/vtt' });

          // Crear una URL para el Blob
          this.subtitlesUrl = URL.createObjectURL(blob);

          // Si ya tenemos la referencia al elemento track, actualizar su src
          if (this.subtitlesTrackRef?.nativeElement) {
            this.subtitlesTrackRef.nativeElement.src = this.subtitlesUrl;
          }
        }
      });
  }

  /**
   * Formatea el texto de subtítulos al formato WebVTT
   */
  private formatTextToVtt(text: string): string {
    // Encabezado WebVTT
    let vttContent = 'WEBVTT\n\n';

    // Dividir el texto en líneas
    const lines = text.split('\n');
    let index = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Buscar líneas con el formato de tiempo "00:00:00,000 --> 00:00:00,000"
      if (line.includes('-->')) {
        // Añadir el índice del subtítulo
        vttContent += index + '\n';
        index++;

        // Convertir el formato de tiempo de SRT (00:00:00,000) a VTT (00:00:00.000)
        const timeRange = line.replace(/,/g, '.');
        vttContent += timeRange + '\n';

        // Añadir el texto del subtítulo (puede estar en múltiples líneas)
        let subtitleText = '';
        i++;
        while (i < lines.length && lines[i].trim() !== '') {
          subtitleText += lines[i] + '\n';
          i++;
        }

        vttContent += subtitleText + '\n';
      }
    }

    return vttContent;
  }

  /**
   * Activa o desactiva los subtítulos
   */
  toggleSubtitles(): void {
    this.showSubtitles = !this.showSubtitles;

    if (this.videoPlayerRef?.nativeElement) {
      const tracks = this.videoPlayerRef.nativeElement.textTracks;
      if (tracks.length > 0) {
        tracks[0].mode = this.showSubtitles ? 'showing' : 'hidden';
      }
    }
  }
}
