<h2 mat-dialog-title>Exportar Excel</h2>
<mat-dialog-content>
  <form [formGroup]="exportForm">
    <mat-radio-group formControlName="tipoReporte" class="radio-group">
      <mat-radio-button value="dia">Reporte del Día</mat-radio-button>
      <mat-radio-button value="mes">Reporte del Mes</mat-radio-button>
    </mat-radio-group>

    <ng-container *ngIf="exportForm.get('tipoReporte')?.value === 'dia'">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Seleccionar Día</mat-label>
        <input matInput [matDatepicker]="pickerDia" formControlName="fecha">
        <mat-datepicker-toggle matSuffix [for]="pickerDia"></mat-datepicker-toggle>
        <mat-datepicker #pickerDia></mat-datepicker>
      </mat-form-field>
    </ng-container>

    <ng-container *ngIf="exportForm.get('tipoReporte')?.value === 'mes'">
      <div class="mes-selector">
        <mat-form-field appearance="outline">
          <mat-label>Mes</mat-label>
          <mat-select formControlName="mes">
            <mat-option *ngFor="let mes of meses" [value]="mes.value">
              {{ mes.nombre }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Año</mat-label>
          <mat-select formControlName="anio">
            <mat-option [value]="anioActual - 1">{{ anioActual - 1 }}</mat-option>
            <mat-option [value]="anioActual">{{ anioActual }}</mat-option>
            <mat-option [value]="anioActual + 1">{{ anioActual + 1 }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </ng-container>
  </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancelar</button>
  <button mat-raised-button color="primary" (click)="onExport()" 
          [disabled]="(exportForm.get('tipoReporte')?.value === 'dia' && !exportForm.get('fecha')?.valid) || 
                     (exportForm.get('tipoReporte')?.value === 'mes' && !exportForm.get('mes')?.valid)">
    Exportar
  </button>
</mat-dialog-actions>