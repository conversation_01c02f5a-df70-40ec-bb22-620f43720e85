// src/app/pages/faq/faq-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AuthGuard } from '@app/guards/auth/auth.guard';

import { FaqListComponent } from './components/faq-list/faq-list.component';

const routes: Routes = [
  { path: '',          component: FaqListComponent, canActivate: [AuthGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FaqRoutingModule {}
