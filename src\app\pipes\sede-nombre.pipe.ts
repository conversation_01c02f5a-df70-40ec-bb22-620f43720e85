import { Pipe, PipeTransform } from '@angular/core';
import { Sede } from '@app/models/backend/sede/sede.model';

@Pipe({
  name: 'sedeNombre'
})
export class SedeNombrePipe implements PipeTransform {
  transform(sedes: Sede[], sedeId: number | undefined): string {
    if (!sedes || !sedeId) {
      return 'No asignada';
    }

    const sede = sedes.find(s => s.id === sedeId);
    return sede ? sede.nombre : 'No asignada';
  }
}
