.faq-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
  border: 1px solid #e3e6f0;
}

/* Estilos para el tema oscuro (darkmode azulino) */
:host-context(.dark-theme) {
  .faq-card {
    background-color: #0a1628;
    border-color: rgba(77, 171, 245, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  // Estilos para botones en tema oscuro
  .btn-outline-info {
    color: rgba(77, 171, 245, 0.9);
    border-color: rgba(77, 171, 245, 0.5);

    &:hover {
      color: #ffffff;
      background-color: rgba(77, 171, 245, 0.7);
      border-color: rgba(77, 171, 245, 0.9);
    }
  }

  .btn-outline-danger {
    color: rgba(231, 74, 59, 0.9);
    border-color: rgba(231, 74, 59, 0.5);

    &:hover {
      color: #ffffff;
      background-color: rgba(231, 74, 59, 0.7);
      border-color: rgba(231, 74, 59, 0.9);
    }
  }

  .btn-outline-success {
    color: rgba(28, 200, 138, 0.9);
    border-color: rgba(28, 200, 138, 0.5);

    &:hover {
      color: #ffffff;
      background-color: rgba(28, 200, 138, 0.7);
      border-color: rgba(28, 200, 138, 0.9);
    }
  }
}

.faq-card-options {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;

  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }
}

// Ya no necesitamos estos estilos porque el botón de responder
// ahora está dentro de .action-buttons

// Estilos comunes para botones
.btn-outline-info, .btn-outline-danger, .btn-outline-success {
  background-color: transparent;

  &:hover {
    color: white;
  }
}

.btn-outline-info {
  color: #4e73df;
  border-color: #4e73df;

  &:hover {
    background-color: #4e73df;
  }
}

.btn-outline-danger {
  color: #e74a3b;
  border-color: #e74a3b;

  &:hover {
    background-color: #e74a3b;
  }
}

.btn-outline-success {
  color: #1cc88a;
  border-color: #1cc88a;

  &:hover {
    background-color: #1cc88a;
  }
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

i {
  margin-right: 0.25rem;
}

.faq-card-content {
  padding-right: 2rem;
}

.faq-card-question {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4e73df;
  margin-bottom: 1rem;
}

.faq-card-answer {
  font-size: 0.95rem;
  color: #5a5c69;
  margin-bottom: 1rem;
}

:host-context(.dark-theme) {
  .faq-card-question {
    color: rgba(77, 171, 245, 0.9);
  }

  .faq-card-answer {
    color: rgba(255, 255, 255, 0.9);
  }
}

.faq-card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.85rem;
  color: #858796;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e3e6f0;
}

:host-context(.dark-theme) {
  .faq-card-meta {
    color: rgba(255, 255, 255, 0.7);
    border-top-color: rgba(77, 171, 245, 0.2);
  }
}

.faq-card-category,
.faq-card-user-type,
.faq-card-creator,
.faq-card-responder,
.faq-card-date,
.faq-card-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fc;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.faq-card-category {
  background-color: #e8f4fd;
  color: #4e73df;
}

.faq-card-user-type {
  background-color: #e8f0fd;
  color: #2e59d9;
}

.faq-card-creator {
  background-color: #e8fdec;
  color: #1cc88a;
}

.faq-card-responder {
  background-color: #fdf8e8;
  color: #f6c23e;
}

.faq-card-date {
  background-color: #f8f9fc;
  color: #858796;
}

.faq-card-status {
  font-weight: 500;

  &.status-answered {
    background-color: #e8fdec;
    color: #1cc88a;
  }

  &.status-pending {
    background-color: #fdf8e8;
    color: #f6c23e;
  }
}

.faq-card-estado {
  font-weight: 500;

  &.estado-abierta {
    background-color: #e8f4fd;
    color: #4e73df;
  }

  &.estado-cerrada {
    background-color: #f8f9fc;
    color: #858796;
  }
}

.faq-card-respuestas-count {
  background-color: #e8f0fd;
  color: #2e59d9;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  i {
    font-size: 1rem;
  }
}

:host-context(.dark-theme) {
  .faq-card-category,
  .faq-card-user-type,
  .faq-card-creator,
  .faq-card-responder,
  .faq-card-date,
  .faq-card-status {
    background-color: rgba(77, 171, 245, 0.1);
  }

  .faq-card-category {
    background-color: rgba(77, 171, 245, 0.15);
    color: rgba(77, 171, 245, 0.9);
  }

  .faq-card-user-type {
    background-color: rgba(77, 171, 245, 0.1);
    color: rgba(77, 171, 245, 0.8);
  }

  .faq-card-creator {
    background-color: rgba(28, 200, 138, 0.1);
    color: rgba(28, 200, 138, 0.8);
  }

  .faq-card-responder {
    background-color: rgba(246, 194, 62, 0.1);
    color: rgba(246, 194, 62, 0.8);
  }

  .faq-card-date {
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
  }

  .faq-card-status {
    &.status-answered {
      background-color: rgba(28, 200, 138, 0.1);
      color: rgba(28, 200, 138, 0.8);
    }

    &.status-pending {
      background-color: rgba(246, 194, 62, 0.1);
      color: rgba(246, 194, 62, 0.8);
    }
  }
}

/* Estilos para el mensaje de archivo adjuntado */
.file-attachment-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  margin-top: 1rem;
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #4e73df;

  i {
    font-size: 1.2rem;
  }

  &.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: underline;
    color: #2e59d9;
    font-weight: 500;

    &:hover {
      background-color: #e8f4fd;
      border-color: #4e73df;
      transform: translateY(-2px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      color: #4e73df;
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* Estilos para los botones de tipo de archivo */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.media-item {
  padding: 0.5rem;
  border-radius: 8px;
  text-align: center;
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

.file-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #4e73df;
}

:host-context(.dark-theme) {
  .media-item {
    background-color: rgba(77, 171, 245, 0.05);
    border-color: rgba(77, 171, 245, 0.2);

    &:hover {
      box-shadow: 0 4px 12px rgba(77, 171, 245, 0.2);
    }
  }

  .file-icon {
    color: rgba(77, 171, 245, 0.9);
  }
}

.file-name {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  word-break: break-word;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.file-size {
  font-size: 0.8rem;
  color: #858796;
}

:host-context(.dark-theme) {
  .file-name {
    color: rgba(255, 255, 255, 0.9);
  }

  .file-size {
    color: rgba(255, 255, 255, 0.6);
  }

  /* Estilos para el mensaje de archivo adjuntado en tema oscuro */
  .file-attachment-notice {
    background-color: rgba(77, 171, 245, 0.1);
    border-color: rgba(77, 171, 245, 0.3);
    color: rgba(77, 171, 245, 0.9);

    &.clickable {
      text-decoration: underline;
      color: rgba(77, 171, 245, 0.9);
      font-weight: 500;

      &:hover {
        background-color: rgba(77, 171, 245, 0.2);
        border-color: rgba(77, 171, 245, 0.5);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        color: rgba(77, 171, 245, 1);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  /* Estilos para los botones de filtro */
  ::ng-deep {
    .btn-outline-primary,
    .btn-outline-success,
    .btn-outline-warning,
    .btn-outline-danger,
    .btn-outline-secondary {
      color: rgba(255, 255, 255, 0.9);
      border-color: rgba(77, 171, 245, 0.3);
      background-color: transparent;

      &:hover, &.active {
        background-color: rgba(77, 171, 245, 0.2);
        color: white;
      }

      .badge {
        background-color: rgba(77, 171, 245, 0.7) !important;
        color: white;
      }
    }
  }
}