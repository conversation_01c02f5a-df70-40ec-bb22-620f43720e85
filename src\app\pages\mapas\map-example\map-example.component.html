<div class="container-fluid max-w-[95%] lg:max-w-[90%] mx-auto p-5">
  <mat-card class="w-full shadow-lg rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 mb-5">
    <mat-card-header class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 p-4">
      <div class="flex items-center w-full">
        <div class="flex justify-center items-center w-10 h-10 rounded-full bg-blue-500 dark:bg-blue-600 shadow-md mr-3">
          <mat-icon class="text-white">map</mat-icon>
        </div>
        <mat-card-title class="text-xl font-semibold text-gray-800 dark:text-white">Ejemplo de Mapa con Leaflet</mat-card-title>
      </div>
    </mat-card-header>
    <mat-card-content class="p-4">
      <div class="relative w-full h-[80vh] rounded-lg border-2 border-gray-300 dark:border-gray-700 shadow-inner overflow-hidden">
        <app-map [center]="mapCenter" [zoom]="mapZoom"></app-map>
      </div>
    </mat-card-content>
  </mat-card>
</div>
