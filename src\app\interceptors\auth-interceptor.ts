import {
  <PERSON>ttpInter<PERSON>,
  HttpHandler,
  HttpRequest,
  HttpErrorResponse,
  HttpEvent,
  HttpClient
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { NotificationService } from "@app/services";
import { catchError, switchMap, tap } from "rxjs/operators";
import { throwError, Observable, of } from "rxjs";
import { GenericResponse } from "@app/models/backend/generic-response";
import { environment } from "@src/environments/environment";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;

  constructor(
    private router: Router,
    private notification: NotificationService,
    private httpClient: HttpClient
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const token = localStorage.getItem("token");

    // Si no hay token, deja pasar la petición sin modificar
    if (!token) return next.handle(req);

    // Clona la petición y agrega el token en el header
    const authReq = req.clone({
      headers: req.headers.set("Authorization", `Bearer ${token}`)
    });

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        // Evalúa tipo de error
        const tokenExpirado = error?.error?.error === "TOKEN_EXPIRADO" || error.status === 401;
        const tokenInvalido =
          error?.error?.error === "TOKEN_INVALIDO" ||
          error?.error?.error === "TOKEN_NO_PROPORCIONADO" ||
          error.status === 403;

        if (tokenInvalido || !token) {
          this.handleLogout("Token inválido o no proporcionado.");
          return throwError(() => error);
        }

        // Si está expirado y no estamos en renovación
        if (tokenExpirado && !this.isRefreshing) {
          this.isRefreshing = true;

          return this.httpClient
            .post<GenericResponse<{ token: string }>>(`${environment.url}api/authentication/refresh-token`, null)
            .pipe(
              tap((response) => {
                if (response.rpta === 1 && response.data?.token) {
                  localStorage.setItem("token", response.data.token);
                  //console.log("🔁 Token renovado automáticamente");
                } else {
                  //this.handleLogout("No se pudo renovar el token.");
                }
              }),
              switchMap(() => {
                const nuevoToken = localStorage.getItem("token");
                const retryRequest = req.clone({
                  headers: req.headers.set("Authorization", `Bearer ${nuevoToken}`)
                });
                this.isRefreshing = false;
                return next.handle(retryRequest);
              }),
              catchError(() => {
                this.isRefreshing = false;
                this.handleLogout("Error al renovar el token.");
                return throwError(() => error);
              })
            );
        }

        // Detectar errores específicos de conexión a la base de datos
        if (error.status === 500 && (
            error.error?.message?.includes('HikariPool') ||
            error.error?.message?.includes('Connection is not available') ||
            error.error?.message?.includes('Communications link failure') ||
            error.error?.message?.includes('Unable to acquire JDBC Connection')
        )) {
          this.notification.error('Error de conexión a la base de datos. Por favor, intente nuevamente en unos momentos.');
          console.error('Error de conexión a la base de datos:', error);
        } else if (error.status === 0) {
          this.notification.error('No se pudo conectar con el servidor. Verifique su conexión a internet.');
          console.error('Error de conexión al servidor:', error);
        }

        return throwError(() => error);
      })
    );
  }

  private handleLogout(mensaje?: string): void {
    localStorage.clear();
    this.router.navigate(["/auth/login"]);
    this.notification.error(mensaje || "Su sesión ha expirado. Por favor, inicie sesión nuevamente.");
  }
}