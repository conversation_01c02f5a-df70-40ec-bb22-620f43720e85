import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatOptionModule } from '@angular/material/core';

import { EstadisticasCoordinadorPageRoutingModule } from './estadisticas-coordinador-page-routing.module';
import { EstadisticasCoordinadorPageComponent } from './estadisticas-coordinador-page.component';
import { SharedComponentsModule } from '../../components/shared/shared-components.module';

@NgModule({
  declarations: [EstadisticasCoordinadorPageComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatOptionModule,
    SharedComponentsModule,
    EstadisticasCoordinadorPageRoutingModule,
  ],
})
export class EstadisticasCoordinadorPageModule {}
