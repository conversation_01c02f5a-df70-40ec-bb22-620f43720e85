import { Component, OnInit, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import * as fromUser from '@app/store/user';
import { Observable, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss']
})
export class RegistrationComponent implements OnInit, OnDestroy {
  // Variables para carga masiva de usuarios ASESOR
  selectedFileAsesor: File | null = null;
  uploadSuccessAsesor$: Observable<boolean>;
  uploadErrorAsesor$: Observable<string | null>;
  loadingAsesor = false;

  // Variables para carga masiva de usuarios BACKOFFICE
  selectedFileBackoffice: File | null = null;
  uploadSuccessBackoffice$: Observable<boolean>;
  uploadErrorBackoffice$: Observable<string | null>;
  loadingBackoffice = false;

  // Tema oscuro
  isDarkTheme = false;

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(private store: Store) {
    this.uploadSuccessAsesor$ = this.store.select(fromUser.getCargaMasivaAsesorSuccess);
    this.uploadErrorAsesor$ = this.store.select(fromUser.getCargaMasivaAsesorError);

    this.uploadSuccessBackoffice$ = this.store.select(fromUser.getCargaMasivaBackofficeSuccess);
    this.uploadErrorBackoffice$ = this.store.select(fromUser.getCargaMasivaBackofficeError);
  }

  ngOnInit(): void {
    // Detectar tema oscuro
    this.isDarkTheme = document.body.classList.contains('dark-theme');

    // Suscribirse a los cambios de estado para ASESOR
    const asesorSuccessSub = this.uploadSuccessAsesor$.subscribe(success => {
      if (success) {
        this.loadingAsesor = false;
        setTimeout(() => {
          this.selectedFileAsesor = null;
          (document.querySelector('#asesorFileInput') as HTMLInputElement).value = '';
        }, 3000);
      }
    });

    const asesorErrorSub = this.uploadErrorAsesor$.subscribe(error => {
      if (error) {
        this.loadingAsesor = false;
      }
    });

    // Suscribirse a los cambios de estado para BACKOFFICE
    const backofficeSuccessSub = this.uploadSuccessBackoffice$.subscribe(success => {
      if (success) {
        this.loadingBackoffice = false;
        setTimeout(() => {
          this.selectedFileBackoffice = null;
          (document.querySelector('#backofficeFileInput') as HTMLInputElement).value = '';
        }, 3000);
      }
    });

    const backofficeErrorSub = this.uploadErrorBackoffice$.subscribe(error => {
      if (error) {
        this.loadingBackoffice = false;
      }
    });

    this.subscriptions.push(asesorSuccessSub, asesorErrorSub, backofficeSuccessSub, backofficeErrorSub);
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones para evitar memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onFileSelected(event: any, type: string) {
    const file = event.target.files[0];
    if (file) {
      // Verificar que sea un archivo Excel
      if (!this.isExcelFile(file)) {
        Swal.fire({
          icon: 'error',
          title: 'Formato no válido',
          text: 'Por favor, seleccione un archivo Excel (.xlsx o .xls)'
        });
        // Limpiar el input
        event.target.value = '';
        return;
      }

      if (type === 'asesor') {
        this.selectedFileAsesor = file;
      } else if (type === 'backoffice') {
        this.selectedFileBackoffice = file;
      }
    }
  }

  uploadAsesorFile() {
    if (!this.selectedFileAsesor) return;

    // Mostrar confirmación antes de subir
    Swal.fire({
      title: '¿Está seguro?',
      text: 'Se registrarán los usuarios ASESOR del archivo Excel',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Sí, subir archivo',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        this.loadingAsesor = true;
        this.store.dispatch(new fromUser.CargaMasivaAsesor(this.selectedFileAsesor!));
      }
    });
  }

  uploadBackofficeFile() {
    if (!this.selectedFileBackoffice) return;

    // Mostrar confirmación antes de subir
    Swal.fire({
      title: '¿Está seguro?',
      text: 'Se registrarán los usuarios BACKOFFICE del archivo Excel',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Sí, subir archivo',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        this.loadingBackoffice = true;
        this.store.dispatch(new fromUser.CargaMasivaBackOffice(this.selectedFileBackoffice!));
      }
    });
  }

  // Verificar si es un archivo Excel
  private isExcelFile(file: File): boolean {
    const validExcelTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12'
    ];
    return validExcelTypes.includes(file.type) ||
           file.name.endsWith('.xlsx') ||
           file.name.endsWith('.xls');
  }
}