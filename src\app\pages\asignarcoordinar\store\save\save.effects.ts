import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import * as CoordinadorActions from './save.actions';
import { mergeMap, map, catchError, tap, switchMap } from 'rxjs/operators';
import { of } from 'rxjs';
import { DatePipe } from '@angular/common';
import { CoordinadorDTO } from '@app/models/backend/dto/coordinador.dto';
import { AsesorDTO } from '@app/models/backend/dto/asesor.dto';
import { ClienteResidencial } from '@app/models/backend/clienteresidencial';
import { GenericResponse } from '@app/models/backend/generic-response';

@Injectable()
export class CoordinadorEffects {
  constructor(
    private actions$: Actions,
    private http: HttpClient,
    private datePipe: DatePipe
  ) {}
  loadAsesoresConClientes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.loadAsesoresConClientes),
      mergeMap(action => {
        let params = new HttpParams()
          .set('page', action.page.toString())
          .set('size', action.size.toString());

        if (action.dni && action.dni.trim() !== '') {
          params = params.set('dni', action.dni);
        }

        if (action.nombre && action.nombre.trim() !== '') {
          params = params.set('nombre', action.nombre);
        }

        if (action.numeroMovil && action.numeroMovil.trim() !== '') {
          params = params.set('numeroMovil', action.numeroMovil);
        }

        if (action.fecha && action.fecha.trim() !== '') {
          // Convertir la fecha al formato yyyy-MM-dd si no lo está ya.
          const formattedDate = this.datePipe.transform(action.fecha, 'yyyy-MM-dd');
          if (formattedDate) {
            params = params.set('fecha', formattedDate);
          }
        }

        const url = `${environment.url}api/asesores/${action.coordinadorId}/asesores-con-clientes`;
        //console.log('Consumiendo el servicio:', url, params.toString());

        return this.http.get(url, { params }).pipe(
          tap(response => console.log("Respuesta del servicio:", response)),
          // Suponiendo que el backend encapsula los datos en una propiedad "data"
          map((response: any) =>
            CoordinadorActions.loadAsesoresConClientesSuccess({ data: response.data })
          ),
          catchError(error => {
            console.error('Error al cargar clientes por coordinador:', error);
            return of(CoordinadorActions.loadAsesoresConClientesFailure({
              error: error.message || 'Error al cargar clientes por coordinador'
            }));
          })
        );
      })
    )
  );

  // Otros efectos (cargando coordinadores, asignación, etc.):
loadCoordinadoresPaginados$ = createEffect(() =>
  this.actions$.pipe(
    ofType(CoordinadorActions.loadCoordinadoresPaginados),
    switchMap(({ page, size, searchTerm }) => {
      let params = new HttpParams()
        .set('page', page.toString())
        .set('size', size.toString());

      // Agregar el término de búsqueda si existe
      if (searchTerm && searchTerm.trim() !== '') {
        params = params.set('search', searchTerm.trim());
      }

      return this.http.get<any>(`${environment.url}api/coordinadores`, { params }).pipe(
        map(response => CoordinadorActions.loadCoordinadoresPaginadosSuccess({
          coordinadores: response.coordinadores,
          totalItems: response.totalItems,
          totalPages: response.totalPages,
          currentPage: response.currentPage,
          size: response.size,
          hasNext: response.hasNext,
          hasPrevious: response.hasPrevious
        })),
        catchError(error => of(CoordinadorActions.loadCoordinadoresPaginadosFailure({ error })))
      );
    })
  )
);


  asignarAsesores$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.asignarAsesores),
      mergeMap(action =>
        this.http.post(`${environment.url}api/coordinadores/asignar-asesores`, action.asignacion).pipe(
          map(coordinador =>
            CoordinadorActions.asignarAsesoresSuccess({ coordinador: coordinador as any })
          ),
          catchError(error =>
            of(CoordinadorActions.asignarAsesoresFailure({ error }))
          )
        )
      )
    )
  );

  // Efecto para cargar asesores disponibles
  loadAsesoresDisponibles$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.loadAsesoresDisponibles),
      mergeMap(() =>
        this.http.get(`${environment.url}api/coordinadores/asesores-disponibles`).pipe(
          tap(asesores => console.log('Asesores disponibles recibidos:', asesores)),
          map(asesores =>
            CoordinadorActions.loadAsesoresDisponiblesSuccess({ asesores: asesores as any })
          ),
          catchError(error => {
            console.error('Error al cargar asesores disponibles:', error);
            return of(CoordinadorActions.loadAsesoresDisponiblesFailure({ error }));
          })
        )
      )
    )
  );

  // Efecto para asignar un asesor individual
  asignarAsesorIndividual$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.asignarAsesorIndividual),
      mergeMap(action =>
        this.http.post(`${environment.url}api/coordinadores/asignar-asesores`, {
          coordinadorId: action.coordinadorId,
          asesorIds: [action.asesorId]
        }).pipe(
          map(response => {
            const coordinador = response as CoordinadorDTO;
            // Encontrar el asesor recién asignado
            const asesorAsignado = coordinador.asesores.find(a => a.id === action.asesorId);

            // Si no encontramos el asesor en la respuesta, puede ser porque ya estaba asignado
            // o porque hubo un problema en el backend. En cualquier caso, consideramos la operación exitosa
            // y devolvemos el coordinador actualizado.
            if (!asesorAsignado) {
              console.warn('Asesor no encontrado en la respuesta, pero la operación fue exitosa');
              // Usamos un objeto con la estructura mínima necesaria
              return CoordinadorActions.asignarAsesorIndividualSuccess({
                coordinadorId: action.coordinadorId,
                asesor: { id: action.asesorId } as AsesorDTO
              });
            }

            return CoordinadorActions.asignarAsesorIndividualSuccess({
              coordinadorId: action.coordinadorId,
              asesor: asesorAsignado
            });
          }),
          catchError(error =>
            of(CoordinadorActions.asignarAsesorIndividualFailure({ error }))
          )
        )
      )
    )
  );

  // Efecto para cargar clientes de un asesor
  loadClientesDeAsesor$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.loadClientesDeAsesor),
      mergeMap(action =>
        this.http.get(`${environment.url}api/asesores/${action.asesorId}/clientes`).pipe(
          map(clientes =>
            CoordinadorActions.loadClientesDeAsesorSuccess({ clientes: clientes as any })
          ),
          catchError(error =>
            of(CoordinadorActions.loadClientesDeAsesorFailure({ error }))
          )
        )
      )
    )
  );

  // Efecto para remover un asesor de un coordinador
  removerAsesor$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.removerAsesor),
      mergeMap(action =>
        this.http.delete(`${environment.url}api/coordinadores/${action.coordinadorId}/asesores/${action.asesorId}`, {
          // Especificar que esperamos una respuesta de texto para manejar respuestas vacías
          responseType: 'text'
        }).pipe(
          // Usar tap para loguear la respuesta
          tap(response => console.log('Respuesta al remover asesor:', response)),
          // Siempre devolver éxito si no hay error, incluso si la respuesta está vacía
          map(() =>
            CoordinadorActions.removerAsesorSuccess({
              coordinadorId: action.coordinadorId,
              asesorId: action.asesorId
            })
          ),
          catchError(error => {
            console.error('Error al remover asesor:', error);
            // Si el error es 204 No Content, considerarlo como éxito
            if (error.status === 204) {
              return of(CoordinadorActions.removerAsesorSuccess({
                coordinadorId: action.coordinadorId,
                asesorId: action.asesorId
              }));
            }
            return of(CoordinadorActions.removerAsesorFailure({ error }));
          })
        )
      )
    )
  );

  obtenerClientePorMovil$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.obtenerClientePorMovil),
      mergeMap(action =>
        this.http.get<GenericResponse<ClienteResidencial>>(
          `${environment.url}api/cliente-promocion/movil/${action.numeroMovil}`
        ).pipe(
          map(response => {
            if (response.rpta === 1) {
              return CoordinadorActions.obtenerClientePorMovilSuccess({ cliente: response.data });
            } else {
              return CoordinadorActions.obtenerClientePorMovilFailure({ error: response.msg });
            }
          }),
          catchError(error =>
            of(CoordinadorActions.obtenerClientePorMovilFailure({ error: error.message }))
          )
        )
      )
    )
  );

  descargarExcelCliente$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.descargarExcelCliente),
      mergeMap(({ numeroMovil }) =>
        this.http.get(`${environment.url}api/clientes/exportar-excel-individual/${numeroMovil}`, {
          responseType: 'blob'
        }).pipe(
          map(blob => {
            const file = new Blob([blob], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(file);
            link.download = `cliente-${numeroMovil}.xlsx`;
            link.click();
            return CoordinadorActions.descargarExcelClienteSuccess();
          }),
          catchError(error => of(CoordinadorActions.descargarExcelClienteFailure({ error })))
        )
      )
    )
  );


  loadClienteByDniAndMobile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.loadClienteByDniAndMobile),
      mergeMap(action => {
        const url = `${environment.url}api/cliente-promocion/detalle?dni=${action.dni}&movil=${action.mobile}&fechaCreacion=${action.fechaCreacion}`;

        return this.http.get<GenericResponse<any>>(url).pipe(
          tap(response => console.log('[EFFECT DEBUG] Respuesta:', response)),
          map(response => {
            if (response.rpta === 1) {
              return CoordinadorActions.loadClienteByDniAndMobileSuccess({ cliente: response.data }); // reutilizas el success
            } else {
              return CoordinadorActions.loadClienteByDniAndMobileFailure({ error: response.msg });
            }
          }),
          catchError(error =>
            of(CoordinadorActions.loadClienteByDniAndMobileFailure({ error: error.message }))
          )
        );
      })
    )
  );

  exportarClientesHoyCoordinador$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CoordinadorActions.exportarClientesHoyCoordinador),
      switchMap(({ coordinadorId }) =>
        this.http.get(`${environment.url}api/asesores/exportar-hoy/coordinador/${coordinadorId}`, {
          responseType: 'blob'
        }).pipe(
          tap((blob: Blob) => {
            if (!blob || blob.size === 0) {
              alert('El archivo está vacío. No se encontraron datos para exportar.');
              return;
            }

            const reader = new FileReader();
            reader.onload = () => {
              const text = reader.result as string;

              // Verificar si el contenido del blob es un JSON de error
              const isJson = text.trim().startsWith('{');
              if (isJson) {
                try {
                  const json = JSON.parse(text);
                  const mensaje = json.msg || 'No se pudo exportar el archivo Excel.';
                  alert(mensaje);
                } catch (e) {
                  alert('Error desconocido al procesar la respuesta del servidor.');
                }
                return;
              }

              // Si es archivo Excel válido
              const fecha = new Date().toISOString().split('T')[0];
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `clientes_asesores_${fecha}.xlsx`;
              a.click();
              window.URL.revokeObjectURL(url);
            };

            reader.readAsText(blob);
          }),
          map(() => CoordinadorActions.exportarClientesHoyCoordinadorSuccess({ excelBlob: null })),
          catchError((error) => {
            console.error('[Effect] Error exportando:', error);
            return of(CoordinadorActions.exportarClientesHoyCoordinadorFailure({ error }));
          })
        )
      )
    ),
    { dispatch: false }
  );



}