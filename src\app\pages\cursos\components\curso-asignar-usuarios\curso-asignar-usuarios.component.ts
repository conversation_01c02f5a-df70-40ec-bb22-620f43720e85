import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { PageEvent } from '@angular/material/paginator';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { User } from '@app/models/backend/user';
import { Curso } from '@app/models/backend/curso/curso.model';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';

import { NotificationService } from '@app/services/notification/notification.service';
import {
  CursoUsuario,
  AsignacionMasivaResponse,
} from '@app/models/backend/curso/curso-usuario.model';
import { GenericResponse } from '@app/models/backend';

@Component({
  selector: 'app-curso-asignar-usuarios',
  templateUrl: './curso-asignar-usuarios.component.html',
})
export class CursoAsignarUsuariosComponent implements OnInit, OnDestroy {
  curso: Curso;
  usuarios: User[] = [];
  usuariosAsignados: CursoUsuario[] = [];
  usuariosSeleccionados: number[] = [];
  loading = false;
  error: string | null = null;
  searchControl = new FormControl('');
  filteredUsuarios: User[] = [];

  // Búsqueda
  searchTerm = '';
  searchLoading = false;

  // Paginación
  currentPage = 0;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;

  // Table configuration
  displayedColumns: string[] = [
    'select',
    'usuario',
    'contacto',
    'rol',
    'sede',
    'estado',
  ];
  selection = new SelectionModel<number>(true, []);

  private destroy$ = new Subject<void>();

  // Inicializar el formulario
  form = this.fb.group({
    usuariosIds: [[] as number[]],
  });

  constructor(
    private fb: FormBuilder,
    private cursoUsuarioService: CursoUsuarioService,
    private notification: NotificationService,
    private dialogRef: MatDialogRef<CursoAsignarUsuariosComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { curso: Curso }
  ) {
    this.curso = data.curso;
  }

  ngOnInit(): void {
    // Cargar usuarios iniciales
    this.loadUsuarios();

    // Configurar búsqueda con debounce
    this.searchControl.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Esperar 300ms después de la última entrada
        distinctUntilChanged() // Solo emitir si el valor ha cambiado
      )
      .subscribe((value) => {
        this.searchTerm = value || '';
        this.searchUsers(this.searchTerm);
      });

    // Sincronizar selection con usuariosSeleccionados
    this.selection.changed.subscribe(() => {
      this.usuariosSeleccionados = this.selection.selected;
      this.form.patchValue({
        usuariosIds: this.usuariosSeleccionados,
      });
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Carga los usuarios disponibles para el curso con paginación real
   * @param page Número de página (0-indexed)
   */
  loadUsuarios(page: number = 0): void {
    this.loading = true;
    this.error = null;

    // Usar el nuevo endpoint para obtener usuarios disponibles para el curso
    this.cursoUsuarioService
      .getUsuariosDisponiblesParaCurso(this.curso.id, page, this.pageSize)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<any>) => {
          if (response.rpta === 1 && response.data) {
            // El endpoint devuelve un objeto paginado
            const users = response.data.users || [];

            // Actualizar información de paginación
            this.currentPage = response.data.currentPage || 0;
            this.totalItems = response.data.totalItems || 0;
            this.totalPages = response.data.totalPages || 0;

            // Reemplazar la lista con los usuarios de la página actual
            this.filteredUsuarios = users;
          } else {
            this.error = response.msg || 'Error al cargar usuarios disponibles';
            this.notification.error(this.error || 'Error desconocido');
            this.filteredUsuarios = [];
          }
          this.loading = false;
        },
        error: (error: any) => {
          this.loading = false;
          this.error =
            'Error al cargar usuarios disponibles. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al cargar usuarios disponibles:', error);
          this.filteredUsuarios = [];
        },
      });
  }

  /**
   * Maneja el cambio de página del paginador
   * @param event Evento de cambio de página
   */
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;

    // Limpiar selecciones al cambiar de página
    this.selection.clear();

    // Cargar la nueva página
    this.loadUsuarios(this.currentPage);
  }

  /**
   * Busca usuarios disponibles para el curso utilizando la API de búsqueda
   * @param query Término de búsqueda
   */
  searchUsers(query: string): void {
    // Si no hay término de búsqueda, cargar la primera página normal
    if (!query || query.trim() === '') {
      this.currentPage = 0;
      this.loadUsuarios(0);
      return;
    }

    // Activar indicador de carga
    this.searchLoading = true;

    // Resetear a la primera página para búsquedas
    this.currentPage = 0;

    // Usar el servicio para buscar usuarios disponibles para el curso
    this.cursoUsuarioService
      .getUsuariosDisponiblesParaCurso(this.curso.id, 0, this.pageSize, query)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<any>) => {
          if (response.rpta === 1 && response.data) {
            // Actualizar la lista filtrada con los resultados de la búsqueda
            this.filteredUsuarios = response.data.users || [];

            // Actualizar información de paginación
            this.totalItems = response.data.totalItems || 0;
            this.totalPages = response.data.totalPages || 0;
            this.currentPage = response.data.currentPage || 0;
          } else {
            this.error = response.msg || 'Error al buscar usuarios disponibles';
            this.notification.error(this.error || 'Error desconocido');
            this.filteredUsuarios = [];
          }
          this.searchLoading = false;
        },
        error: (error: any) => {
          this.searchLoading = false;
          this.error =
            'Error al buscar usuarios disponibles. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al buscar usuarios disponibles:', error);
          this.filteredUsuarios = [];
        },
      });
  }

  isUsuarioAsignado(usuarioId: number): boolean {
    // Verificar que usuariosAsignados exista y tenga elementos antes de usar some
    return this.usuariosAsignados && this.usuariosAsignados.length > 0
      ? this.usuariosAsignados.some((ua) => ua.usuarioId === usuarioId)
      : false;
  }

  onSelectionChange(event: any): void {
    // Obtener los elementos seleccionados
    const selectedOptions = event.source.selectedOptions.selected;
    // Mapear a un array de IDs
    this.usuariosSeleccionados = selectedOptions.map(
      (option: any) => option.value
    );

    // Actualizar el valor del formulario
    this.form.patchValue({
      usuariosIds: this.usuariosSeleccionados,
    });
  }

  onSubmit(): void {
    if (this.loading) return;

    this.loading = true;
    this.error = null;

    // Obtener los IDs de usuarios seleccionados
    const usuariosIds = (this.form.value.usuariosIds as number[]) || [];

    // Asignar el curso a los usuarios seleccionados
    this.cursoUsuarioService
      .asignarCursoAUsuariosMasivo(this.curso.id, usuariosIds)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<AsignacionMasivaResponse>) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success(
              `Curso asignado a ${
                response.data?.totalAsignados || 0
              } usuarios correctamente`
            );
            this.dialogRef.close(true);
          } else {
            this.error = response.msg || 'Error al asignar curso a usuarios';
            this.notification.error(this.error || 'Error desconocido');
          }
        },
        error: (error: any) => {
          this.loading = false;
          this.error =
            'Error al asignar curso a usuarios. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al asignar curso a usuarios:', error);
        },
      });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Limpia el campo de búsqueda
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.searchControl.setValue('');

    // Resetear a la primera página y cargar usuarios normales
    this.currentPage = 0;
    this.loadUsuarios(0);
  }

  /**
   * Obtiene las iniciales del nombre y apellido del usuario
   * @param nombre Nombre del usuario
   * @param apellido Apellido del usuario
   * @returns Iniciales del nombre y apellido
   */
  getInitials(nombre?: string, apellido?: string): string {
    const n = nombre ? nombre.charAt(0) : '';
    const a = apellido ? apellido.charAt(0) : '';
    return (n + a).toUpperCase();
  }

  /**
   * Genera un color para el avatar basado en el username
   * @param username Username del usuario
   * @returns Color en formato hexadecimal
   */
  getAvatarColor(username?: string): string {
    if (!username) return '#3f51b5'; // Color por defecto

    // Generar un color basado en el username
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
      hash = username.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convertir a color hexadecimal
    let color = '#';
    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xff;
      // Usar padStart en lugar de substr (que está obsoleto)
      color += value.toString(16).padStart(2, '0');
    }

    return color;
  }

  // Table selection methods
  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.filteredUsuarios.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.filteredUsuarios.forEach((usuario) =>
        this.selection.select(usuario.id)
      );
    }
  }

  /** Toggle selection for a specific user */
  toggleUsuarioSelection(usuarioId: number): void {
    this.selection.toggle(usuarioId);
  }
}
