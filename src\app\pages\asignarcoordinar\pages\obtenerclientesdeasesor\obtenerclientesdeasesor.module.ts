import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ObtenerclientesdeasesorComponent } from './obtenerclientesdeasesor.component';
import { ObtenerclientesdeasesorRoutingModule } from './obtenerclientesdeasesorRoutingModule';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Módulos de Angular Material necesarios
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginatorModule } from '@angular/material/paginator';
import { FlexLayoutModule } from '@angular/flex-layout';

@NgModule({
  declarations: [
    ObtenerclientesdeasesorComponent
  ],
  imports: [
    CommonModule,
    ObtenerclientesdeasesorRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatPaginatorModule,
    FlexLayoutModule
  ]
})
export class ObtenerclientesdeasesorModule { }
