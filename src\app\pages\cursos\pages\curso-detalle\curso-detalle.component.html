<div class="flex flex-col h-screen overflow-hidden">
  <!-- Barra superior con navegación -->
  <div class="flex justify-between items-center px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm">
    <div class="flex items-center">
      <button
        class="p-1.5 mr-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
        (click)="volver()"
        title="Volver a mis cursos">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
      </button>
      <div class="flex items-center text-sm">
        <span
          class="cursor-pointer text-indigo-600 dark:text-indigo-400 hover:underline"
          (click)="volver()">
          Mis Cursos
        </span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mx-1 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
        <span class="font-medium text-gray-800 dark:text-white truncate max-w-xs">
          {{ curso?.nombre || 'Cargando...' }}
        </span>
      </div>
    </div>
    <div class="flex items-center">
      <div class="w-40 md:w-48">
        <div class="text-xs text-right mb-1 text-gray-700 dark:text-gray-300 font-medium">
          Progreso: {{ porcentajeCompletado }}%
        </div>
        <div class="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            class="h-full rounded-full transition-all duration-300"
            [style.width.%]="porcentajeCompletado"
            [ngClass]="{
              'bg-red-500 dark:bg-red-600': porcentajeCompletado < 30,
              'bg-yellow-500 dark:bg-yellow-600': porcentajeCompletado >= 30 && porcentajeCompletado < 70,
              'bg-green-500 dark:bg-green-600': porcentajeCompletado >= 70
            }">
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Spinner de carga -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center flex-grow py-8">
    <div class="w-10 h-10 border-4 border-indigo-500 dark:border-indigo-400 border-t-transparent dark:border-t-transparent rounded-full animate-spin"></div>
    <p class="mt-3 text-gray-600 dark:text-gray-300 font-medium">Cargando curso...</p>
  </div>

  <!-- Mensaje de error -->
  <div *ngIf="error" class="flex items-center flex-wrap p-3 mx-4 my-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-lg border-l-4 border-red-500 dark:border-red-600 shadow-sm">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
    </svg>
    <span class="flex-1 mr-3">{{ error }}</span>
    <button
      class="mt-1 sm:mt-0 inline-flex items-center px-2.5 py-1 text-sm font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-900/50 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
      (click)="loadCursoDetalle()">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
      </svg>
      Reintentar
    </button>
  </div>

  <!-- Contenido principal -->
  <div *ngIf="!loading && !error && curso" class="flex flex-col flex-grow overflow-hidden">
    <!-- Información del curso (sin video automático) -->
    <div *ngIf="!leccionActual && mostrarBienvenida" class="px-4 py-6 max-w-3xl mx-auto w-full">
      <h2 class="text-2xl font-medium mb-6 text-indigo-600 dark:text-indigo-400 text-center">Bienvenido al curso: {{ curso.nombre }}</h2>
      <div class="mb-8">
        <h3 class="text-xl font-medium mb-3 text-indigo-600 dark:text-indigo-400">Descripción del Curso</h3>
        <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ curso.descripcion }}</p>
      </div>

      <!-- Botón para ver video introductorio si existe -->
      <div *ngIf="curso.videoUrl" class="flex justify-center mt-6">
        <button
          class="inline-flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-700 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-800 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:ring-offset-2"
          (click)="mostrarVideoIntroductorio()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          Ver Video Introductorio
        </button>
      </div>
    </div>

    <!-- Video introductorio del curso (solo se muestra cuando se hace clic en el botón) -->
    <div *ngIf="!leccionActual && mostrarVideo && curso.videoUrl" class="px-4 py-6 max-w-3xl mx-auto w-full">
      <h2 class="text-xl font-medium mb-4 text-indigo-600 dark:text-indigo-400">Video Introductorio</h2>
      <div class="mb-6 rounded-lg overflow-hidden shadow-md bg-black">
        <video
          #videoIntroductorio
          controls
          class="video-player w-full"
          [poster]="cursoPlaceholderUrl"
          controlsList="nodownload">
          <source [src]="curso.videoUrl" type="video/mp4">
          Tu navegador no soporta la reproducción de videos.
        </video>
      </div>
      <div class="flex justify-center mt-4">
        <button
          class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 rounded-md hover:bg-indigo-200 dark:hover:bg-indigo-900/50 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
          (click)="ocultarVideoIntroductorio()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Volver al contenido del curso
        </button>
      </div>
    </div>

    <!-- Contenedor principal con dos columnas (responsivo) -->
    <div class="flex flex-col md:flex-row flex-1 overflow-hidden">
      <!-- Columna izquierda: Menú de módulos y lecciones (siempre visible) -->
      <div class="w-full sm:w-80 md:w-96 lg:w-1/4 border-r border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden transition-all duration-300">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Contenido del curso</h3>
        </div>

        <div class="flex-1 overflow-y-auto p-2 bg-white dark:bg-gray-900">
          <mat-accordion class="space-y-2">
            <mat-expansion-panel *ngFor="let modulo of modulos" [expanded]="isModuloExpanded(modulo)" class="rounded-md shadow-sm dark:bg-gray-800 dark:shadow-gray-900">
              <mat-expansion-panel-header class="py-2 px-3 hover:bg-gray-50 dark:hover:bg-gray-700">
                <mat-panel-title>
                  <div class="flex justify-between items-start w-full" [title]="modulo.titulo || modulo.nombre">
                    <div class="font-medium text-sm text-gray-800 dark:text-gray-200 pr-2 break-words">{{ modulo.titulo || modulo.nombre }}</div>
                    <div *ngIf="getModuloProgreso(modulo)" class="text-xs font-semibold px-2 py-0.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5">
                      {{ getModuloProgreso(modulo) }}%
                    </div>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <!-- Lista de secciones del módulo -->
              <div class="mt-1 space-y-2">
                <ng-container *ngIf="modulo.secciones">
                  <div *ngFor="let seccion of modulo.secciones" class="rounded overflow-hidden bg-gray-50 dark:bg-gray-800/50">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"
                         [ngClass]="{'bg-green-50 dark:bg-green-900/20': isSeccionCompleted(seccion)}">
                      <div class="flex items-start text-sm font-medium text-gray-700 dark:text-gray-300">
                        <svg *ngIf="isSeccionCompleted(seccion)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-green-500 dark:text-green-400 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <svg *ngIf="!isSeccionCompleted(seccion)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-500 dark:text-gray-400 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                        </svg>
                        <span class="break-words">{{ seccion.titulo }}</span>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">
                        {{ seccion.lecciones?.length || 0 }} lecciones
                      </div>
                    </div>

                    <!-- Lista de lecciones de la sección -->
                    <div class="p-1.5 space-y-1">
                      <ng-container *ngIf="seccion.lecciones">
                        <div *ngFor="let leccion of seccion.lecciones"
                            class="flex items-start p-2 rounded cursor-pointer transition-all duration-200 border-l-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-700"
                            [ngClass]="{
                              'bg-blue-50 dark:bg-blue-900/30 border-l-2 border-blue-500 dark:border-blue-400': leccion.id === leccionActual?.id,
                              'text-green-600 dark:text-green-400': esLeccionCompletada(leccion.id)
                            }"
                            (click)="cargarLeccion(leccion)">
                          <div class="mr-2 mt-0.5">
                            <svg *ngIf="esLeccionCompletada(leccion.id)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <svg *ngIf="!esLeccionCompletada(leccion.id) && leccion.id === leccionActual?.id" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                            </svg>
                            <svg *ngIf="!esLeccionCompletada(leccion.id) && leccion.id !== leccionActual?.id" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
                            </svg>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1 break-words">{{ leccion.titulo || leccion.nombre }}</div>
                            <div class="flex items-center flex-wrap gap-2 text-xs text-gray-500 dark:text-gray-400">
                              <span *ngIf="leccion.duracion" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                </svg>
                                {{ leccion.duracion }} min
                              </span>
                              <span *ngIf="leccion.tipoLeccion === 'VIDEO' && leccion.videoUrl" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                                </svg>
                                Video
                              </span>
                              <span *ngIf="leccion.tipoLeccion === 'VIDEO' && leccion.subtitlesUrl" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                                </svg>
                                Subtítulos
                              </span>
                              <span *ngIf="leccion.tipoLeccion === 'CUESTIONARIO'" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                  <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                </svg>
                                Cuestionario
                              </span>
                              <span *ngIf="leccion.tipoLeccion === 'PDF'" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                </svg>
                                PDF
                              </span>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                  </div>
                </ng-container>
              </div>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </div>

      <!-- Columna derecha: Contenido de la lección actual -->
      <div class="flex-1 p-4 md:p-6 overflow-y-auto bg-white dark:bg-gray-900 min-h-[300px]">
        <!-- Spinner de carga para la lección -->
        <div *ngIf="loadingLeccion" class="flex items-center justify-center p-6">
          <div class="w-8 h-8 border-4 border-indigo-500 dark:border-indigo-400 border-t-transparent dark:border-t-transparent rounded-full animate-spin"></div>
          <span class="ml-3 text-gray-600 dark:text-gray-400">Cargando lección...</span>
        </div>

        <!-- Contenido de la lección -->
        <div *ngIf="!loadingLeccion && leccionActual" class="max-w-5xl mx-auto">
          <h2 class="text-xl md:text-2xl font-medium mb-6 text-indigo-600 dark:text-indigo-400">{{ leccionActual.titulo || leccionActual.nombre }}</h2>

          <!-- Contenido de la lección según su tipo -->
          <ng-container [ngSwitch]="leccionActual.tipoLeccion">
            <!-- Lección tipo VIDEO -->
            <div *ngSwitchCase="'VIDEO'" class="mb-6 rounded-lg overflow-hidden shadow-md">
              <!-- Indicador de carga -->
              <div *ngIf="loadingLeccion" class="flex flex-col items-center justify-center h-64 bg-gray-100 dark:bg-gray-800">
                <div class="w-10 h-10 border-4 border-indigo-500 dark:border-indigo-400 border-t-transparent dark:border-t-transparent rounded-full animate-spin"></div>
                <p class="mt-4 text-gray-600 dark:text-gray-400">Cargando contenido...</p>
              </div>

              <!-- Reproducción directa del video -->
              <div *ngIf="videoUrl && !loadingLeccion" class="w-full">
                <video
                  #videoPlayer
                  controls
                  class="video-player w-full bg-black"
                  [poster]="cursoPlaceholderUrl"
                  controlsList="nodownload"
                  (loadedmetadata)="onVideoLoaded()">
                  <source [src]="videoUrl" type="video/mp4">
                  <!-- No usamos el elemento track directamente para evitar problemas con Blob URLs -->
                  Tu navegador no soporta la reproducción de videos.
                </video>
                <div *ngIf="haySubtitulos" class="flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-800 text-sm text-gray-700 dark:text-gray-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                  </svg>
                  <span>Subtítulos disponibles en español</span>
                </div>
              </div>

              <!-- Mensaje si no hay video disponible -->
              <div *ngIf="!videoUrl && !loadingLeccion" class="flex flex-col items-center justify-center h-64 bg-gray-100 dark:bg-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-4 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                  <path fill-rule="evenodd" d="M1 4a1 1 0 011-1h16a1 1 0 110 2H2a1 1 0 01-1-1zm0 12a1 1 0 011-1h16a1 1 0 110 2H2a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
                <p class="text-gray-600 dark:text-gray-400">No hay video disponible para esta lección</p>
              </div>
            </div>

            <!-- Lección tipo CUESTIONARIO -->
            <div *ngSwitchCase="'CUESTIONARIO'" class="mt-4">
              <app-cuestionario-player
                [leccionId]="leccionActual.id"
                [user]="user"
                (completed)="onCuestionarioCompleted($event)">
              </app-cuestionario-player>
            </div>

            <!-- Lección tipo PDF -->
            <div *ngSwitchCase="'PDF'" class="mb-6 w-full">
              <!-- Indicador de carga -->
              <div *ngIf="loadingLeccion" class="flex flex-col items-center justify-center p-12 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <div class="w-10 h-10 border-4 border-indigo-500 dark:border-indigo-400 border-t-transparent dark:border-t-transparent rounded-full animate-spin"></div>
                <p class="mt-4 text-gray-600 dark:text-gray-400">Cargando documento PDF...</p>
              </div>

              <!-- Visualización del PDF -->
              <div *ngIf="leccionActual.pdfUrl && !loadingLeccion" class="rounded-lg overflow-hidden shadow-md">
                <!-- Mensaje informativo -->
                <div class="flex flex-col items-center justify-center p-10 bg-gray-100 dark:bg-gray-800 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-red-500 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                  <h3 class="text-xl font-medium mb-2 text-gray-800 dark:text-gray-200">Documentos PDF disponibles</h3>
                  <p class="text-gray-600 dark:text-gray-400 max-w-md mb-4">Selecciona un documento PDF para visualizarlo o descargarlo.</p>
                </div>

                <!-- Lista de PDFs disponibles -->
                <div class="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md divide-y divide-gray-200 dark:divide-gray-700">
                  <div *ngFor="let pdfUrl of getPdfUrls(leccionActual.pdfUrl); let i = index" class="flex justify-between items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-red-500 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                      </svg>
                      <span class="text-gray-800 dark:text-gray-200 font-medium">Documento PDF {{i+1}}</span>
                    </div>
                    <div class="flex space-x-2">
                      <button
                        class="inline-flex items-center px-3 py-1.5 bg-indigo-600 dark:bg-indigo-700 text-white text-sm rounded hover:bg-indigo-700 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 transition-colors"
                        (click)="openPdfInNewTab(pdfUrl, $event)">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                          <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                        </svg>
                        Abrir
                      </button>
                      <a [href]="pdfUrl" download
                        class="inline-flex items-center px-3 py-1.5 bg-pink-600 dark:bg-pink-700 text-white text-sm rounded hover:bg-pink-700 dark:hover:bg-pink-800 focus:outline-none focus:ring-2 focus:ring-pink-500 dark:focus:ring-pink-400 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        Descargar
                      </a>
                    </div>
                  </div>
                </div>

                <!-- Espacio para mantener el diseño -->
                <div class="mt-6"></div>
              </div>

              <!-- Mensaje si no hay PDF disponible -->
              <div *ngIf="!leccionActual.pdfUrl && !loadingLeccion" class="flex flex-col items-center justify-center p-12 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-4 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                </svg>
                <p class="text-gray-600 dark:text-gray-400">No hay documento PDF disponible para esta lección</p>
              </div>
            </div>

            <!-- Tipo de lección no reconocido o no especificado -->
            <div *ngSwitchDefault class="flex flex-col items-center justify-center p-12 bg-gray-100 dark:bg-gray-800 rounded-lg mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-4 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              <p class="text-gray-600 dark:text-gray-400">Tipo de lección no reconocido</p>
            </div>
          </ng-container>

          <!-- Descripción de la lección -->
          <div class="mb-6">
            <h3 class="text-lg font-medium mb-3 text-indigo-600 dark:text-indigo-400">Descripción</h3>
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ leccionActual.descripcion }}</p>
          </div>

          <!-- Botones de navegación -->
          <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
            <div class="flex space-x-4 items-center">
              <button
                *ngIf="hayLeccionAnterior()"
                class="inline-flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-700 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 transition-colors"
                (click)="cargarLeccionAnterior()">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Anterior
              </button>
            </div>

            <div class="flex-1 flex justify-center">
              <ng-container *ngIf="!esLeccionCompletada(leccionActual.id); else leccionCompletada">
                <button
                  class="inline-flex items-center px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 transition-colors"
                  (click)="actualizarProgresoLeccion(leccionActual.id)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Marcar como completada
                </button>
              </ng-container>
              <ng-template #leccionCompletada>
                <button
                  class="inline-flex items-center px-4 py-2 bg-red-600 dark:bg-red-700 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 transition-colors"
                  (click)="desmarcarProgresoLeccion(leccionActual.id)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  Desmarcar como completada
                </button>
              </ng-template>
            </div>

            <div class="flex space-x-4 items-center">
              <button
                *ngIf="hayLeccionSiguiente()"
                class="inline-flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-700 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 transition-colors"
                (click)="cargarLeccionSiguiente()">
                Siguiente
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Mensaje si no hay lección seleccionada -->
        <div *ngIf="!loadingLeccion && !leccionActual && !mostrarVideo" class="flex flex-col items-center justify-center p-12 text-center max-w-md mx-auto my-6 rounded-lg bg-gray-50 dark:bg-gray-800 shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-6 text-indigo-500 dark:text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
          </svg>
          <p class="text-lg text-gray-700 dark:text-gray-300">Selecciona un módulo y una lección del menú para comenzar</p>
        </div>
      </div>
    </div>
  </div>
</div>
