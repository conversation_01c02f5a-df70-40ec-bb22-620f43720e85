import {
  Component,
  OnInit,
  OnDestroy,
  Output,
  EventEmitter,
  Input,
} from '@angular/core';
import { UserResponse } from '@app/store/user';
import { ThemeService } from '@app/services';
import { GeneralService } from '@app/services/general.service';
import { HttpClient } from '@angular/common/http';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import * as fromUser from '@app/store/user';
import { WebSocketService } from '@app/services/websocket/WebSocketService';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Output() menuToggle = new EventEmitter<void>();
  @Input() user!: UserResponse | null;
  @Input() isAuthorized!: boolean | null;
  @Input() isMobile!: boolean;
  @Output() signOut = new EventEmitter<void>();
  roleUser: string = '';
  horaPeru: Date = new Date();
  horaEspana: Date = new Date();
  ventasPendientes: number = 0;
  isDarkMode = false;
  idUsuario: number | null | string = null;
  isUserConnected: boolean = false;
  private subscription = new Subscription();

  constructor(
    private themeService: ThemeService,
    private generalService: GeneralService,
    private http: HttpClient,
    private store: Store,
    private webSocketService: WebSocketService
  ) {}

  ngOnInit(): void {
    this.actualizarHoras();
    setInterval(() => {
      this.actualizarHoras();
    }, 1000); // Actualiza cada segundo

    // Subscribe to theme changes
    this.themeService.darkMode$.subscribe((isDarkMode) => {
      this.isDarkMode = isDarkMode;
    });
    const usuario = this.generalService.usuario$;

    if (usuario) {
      this.roleUser = usuario.role || '';
      this.idUsuario = usuario.id || null;
      if (
        this.roleUser === 'BACKOFFICE' ||
        this.roleUser === 'BACKOFFICETRAMITADOR' ||
        this.roleUser === 'BACKOFFICESEGUIMIENTO'
      ) {
        this.obtenerVentasPendientes();
      }
    } else {
      const userFromLocalStorage = localStorage.getItem('user');
      if (userFromLocalStorage) {
        const user = JSON.parse(userFromLocalStorage);
        this.roleUser = user.role || '';
        this.idUsuario = user.id || null;

        if (
          this.roleUser === 'BACKOFFICE' ||
          this.roleUser === 'BACKOFFICETRAMITADOR' ||
          this.roleUser === 'BACKOFFICESEGUIMIENTO'
        ) {
          this.obtenerVentasPendientes();
        }
      } else {
        console.warn('No se encontró información del usuario.');
      }
    }

    // Obtener el estado del usuario del localStorage
    const userStatus = localStorage.getItem('userStatus');
    if (userStatus) {
      this.isUserConnected = userStatus === 'ONLINE';
    }

    // Suscribirse a cambios en el estado del usuario desde el store
    this.subscription.add(
      this.store
        .pipe(select(fromUser.getUserStatus))
        .subscribe((status: string) => {
          this.isUserConnected = status === 'ONLINE';
        })
    );

    // Suscribirse a cambios en el estado de la conexión WebSocket
    // Solo para actualizar el estado del usuario, no para iniciar conexiones
    this.subscription.add(
      this.webSocketService.getConnectionStatus().subscribe({
        next: (connected: boolean) => {
          if (!connected) {
            // Si se desconecta el WebSocket, actualizar el estado del usuario a OFFLINE
            this.store.dispatch(new fromUser.UpdateUserStatus('OFFLINE'));
            localStorage.setItem('userStatus', 'OFFLINE');
            this.isUserConnected = false;
          }
        },
        error: (error) =>
          console.error(
            'HeaderComponent: Error en suscripción a WebSocket status:',
            error
          ),
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onMenuToggleDispatch(): void {
    this.menuToggle.emit();
  }

  onSignOut(): void {
    this.signOut.emit();
  }

  toggleDarkMode(): void {
    this.themeService.toggleDarkMode();
  }
  isBACKOFFICETRAMITADOR(): boolean {
    return this.roleUser === 'BACKOFFICETRAMITADOR';
  }
  isBACKOFFICESEGUIMIENTO(): boolean {
    return this.roleUser === 'BACKOFFICESEGUIMIENTO';
  }
  isBACKOFFICE(): boolean {
    return this.roleUser === 'BACKOFFICE';
  }

  isPROGRAMADOR(): boolean {
    return this.roleUser === 'PROGRAMADOR';
  }
  private actualizarHoras(): void {
    const ahora = new Date();
    this.horaPeru = new Date(
      ahora.toLocaleString('en-US', { timeZone: 'America/Lima' })
    );
    this.horaEspana = new Date(
      ahora.toLocaleString('en-US', { timeZone: 'Europe/Madrid' })
    );
  }

  private obtenerVentasPendientes(): void {
    const baseUrl = 'https://apisozarusac.com/ventas/api/ventas/';
    const url =
      this.isBACKOFFICE() || this.isBACKOFFICETRAMITADOR()
        ? baseUrl
        : `${baseUrl}/backoffice/${this.idUsuario}/`;

    const params = new URLSearchParams();
    params.append('estado', 'PENDIENTE DE FILTRO');

    this.http.get<any[]>(`${url}?${params}`).subscribe({
      next: (ventas) => {
        this.ventasPendientes = ventas.length;
      },
      error: (_) => {
        // En caso de error, simplemente establecer a 0
        this.ventasPendientes = 0;
      },
    });
  }
}
