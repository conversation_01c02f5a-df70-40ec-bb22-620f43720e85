// app/models/backend/faq/faq.model.ts

import { FileFaq } from "./file-faq.model";
import { FaqRespuesta } from "./faq-respuesta.model";
import { User } from "../user";

/**
 * Modelo que representa una pregunta frecuente (FAQ)
 *
 * En el backend, usuarioPregunta y usuarioRespuesta son objetos User completos,
 * pero en el frontend podemos manejarlos de diferentes formas dependiendo del contexto:
 * - Como objetos User completos cuando vienen del backend
 * - Como IDs y nombres cuando los enviamos al backend
 */
export interface Faq {
  id?: number;
  pregunta: string;
  respuesta?: string;
  categoria?: string;
  tipoUsuario?: string;
  views?: number;

  // Usuario que crea la pregunta
  usuarioPregunta?: User | null;  // Objeto User completo del backend
  usuarioPreguntaId?: number;     // ID del usuario para enviar al backend
  usuarioPreguntaNombre?: string; // Nombre del usuario para mostrar en la UI

  // Usuario que responde la pregunta
  usuarioRespuesta?: User | null; // Objeto User completo del backend
  usuarioRespuestaId?: number;    // ID del usuario para enviar al backend
  usuarioRespuestaNombre?: string; // Nombre del usuario para mostrar en la UI

  // Indica si la pregunta es pública o privada
  esPublica?: boolean;

  // Indica si la pregunta ha sido respondida
  respondida?: boolean;

  // Estado de la pregunta (ABIERTA, CERRADA)
  estado?: 'ABIERTA' | 'CERRADA';

  // Archivos adjuntos
  archivos?: FileFaq[];

  // Respuestas adicionales
  respuestas?: FaqRespuesta[];

  // Fechas
  createdAt?: string;   // ISO‑8601 recibido del backend
  updatedAt?: string;
}