import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ModuloService } from '@app/services/modulo.service';
import { Modulo, ModuloCreateRequest, ModuloUpdateRequest } from '@app/models/backend/curso/modulo.model';
import { NotificationService } from '@app/services/notification/notification.service';

@Component({
  selector: 'app-modulo-form',
  templateUrl: './modulo-form.component.html',
  styleUrls: ['./modulo-form.component.scss']
})
export class ModuloFormComponent implements OnInit, OnDestroy {
  @Input() cursoId!: number;
  @Input() modulo: Modulo | null = null;
  @Input() isDarkTheme: boolean = false;
  @Output() saved = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();

  form!: FormGroup;
  loading: boolean = false;
  error: string | null = null;
  isEditMode: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private moduloService: ModuloService,
    private notification: NotificationService
  ) { }

  ngOnInit(): void {
    this.isEditMode = !!this.modulo;
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.group({
      titulo: [this.modulo?.titulo || this.modulo?.nombre || '', [Validators.required, Validators.maxLength(100)]],
      descripcion: [this.modulo?.descripcion || '', [Validators.maxLength(500)]],
      orden: [this.modulo?.orden || 0, [Validators.min(0)]],
      estado: [this.modulo?.estado || 'A']
    });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;

    if (this.isEditMode) {
      this.updateModulo();
    } else {
      this.createModulo();
    }
  }

  private createModulo(): void {
    // Verificar que el cursoId sea válido
    if (!this.cursoId) {
      this.loading = false;
      this.error = 'Error: No se ha especificado un curso válido';
      this.notification.error(this.error || 'Error desconocido');
      return;
    }

    // Verificar que el título no esté vacío
    if (!this.form.value.titulo || this.form.value.titulo.trim() === '') {
      this.loading = false;
      this.error = 'Error: El título del módulo es obligatorio';
      this.notification.error(this.error);
      return;
    }

    // Verificar que el orden sea un número válido
    if (this.form.value.orden === null || this.form.value.orden === undefined || isNaN(Number(this.form.value.orden))) {
      this.loading = false;
      this.error = 'Error: El orden debe ser un número válido';
      this.notification.error(this.error);
      return;
    }

    const moduloData: ModuloCreateRequest = {
      titulo: this.form.value.titulo.trim(),
      descripcion: this.form.value.descripcion ? this.form.value.descripcion.trim() : '',
      orden: Number(this.form.value.orden),
      cursoId: this.cursoId
    };

    // Solo añadir el estado si está definido
    if (this.form.value.estado) {
      moduloData.estado = this.form.value.estado;
    }

    console.log('Datos del módulo a crear:', moduloData);

    this.moduloService.createModulo(moduloData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Módulo creado exitosamente');
            this.saved.emit();
          } else {
            this.error = response.msg || 'Error al crear el módulo';
            this.notification.error(this.error);

            // Si hay errores de validación en la respuesta, mostrarlos
            if (response.data && typeof response.data === 'object') {
              console.error('Errores de validación:', response.data);
              const validationErrors = Object.values(response.data).join(', ');
              if (validationErrors) {
                this.error = `Errores de validación: ${validationErrors}`;
                this.notification.error(this.error);
              }
            }
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al crear el módulo. Por favor, inténtelo de nuevo.';

          // Intentar extraer información más detallada del error
          if (error.error && error.error.msg) {
            this.error = error.error.msg;
          }

          // Si hay errores de validación en la respuesta
          if (error.error && error.error.data && typeof error.error.data === 'object') {
            console.error('Errores de validación:', error.error.data);
            const validationErrors = Object.values(error.error.data).join(', ');
            if (validationErrors) {
              this.error = `Errores de validación: ${validationErrors}`;
            }
          }

          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al crear módulo:', error);
        }
      });
  }

  private updateModulo(): void {
    if (!this.modulo) {
      this.loading = false;
      this.error = 'Error: No se ha especificado un módulo válido';
      this.notification.error(this.error);
      return;
    }

    // Verificar que el título no esté vacío
    if (!this.form.value.titulo || this.form.value.titulo.trim() === '') {
      this.loading = false;
      this.error = 'Error: El título del módulo es obligatorio';
      this.notification.error(this.error);
      return;
    }

    // Verificar que el orden sea un número válido
    if (this.form.value.orden === null || this.form.value.orden === undefined || isNaN(Number(this.form.value.orden))) {
      this.loading = false;
      this.error = 'Error: El orden debe ser un número válido';
      this.notification.error(this.error);
      return;
    }

    const moduloData: ModuloUpdateRequest = {
      titulo: this.form.value.titulo.trim(),
      descripcion: this.form.value.descripcion ? this.form.value.descripcion.trim() : '',
      orden: Number(this.form.value.orden)
    };

    // Solo añadir el estado si está definido
    if (this.form.value.estado) {
      moduloData.estado = this.form.value.estado;
    }

    console.log('Datos del módulo a actualizar:', moduloData);

    this.moduloService.updateModulo(this.modulo.id, moduloData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Módulo actualizado exitosamente');
            this.saved.emit();
          } else {
            this.error = response.msg || 'Error al actualizar el módulo';
            this.notification.error(this.error);

            // Si hay errores de validación en la respuesta, mostrarlos
            if (response.data && typeof response.data === 'object') {
              console.error('Errores de validación:', response.data);
              const validationErrors = Object.values(response.data).join(', ');
              if (validationErrors) {
                this.error = `Errores de validación: ${validationErrors}`;
                this.notification.error(this.error);
              }
            }
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al actualizar el módulo. Por favor, inténtelo de nuevo.';

          // Intentar extraer información más detallada del error
          if (error.error && error.error.msg) {
            this.error = error.error.msg;
          }

          // Si hay errores de validación en la respuesta
          if (error.error && error.error.data && typeof error.error.data === 'object') {
            console.error('Errores de validación:', error.error.data);
            const validationErrors = Object.values(error.error.data).join(', ');
            if (validationErrors) {
              this.error = `Errores de validación: ${validationErrors}`;
            }
          }

          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al actualizar módulo:', error);
        }
      });
  }

  onCancel(): void {
    this.cancelled.emit();
  }
}
