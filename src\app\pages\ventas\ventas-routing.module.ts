import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { VentasComponent } from './ventas.component';
import { AuthGuard } from '@app/guards/auth/auth.guard';
import { GraficosVentasComponent } from './graficos-ventas/graficos-ventas.component';

const routes: Routes = [
  {
    path: '',
    component: VentasComponent
  },
  {
    path: 'crear',
    loadChildren: () => import('./crear-venta/crear-venta.module').then(m => m.CrearVentaModule)
  },
 
  {
    path: 'graficos',
    component: GraficosVentasComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class VentasRoutingModule { }

