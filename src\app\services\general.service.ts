import { Injectable } from '@angular/core';
import { UserResponse } from '@app/store/user';
import { environment } from '@src/environments/environment';

@Injectable({ providedIn: 'root' })
export class GeneralService {
  /** Último usuario autenticado en memoria (inyectado por NGRX u otros) */
  usuario$!: UserResponse;

  /** Obtiene el JWT almacenado */
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  /** Devuelve el id de usuario o 0 si no existe (mínimo necesario) */
  getUserId(): number {
    if (this.usuario$?.id) {
      return this.usuario$.id;
    }
    try {
      const raw = localStorage.getItem('user');
      return raw ? JSON.parse(raw).id ?? 0 : 0;
    } catch {
      return 0;
    }
  }
}