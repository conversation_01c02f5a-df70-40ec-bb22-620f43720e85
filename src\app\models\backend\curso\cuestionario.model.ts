import { Leccion } from './leccion.model';
import { Pregunta } from './pregunta.model';

/**
 * Modelo que representa un cuestionario asociado a una lección
 */
export interface Cuestionario {
  id: number;
  titulo: string;
  descripcion: string;
  tiempoLimite: number; // Tiempo límite en minutos
  puntajeAprobacion: number; // Puntaje mínimo para aprobar (0-100)
  intentosMaximos: number; // Número máximo de intentos permitidos
  mostrarRespuestas: boolean; // Mostrar respuestas correctas al finalizar
  aleatorizarPreguntas: boolean; // Mostrar preguntas en orden aleatorio
  leccionId: number;
  leccionTitulo: string;
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
  preguntas: Pregunta[];
}

/**
 * Modelo para crear un nuevo cuestionario
 */
export interface CuestionarioCreateRequest {
  titulo: string;
  descripcion: string;
  tiempoLimite: number;
  puntajeAprobacion: number;
  intentosMaximos: number;
  mostrarRespuestas: boolean;
  aleatorizarPreguntas: boolean;
  leccionId: number;
}

/**
 * Modelo para actualizar un cuestionario existente
 */
export interface CuestionarioUpdateRequest {
  titulo?: string;
  descripcion?: string;
  tiempoLimite?: number;
  puntajeAprobacion?: number;
  intentosMaximos?: number;
  mostrarRespuestas?: boolean;
  aleatorizarPreguntas?: boolean;
  estado?: string;
}
