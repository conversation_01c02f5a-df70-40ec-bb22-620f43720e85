import { Curso } from './curso.model';
import { Seccion } from './seccion.model';

/**
 * Modelo que representa un módulo de un curso
 */
export interface Modulo {
  id: number;
  nombre?: string;
  titulo?: string; // Campo que viene del backend
  descripcion: string;
  orden: number;
  estado: string; // A: Activo, I: Inactivo
  curso: Curso | null;
  cursoId?: number;
  secciones?: Seccion[];
  fechaCreacion?: string;
  fechaActualizacion?: string;
}

/**
 * Modelo para crear un nuevo módulo
 */
export interface ModuloCreateRequest {
  titulo: string;  // Cambiado de 'nombre' a 'titulo' para coincidir con el backend
  descripcion: string;
  orden?: number;
  cursoId: number;
  estado?: string;
}

/**
 * Modelo para actualizar un módulo existente
 */
export interface ModuloUpdateRequest {
  titulo?: string;  // Cambiado de 'nombre' a 'titulo' para coincidir con el backend
  descripcion?: string;
  orden?: number;
  estado?: string;
}
