import { UserDTO } from '../user/user-dto.model';
import { <PERSON><PERSON><PERSON> } from './modulo.model';

export interface Curso {
  id: number;
  nombre: string;
  descripcion: string;
  fechaInicio: any; // Puede ser un array [año, mes, día, ...] o string
  fechaFin: any; // Puede ser un array [año, mes, día, ...] o string
  estado: string;
  videoUrl: string; // URL del video introductorio del curso
  usuario: UserDTO | null;
  asignacion?: any; // Información de la asignación del curso al usuario
  modulos?: Modulo[]; // Módulos del curso
}

export interface CursoCreateRequest {
  nombre: string;
  descripcion: string;
  fechaInicio: string | null;
  fechaFin: string | null;
  usuarioId: number | null;
  videoUrl: string | null;
}

export interface CursoUpdateRequest {
  nombre?: string;
  descripcion?: string;
  fechaInicio?: string;
  fechaFin?: string;
  estado?: string;
  videoUrl?: string;
}
