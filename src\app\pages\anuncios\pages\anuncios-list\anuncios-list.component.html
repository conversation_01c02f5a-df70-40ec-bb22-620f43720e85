<div class="p-6 min-h-screen">
  <div
    class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6 bg-white p-4 lg:p-6 rounded-xl shadow-sm"
  >
    <h2 class="text-2xl font-medium text-blue-600 mb-4 lg:mb-0">
      Listado de anuncios
    </h2>
    <div
      class="flex flex-col lg:flex-row gap-4 lg:items-center w-full lg:w-auto"
    >
      <div class="relative w-full lg:w-80">
        <input
          placeholder="Buscar"
          [(ngModel)]="searchTerm"
          class="w-full rounded-full border border-gray-300 px-4 py-2.5 pr-12 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500"
        />
        <mat-icon
          class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"
          >search</mat-icon
        >
      </div>
      <button
        mat-raised-button
        (click)="openNuevoAnuncioDialog()"
        class="bg-green-500 hover:bg-green-600 text-white rounded-full px-5 py-2.5 h-10 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5 active:translate-y-0 flex items-center gap-2 justify-center lg:justify-start"
      >
        <mat-icon>add</mat-icon>
        <span class="font-medium">Nuevo anuncio</span>
      </button>
      <button
        mat-raised-button
        (click)="recargarAnuncios()"
        title="Recargar anuncios"
        class="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-10 h-10 min-w-10 p-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5 hover:rotate-180 active:translate-y-0 flex items-center justify-center"
      >
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <!-- Indicador de carga -->
  <div
    class="flex flex-col items-center justify-center my-10"
    *ngIf="loading | async"
  >
    <app-spinner></app-spinner>
    <p class="mt-4 text-base text-blue-600 font-medium">Cargando anuncios...</p>
  </div>

  <!-- Mensaje de error -->
  <ng-container *ngIf="error | async as errorState">
    <div class="flex justify-center my-10" *ngIf="errorState.error">
      <mat-card class="max-w-md p-5 text-center">
        <mat-card-content>
          <mat-icon class="text-5xl h-12 w-12 text-red-500 mb-4"
            >error</mat-icon
          >
          <p class="text-base mb-5">{{ errorState.message }}</p>
          <button
            mat-raised-button
            color="primary"
            (click)="recargarAnuncios()"
            class="mt-2.5 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white border-none rounded cursor-pointer"
          >
            <mat-icon>refresh</mat-icon> Intentar nuevamente
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  </ng-container>

  <!-- Indicador de modo de respaldo HTTP -->
  <div
    class="flex items-center justify-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded mb-4"
    *ngIf="usingHttpFallback"
  >
    <mat-icon class="mr-2">warning</mat-icon>
    <span>Usando modo de respaldo (sin actualizaciones en tiempo real)</span>
  </div>

  <div
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-2"
    *ngIf="anuncios$ | async as anuncios"
  >
    <mat-card
      class="bg-white rounded-2xl overflow-hidden transition-all duration-300 h-full flex flex-col shadow-md hover:shadow-xl hover:-translate-y-1"
      *ngFor="let anuncio of anuncios | filter : searchTerm"
    >
      <div
        class="relative cursor-pointer overflow-hidden h-56 group"
        (click)="
          openImageViewer(anuncio.imagenUrl || pictureDefault, anuncio.titulo)
        "
      >
        <img
          [src]="anuncio.imagenUrl || pictureDefault"
          [alt]="anuncio.titulo"
          class="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div
          class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      <mat-card-content class="flex-grow p-5 relative">
        <h3 class="text-lg font-medium text-gray-800 mb-3 leading-tight">
          {{ anuncio.titulo }}
        </h3>
        <div
          class="absolute top-5 right-5 px-3 py-1 rounded-full text-xs font-medium uppercase"
          [ngClass]="{
            'bg-sky-400 text-white': anuncio.categoria === 'interno',
            'bg-blue-800 text-white': anuncio.categoria === 'externo'
          }"
        ></div>
        <p
          class="text-gray-600 text-sm leading-relaxed mb-4 overflow-hidden"
          style="
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          "
        >
          {{ anuncio.descripcion }}
        </p>

        <!-- Información del anuncio -->
        <div class="flex flex-wrap gap-3 mb-4">
          <!-- Categoría -->
          <div class="flex items-center gap-1.5 text-sm text-gray-600">
            <mat-icon class="text-blue-600 text-lg">category</mat-icon>
            <span>{{ anuncio.categoria }}</span>
          </div>

          <!-- Estado del anuncio -->
          <div class="flex items-center gap-1.5 text-sm text-gray-600">
            <mat-icon
              [class]="
                (anuncio.estado || 'ACTIVO') === 'ACTIVO'
                  ? 'text-green-500 text-lg'
                  : 'text-red-500 text-lg'
              "
              >{{
                (anuncio.estado || "ACTIVO") === "ACTIVO"
                  ? "check_circle"
                  : "cancel"
              }}</mat-icon
            >
            <div
              class="px-2.5 py-1 rounded text-xs font-medium uppercase text-white"
              [ngClass]="{
                'bg-green-500': (anuncio.estado || 'ACTIVO') === 'ACTIVO',
                'bg-red-500': (anuncio.estado || 'ACTIVO') === 'INACTIVO'
              }"
            >
              {{ anuncio.estado || "ACTIVO" }}
            </div>
          </div>

          <!-- Orden de visualización -->
          <div
            class="flex items-center gap-1.5 text-sm text-gray-600"
            *ngIf="anuncio.orden !== undefined && anuncio.orden !== null"
          >
            <mat-icon class="text-orange-500 text-lg">sort</mat-icon>
            <span>Orden: {{ anuncio.orden }}</span>
          </div>

          <!-- Sede del anuncio -->
          <div
            class="flex items-center gap-1.5 text-sm text-gray-600"
            *ngIf="anuncio.sedeId || anuncio.nombreSede"
          >
            <mat-icon class="text-gray-500 text-lg">location_city</mat-icon>
            <span
              >Sede:
              {{
                anuncio.nombreSede ||
                  (anuncio.sedeId ? "ID: " + anuncio.sedeId : "Todas")
              }}</span
            >
          </div>
          <div
            class="flex items-center gap-1.5 text-sm text-gray-600"
            *ngIf="!anuncio.sedeId && !anuncio.nombreSede"
          >
            <mat-icon class="text-gray-500 text-lg">public</mat-icon>
            <span>Sede: Todas</span>
          </div>
        </div>

        <!-- Fechas de vigencia -->
        <div class="mt-4 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-600">
          <div class="flex items-center gap-2 mb-2.5 font-medium text-blue-600">
            <mat-icon class="text-lg">date_range</mat-icon>
            <span>Vigencia</span>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div class="flex flex-col" *ngIf="anuncio.fechaInicio">
              <span class="text-xs text-gray-600 mb-1">Desde:</span>
              <span class="text-sm font-medium text-gray-800">{{
                formatDate(anuncio.fechaInicio)
              }}</span>
            </div>

            <div class="flex flex-col" *ngIf="anuncio.fechaFin">
              <span class="text-xs text-gray-600 mb-1">Hasta:</span>
              <span class="text-sm font-medium text-gray-800">{{
                formatDate(anuncio.fechaFin)
              }}</span>
            </div>

            <div class="flex flex-col" *ngIf="!anuncio.fechaInicio">
              <span class="text-xs text-gray-600 mb-1">Desde:</span>
              <span class="text-sm font-medium text-gray-800">No definida</span>
            </div>

            <div class="flex flex-col" *ngIf="!anuncio.fechaFin">
              <span class="text-xs text-gray-600 mb-1">Hasta:</span>
              <span class="text-sm font-medium text-gray-800">No definida</span>
            </div>
          </div>
        </div>
      </mat-card-content>
      <div class="flex justify-between p-3 m-0 border-t border-gray-100">
        <button
          mat-button
          class="font-medium relative overflow-hidden px-5 py-2 rounded-md transition-all duration-300 text-blue-600 hover:bg-blue-50"
          (click)="openEditarDialog(anuncio)"
        >
          Actualizar
        </button>
        <button
          mat-button
          class="font-medium relative overflow-hidden px-5 py-2 rounded-md transition-all duration-300 text-red-600 hover:bg-red-50"
          (click)="eliminarAnuncio(anuncio.id)"
        >
          <mat-icon>delete</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>

  <ng-container *ngIf="totalElements$ | async as total">
    <div class="flex justify-center mt-6 p-4 bg-white rounded-xl shadow-sm">
      <mat-paginator
        *ngIf="total > 0"
        [length]="total"
        [pageSize]="pageSize"
        [pageIndex]="(currentPage$ | async) || 0"
        [pageSizeOptions]="[5, 8, 10, 20, 50]"
        [hidePageSize]="false"
        [showFirstLastButtons]="true"
        (page)="onPageChange($event.pageIndex, $event.pageSize)"
        aria-label="Seleccionar página"
        class="bg-transparent flex justify-center"
      >
      </mat-paginator>
    </div>
  </ng-container>
</div>

<ng-template #formDialog>
  <app-anuncios-nuevo></app-anuncios-nuevo>
</ng-template>
