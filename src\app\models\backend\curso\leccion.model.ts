import { Seccion } from './seccion.model';
import { Cuestionario } from './cuestionario.model';

/**
 * Enum que define los tipos de lecciones disponibles
 */
export enum TipoLeccion {
  VIDEO = 'VIDEO',         // Lección con video
  CUESTIONARIO = 'CUESTIONARIO',   // Lección con cuestionario
  PDF = 'PDF'              // Lección con documento PDF
}

/**
 * Modelo que representa una lección de una sección
 */
export interface Leccion {
  id: number;
  nombre: string;
  titulo?: string; // Añadido para coincidir con el backend
  descripcion: string;
  tipoLeccion?: TipoLeccion; // Tipo de lección: VIDEO, CUESTIONARIO o PDF
  videoUrl?: string;
  subtitlesUrl?: string;
  pdfUrl?: string; // URL del documento PDF almacenado en Firebase
  duracion?: number; // Duración en minutos
  orden: number;
  estado: string; // A: Activo, I: Inactivo
  seccion: Seccion | null;
  seccionId?: number;
  seccionTitulo?: string; // Añadido para coincidir con el backend
  fechaCreacion?: string;
  fechaActualizacion?: string;
  thumbnailUrl?: string; // Añadido para coincidir con el backend
  cuestionario?: Cuestionario; // Cuestionario asociado a la lección (si tipoLeccion es CUESTIONARIO)
}

/**
 * Modelo para crear una nueva lección
 */
export interface LeccionCreateRequest {
  titulo: string;  // Cambiado de nombre a titulo para coincidir con el backend
  descripcion: string;
  tipoLeccion?: TipoLeccion; // Tipo de lección: VIDEO, CUESTIONARIO o PDF
  videoUrl?: string;
  subtitlesUrl?: string;
  pdfUrl?: string; // URL del documento PDF almacenado en Firebase
  duracion?: number;
  orden?: number;
  seccionId: number;
  estado?: string;
}

/**
 * Modelo para actualizar una lección existente
 */
export interface LeccionUpdateRequest {
  titulo?: string;  // Cambiado de nombre a titulo para coincidir con el backend
  descripcion?: string;
  tipoLeccion?: TipoLeccion; // Tipo de lección: VIDEO, CUESTIONARIO o PDF
  videoUrl?: string;
  subtitlesUrl?: string;
  pdfUrl?: string; // URL del documento PDF almacenado en Firebase
  duracion?: number;
  orden?: number;
  estado?: string;
}
