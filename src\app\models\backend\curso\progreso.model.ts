import { User } from '../user';
import { Curso } from './curso.model';
import { Leccion } from './leccion.model';

/**
 * Modelo que representa el progreso de un usuario en un curso
 */
export interface ProgresoUsuario {
  id: number;
  usuario: User | null;
  usuarioId?: number;
  curso: Curso | null;
  cursoId?: number;
  porcentajeCompletado: number;
  fechaInicio: string;
  fechaUltimoAcceso: string;
  fechaCompletado?: string;
  estado: string; // EN_PROGRESO, COMPLETADO, ABANDONADO
  leccionesCompletadas?: ProgresoLeccion[];
}

/**
 * Modelo que representa el progreso de un usuario en una lección
 */
export interface ProgresoLeccion {
  id: number;
  usuario: User | null;
  usuarioId?: number;
  leccion: Leccion | null;
  leccionId?: number;
  completado: boolean;
  fechaCompletado?: string;
  tiempoVisto?: number; // Tiempo visto en segundos
}

/**
 * Modelo para crear un nuevo registro de progreso
 */
export interface ProgresoCreateRequest {
  usuarioId: number;
  cursoId: number;
  estado?: string;
}

/**
 * Modelo para actualizar un registro de progreso
 */
export interface ProgresoUpdateRequest {
  porcentajeCompletado?: number;
  estado?: string;
}

/**
 * Modelo para marcar una lección como completada
 */
export interface CompletarLeccionRequest {
  usuarioId: number;
  leccionId: number;
  completado?: boolean;
  tiempoVisto?: number;
}
