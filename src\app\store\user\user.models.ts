import { User } from '@app/models/backend/user';
export { User as UserResponse } from '@app/models/backend/user';
export { UserPageResponse as UserPageResponse } from '@app/models/backend/user';

/* export interface EmailPasswordCredentials {
  email: string;
  password: string;
} */

// user.models.ts
export interface UsernamePasswordCredentials {
  username: string;
  password: string;
}

export interface UserRequest extends User {
  password: string;
}

export interface UserCreate extends Omit<User, 'id' | 'token'> {
  sede_id: number; // Campo obligatorio para el ID de la sede
  telefono?: string | null; // Campo opcional que puede ser null
  email?: string | null; // Campo opcional que puede ser null
}
