export interface User {
  id: number;
  username: string;
  nombre: string;
  apellido: string;
  telefono?: string | null;
  email?: string | null;
  token: string;
  sede?: any | null; // Este campo debe quedar en null
  sedeNombre?: string | null; // Campo que contiene el nombre de la sede en la BD
  sedeId?: number | null; // ID de la sede que devuelve el backend
  sede_id?: number | null; // ID de la sede para enviar al backend
  role?: string;
  estado?: string;
  dni?: string;
  imageUrl?: string | null;
  password?: string;
}
export interface Coordinador {
  id: number;
  nombre: string;
  apellido: string;
  username: string;
}
export interface UserResponse2 {
  user: User;
  coordinador: Coordinador;
}
/**
 * Estructura que retorna tu backend en el endpoint /listar
 * cuando usas paginación en Spring Boot.
 */
export interface UserPageResponse {
  users: User[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
}
