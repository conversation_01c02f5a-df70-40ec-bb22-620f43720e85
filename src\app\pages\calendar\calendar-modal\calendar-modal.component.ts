import { Component, OnInit, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Calendar } from '@app/models/backend/calendar/calendar.model';

@Component({
  selector: 'app-calendar-modal',
  templateUrl: './calendar-modal.component.html',
  styleUrls: ['./calendar-modal.component.scss']
})
export class CalendarModalComponent implements OnInit {
  calendarForm!: FormGroup;
  isNewData: boolean = true;
  submitted: boolean = false;
  categories: any[] = [];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<CalendarModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.initCategories();

    if (this.data && this.data.event) {
      this.isNewData = false;
      this.patchFormValues(this.data.event);
    } else if (this.data && this.data.date) {
      this.calendarForm.patchValue({
        fecha_inicio: this.data.date,
        fecha_final: this.data.date,
        hora_inicio: '08:00',
        hora_final: '09:00'
      });
    }
  }

  initForm(): void {
    this.calendarForm = this.fb.group({
      id: [0],
      titulo: ['', [Validators.required]],
      descripcion: [''],
      color: ['bg-primary'],
      fecha_inicio: ['', [Validators.required]],
      fecha_final: ['', [Validators.required]],
      hora_inicio: ['', [Validators.required]],
      hora_final: ['', [Validators.required]],
      is_active: [true],
      is_seen: [false]
    });
  }

  initCategories(): void {
    this.categories = [
      {
        name: 'Azul',
        value: 'bg-primary'
      },
      {
        name: 'Verde',
        value: 'bg-success'
      },
      {
        name: 'Rojo',
        value: 'bg-danger'
      },
      {
        name: 'Amarillo',
        value: 'bg-warning'
      },
      {
        name: 'Celeste',
        value: 'bg-info'
      },
      {
        name: 'Morado',
        value: 'bg-purple'
      },
      {
        name: 'Gris',
        value: 'bg-secondary'
      },
      {
        name: 'Negro',
        value: 'bg-dark'
      }
    ];
  }

  patchFormValues(event: Calendar): void {
    this.calendarForm.patchValue({
      id: event.id,
      titulo: event.titulo,
      descripcion: event.descripcion,
      color: event.color,
      fecha_inicio: event.fecha_inicio,
      fecha_final: event.fecha_final,
      hora_inicio: event.hora_inicio,
      hora_final: event.hora_final,
      is_active: event.is_active,
      is_seen: event.is_seen
    });
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.calendarForm.valid) {
      // Obtener los datos del formulario
      const formData = this.calendarForm.value;

      // Obtener el userId del localStorage
      const userStr = localStorage.getItem('user');
      let userId = 0;
      if (userStr) {
        const user = JSON.parse(userStr);
        userId = user.id || 0;
      }

      // Asegurarnos de que los campos tengan el formato correcto
      // El backend espera fechaInicio, horaInicio, etc. en lugar de fecha_inicio, hora_inicio
      const formattedData = {
        id: formData.id,
        titulo: formData.titulo,
        descripcion: formData.descripcion,
        color: formData.color,
        fechaInicio: formData.fecha_inicio,
        fechaFinal: formData.fecha_final,
        horaInicio: formData.hora_inicio,
        horaFinal: formData.hora_final,
        is_active: formData.is_active,
        is_seen: formData.is_seen,
        userId: userId // Agregar el userId
      };

      //console.log('Datos formateados:', formattedData);

      // Cerrar el diálogo y enviar los datos formateados
      this.dialogRef.close({
        action: this.isNewData ? 'create' : 'update',
        data: formattedData
      });
    }
  }

  onDelete(): void {
    if (this.calendarForm.get('id')?.value) {
      this.dialogRef.close({
        action: 'delete',
        data: { id: this.calendarForm.get('id')?.value }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  get f() {
    return this.calendarForm.controls;
  }

  // Método para obtener el nombre del color a partir de su valor
  getColorName(colorValue: string): string {
    if (!colorValue) return '';

    const category = this.categories.find(cat => cat.value === colorValue);
    return category ? category.name : '';
  }
}
