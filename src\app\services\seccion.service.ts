import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend';
import { Seccion, SeccionCreateRequest, SeccionUpdateRequest } from '@app/models/backend/curso/seccion.model';

@Injectable({
  providedIn: 'root'
})
export class SeccionService {
  private baseUrl = environment.url + 'api/secciones';
  private seccionesSubject = new BehaviorSubject<Seccion[]>([]);
  public secciones$ = this.seccionesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene todas las secciones de un módulo
   */
  getSeccionesByModuloId(moduloId: number): Observable<GenericResponse<Seccion[]>> {
    return this.http.get<GenericResponse<Seccion[]>>(`${this.baseUrl}/modulo/${moduloId}`).pipe(
      map(response => {
        if (response.data) {
          this.seccionesSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene una sección por su ID
   */
  getSeccionById(id: number): Observable<GenericResponse<Seccion>> {
    return this.http.get<GenericResponse<Seccion>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea una nueva sección
   */
  createSeccion(seccion: SeccionCreateRequest): Observable<GenericResponse<Seccion>> {
    return this.http.post<GenericResponse<Seccion>>(this.baseUrl, seccion);
  }

  /**
   * Actualiza una sección existente
   */
  updateSeccion(id: number, seccion: SeccionUpdateRequest): Observable<GenericResponse<Seccion>> {
    return this.http.put<GenericResponse<Seccion>>(`${this.baseUrl}/${id}`, seccion);
  }

  /**
   * Elimina una sección
   */
  deleteSeccion(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }
}
