<div class="container mx-auto p-4">
  <div *ngIf="loading" class="flex justify-center my-8">
    <mat-spinner></mat-spinner>
  </div>

  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded my-4"
  >
    Error al cargar los resultados de la encuesta. Por favor, intente
    nuevamente.
  </div>

  <div *ngIf="encuesta && estadisticas && !loading && !error">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
        Resultados: {{ encuesta.titulo }}
      </h1>

      <button
        mat-raised-button
        color="primary"
        [routerLink]="['/encuestas']"
        class="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2"
      >
        <mat-icon class="text-white">arrow_back</mat-icon>
        <span>Volver</span>
      </button>
    </div>

    <!-- Resumen de estadísticas -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <mat-card class="dark:bg-gray-800">
        <mat-card-content>
          <div class="flex flex-col items-center">
            <div class="text-4xl font-bold text-blue-600 dark:text-blue-400">
              {{ estadisticas.totalRespuestas || 0 }}
            </div>
            <div class="text-gray-600 dark:text-gray-300 mt-2">
              Total de respuestas
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="dark:bg-gray-800">
        <mat-card-content>
          <div class="flex flex-col items-center">
            <div class="text-4xl font-bold text-green-600 dark:text-green-400">
              {{ estadisticas.respuestasCompletadas || 0 }}
            </div>
            <div class="text-gray-600 dark:text-gray-300 mt-2">
              Respuestas completadas
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="dark:bg-gray-800">
        <mat-card-content>
          <div class="flex flex-col items-center">
            <div
              class="text-4xl font-bold text-purple-600 dark:text-purple-400"
            >
              {{ getPorcentajeCompletadas() | number : "1.0-0" }}%
            </div>
            <div class="text-gray-600 dark:text-gray-300 mt-2">
              Porcentaje de completitud
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Resultados por pregunta -->
    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">
      Resultados por pregunta
    </h2>

    <div
      *ngIf="encuesta.preguntas && encuesta.preguntas.length > 0"
      class="space-y-6"
    >
      <mat-card
        *ngFor="let pregunta of encuesta.preguntas; let i = index"
        class="dark:bg-gray-800"
      >
        <mat-card-header>
          <mat-card-title class="dark:text-white"
            >{{ i + 1 }}. {{ pregunta.enunciado }}</mat-card-title
          >
          <mat-card-subtitle class="dark:text-gray-300">
            Total de respuestas: {{ getTotalRespuestasPregunta(pregunta) }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content class="mt-4">
          <div [ngSwitch]="pregunta.tipo">
            <!-- Opciones múltiples o Selección múltiple -->
            <div *ngSwitchCase="tipoPregunta.OPCION_MULTIPLE" class="space-y-4">
              <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                <div class="flex justify-between mb-1">
                  <span class="dark:text-gray-300">{{ opcion.texto }}</span>
                  <span class="dark:text-gray-300"
                    >{{ getCantidadOpcion(pregunta, opcion.id) }} ({{
                      getPorcentajeOpcion(pregunta, opcion.id)
                        | number : "1.0-0"
                    }}%)</span
                  >
                </div>
                <div
                  class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5"
                >
                  <div
                    class="bg-blue-600 dark:bg-blue-500 h-2.5 rounded-full"
                    [style.width]="
                      getPorcentajeOpcion(pregunta, opcion.id) + '%'
                    "
                  ></div>
                </div>
              </div>
            </div>

            <div
              *ngSwitchCase="tipoPregunta.SELECCION_MULTIPLE"
              class="space-y-4"
            >
              <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                <div class="flex justify-between mb-1">
                  <span class="dark:text-gray-300">{{ opcion.texto }}</span>
                  <span class="dark:text-gray-300"
                    >{{ getCantidadOpcion(pregunta, opcion.id) }} ({{
                      getPorcentajeOpcion(pregunta, opcion.id)
                        | number : "1.0-0"
                    }}%)</span
                  >
                </div>
                <div
                  class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5"
                >
                  <div
                    class="bg-green-600 dark:bg-green-500 h-2.5 rounded-full"
                    [style.width]="
                      getPorcentajeOpcion(pregunta, opcion.id) + '%'
                    "
                  ></div>
                </div>
              </div>
            </div>

            <!-- Escala Likert -->
            <div *ngSwitchCase="tipoPregunta.ESCALA_LIKERT" class="space-y-4">
              <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                <div class="flex justify-between mb-1">
                  <span class="dark:text-gray-300">{{ opcion.texto }}</span>
                  <span class="dark:text-gray-300"
                    >{{ getCantidadOpcion(pregunta, opcion.id) }} ({{
                      getPorcentajeOpcion(pregunta, opcion.id)
                        | number : "1.0-0"
                    }}%)</span
                  >
                </div>
                <div
                  class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5"
                >
                  <div
                    class="bg-purple-600 dark:bg-purple-500 h-2.5 rounded-full"
                    [style.width]="
                      getPorcentajeOpcion(pregunta, opcion.id) + '%'
                    "
                  ></div>
                </div>
              </div>
            </div>

            <!-- Número -->
            <div
              *ngSwitchCase="tipoPregunta.NUMERO"
              class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg"
            >
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-800 dark:text-white">
                  {{ getPromedioNumerico(pregunta) | number : "1.0-2" }}
                </div>
                <div class="text-gray-600 dark:text-gray-300 mt-2">
                  Promedio de respuestas numéricas
                </div>
              </div>
            </div>

            <!-- Otros tipos -->
            <div
              *ngSwitchDefault
              class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-center text-gray-600 dark:text-gray-300"
            >
              No hay estadísticas disponibles para este tipo de pregunta
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div
      *ngIf="!encuesta.preguntas || encuesta.preguntas.length === 0"
      class="p-6 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
    >
      Esta encuesta no tiene preguntas.
    </div>
  </div>
</div>
