import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SeccionService } from '@app/services/seccion.service';
import { Seccion, SeccionCreateRequest, SeccionUpdateRequest } from '@app/models/backend/curso/seccion.model';
import { NotificationService } from '@app/services/notification/notification.service';

interface SeccionFormData {
  modulo: {
    id: number;
    titulo: string;
  };
  seccion?: Seccion; // Si se proporciona, estamos editando una sección existente
}

@Component({
  selector: 'app-seccion-form',
  templateUrl: './seccion-form.component.html',
  styleUrls: ['./seccion-form.component.scss']
})
export class SeccionFormComponent implements OnInit, OnDestroy {
  seccionForm: FormGroup;
  loading: boolean = false;
  isEditMode: boolean = false;
  title: string = 'Crear nueva sección';

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private seccionService: SeccionService,
    private notification: NotificationService,
    private dialogRef: MatDialogRef<SeccionFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SeccionFormData
  ) {
    this.seccionForm = this.fb.group({
      titulo: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', Validators.maxLength(500)],
      orden: [null]
    });
  }

  ngOnInit(): void {
    this.isEditMode = !!this.data.seccion;
    
    if (this.isEditMode) {
      this.title = 'Editar sección';
      this.seccionForm.patchValue({
        titulo: this.data.seccion?.titulo,
        descripcion: this.data.seccion?.descripcion,
        orden: this.data.seccion?.orden
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit(): void {
    if (this.seccionForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.isEditMode) {
      this.updateSeccion();
    } else {
      this.createSeccion();
    }
  }

  private createSeccion(): void {
    const seccionData: SeccionCreateRequest = {
      titulo: this.seccionForm.value.titulo,
      descripcion: this.seccionForm.value.descripcion,
      orden: this.seccionForm.value.orden,
      moduloId: this.data.modulo.id
    };

    this.seccionService.createSeccion(seccionData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.notification.success('Sección creada exitosamente');
            this.dialogRef.close(response.data);
          } else {
            this.notification.error(response.msg || 'Error al crear la sección');
          }
        },
        error: (error) => {
          this.loading = false;
          this.notification.error('Error al crear la sección. Por favor, inténtelo de nuevo.');
          console.error('Error al crear sección:', error);
        }
      });
  }

  private updateSeccion(): void {
    if (!this.data.seccion) {
      return;
    }

    const seccionData: SeccionUpdateRequest = {
      titulo: this.seccionForm.value.titulo,
      descripcion: this.seccionForm.value.descripcion,
      orden: this.seccionForm.value.orden
    };

    this.seccionService.updateSeccion(this.data.seccion.id, seccionData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.notification.success('Sección actualizada exitosamente');
            this.dialogRef.close(response.data);
          } else {
            this.notification.error(response.msg || 'Error al actualizar la sección');
          }
        },
        error: (error) => {
          this.loading = false;
          this.notification.error('Error al actualizar la sección. Por favor, inténtelo de nuevo.');
          console.error('Error al actualizar sección:', error);
        }
      });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
