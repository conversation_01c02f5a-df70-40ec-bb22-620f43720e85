// src/app/pages/faq/components/form-upload/form-upload.component.ts
import { Component, Output, EventEmitter } from '@angular/core';
import { FileFaq } from '@app/models/backend/faq/file-faq.model';

@Component({
  selector: 'app-form-upload',
  template: `
    <div class="file-upload-container">
      <input
        type="file"
        multiple
        id="file-upload"
        class="file-input"
        (change)="onFiles($event)"
        style="display: none;"
      />
      <label for="file-upload" class="btn btn-primary">
        <i class="mdi mdi-upload me-1"></i> Seleccionar archivos
      </label>
    </div>
  `,
  styles: [`
    .file-upload-container {
      display: flex;
      justify-content: center;
    }
    .file-input {
      display: none;
    }
    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      border-radius: 0;
    }
  `]
})
export class FormUploadComponent {
  @Output() emitFiles = new EventEmitter<FileFaq[]>();

  onFiles(e: Event) {
    const files = (e.target as HTMLInputElement).files;
    if (!files) { return; }
    const arr: FileFaq[] = Array.from(files).map(f => ({
      name: f.name,
      type: f.type,
      size: f.size,
      url : URL.createObjectURL(f),
      fileType: this.detectType(f.type),
    }));
    this.emitFiles.emit(arr);
  }

  private detectType(mime: string): FileFaq['fileType'] {
    if (mime.startsWith('image/'))      { return 'image'; }
    if (mime.startsWith('video/'))      { return 'video'; }
    if (mime.startsWith('audio/'))      { return 'audio'; }
    if (mime === 'application/pdf' ||
        mime.startsWith('application/')){ return 'document'; }
    return 'other';
  }
}
