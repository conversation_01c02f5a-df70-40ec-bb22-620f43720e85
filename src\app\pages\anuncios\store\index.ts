import * as fromList from './save/save.reducer';
import { SaveEffects } from './save/save.effects';
import { ActionReducerMap, createFeatureSelector } from '@ngrx/store';


export interface AnunciosState {
  list: fromList.ListState;
}

export const reducers : ActionReducerMap<AnunciosState> = {
  list: fromList.reducer
}

export const effects : any = [
  SaveEffects
]

export const getAnunciosState = createFeatureSelector<AnunciosState>('anuncios');




