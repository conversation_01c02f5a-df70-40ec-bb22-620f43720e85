
<div class="p-3 rounded bg-gray-100 dark:bg-gray-800 mt-3">
  {{ file.name }}

  <progress
    class="w-full"
    [value]="(percentage$ | async) || 0"
    max="100"
  ></progress>

  <div class="flex justify-between" *ngIf="snapshot$ | async as snapshot">
    <div>{{ snapshot.bytesTransferred | fileSize}} of {{ snapshot.totalBytes | fileSize }}</div>

    <div>
      <div *ngIf="downloadURL; then completed; else inProgress"></div>
      <ng-template #completed>
          <a class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300" [href]="downloadURL" target="_blank" rel="noopener">Descargar</a>
      </ng-template>
      <ng-template #inProgress>
        <button
          (click)="task.pause()"
          class="inline-block mx-1 my-0.5 px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
          [disabled]="!(snapshot?.state === 'running')"
        >
          Pausa
        </button>
        <button
          (click)="task.cancel()"
          class="inline-block mx-1 my-0.5 px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
          [disabled]="!(snapshot?.state === 'running')"
        >
          Cancelar
        </button>
        <button
          (click)="task.resume()"
          class="inline-block mx-1 my-0.5 px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
          [disabled]="!(snapshot?.state === 'paused')"
        >
          Continuar
        </button>
      </ng-template>
    </div>
  </div>
</div>
