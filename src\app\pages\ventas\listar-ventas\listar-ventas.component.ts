import { Component, OnInit, OnDestroy } from '@angular/core';
import { VentaService, VentaRequest, VentaPageResponse } from '@app/services/venta.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-listar-ventas',
  templateUrl: './listar-ventas.component.html',
  styleUrls: ['./listar-ventas.component.scss']
})
export class ListarVentasComponent implements OnInit, OnDestroy {
  ventas: VentaRequest[] = [];
  currentPage = 0;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;
  loading = false;
  
  filterForm: FormGroup;
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  searchTerm: string = '';

  constructor(
    private ventaService: VentaService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      searchTerm: ['']
    });
  }

  ngOnInit(): void {
    this.loadVentas(this.currentPage, this.pageSize);

    this.searchSubject
      .pipe(
        debounceTime(500),
        takeUntil(this.destroy$)
      )
      .subscribe(term => {
        this.searchTerm = term;
        this.currentPage = 0;
        this.loadVentas(this.currentPage, this.pageSize);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadVentas(page: number, size: number): void {
    this.loading = true;
    if (this.searchTerm) {
      this.ventaService.searchVentas(this.searchTerm, page, size)
        .subscribe(this.handleResponse.bind(this));
    } else {
      this.ventaService.getVentas(page, size)
        .subscribe(this.handleResponse.bind(this));
    }
  }

  private handleResponse(response: any): void {
    this.loading = false;
    if (response.rpta === 1) {
      this.ventas = response.data.ventas;
      this.currentPage = response.data.currentPage;
      this.totalItems = response.data.totalItems;
      this.totalPages = response.data.totalPages;
    } else {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: response.msg || 'Error al cargar las ventas',
        confirmButtonColor: '#d33'
      });
    }
  }

  onSearch(event: any): void {
    const term = event.target.value;
    this.searchSubject.next(term);
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.loadVentas(this.currentPage, this.pageSize);
    }
  }

  prevPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.loadVentas(this.currentPage, this.pageSize);
    }
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.loadVentas(this.currentPage, this.pageSize);
  }

  goToLastPage(): void {
    this.currentPage = this.totalPages - 1;
    this.loadVentas(this.currentPage, this.pageSize);
  }
}