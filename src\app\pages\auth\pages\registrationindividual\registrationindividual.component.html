<div
  class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden max-w-3xl mx-auto w-full"
>
  <!-- Título del diálogo -->
  <div
    class="bg-gray-100 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-4 px-6"
  >
    <h2
      class="text-xl font-bold text-center text-gray-800 dark:text-white uppercase tracking-wider"
    >
      Registrar Usuario
    </h2>
  </div>

  <!-- Contenido del diálogo -->
  <div class="flex-1 p-6 overflow-hidden">
    <form
      #f="ngForm"
      (ngSubmit)="registrarUsuario(f)"
      class="flex flex-col h-full"
    >
      <!-- Contenedor para los campos del formulario con scroll -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-y-auto pr-2 pb-4 max-h-[calc(70vh-120px)]"
      >
        <!-- Nombre -->
        <div class="flex flex-col space-y-1">
          <label
            for="nombre"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Nombre <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="nombre"
            ngModel
            name="nombre"
            required
            minlength="2"
            placeholder="Ingrese su nombre"
            autocomplete="off"
            #nombreInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <div class="flex justify-between">
            <p
              *ngIf="nombreInput.invalid && nombreInput.touched"
              class="text-xs text-red-500"
            >
              Nombre: mínimo 2 caracteres
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Mínimo 2 caracteres
            </p>
          </div>
        </div>

        <!-- Apellidos -->
        <div class="flex flex-col space-y-1">
          <label
            for="apellidos"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Apellidos <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="apellidos"
            ngModel
            name="apellidos"
            required
            minlength="2"
            placeholder="Ingrese sus apellidos"
            autocomplete="off"
            #apellidosInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <div class="flex justify-between">
            <p
              *ngIf="apellidosInput.invalid && apellidosInput.touched"
              class="text-xs text-red-500"
            >
              Apellidos: mínimo 2 caracteres
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Mínimo 2 caracteres
            </p>
          </div>
        </div>

        <!-- Username -->
        <div class="flex flex-col space-y-1">
          <label
            for="username"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Username <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="username"
            ngModel
            name="username"
            required
            minlength="3"
            placeholder="Ingrese su nombre de usuario"
            autocomplete="off"
            #usernameInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <div class="flex justify-between">
            <p
              *ngIf="usernameInput.invalid && usernameInput.touched"
              class="text-xs text-red-500"
            >
              Username: mínimo 3 caracteres
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Mínimo 3 caracteres
            </p>
          </div>
        </div>

        <!-- Sede -->
        <div class="flex flex-col space-y-1">
          <label
            for="sede"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Sede <span class="text-red-500">*</span></label
          >
          <select
            id="sede"
            [(ngModel)]="selectedSede"
            name="sede"
            required
            [disabled]="loading"
            #sedeInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option *ngIf="loading" value="">Cargando sedes...</option>
            <option *ngIf="!loading" value="" disabled>
              Seleccione una sede
            </option>
            <option *ngFor="let sede of sedes" [ngValue]="sede">
              {{ sede.nombre }} - {{ sede.ciudad }}
            </option>
          </select>
          <div class="flex justify-between">
            <p
              *ngIf="sedeInput.invalid && sedeInput.touched"
              class="text-xs text-red-500"
            >
              Sede: campo requerido
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Campo obligatorio
            </p>
          </div>
        </div>

        <!-- DNI -->
        <div class="flex flex-col space-y-1">
          <label
            for="dni"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >DNI <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="dni"
            ngModel
            name="dni"
            required
            minlength="8"
            maxlength="8"
            pattern="[0-9]{8}"
            placeholder="Ingrese su DNI (8 dígitos)"
            autocomplete="off"
            #dniInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <div class="flex justify-between">
            <p
              *ngIf="dniInput.invalid && dniInput.touched"
              class="text-xs text-red-500"
            >
              DNI: 8 dígitos numéricos
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              8 dígitos numéricos
            </p>
          </div>
        </div>

        <!-- Email -->
        <div class="flex flex-col space-y-1">
          <label
            for="email"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Email</label
          >
          <input
            type="email"
            id="email"
            ngModel
            name="email"
            placeholder="Ingrese su correo electrónico"
            autocomplete="off"
            #emailInput="ngModel"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <div class="flex justify-between">
            <p
              *ngIf="emailInput.invalid && emailInput.touched"
              class="text-xs text-red-500"
            >
              Email: formato inválido
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Campo opcional
            </p>
          </div>
        </div>

        <!-- Teléfono -->
        <div class="flex flex-col space-y-1">
          <label
            for="telefono"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Teléfono</label
          >
          <input
            type="text"
            id="telefono"
            ngModel
            name="telefono"
            placeholder="Ingrese su número de teléfono"
            autocomplete="off"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 text-right">
            Campo opcional
          </p>
        </div>

        <!-- Role -->
        <div class="flex flex-col space-y-1">
          <label
            for="role"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Role</label
          >
          <select
            id="role"
            ngModel
            name="role"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="ASESOR" selected>ASESOR</option>
            <option value="BACKOFFICE">BACKOFFICE</option>
            <option value="BACKOFFICETRAMITADOR">BACKOFFICE TRAMITADOR</option>
            <option value="BACKOFFICESEGUIMIENTO">
              BACKOFFICE SEGUIMIENTO
            </option>
            <option value="ADMIN">ADMIN</option>
            <option value="COORDINADOR">COORDINADOR</option>
            <option value="AUDITOR">AUDITOR</option>
            <option value="GERENCIA">GERENCIA</option>
            <option value="PSICOLOGO">PSICOLOGO</option>
          </select>
          <p class="text-xs text-gray-500 dark:text-gray-400 text-right">
            ASESOR por defecto
          </p>
        </div>

        <!-- Contenedor para Password y Confirmar Password -->
        <div
          class="col-span-1 md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4"
        >
          <!-- Password -->
          <div class="flex flex-col space-y-1">
            <label
              for="password"
              class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Password <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <input
                [type]="hidePassword ? 'password' : 'text'"
                id="password"
                ngModel
                name="password"
                required
                minlength="6"
                placeholder="Ingrese su contraseña"
                autocomplete="new-password"
                #passwordInput="ngModel"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
              />
              <button
                type="button"
                (click)="hidePassword = !hidePassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400"
              >
                <mat-icon>{{
                  hidePassword ? "visibility_off" : "visibility"
                }}</mat-icon>
              </button>
            </div>
            <div class="flex justify-between">
              <p
                *ngIf="passwordInput.invalid && passwordInput.touched"
                class="text-xs text-red-500"
              >
                Password: mínimo 6 caracteres
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Mínimo 6 caracteres
              </p>
            </div>
          </div>

          <!-- Confirmar Password -->
          <div class="flex flex-col space-y-1">
            <label
              for="passwordConfirme"
              class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Confirmar Password <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <input
                [type]="hidePassword ? 'password' : 'text'"
                id="passwordConfirme"
                ngModel
                name="passwordConfirme"
                required
                minlength="6"
                placeholder="Confirme su contraseña"
                autocomplete="new-password"
                #passwordConfirmeInput="ngModel"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
              />
              <button
                type="button"
                (click)="hidePassword = !hidePassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400"
              >
                <mat-icon>{{
                  hidePassword ? "visibility_off" : "visibility"
                }}</mat-icon>
              </button>
            </div>
            <p
              *ngIf="
                passwordConfirmeInput.touched &&
                passwordConfirmeInput.value !== passwordInput.value
              "
              class="text-xs text-red-500"
            >
              Las contraseñas no coinciden
            </p>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Acciones del diálogo -->
  <div
    class="bg-gray-50 dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3"
  >
    <button
      (click)="cancelar()"
      [disabled]="submitting"
      class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
    >
      Cancelar
    </button>
    <button
      (click)="f.ngSubmit.emit()"
      [disabled]="f.invalid || submitting"
      class="px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 flex items-center space-x-2"
    >
      <mat-icon>{{ submitting ? "hourglass_empty" : "save" }}</mat-icon>
      <span>{{ submitting ? "Guardando..." : "Guardar Usuario" }}</span>
    </button>
  </div>
</div>
