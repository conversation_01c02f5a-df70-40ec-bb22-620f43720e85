<div class="container-fluid">
  <div class="row mx-2">
    <div class="col-12">
      <div class="card shadow-sm">
        <!-- Nuevo header con Tailwind y la imagen de fondo -->
        <div
          class="relative h-32 bg-cover bg-center"
          style="background-image: url('assets/landscape.jpg')"
        >
          <div class="absolute inset-0 flex items-center justify-between px-6">
            <div class="flex items-center space-x-4">
              <!-- Botones de navegación de año -->
              <button
                class="text-white hover:text-gray-200"
                (click)="navigateCalendar('prevYear')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
              <!-- Botón de navegación anterior -->
              <button
                class="text-white hover:text-gray-200"
                (click)="navigateCalendar('prev')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
              <!-- Botón de hoy -->
              <button
                class="px-3 py-1 text-white bg-white/20 hover:bg-white/30 rounded-md"
                (click)="navigateCalendar('today')"
              >
                Hoy
              </button>
              <!-- Botón de navegación siguiente -->
              <button
                class="text-white hover:text-gray-200"
                (click)="navigateCalendar('next')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
              <!-- Botones de navegación de año -->
              <button
                class="text-white hover:text-gray-200"
                (click)="navigateCalendar('nextYear')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>

            <!-- Título del calendario (mes y año) -->
            <div class="text-white font-medium text-xl">{{ currentDate }}</div>

            <!-- Botones de vista -->
            <div class="flex bg-white/20 rounded-md">
              <button
                class="px-3 py-1 text-white hover:bg-white/30 rounded-l-md"
                [ngClass]="{ 'bg-white/30': currentView === 'dayGridMonth' }"
                (click)="changeView('dayGridMonth')"
              >
                Mes
              </button>
              <button
                class="px-3 py-1 text-white hover:bg-white/30"
                [ngClass]="{ 'bg-white/30': currentView === 'dayGridWeek' }"
                (click)="changeView('dayGridWeek')"
              >
                Semana
              </button>
              <button
                class="px-3 py-1 text-white hover:bg-white/30 rounded-r-md"
                [ngClass]="{ 'bg-white/30': currentView === 'dayGridDay' }"
                (click)="changeView('dayGridDay')"
              >
                Día
              </button>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="app-calendar">
            <!-- calendar - Eliminamos [events] para evitar doble inicialización -->
            <full-calendar
              #fullcalendar
              [options]="calendarOptions"
            ></full-calendar>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- El modal ahora se maneja a través de MatDialog -->
