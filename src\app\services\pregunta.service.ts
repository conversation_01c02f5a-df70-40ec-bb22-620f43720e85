import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend';
import { Pregunta, PreguntaCreateRequest, PreguntaUpdateRequest } from '@app/models/backend/curso/pregunta.model';

@Injectable({
  providedIn: 'root'
})
export class PreguntaService {
  private baseUrl = environment.url + 'api/preguntas';
  private preguntasSubject = new BehaviorSubject<Pregunta[]>([]);
  public preguntas$ = this.preguntasSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene todas las preguntas
   */
  getAllPreguntas(): Observable<GenericResponse<Pregunta[]>> {
    return this.http.get<GenericResponse<Pregunta[]>>(this.baseUrl).pipe(
      map(response => {
        if (response.data) {
          this.preguntasSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene las preguntas de un cuestionario
   */
  getPreguntasByCuestionarioId(cuestionarioId: number): Observable<GenericResponse<Pregunta[]>> {
    return this.http.get<GenericResponse<Pregunta[]>>(`${this.baseUrl}/cuestionario/${cuestionarioId}`).pipe(
      map(response => {
        if (response.data) {
          this.preguntasSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene una pregunta por su ID
   */
  getPreguntaById(id: number): Observable<GenericResponse<Pregunta>> {
    return this.http.get<GenericResponse<Pregunta>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea una nueva pregunta
   */
  createPregunta(pregunta: PreguntaCreateRequest): Observable<GenericResponse<Pregunta>> {
    return this.http.post<GenericResponse<Pregunta>>(this.baseUrl, pregunta);
  }

  /**
   * Actualiza una pregunta existente
   */
  updatePregunta(id: number, pregunta: PreguntaUpdateRequest): Observable<GenericResponse<Pregunta>> {
    return this.http.put<GenericResponse<Pregunta>>(`${this.baseUrl}/${id}`, pregunta);
  }

  /**
   * Elimina una pregunta
   */
  deletePregunta(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }
}
