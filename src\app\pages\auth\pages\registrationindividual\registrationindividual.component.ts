import { Component, OnInit, OnDestroy } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Store } from '@ngrx/store';
import * as fromUser from '@app/store/user';
import { UserCreate } from '@app/store/user/user.models';
import { SedeService } from '@app/services/sede.service';
import { Sede } from '@app/models/backend/sede/sede.model';
import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { UserWsService } from '@app/services/user-ws/user-ws.service';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-registrationindividual',
  templateUrl: './registrationindividual.component.html',
})
export class RegistrationIndividualComponent implements OnInit, OnDestroy {
  hidePassword = true;

  // Lista de sedes para el select
  sedes: Sede[] = [];

  // Sede seleccionada
  selectedSede: Sede | null = null;

  // Indicador de carga
  loading = false;

  // Indicador de envío del formulario
  submitting = false;

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private store: Store,
    private sedeService: SedeService,
    private userWsService: UserWsService,
    private webSocketService: WebSocketService,
    private dialogRef: MatDialogRef<RegistrationIndividualComponent>
  ) {}

  ngOnInit(): void {
    this.loadSedes();

    // Inicializar el servicio WebSocket si no está conectado
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();
    }

    // Inicializar el servicio de usuarios WebSocket
    this.userWsService.initialize();
  }

  ngOnDestroy(): void {
    // Desuscribirse para evitar memory leaks
    this.subscriptions.forEach((sub) => sub.unsubscribe());

    // Asegurarse de cerrar cualquier diálogo de SweetAlert abierto
    Swal.close();
  }

  // Método para cerrar el diálogo
  cancelar(): void {
    this.selectedSede = null; // Limpiar la sede seleccionada
    this.dialogRef.close(false);
  }

  // Cargar todas las sedes activas
  loadSedes(): void {
    this.loading = true;

    this.subscriptions.push(
      this.sedeService.getAllSedes().subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            // Filtrar sedes activas
            this.sedes = response.data.filter((sede) => sede.activo);

            // Ordenar las sedes alfabéticamente por nombre
            this.sedes.sort((a, b) => a.nombre.localeCompare(b.nombre));

            // Buscar la sede de Chiclayo para seleccionarla por defecto
            const chiclayoSede = this.sedes.find(sede =>
              sede.nombre.toUpperCase().includes('CHICLAYO') ||
              sede.ciudad.toUpperCase().includes('CHICLAYO')
            );

            if (chiclayoSede) {
              this.selectedSede = chiclayoSede;
            } else if (this.sedes.length > 0) {
              // Si no se encuentra Chiclayo, seleccionar la primera sede
              this.selectedSede = this.sedes[0];
            }
          } else {
            this.sedes = [];
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'No se pudieron cargar las sedes. ' + (response.msg || ''),
            });
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error al cargar sedes:', error);
          this.loading = false;
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'No se pudieron cargar las sedes. Por favor, intente nuevamente.',
          });
        },
      })
    );
  }

  registrarUsuario(form: NgForm): void {
    // Validación básica del formulario
    if (!form.valid) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Por favor, complete todos los campos obligatorios correctamente.',
      });
      return;
    }

    // Verificar que se haya seleccionado una sede
    if (!this.selectedSede) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Debe seleccionar una sede.',
      });
      return;
    }

    // Verificar que las contraseñas coincidan
    if (form.value.password !== form.value.passwordConfirme) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Las contraseñas no coinciden.',
      });
      return;
    }

    // Crear el objeto de usuario con los datos del formulario
    const user: UserCreate = {
      nombre: form.value.nombre,
      apellido: form.value.apellidos,
      username: form.value.username,
      password: form.value.password,
      sede_id: this.selectedSede?.id as number, // Usamos selectedSede en lugar de form.value.sede
      dni: form.value.dni,
      email: form.value.email || null,
      telefono: form.value.telefono || null,
      role: form.value.role || 'ASESOR',
      estado: 'A',
    };

    // Validación adicional para la sede
    if (!user.sede_id) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Debe seleccionar una sede válida.',
      });
      return;
    }

    // Limpiar los campos opcionales si están vacíos
    if (!user.telefono || user.telefono === 'null') {
      user.telefono = null;
    }
    if (!user.email || user.email === 'null') {
      user.email = null;
    }

    // Mostrar indicador de carga
    this.submitting = true;

    // Mostrar mensaje de procesamiento
    Swal.fire({
      title: 'Procesando...',
      text: 'Registrando usuario, por favor espere.',
      icon: 'info',
      allowOutsideClick: false,
      showConfirmButton: false,
      willOpen: () => {
        Swal.showLoading();
      },
    });

    // Suscribirse al estado para detectar éxito o error
    const registerSub = this.store
      .select((state) => state)
      .subscribe((state: any) => {
        if (state.user && state.user.error && this.submitting) {
          // Si hay un error, mostrar mensaje
          this.submitting = false;
          Swal.fire({
            title: 'Error',
            text: state.user.error || 'Error al registrar usuario',
            icon: 'error',
          });
        } else if (state.user && !state.user.loading && this.submitting) {
          // Si ya no está cargando y no hay error, asumir éxito
          this.submitting = false;
          Swal.fire({
            title: 'Éxito',
            text: 'Usuario registrado correctamente',
            icon: 'success',
            confirmButtonText: 'Aceptar'
          }).then((result) => {
            if (result.isConfirmed) {
              // Cerrar el diálogo y devolver true para indicar éxito
              this.dialogRef.close(true);
            }
          });
          form.reset(); // Limpiar los campos del formulario
          this.selectedSede = null; // Limpiar la sede seleccionada
        }
      });

    this.subscriptions.push(registerSub);

    // Usar el servicio WebSocket para crear el usuario
    // Esto internamente usará el store para la creación HTTP
    // y luego notificará a todos los clientes mediante WebSocket
    this.userWsService.createUser(user);
  }
}
