import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import * as fromList from '../../store/save';
import { select, Store } from '@ngrx/store';
import { User } from '@app/models/backend/user/index';
import { MatDialogRef } from '@angular/material/dialog';
import { SedeService } from '@app/services/sede.service';
import { Sede } from '@app/models/backend/sede/sede.model';
import { SedeUserService } from '@app/services/sede-user.service';

@Component({
  selector: 'app-anuncios-nuevo',
  templateUrl: './anuncios-nuevo.component.html',
  styles: []
})
export class AnunciosNuevoComponent implements OnInit, OnD<PERSON>roy {
  loading$ !: Observable<boolean | null>;
  photoLoaded!: string;
  user$ !: Observable<User | null>;
  private user: User | null = null;
  private destroy$ = new Subject<void>();

  // Categorías disponibles
  categorias: string[] = ['INTERNO', 'EXTERNO'];

  // Estados disponibles
  estados: string[] = ['ACTIVO', 'INACTIVO'];

  // Fechas por defecto
  fechaInicio: Date = new Date();
  fechaFin: Date = new Date(new Date().setDate(new Date().getDate() + 30));

  // Sedes disponibles
  sedes: Sede[] = [];
  selectedSedeId: number | null = null;
  userSedeId: number | null = null;

  constructor(
    private store: Store<fromRoot.State>,
    public dialogRef: MatDialogRef<AnunciosNuevoComponent>,
    private sedeService: SedeService,
    private sedeUserService: SedeUserService
  ) { }

  ngOnInit(): void {
    this.loading$ = this.store.pipe(select(fromList.getLoading));
    this.user$ = this.store.pipe(select(fromUser.getUser));

    this.user$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => this.user = user);

    // Obtener el ID de la sede del usuario
    this.userSedeId = this.sedeUserService.getSedeIdSync();
    this.selectedSedeId = this.userSedeId;

    // Cargar las sedes disponibles
    this.loadSedes();
  }

  /**
   * Carga las sedes disponibles desde el servicio
   */
  loadSedes(): void {
    this.sedeService.getSedesActivas().subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.sedes = response.data;
        }
      },
      error: (error) => {
        console.error('Error al cargar sedes:', error);
      }
    });
  }

  registrarAnuncio(form: NgForm): void {
      if (!form.valid) return;
      if (!this.user?.id) {
        console.error('No user ID available');
        return;
      }

      // Obtener fechas del formulario o usar las predeterminadas
      const fechaInicio = form.value.fechaInicio || this.fechaInicio;
      const fechaFin = form.value.fechaFin || this.fechaFin;

      // Obtener el ID de la sede seleccionada o usar la sede del usuario
      const sedeId = form.value.sedeId || this.selectedSedeId || this.userSedeId;

      const anuncioCreateRequest : fromList.AnuncioCreateRequest = {
          titulo: form.value.titulo,
          descripcion: form.value.descripcion,
          imagenUrl: this.photoLoaded,
          categoria: form.value.categoria,
          fechaInicio: fechaInicio.toISOString(),
          fechaFin: fechaFin.toISOString(),
          orden: form.value.orden || 0,
          estado: form.value.estado || 'ACTIVO',
          usuarioId: this.user.id,
          sedeId: sedeId
      }
      this.store.dispatch(new fromList.Create(anuncioCreateRequest));
  }

  onFilesChanged(url: any): void {
    if(url){
      this.photoLoaded = url;
    }
  }

  onClose(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
