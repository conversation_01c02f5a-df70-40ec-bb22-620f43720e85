import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FlexLayoutModule } from '@angular/flex-layout';

import { AnunciosRoutingModule } from './anuncios-routing.module';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';

import { reducers, effects } from './store';

// Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDialogModule } from '@angular/material/dialog';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';

// Shared Components
import { LayoutsModule } from '@app/shared/layouts/layouts.module';
import { FilesUploadModule } from '@app/shared/popups/files-upload/files-upload.module';

// Components
import { AnunciosListComponent } from './pages/anuncios-list/anuncios-list.component';
import { AnunciosNuevoComponent } from './pages/anuncios-nuevo/anuncios-nuevo.component';
import { AnunciosEditarComponent } from './pages/anuncios-editar/anuncios-editar.component';
import { ImageViewerComponent } from './components/image-viewer/image-viewer.component';
import { FilterPipe } from './pipes/filter.pipe';
import { SpinnerModule } from '@app/shared';
import { AnunciosRecientesComponent } from './components/anuncios-recientes/anuncios-recientes.component';
import { AnuncioFormDialogComponent } from './components/anuncio-form-dialog/anuncio-form-dialog.component';

@NgModule({
  declarations: [
    AnunciosListComponent,
    AnunciosNuevoComponent,
    AnunciosEditarComponent,
    ImageViewerComponent,
    FilterPipe,
    AnunciosRecientesComponent,
    AnuncioFormDialogComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    AnunciosRoutingModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatDialogModule,
    MatToolbarModule,
    MatPaginatorModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    SpinnerModule,
    LayoutsModule,
    FilesUploadModule,
    StoreModule.forFeature('anuncios', reducers),
    EffectsModule.forFeature(effects),
  ],
  exports: [
    AnunciosNuevoComponent,
    AnunciosRecientesComponent
  ]
})
export class AnunciosModule { }
