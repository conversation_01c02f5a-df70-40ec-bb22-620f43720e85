<div class="p-4 ">
  <div
    class="w-full bg-white dark:bg-gray-800 shadow-md rounded-2xl p-6 border border-gray-200 dark:border-gray-700 max-w-7xl mx-auto"
  >
    <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
      Listado de Contactos
    </h2>

    <!-- Filtros -->
    <div class="flex flex-col md:flex-row md:items-end gap-4 mb-6">
      <input
        type="text"
        placeholder="Buscar por nombre o correo"
        class="border dark:border-gray-600 px-4 py-2 rounded w-full md:w-1/2 dark:bg-gray-700 dark:text-white"
        [(ngModel)]="searchText"
        (input)="filtrarContactos()"
      />

      <!-- Fecha desde -->
      <input
        type="date"
        class="border dark:border-gray-600 px-4 py-2 rounded w-full md:w-1/4 dark:bg-gray-700 dark:text-white"
        [(ngModel)]="fechaDesde"
      />

      <!-- <PERSON><PERSON> hasta -->
      <input
        type="date"
        class="border dark:border-gray-600 px-4 py-2 rounded w-full md:w-1/4 dark:bg-gray-700 dark:text-white"
        [(ngModel)]="fechaHasta"
      />

      <button
        (click)="filtrarPorFecha()"
        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
      >
        Filtrar
      </button>

      <button
        (click)="exportarExcel()"
        class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
      >
        Excel
      </button>
    </div>

    <!-- Tabla -->
    <div
      class="overflow-auto rounded-xl border border-gray-200 dark:border-gray-700"
    >
      <table class="min-w-full bg-white dark:bg-gray-800 text-sm">
        <thead
          class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 font-semibold"
        >
          <tr>
            <th class="text-left p-3">Nombre</th>
            <th class="text-left p-3">Correo</th>
            <th class="text-left p-3">Mensaje</th>
            <th class="text-left p-3">Preferencia</th>
            <th class="text-left p-3">Procedencia</th>
            <th class="text-left p-3">Fecha</th>
            <th class="text-left p-3">Hora</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let contacto of contactosFiltrados"
            class="border-t dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <td class="p-3 dark:text-gray-200">{{ contacto.nombres }}</td>
            <td class="p-3 dark:text-gray-200">{{ contacto.correo }}</td>
            <td class="p-3 dark:text-gray-200">{{ contacto.mensaje }}</td>
            <td class="p-3 dark:text-gray-200">{{ contacto.preferencia }}</td>
            <td class="p-3 dark:text-gray-200">{{ contacto.procedencia }}</td>
            <td class="p-3 dark:text-gray-200">
              {{ contacto.fecha_registro | date : "yyyy-MM-dd" }}
            </td>
            <td class="p-3 dark:text-gray-200">{{ contacto.hora_registro }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
