
import {createSelector} from '@ngrx/store';
import {getAnunciosState, AnunciosState} from '../index';

import { ListState } from './save.reducer';

export const getListState = createSelector(
  getAnunciosState,
  (state: AnunciosState) => state.list
)

export const getLoading = createSelector(
  getListState,
  (state: ListState) => state.loading
)

export const getAnuncios = createSelector(
  getListState,
  (state: ListState) => state.anuncios
)

export const getCurrentPage = createSelector(
  getListState,
  (state: ListState) => state.currentPage
)

export const getTotalPages = createSelector(
  getListState,
  (state: ListState) => state.totalPages
)

export const getTotalElements = createSelector(
  getListState,
  (state: ListState) => state.totalElements
)

export const getPaginationInfo = createSelector(
  getListState,
  (state: ListState) => ({
    totalElements: state.totalElements,
    totalPages: state.totalPages,
    currentPage: state.currentPage
  })
)
