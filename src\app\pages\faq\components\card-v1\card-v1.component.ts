import { Component, EventEmitter, Input, OnChanges, Output, OnInit } from '@angular/core';
import { Faq } from '@app/models/backend/faq/faq.model';
import { FileFaq } from '@app/models/backend/faq/file-faq.model';
import { FaqRespuesta } from '@app/models/backend/faq/faq-respuesta.model';
import { User } from '@app/models/backend/user';
import { DateUtils } from '@app/utils/date-utils';

@Component({
  selector: 'app-card-v1',
  templateUrl: './card-v1.component.html',
  styleUrls: ['./card-v1.component.scss']
})
export class CardV1Component implements OnChanges, OnInit {
  /** Propiedades de entrada y salida */
  @Input() data!: Faq;
  @Input() searchText = '';
  @Input() showOption = false;
  @Output() optionClick = new EventEmitter<{ option: string; data: Faq }>();

  countedFileByType: Record<string, number> = {};
  filesFilter: FileFaq[] = [];
  activeFilter: string | null = null;

  // Variables para controlar el acceso a archivos y permisos
  currentUserRole: string = '';
  currentUserId: number | null = null;
  hasAttachedFiles: boolean = false;
  canViewFiles: boolean = false;
  isAdmin: boolean = false;
  canRespond: boolean = false; // Para ADMIN o PROGRAMADOR
  isCreator: boolean = false;  // Si el usuario actual es quien creó la pregunta
  isResponder: boolean = false; // Si el usuario actual es quien respondió la pregunta


  ngOnInit(): void {
    // Obtener el rol del usuario actual desde localStorage (una sola vez)
    this.getCurrentUserRole();
  }

  ngOnChanges(): void {
    // No necesitamos actualizar el rol del usuario en cada cambio
    this.buildCounters();
    this.checkFileAccess();

    // Verificar si el usuario actual es el creador de la pregunta
    this.checkIfUserIsCreator();

    // Verificar si el usuario actual es quien respondió la pregunta
    this.checkIfUserIsResponder();

    // Si no viene el estado, asumimos que está abierta
    if (!this.data.estado) {
      this.data.estado = 'ABIERTA';
    }
  }



  private buildCounters() {
    const counters: Record<string, number> = {};
    (this.data.archivos || []).forEach(f => {
      if (f.fileType) {
        counters[f.fileType] = (counters[f.fileType] || 0) + 1;
      }
    });
    this.countedFileByType = counters;
  }


  filterByType(type: string) {
    this.activeFilter = type;
    this.filesFilter = (this.data.archivos || []).filter(f => f.fileType === type);
  }

  changeOption(opt: string) {
    // No necesitamos verificar permisos aquí porque los botones
    // solo se muestran a usuarios con los permisos adecuados (controlado por *ngIf en el template)

    // Emitir el evento al componente padre
    this.optionClick.emit({ option: opt, data: this.data });
  }

  /** Métodos para verificar y obtener información de usuarios */
  isUserObject(user: any): boolean {
    return user && typeof user === 'object' && 'id' in user;
  }

  getUserCreatorName(): string {
    if (this.data.usuarioPregunta && this.isUserObject(this.data.usuarioPregunta)) {
      const user = this.data.usuarioPregunta as User;
      return `${user.nombre || ''} ${user.apellido || ''}`.trim();
    }
    return this.data.usuarioPreguntaNombre || '';
  }

  getUserCreatorRole(): string {
    if (this.data.usuarioPregunta && this.isUserObject(this.data.usuarioPregunta)) {
      const user = this.data.usuarioPregunta as User;
      return user.role || '';
    }
    // Si usuarioPregunta es una cadena (el rol), devolverla directamente
    if (this.data.usuarioPregunta && typeof this.data.usuarioPregunta === 'string') {
      return this.data.usuarioPregunta;
    }
    return '';
  }

  getUserResponderName(): string {
    if (this.data.usuarioRespuesta && this.isUserObject(this.data.usuarioRespuesta)) {
      const user = this.data.usuarioRespuesta as User;
      return `${user.nombre || ''} ${user.apellido || ''}`.trim();
    }
    return this.data.usuarioRespuestaNombre || '';
  }

  getUserResponderRole(): string {
    if (this.data.usuarioRespuesta && this.isUserObject(this.data.usuarioRespuesta)) {
      const user = this.data.usuarioRespuesta as User;
      return user.role || '';
    }
    // Si usuarioRespuesta es una cadena (el rol), devolverla directamente
    if (this.data.usuarioRespuesta && typeof this.data.usuarioRespuesta === 'string') {
      return this.data.usuarioRespuesta;
    }
    return '';
  }

  /**
   * Obtiene el rol del usuario actual desde localStorage (se ejecuta una sola vez)
   */
  getCurrentUserRole(): void {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        this.currentUserRole = user.role || '';
        this.currentUserId = user.id || null;
        this.isAdmin = this.currentUserRole === 'ADMIN';
        // Verificar si el usuario puede responder (ADMIN o PROGRAMADOR)
        this.canRespond = this.currentUserRole === 'ADMIN' || this.currentUserRole === 'PROGRAMADOR';

        // Verificar si el usuario actual es el creador de la pregunta
        this.checkIfUserIsCreator();

        // Verificar si el usuario actual es quien respondió la pregunta
        this.checkIfUserIsResponder();
      }
    } catch (error) {
      // Error al obtener el rol del usuario
    }
  }

  /**
   * Verifica si el usuario actual es quien creó la pregunta
   */
  checkIfUserIsCreator(): void {
    if (!this.currentUserId || !this.data) return;

    // Verificar si el usuario actual es el creador de la pregunta
    if (this.data.usuarioPreguntaId === this.currentUserId) {
      this.isCreator = true;
    } else if (this.data.usuarioPregunta && this.data.usuarioPregunta.id === this.currentUserId) {
      this.isCreator = true;
    } else {
      this.isCreator = false;
    }
  }

  /**
   * Verifica si el usuario actual es quien respondió la pregunta
   */
  checkIfUserIsResponder(): void {
    if (!this.currentUserId || !this.data) return;

    // Verificar si el usuario actual es quien respondió la pregunta
    if (this.data.usuarioRespuestaId === this.currentUserId) {
      this.isResponder = true;
    } else if (this.data.usuarioRespuesta && this.data.usuarioRespuesta.id === this.currentUserId) {
      this.isResponder = true;
    } else {
      this.isResponder = false;
    }
  }

  /**
   * Maneja el evento cuando se agrega una nueva respuesta
   */
  onRespuestaAgregada(respuesta: FaqRespuesta): void {
    // Ya no necesitamos actualizar manualmente el array de respuestas
    // porque se actualizará automáticamente a través de WebSocket

    // Simplemente notificamos al componente padre que se agregó una respuesta
    // para que pueda actualizar la lista de FAQs si es necesario
    this.optionClick.emit({ option: 'respuesta-agregada', data: this.data });
  }

  /**
   * Verifica si el usuario tiene acceso a los archivos
   */
  checkFileAccess(): void {
    // Verificar si hay archivos adjuntos
    this.hasAttachedFiles = Array.isArray(this.data.archivos) && this.data.archivos.length > 0;

    // Verificar si el usuario tiene permisos para ver los archivos
    // No necesitamos recalcular canViewFiles aquí porque ya se estableció en getCurrentUserRole()
    // pero lo mantenemos por si acaso
    this.canViewFiles = this.currentUserRole === 'ADMIN' || this.currentUserRole === 'PROGRAMADOR';
  }

  /**
   * Abre el primer archivo adjunto
   */
  openFirstAttachment(): void {
    if (this.hasAttachedFiles && this.data.archivos && this.data.archivos.length > 0) {
      const fileUrl = this.data.archivos[0].url;

      // Verificar que la URL sea válida
      if (fileUrl && fileUrl.trim() !== '') {
        this.showFileLink(fileUrl);
      }
    }
  }

  /** utils */
  showFileLink(url: string) {
    if (url && url.trim() !== '') {
      try {
        // Verificar si la URL comienza con http:// o https://
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
        }

        // Intentar abrir la URL en una nueva pestaña
        const newWindow = window.open(url, '_blank');

        // Verificar si la ventana se abrió correctamente
        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
          // Mostrar un mensaje al usuario
          alert('No se pudo abrir el archivo. Por favor, permita las ventanas emergentes para este sitio.');
        }
      } catch (error) {
        alert('Error al abrir el archivo. Por favor, inténtelo de nuevo.');
      }
    } else {
      alert('La URL del archivo no es válida.');
    }
  }

  showFileLightBox(ix: number) {
    if (this.filesFilter && this.filesFilter[ix] && this.filesFilter[ix].url) {
      this.showFileLink(this.filesFilter[ix].url);
    } else {
      alert('No se pudo obtener la URL del archivo');
    }
  }

  formatFileSize(size?: number) { return size ? (size / 1024).toFixed(1) + ' KB' : ''; }

  /**
   * Formatea una fecha que puede venir como array [año, mes, día, ...] o como string
   * @param dateValue Fecha a formatear
   * @returns Fecha formateada en formato dd/MM/yyyy HH:mm
   */
  getFormattedDate(dateValue: any): string {
    if (!dateValue) return '';

    try {
      // Si es un array (formato del backend)
      if (Array.isArray(dateValue)) {
        // Verificar que tenga al menos año, mes y día
        if (dateValue.length >= 3) {
          // Crear objeto Date
          const date = DateUtils.dateArrayToDate(dateValue);
          if (!date) return '';

          // Formatear la fecha
          const day = date.getDate().toString().padStart(2, '0');
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const year = date.getFullYear();

          // Si hay hora y minuto
          let hours = '00';
          let minutes = '00';
          if (dateValue.length >= 5) {
            hours = dateValue[3].toString().padStart(2, '0');
            minutes = dateValue[4].toString().padStart(2, '0');
          }

          return `${day}/${month}/${year} ${hours}:${minutes}`;
        }
        return '';
      }

      // Si ya es un string o Date, usar el objeto Date directamente
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return '';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${day}/${month}/${year} ${hours}:${minutes}`;
    } catch (error) {
      return '';
    }
  }
}
