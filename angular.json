{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"client-inmueble-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "outputHashing": "all", "optimization": false, "sourceMap": true, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "7.1mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "7mb", "maximumError": "7mb"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/leaflet/dist/images/", "output": "./assets/leaflet/"}], "styles": ["src/styles.scss", "node_modules/leaflet/dist/leaflet.css", "node_modules/mapbox-gl/dist/mapbox-gl.css"], "scripts": [], "allowedCommonJsDependencies": ["sockjs-client", "@stomp/stompjs", "buffer", "mapbox-gl"]}, "configurations": {"prod": {"optimization": true, "sourceMap": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "7.1mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "7mb", "maximumError": "7mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "client-inmueble-app:build", "port": 5200}, "configurations": {"prod": {"browserTarget": "client-inmueble-app:build:prod"}, "dev": {"browserTarget": "client-inmueble-app:build:dev"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "client-inmueble-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/leaflet/dist/images/", "output": "./assets/leaflet/"}], "styles": ["src/styles.scss", "node_modules/leaflet/dist/leaflet.css", "node_modules/mapbox-gl/dist/mapbox-gl.css"], "scripts": []}}}}}, "cli": {"analytics": false}}