import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'leads',
        loadChildren: () =>
          import(
            './pages/clienteresidencial/clienteResidencialComponent.module'
          ).then((m) => m.ClienteResidencialComponentModule),
        data: { animation: 'ClienteResidencialPage' },
      },
      {
        path: 'coordinador',
        loadChildren: () =>
          import('./pages/asignarcoordinar/asignarcoordinar.module').then(
            (m) => m.AsignarcoordinarModule
          ),
        data: { animation: 'CoordinadorPage' },
      },
      {
        path: 'auth',
        loadChildren: () =>
          import('./pages/auth/auth.module').then((m) => m.AuthModule),
        data: { animation: 'AuthPage' },
      },
      {
        path: 'static',
        loadChildren: () =>
          import('./pages/static/static.module').then((m) => m.StaticModule),
        data: { animation: 'StaticPage' },
      },
      {
        path: 'home',
        loadChildren: () =>
          import('./pages/home/<USER>').then((m) => m.HomeModule),
        data: { animation: 'HomePage' },
      },
      {
        path: 'ventas',
        loadChildren: () =>
          import('./pages/ventas/ventas.module').then((m) => m.VentasModule),
        data: { animation: 'VentasPage' },
      },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'static/welcome',
      },
      {
        path: 'anuncios',
        loadChildren: () =>
          import('./pages/anuncios/anuncios.module').then(
            (m) => m.AnunciosModule
          ),
        data: { animation: 'AnunciosPage' },
      },
      {
        path: 'calendar',
        loadChildren: () =>
          import('./pages/calendar/calendar.module').then(
            (m) => m.CalendarModule
          ),
        data: { animation: 'CalendarPage' },
      },
      {
        path: 'manual',
        loadChildren: () =>
          import('./pages/manual/manual.module').then((m) => m.ManualModule),
        data: { animation: 'ManualPage' },
      },
      {
        path: 'ipsPermitidas',
        loadChildren: () =>
          import('./pages/ip-permitidas/ip-permitidas.module').then(
            (m) => m.IpPermitidasModule
          ),
        canActivate: [AuthGuard],
        data: {
          animation: 'IpsPermitidasPage',
          roles: ['ADMIN'], // Asegura que solo ADMIN puede acceder
        },
      },
      {
        path: 'faq',
        loadChildren: () =>
          import('./pages/faq/FaqModule').then((m) => m.FaqModule),
        canActivate: [AuthGuard],
        data: { animation: 'FaqPage' },
      },
      {
        path: 'landing-contact',
        loadChildren: () =>
          import('./pages/landing-contact/landing-contact.module').then(
            (m) => m.LandingContactModule
          ),
        canActivate: [AuthGuard],
        data: { animation: 'LandingContactPage' },
      },
      {
        path: 'sede',
        loadChildren: () =>
          import('./pages/sede/sede.module').then((m) => m.SedeModule),
        canActivate: [AuthGuard],
        data: { animation: 'SedePage' },
      },
    ],
  },
  {
    path: 'landing-contact',
    loadChildren: () =>
      import('./pages/landing-contact/landing-contact.module').then(
        (m) => m.LandingContactModule
      ),
  },
  {
    path: 'coaching',
    loadChildren: () =>
      import('./pages/coaching/coaching.module').then(
        (m) => m.CoachingModule
      ),
  },
  {
    path: 'sede',
    loadChildren: () =>
      import('./pages/sede/sede.module').then((m) => m.SedeModule),
  },
  {
    path: 'cursos',
    loadChildren: () =>
      import('./pages/cursos/cursos.module').then((m) => m.CursosModule),
  },
  {
    path: 'postulacion',
    loadChildren: () =>
      import('./pages/postulacion/postulacion.module').then(
        (m) => m.PostulacionModule
      ),
  },
  {
    path: 'mapas',
    loadChildren: () =>
      import('./pages/mapas/mapas.module').then((m) => m.MapasModule),
    data: { animation: 'MapasPage' },
  },
  {
    path: 'encuestas',
    loadChildren: () =>
      import('./pages/encuestas/encuestas.module').then(
        (m) => m.EncuestasModule
      ),
    canActivate: [AuthGuard],
    data: { animation: 'EncuestasPage' },
  },
  {
    path: 'notificaciones',
    loadChildren: () =>
      import('./pages/notificaciones/notificaciones.module').then(
        (m) => m.NotificacionesModule
      ),
    canActivate: [AuthGuard],
    data: { animation: 'NotificacionesPage' },
  },
  {
    path: 'certificados',
    loadChildren: () =>
      import('./pages/certificados/certificados.module').then(
        (m) => m.CertificadosModule
      ),
    canActivate: [AuthGuard],
    data: {
      animation: 'CertificadosPage',
      roles: ['ADMIN'], // Solo administradores pueden acceder
    },
  },
  {
    path: '**',
    pathMatch: 'full',
    redirectTo: 'static/404',
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
