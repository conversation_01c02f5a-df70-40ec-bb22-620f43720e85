import {
  Component,
  OnInit,
  HostB<PERSON>ing,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import * as CoordinadorActions from '@app/pages/asignarcoordinar/store/save/save.actions';
import {
  getAllCoordinadores,
  getCoordinadorLoading,
  getCoordinadorError,
  selectPaginationMetadata,
  getAsesoresDisponibles,
} from '@app/pages/asignarcoordinar/store/save/save.selectors';
import {
  Observable,
  map,
  take,
  filter,
  Subject,
  debounceTime,
  takeUntil,
  combineLatest,
} from 'rxjs';
import { CoordinadorDTO } from '@app/models/backend/dto/coordinador.dto';
import { AsignacionAsesorDTO } from '@app/models/backend/dto/asignacion-asesor.dto';
import { AsesorDTO } from '@app/models/backend/dto/asesor.dto';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { SidenavStateService } from '@app/shared/services/sidenav-state.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import { Router, NavigationStart } from '@angular/router';
import Swal from 'sweetalert2';
import { ThemeService } from '@app/services/theme.service';

@Component({
  selector: 'app-asignarcoordinar-list',
  templateUrl: './asignarcoordinar-list.component.html',
  styleUrls: ['./asignarcoordinar-list.component.scss'],
})
export class AsignarcoordinarListComponent implements OnInit, OnDestroy {
  // Clase CSS condicional para ajustar los modales cuando el menú está abierto
  @HostBinding('class.sidenav-open') get sidenavOpen() {
    return this.sidenavStateService.getSidenavState();
  }
  coordinadores$!: Observable<CoordinadorDTO[]>;
  loading$!: Observable<boolean>;
  error$!: Observable<any>;
  totalItems = 0;
  currentPage = 0;
  pageSize = 10;

  // Almacenar los coordinadores originales para filtrado local
  private allCoordinadores: CoordinadorDTO[] = [];
  // Coordinadores filtrados para mostrar
  filteredCoordinadores: CoordinadorDTO[] = [];

  isDarkTheme = false;
  searchTerm: string = '';
  selectedCoordinatorId: number | null = null;
  isModalOpen = false;
  asesoresDisponibles$!: Observable<AsesorDTO[]>;
  filteredAsesores$!: Observable<AsesorDTO[]>;

  // Columnas a mostrar en la tabla
  displayedColumns: string[] = [
    'nombreCompleto',
    'dni',
    'sede',
    'asesores',
    'acciones',
  ];

  // Subject para la búsqueda en tiempo real
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Propiedades para el modal de detalle
  @ViewChild('asesoresDetallePaginator')
  asesoresDetallePaginator!: MatPaginator;
  @ViewChild('asesoresDisponiblesPaginator')
  asesoresDisponiblesPaginator!: MatPaginator;

  isDetalleAsesoresModalOpen = false;
  isAsignarAsesorModalOpen = false;
  selectedCoordinador: CoordinadorDTO | null = null;
  searchAsesor: string = '';
  searchAsesorDisponible: string = '';
  filteredAsesoresDetalle: AsesorDTO[] = [];
  paginatedAsesoresDetalle: AsesorDTO[] = [];
  // Almacenar todos los asesores disponibles originales para filtrado
  allAsesoresDisponibles: AsesorDTO[] = [];
  filteredAsesoresDisponibles: AsesorDTO[] = [];
  paginatedAsesoresDisponibles: AsesorDTO[] = [];
  loadingAsesoresDisponibles = false;

  // Variables para paginación del modal de detalle
  currentPageDetalle = 0;
  currentPageDisponibles = 0;
  pageSizeOptions = [5, 10, 20, 40];
  totalAsesoresDetalle = 0;
  totalAsesoresDisponibles = 0;

  constructor(
    private store: Store,
    private snackBar: MatSnackBar,
    public sidenavStateService: SidenavStateService,
    private http: HttpClient,
    private router: Router,
    public themeService: ThemeService
  ) {
    // Suscribirse a los eventos de navegación para cerrar modales
    this.router.events
      .pipe(
        takeUntil(this.destroy$),
        filter((event) => event instanceof NavigationStart)
      )
      .subscribe(() => {
        // Cerrar todos los modales abiertos cuando se navega a otra ruta
        this.closeAllModals();
      });
  }

  ngOnInit(): void {
    this.loadCoordinadores();
    this.checkDarkTheme();
    // Observar cambios en el tema
    this.observeThemeChanges();

    // Configurar la búsqueda en tiempo real
    this.searchSubject
      .pipe(
        debounceTime(300), // Esperar 300ms después de la última entrada
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.filterCoordinadores();
      });

    // Suscribirse a los coordinadores para almacenarlos localmente
    this.coordinadores$ = this.store.select(getAllCoordinadores);

    // Suscribirse para actualizar los datos locales
    this.coordinadores$
      .pipe(takeUntil(this.destroy$))
      .subscribe((coordinadores) => {
        this.allCoordinadores = coordinadores;
        this.filterCoordinadores(); // Aplicar filtro actual
      });

    // Suscribirse a los errores para mostrarlos
    this.store
      .select(getCoordinadorError)
      .pipe(takeUntil(this.destroy$))
      .subscribe((error) => {
        if (error) {
          //console.error('Error en la aplicación:', error);
        }
      });

    // Suscribirse a los metadatos de paginación
    this.store
      .select(selectPaginationMetadata)
      .pipe(takeUntil(this.destroy$))
      .subscribe((metadata) => {
        this.totalItems = metadata.totalItems;
        this.currentPage = metadata.currentPage;
        this.pageSize = metadata.size;
      });

    // Suscribirse al estado de carga
    this.loading$ = this.store.select(getCoordinadorLoading);
  }

  ngOnDestroy(): void {
    // Completar todos los subjects para evitar memory leaks
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    // Suscribirse al observable del servicio de tema
    this.themeService.darkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isDarkMode) => {
        this.isDarkTheme = isDarkMode;

        // Aplicar la clase 'dark' al elemento HTML para que Tailwind detecte el modo oscuro
        if (isDarkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      });
  }

  /**
   * Observa cambios en el tema
   */
  observeThemeChanges(): void {
    // Ya no es necesario usar MutationObserver porque estamos usando el servicio de tema
    // La suscripción en checkDarkTheme() ya maneja los cambios
  }

  loadCoordinadores(): void {
    // Despachar la acción para cargar los coordinadores paginados
    this.store.dispatch(
      CoordinadorActions.loadCoordinadoresPaginados({
        page: this.currentPage,
        size: this.pageSize,
      })
    );
  }

  /**
   * Busca coordinadores según el término de búsqueda
   */
  buscarCoordinadores(): void {
    this.searchSubject.next(this.searchTerm);
  }

  /**
   * Limpia el término de búsqueda y recarga los coordinadores
   */
  limpiarBusqueda(): void {
    this.searchTerm = '';
    this.searchSubject.next('');
  }

  /**
   * Filtra los coordinadores localmente según el término de búsqueda
   */
  filterCoordinadores(): void {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredCoordinadores = [...this.allCoordinadores];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();

    // Dividir el término de búsqueda en palabras para buscar por nombre completo
    const searchTerms = searchTermLower
      .split(/\s+/)
      .filter((term) => term.length > 0);

    this.filteredCoordinadores = this.allCoordinadores.filter((coordinador) => {
      // Crear un nombre completo para búsqueda
      const nombreCompleto = `${coordinador.nombre || ''} ${
        coordinador.apellido || ''
      }`.toLowerCase();

      // Verificar si todas las palabras del término de búsqueda están en el nombre completo
      const matchesNombreCompleto =
        searchTerms.length > 1 &&
        searchTerms.every((term) => nombreCompleto.includes(term));

      // Buscar en todos los campos relevantes
      return (
        // Nombre completo (coincidencia de todas las palabras)
        matchesNombreCompleto ||
        // Nombre
        (coordinador.nombre &&
          coordinador.nombre.toLowerCase().includes(searchTermLower)) ||
        // Apellido
        (coordinador.apellido &&
          coordinador.apellido.toLowerCase().includes(searchTermLower)) ||
        // DNI
        (coordinador.dni &&
          coordinador.dni.toLowerCase().includes(searchTermLower)) ||
        // Email
        (coordinador.email &&
          coordinador.email.toLowerCase().includes(searchTermLower)) ||
        // Sede
        (coordinador.sede &&
          coordinador.sede.toLowerCase().includes(searchTermLower))
      );
    });
  }

  asignarAsesores(asignacion: AsignacionAsesorDTO): void {
    this.store.dispatch(CoordinadorActions.asignarAsesores({ asignacion }));
  }

  openModal(coordinatorId: number): void {
    // Cargar los datos del coordinador seleccionado
    this.coordinadores$
      .pipe(
        take(1),
        map((coordinadores) =>
          coordinadores.find((c) => c.id === coordinatorId)
        )
      )
      .subscribe({
        next: (coordinador) => {
          if (coordinador) {
            // Crear una copia profunda del coordinador para evitar referencias
            const coordinadorCopia = JSON.parse(JSON.stringify(coordinador));
            this.selectedCoordinador = coordinadorCopia;

            // Activar el indicador de carga
            this.loadingAsesoresDisponibles = true;

            // Abrir el modal primero para mostrar el indicador de carga
            this.isAsignarAsesorModalOpen = true;

            // El tema se maneja automáticamente a través de la suscripción en checkDarkTheme()

            // Cargar asesores disponibles directamente desde la API
            this.http
              .get<AsesorDTO[]>(
                `${environment.url}api/coordinadores/asesores-disponibles`
              )
              .pipe(take(1))
              .subscribe({
                next: (asesores: AsesorDTO[]) => {
                  // Desactivar el indicador de carga
                  this.loadingAsesoresDisponibles = false;

                  if (!asesores || asesores.length === 0) {
                    // No hay asesores disponibles
                  }

                  this.filteredAsesoresDisponibles = asesores
                    ? [...asesores]
                    : [];
                  this.totalAsesoresDisponibles =
                    this.filteredAsesoresDisponibles.length;
                  this.currentPageDisponibles = 0;

                  // Aplicar paginación
                  this.applyPaginationDisponibles();
                },
                error: (error) => {
                  // En caso de error, desactivar el indicador de carga
                  this.loadingAsesoresDisponibles = false;
                  console.error('Error al cargar asesores disponibles:', error);

                  // Mostrar mensaje de error
                  this.snackBar.open(
                    'Error al cargar asesores disponibles',
                    'Cerrar',
                    {
                      duration: 3000,
                      horizontalPosition: 'end',
                      verticalPosition: 'top',
                      panelClass: ['error-snackbar'],
                    }
                  );
                },
              });
          }
        },
        error: (error) => {
          console.error('Error al cargar el coordinador:', error);

          // Mostrar mensaje de error
          this.snackBar.open('Error al cargar el coordinador', 'Cerrar', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['error-snackbar'],
          });
        },
      });
  }

  closeAsignarAsesorModal(): void {
    this.isAsignarAsesorModalOpen = false;
    this.selectedCoordinador = null;
    this.searchAsesorDisponible = '';
    this.currentPageDisponibles = 0;
    this.totalAsesoresDisponibles = 0;
    this.filteredAsesoresDisponibles = [];
    this.paginatedAsesoresDisponibles = [];
    // No recargamos los coordinadores automáticamente para evitar el loading innecesario
  }

  /**
   * Finaliza la asignación de asesores, cierra el modal y actualiza la lista de coordinadores
   */
  finalizarAsignacion(): void {
    // Cerrar el modal
    this.isAsignarAsesorModalOpen = false;
    this.selectedCoordinador = null;
    this.searchAsesorDisponible = '';
    this.currentPageDisponibles = 0;
    this.totalAsesoresDisponibles = 0;
    this.filteredAsesoresDisponibles = [];
    this.paginatedAsesoresDisponibles = [];

    // Actualizar la lista de coordinadores
    this.actualizarListaCoordinadores();
  }

  /**
   * Actualiza la lista de coordinadores con un indicador de carga discreto
   */
  actualizarListaCoordinadores(): void {
    // Mostrar un indicador de carga discreto
    const loadingSnackBarRef = this.snackBar.open('Actualizando lista...', '', {
      duration: 0,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['mini-snackbar'],
    });

    // Recargar la lista de coordinadores
    this.loadCoordinadores();

    // Esperar a que termine la carga
    this.store
      .select(getCoordinadorLoading)
      .pipe(
        filter((loading) => !loading),
        take(1)
      )
      .subscribe(() => {
        // Cerrar el indicador de carga
        loadingSnackBarRef.dismiss();
      });
  }

  filterAsesoresDisponibles(): void {
    // Si no hay asesores disponibles cargados, no hacemos nada
    if (!this.filteredAsesoresDisponibles) return;

    // Si no hay término de búsqueda, mostrar todos los asesores disponibles
    if (
      !this.searchAsesorDisponible ||
      this.searchAsesorDisponible.trim() === ''
    ) {
      // Cargar asesores disponibles directamente desde la API
      this.loadingAsesoresDisponibles = true;
      this.http
        .get<AsesorDTO[]>(
          `${environment.url}api/coordinadores/asesores-disponibles`
        )
        .pipe(take(1))
        .subscribe({
          next: (asesores: AsesorDTO[]) => {
            this.loadingAsesoresDisponibles = false;
            this.filteredAsesoresDisponibles = asesores || [];
            this.totalAsesoresDisponibles =
              this.filteredAsesoresDisponibles.length;
            this.resetPaginationDisponibles();
          },
          error: (error) => {
            this.loadingAsesoresDisponibles = false;
            console.error('Error al cargar asesores disponibles:', error);
          },
        });
      return;
    }

    const searchTerm = this.searchAsesorDisponible.toLowerCase().trim();

    // Realizar la búsqueda directamente desde el backend para asegurar resultados actualizados
    this.loadingAsesoresDisponibles = true;
    this.http
      .get<AsesorDTO[]>(
        `${environment.url}api/coordinadores/asesores-disponibles`
      )
      .pipe(take(1))
      .subscribe({
        next: (asesores: AsesorDTO[]) => {
          this.loadingAsesoresDisponibles = false;

          // Dividir el término de búsqueda en palabras para buscar por nombre completo
          const searchTerms = searchTerm
            .split(/\s+/)
            .filter((term) => term.length > 0);

          // Filtrar los asesores según el término de búsqueda
          this.filteredAsesoresDisponibles = asesores.filter((asesor) => {
            // Crear un nombre completo para búsqueda
            const nombreCompleto = `${asesor.nombre || ''} ${
              asesor.apellido || ''
            }`.toLowerCase();

            // Verificar si todas las palabras del término de búsqueda están en el nombre completo
            const matchesNombreCompleto =
              searchTerms.length > 1 &&
              searchTerms.every((term) => nombreCompleto.includes(term));

            return (
              // Nombre completo (coincidencia de todas las palabras)
              matchesNombreCompleto ||
              // Nombre
              (asesor.nombre &&
                asesor.nombre.toLowerCase().includes(searchTerm)) ||
              // Apellido
              (asesor.apellido &&
                asesor.apellido.toLowerCase().includes(searchTerm)) ||
              // DNI
              (asesor.dni && asesor.dni.toLowerCase().includes(searchTerm)) ||
              // Email
              (asesor.email && asesor.email.toLowerCase().includes(searchTerm))
            );
          });

          // Actualizar el contador total para la paginación
          this.totalAsesoresDisponibles =
            this.filteredAsesoresDisponibles.length;
          this.resetPaginationDisponibles();
        },
        error: (error) => {
          this.loadingAsesoresDisponibles = false;
          console.error(
            'Error al cargar asesores disponibles para filtrar:',
            error
          );
        },
      });
  }

  resetFilterAsesoresDisponibles(): void {
    // Limpiar el término de búsqueda
    this.searchAsesorDisponible = '';

    // Llamar a la función de filtrado sin término de búsqueda
    // para que cargue todos los asesores disponibles
    this.filterAsesoresDisponibles();
  }

  // Método para aplicar paginación a los resultados de asesores disponibles
  applyPaginationDisponibles(): void {
    const startIndex = this.currentPageDisponibles * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedAsesoresDisponibles = this.filteredAsesoresDisponibles.slice(
      startIndex,
      endIndex
    );
  }

  // Método para manejar eventos de paginación en el modal de asesores disponibles
  onPageChangeDisponibles(event: PageEvent): void {
    this.currentPageDisponibles = event.pageIndex;
    this.pageSize = event.pageSize;
    this.applyPaginationDisponibles();
  }

  // Método para manejar el cambio de tamaño de página
  onPageSizeChange(size: string | number): void {
    // Convertir el valor a número si viene como string
    const numericSize = typeof size === 'string' ? parseInt(size, 10) : size;

    if (isNaN(numericSize) || numericSize <= 0) {
      console.error(`Tamaño de página inválido: ${size}`);
      return;
    }

    console.log(`Cambiando tamaño de página a ${numericSize}`);

    // Actualizar el tamaño de página
    this.pageSize = numericSize;

    // Determinar el contexto en el que se está cambiando el tamaño de página
    if (this.isDetalleAsesoresModalOpen) {
      // Si estamos en el modal de detalle de asesores
      this.currentPageDetalle = 0; // Volver a la primera página
      this.applyPaginationDetalle();
    } else if (this.isAsignarAsesorModalOpen) {
      // Si estamos en el modal de asignar asesores
      this.currentPageDisponibles = 0; // Volver a la primera página
      this.applyPaginationDisponibles();
    } else {
      // Si estamos en la lista principal de supervisores
      this.currentPage = 0; // Volver a la primera página

      // Despachar la acción para cargar los datos paginados con el nuevo tamaño
      this.store.dispatch(
        CoordinadorActions.loadCoordinadoresPaginados({
          page: this.currentPage,
          size: this.pageSize,
        })
      );
    }
  }

  // Exponer Math para usar en la plantilla
  get Math() {
    return Math;
  }

  // Método para resetear la paginación del modal de asesores disponibles
  resetPaginationDisponibles(): void {
    this.currentPageDisponibles = 0;
    if (this.asesoresDisponiblesPaginator) {
      this.asesoresDisponiblesPaginator.pageIndex = 0;
    }
    this.applyPaginationDisponibles();
  }

  /**
   * Cierra todos los modales abiertos
   * Este método se llama cuando se navega a otra ruta
   */
  closeAllModals(): void {
    // Cerrar el modal de asignar asesores si está abierto
    if (this.isAsignarAsesorModalOpen) {
      this.closeAsignarAsesorModal();
    }

    // Cerrar el modal de detalle de asesores si está abierto
    if (this.isDetalleAsesoresModalOpen) {
      this.closeDetalleAsesoresModal();
    }
  }

  /**
   * Cierra el modal de detalle de asesores
   */
  closeDetalleAsesoresModal(): void {
    this.isDetalleAsesoresModalOpen = false;
    this.selectedCoordinador = null;
    this.searchAsesor = '';
    this.currentPageDetalle = 0;
    this.totalAsesoresDetalle = 0;
    this.filteredAsesoresDetalle = [];
    this.paginatedAsesoresDetalle = [];

    // Asegurarse de que el paginador se resetee
    if (this.asesoresDetallePaginator) {
      this.asesoresDetallePaginator.pageIndex = 0;
    }
  }

  // Método para asignar un asesor al coordinador seleccionado
  asignarAsesor(asesorId: number): void {
    if (!this.selectedCoordinador?.id) return;

    // Mostrar un indicador de carga solo en el botón, no en toda la pantalla
    const loadingSnackBarRef = this.snackBar.open('Asignando asesor...', '', {
      duration: 0, // No se cierra automáticamente
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['mini-snackbar'], // Clase para un snackbar más pequeño y discreto
    });

    // Llamar directamente a la API para asignar el asesor
    this.http
      .post(`${environment.url}api/coordinadores/asignar-asesores`, {
        coordinadorId: this.selectedCoordinador.id,
        asesorIds: [asesorId],
      })
      .pipe(take(1))
      .subscribe({
        next: () => {
          // Cerrar el indicador de carga
          loadingSnackBarRef.dismiss();

          // Mostrar mensaje de éxito
          this.snackBar.open('✅ Asesor asignado exitosamente', 'Cerrar', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['success-snackbar'],
          });

          // Activar el indicador de carga para los asesores disponibles
          this.loadingAsesoresDisponibles = true;

          // Cargar asesores disponibles directamente desde la API
          this.http
            .get<AsesorDTO[]>(
              `${environment.url}api/coordinadores/asesores-disponibles`
            )
            .pipe(take(1))
            .subscribe({
              next: (asesores: AsesorDTO[]) => {
                // Desactivar el indicador de carga
                this.loadingAsesoresDisponibles = false;

                // Asesores disponibles actualizados

                this.filteredAsesoresDisponibles = asesores
                  ? [...asesores]
                  : [];
                this.totalAsesoresDisponibles =
                  this.filteredAsesoresDisponibles.length;
                this.applyPaginationDisponibles();
              },
              error: (error) => {
                // En caso de error, desactivar el indicador de carga
                this.loadingAsesoresDisponibles = false;
                console.error('Error al cargar asesores disponibles:', error);
              },
            });
        },
        error: (error) => {
          // Cerrar el indicador de carga
          loadingSnackBarRef.dismiss();

          console.error('Error al asignar asesor:', error);

          // Mostrar mensaje de error
          this.snackBar.open('❌ Error al asignar asesor', 'Cerrar', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['error-snackbar'],
          });
        },
      });
  }

  verDetalleAsesores(coordinador: CoordinadorDTO): void {
    // Crear una copia profunda del coordinador para evitar referencias
    const coordinadorCopia = JSON.parse(JSON.stringify(coordinador));
    this.selectedCoordinador = coordinadorCopia;
    this.filteredAsesoresDetalle = coordinadorCopia.asesores
      ? [...coordinadorCopia.asesores]
      : [];
    this.totalAsesoresDetalle = this.filteredAsesoresDetalle.length;
    this.currentPageDetalle = 0;

    // Abrir el modal
    this.isDetalleAsesoresModalOpen = true;

    // Aplicar paginación
    this.applyPaginationDetalle();
  }

  resetFilterAsesoresDetalle(): void {
    this.searchAsesor = '';
    if (this.selectedCoordinador?.asesores) {
      this.filteredAsesoresDetalle = [...this.selectedCoordinador.asesores];
      this.totalAsesoresDetalle = this.filteredAsesoresDetalle.length;
      this.resetPaginationDetalle();
    }
  }

  filterAsesoresDetalle(): void {
    if (!this.selectedCoordinador?.asesores) return;

    const searchTerm = this.searchAsesor.toLowerCase().trim();

    // Dividir el término de búsqueda en palabras para buscar por nombre completo
    const searchTerms = searchTerm
      .split(/\s+/)
      .filter((term) => term.length > 0);

    this.filteredAsesoresDetalle = this.selectedCoordinador.asesores.filter(
      (asesor) => {
        // Crear un nombre completo para búsqueda
        const nombreCompleto = `${asesor.nombre || ''} ${
          asesor.apellido || ''
        }`.toLowerCase();

        // Verificar si todas las palabras del término de búsqueda están en el nombre completo
        const matchesNombreCompleto =
          searchTerms.length > 1 &&
          searchTerms.every((term) => nombreCompleto.includes(term));

        return (
          // Nombre completo (coincidencia de todas las palabras)
          matchesNombreCompleto ||
          // Nombre
          asesor.nombre.toLowerCase().includes(searchTerm) ||
          // Apellido
          asesor.apellido.toLowerCase().includes(searchTerm) ||
          // DNI
          asesor.dni.toLowerCase().includes(searchTerm) ||
          // Email
          asesor.email.toLowerCase().includes(searchTerm)
        );
      }
    );

    // Actualizar el contador total para la paginación
    this.totalAsesoresDetalle = this.filteredAsesoresDetalle.length;
    this.resetPaginationDetalle();
  }

  removerAsesor(asesorId: number): void {
    if (!this.selectedCoordinador?.id) return;

    // Obtener el nombre del asesor para mostrar en el diálogo
    const asesor = this.selectedCoordinador.asesores.find(
      (a) => a.id === asesorId
    );
    if (!asesor) return;

    // Mostrar SweetAlert2
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas remover al asesor ${asesor.nombre} ${asesor.apellido}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, remover',
      cancelButtonText: 'Cancelar',
      allowOutsideClick: false,
      allowEscapeKey: false,
      allowEnterKey: false,
    }).then((result) => {
      if (result.isConfirmed && this.selectedCoordinador) {
        // Mostrar indicador de carga discreto
        const loadingSnackBarRef = this.snackBar.open(
          'Removiendo asesor...',
          '',
          {
            duration: 0, // No se cierra automáticamente
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['mini-snackbar'], // Clase para un snackbar más pequeño y discreto
          }
        );

        // Dispatch de la acción para remover el asesor
        this.store.dispatch(
          CoordinadorActions.removerAsesor({
            coordinadorId: this.selectedCoordinador.id,
            asesorId,
          })
        );

        // Combinar los observables de loading y error para detectar cuando termina la operación
        combineLatest([
          this.store.select(getCoordinadorLoading),
          this.store.select(getCoordinadorError),
        ])
          .pipe(
            // Esperar hasta que loading sea false (operación completada)
            filter(([loading, _]) => !loading),
            // Tomar solo el primer valor después de que loading sea false
            take(1)
          )
          .subscribe(([_, error]) => {
            // Cerrar el snackbar de carga
            loadingSnackBarRef.dismiss();

            if (error) {
              // Si hay un error, mostrar mensaje de error pero NO cerrar el modal
              this.snackBar.open(
                `❌ Error: ${error.message || 'No se pudo remover el asesor'}`,
                'Cerrar',
                {
                  duration: 5000,
                  horizontalPosition: 'end',
                  verticalPosition: 'top',
                  panelClass: ['error-snackbar'],
                }
              );
              return;
            }

            // Si no hay error, actualizar la vista local
            if (this.selectedCoordinador?.asesores) {
              this.selectedCoordinador.asesores =
                this.selectedCoordinador.asesores.filter(
                  (a) => a.id !== asesorId
                );
              this.filteredAsesoresDetalle = [
                ...this.selectedCoordinador.asesores,
              ];
            }

            // Mostrar mensaje de éxito
            this.snackBar.open('✅ Asesor removido exitosamente', 'Cerrar', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
              panelClass: ['success-snackbar'],
            });

            // No recargamos automáticamente la lista de coordinadores para evitar el loading innecesario

            // Si no quedan asesores, cerrar el modal
            if (this.selectedCoordinador?.asesores.length === 0) {
              this.closeDetalleAsesoresModal();
            }
          });
      }
    });
  }

  onPageChange(event: PageEvent): void {
    // Actualizar las variables locales
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;

    // Despachar la acción para cargar los datos paginados
    this.store.dispatch(
      CoordinadorActions.loadCoordinadoresPaginados({
        page: this.currentPage,
        size: this.pageSize,
      })
    );
  }

  // Método para aplicar paginación a los resultados del modal de detalle
  applyPaginationDetalle(): void {
    const startIndex = this.currentPageDetalle * this.pageSize;
    this.paginatedAsesoresDetalle = this.filteredAsesoresDetalle.slice(
      startIndex,
      startIndex + this.pageSize
    );
  }

  // Método para manejar eventos de paginación en el modal de detalle
  onPageChangeDetalle(event: PageEvent): void {
    this.currentPageDetalle = event.pageIndex;
    this.pageSize = event.pageSize;
    this.applyPaginationDetalle();
  }

  // Método para resetear la paginación del modal de detalle
  resetPaginationDetalle(): void {
    this.currentPageDetalle = 0;
    if (this.asesoresDetallePaginator) {
      this.asesoresDetallePaginator.pageIndex = 0;
    }
    this.applyPaginationDetalle();
  }

  /**
   * Genera un color de fondo para el avatar basado en el DNI
   * @param dni DNI del asesor
   * @returns Color en formato hexadecimal
   */
  getAvatarColor(dni: string): string {
    // Usar el DNI como semilla para generar un color
    const hash = dni.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    // Convertir a un color HSL con buena saturación y luminosidad
    const h = Math.abs(hash) % 360;
    return `hsl(${h}, 70%, 60%)`;
  }

  /**
   * Obtiene las iniciales del nombre y apellido
   * @param nombre Nombre del asesor
   * @param apellido Apellido del asesor
   * @returns Iniciales en mayúsculas
   */
  getInitials(nombre: string, apellido: string): string {
    const nombreInicial =
      nombre && nombre.length > 0 ? nombre.charAt(0).toUpperCase() : '';
    const apellidoInicial =
      apellido && apellido.length > 0 ? apellido.charAt(0).toUpperCase() : '';
    return nombreInicial + apellidoInicial;
  }
}
