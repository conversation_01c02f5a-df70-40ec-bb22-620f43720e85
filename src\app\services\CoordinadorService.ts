import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CoordinadorService {
  private coordinadorSubject = new BehaviorSubject<any | null>(null);
  coordinador$ = this.coordinadorSubject.asObservable();

  constructor() {
    // Si ya hubiera un coordinador en localStorage,
    // lo cargamos de inicio para que no se pierda al recargar la app
    const stored = localStorage.getItem('coordinador');
    if (stored) {
      this.coordinadorSubject.next(JSON.parse(stored));
    }
  }

  /**
   * Seteamos un nuevo coordinador: lo guardamos en localStorage
   * y emitimos el cambio a todos los suscriptores
   */
  setCoordinador(coordinador: any) {
    localStorage.setItem('coordinador', JSON.stringify(coordinador));
    this.coordinadorSubject.next(coordinador);
  }

  /**
   * Borramos el coordinador (por ejemplo, cuando se cierra sesión)
   */
  clearCoordinador() {
    localStorage.removeItem('coordinador');
    this.coordinadorSubject.next(null);
  }
}
