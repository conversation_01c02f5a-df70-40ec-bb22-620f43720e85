<section class="p-4 w-full">
  <!-- Spinner Overlay: se muestra mientras loading sea true -->
  <div
    class="fixed top-0 left-0 w-full h-full bg-white/70 dark:bg-gray-900/70 flex justify-center items-center z-50"
    *ngIf="loading$ | async"
  >
    <app-spinner></app-spinner>
  </div>

  <!-- Error message -->
  <div
    class="flex items-center gap-2 p-4 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md my-4"
    *ngIf="errorPage$ | async as error"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
    <span>{{ error }}</span>
  </div>

  <!-- Contenedor principal -->
  <div class="bg-white dark:bg-gray-800 rounded-md shadow-sm">
    <!-- Título, subtítulo, buscador y botones en la misma fila -->
    <div class="p-4">
      <div
        class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4"
      >
        <div>
          <div class="flex items-center">
            <h2
              class="text-xl font-medium text-blue-600 dark:text-blue-400 m-0"
            >
              Gestión de Usuarios
            </h2>
            <!-- Indicador de WebSocket conectado -->
            <div
              *ngIf="webSocketService.isConnected()"
              class="flex items-center ml-2 bg-green-100 dark:bg-green-900/20 px-2 py-1 rounded-full"
            >
              <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
              <span class="text-xs text-green-600 dark:text-green-400"
                >Tiempo real</span
              >
            </div>
            <!-- Indicador de WebSocket desconectado -->
            <div
              *ngIf="!webSocketService.isConnected()"
              class="flex items-center ml-2 bg-red-100 dark:bg-red-900/20 px-2 py-1 rounded-full"
            >
              <span class="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
              <span class="text-xs text-red-600 dark:text-red-400"
                >Desconectado</span
              >
            </div>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Administración de usuarios del sistema
          </p>
        </div>
        <div class="w-full md:w-auto">
          <div class="flex flex-col md:flex-row gap-2 mb-2 md:mb-0">
            <!-- Buscador de usuarios -->
            <div class="relative w-full md:w-64">
              <input
                type="text"
                [(ngModel)]="searchText"
                (keyup)="onSearchChange(searchText)"
                (keyup.enter)="aplicarFiltros(true)"
                placeholder="Buscar usuarios"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <!-- Icono de búsqueda cuando no hay texto -->
                <button
                  *ngIf="!searchText"
                  (click)="aplicarFiltros(true)"
                  class="text-gray-400 hover:text-blue-500 dark:text-gray-500 dark:hover:text-blue-400"
                  title="Buscar"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </button>

                <!-- Botón para limpiar la búsqueda cuando hay texto -->
                <button
                  *ngIf="searchText"
                  (click)="clearSearch()"
                  class="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400"
                  title="Limpiar búsqueda"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Filtro por sede -->
            <div class="relative w-full md:w-48" [formGroup]="filterForm">
              <select
                formControlName="sede"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 appearance-none"
              >
                <option [ngValue]="null">Todas las sedes</option>
                <option *ngFor="let sede of sedes" [ngValue]="sede.id">
                  {{ sede.nombre }}
                </option>
              </select>
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-gray-400 dark:text-gray-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>

            <!-- Toggle para mostrar solo usuarios conectados -->
            <div
              class="flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
            >
              <input
                type="checkbox"
                id="soloConectados"
                [(ngModel)]="soloConectados"
                (change)="onToggleConectados()"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label
                for="soloConectados"
                class="text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer"
              >
                Solo Conectados
              </label>
            </div>

            <!-- Botón limpiar filtros -->
            <button
              (click)="clearAllFilters()"
              class="p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              title="Limpiar filtros"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 4l18 0"
                />
              </svg>
            </button>
          </div>
          <div class="flex flex-wrap gap-2 mt-2">
            <button
              (click)="openCreateUserDialogMasivo()"
              class="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <span>Importar Usuarios</span>
            </button>
            <button
              (click)="onTableAdd()"
              class="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <span>Nuevo Usuario</span>
            </button>
            <button
              (click)="onTableRefresh()"
              class="p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              title="Refrescar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Indicador de búsqueda en progreso - solo se muestra cuando está cargando y no hay datos -->
    <div
      *ngIf="tableLoading && (!tableData || tableData.length === 0)"
      class="flex items-center m-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800"
    >
      <div class="mr-3">
        <svg
          class="animate-spin h-5 w-5 text-blue-600 dark:text-blue-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
      <div>
        <p class="text-sm font-medium text-blue-800 dark:text-blue-300">
          <span *ngIf="sedeSeleccionada">Filtrando usuarios por sede...</span>
          <span *ngIf="!sedeSeleccionada">Cargando usuarios...</span>
        </p>
        <p class="text-xs text-blue-600 dark:text-blue-400">
          Esto puede tardar unos momentos si hay muchos usuarios con
          coordinadores asignados.
        </p>
      </div>
    </div>

    <!-- Indicador de carga discreto cuando ya hay datos -->
    <div
      *ngIf="tableLoading && tableData && tableData.length > 0"
      class="flex justify-center my-2"
    >
      <div class="w-full max-w-md">
        <div
          class="overflow-hidden h-1 bg-gray-200 dark:bg-gray-700 rounded-full"
        >
          <div class="animate-pulse h-1 bg-blue-500 dark:bg-blue-400"></div>
        </div>
      </div>
    </div>

    <!-- Tabla personalizada con columna de acciones -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <!-- Encabezados de columnas -->
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              N°
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Nombres
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Username
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Sede
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Rol
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Supervisor
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Fecha Creación
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Estado
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Conectado
            </th>
            <th
              scope="col"
              class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Acciones
            </th>
          </tr>
        </thead>
        <tbody
          class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800"
        >
          <tr
            *ngFor="let user of tableData; let i = index"
            class="hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <!-- Columna Número -->
            <td
              class="px-6 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              {{ currentPage * pageSize + i + 1 }}
            </td>
            <!-- Columna Nombre -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              {{ user.nombre + " " + user.apellido }}
            </td>

            <!-- Columna Username -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              {{ user.username }}
            </td>

            <!-- Columna Sede -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              {{ user.sede }}
            </td>

            <!-- Columna Rol -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
              >
                {{ user.role }}
              </span>
            </td>

            <!-- Columna Coordinador -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              <span *ngIf="user.coordinador"
                >{{ user.coordinador.nombre }}
                {{ user.coordinador.apellido }}</span
              >
              <span
                *ngIf="!user.coordinador"
                class="text-gray-500 dark:text-gray-400"
                >-</span
              >
            </td>

            <!-- Columna Fecha Creación -->
            <td
              class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
            >
              {{ formatDateArray(user.fechaCreacion) }}
            </td>

            <!-- Columna Estado -->
            <td class="px-3 py-2 whitespace-nowrap text-sm">
              <span
                *ngIf="user.estado === 'A'"
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
              >
                Activo
              </span>
              <span
                *ngIf="user.estado === 'I'"
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
              >
                Inactivo
              </span>
            </td>

            <!-- Columna Online -->
            <td class="px-3 py-2 whitespace-nowrap text-sm text-center">
              <div class="flex justify-center">
                <span
                  *ngIf="isUserOnline(user.id)"
                  class="w-2 h-2 bg-green-500 rounded-full"
                  title="Usuario conectado"
                ></span>
                <span
                  *ngIf="!isUserOnline(user.id)"
                  class="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full"
                  title="Usuario desconectado"
                ></span>
              </div>
            </td>

            <!-- Columna Acciones -->
            <td class="px-3 py-2 whitespace-nowrap text-sm text-center">
              <div class="flex justify-center space-x-1">
                <button
                  (click)="onTableEdit(user)"
                  class="p-1 rounded-full text-blue-600 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900/30 focus:outline-none"
                  title="Editar usuario"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </button>
                <button
                  (click)="onTableDelete(user)"
                  class="p-1 rounded-full text-red-600 hover:bg-red-100 dark:text-red-400 dark:hover:bg-red-900/30 focus:outline-none"
                  title="Eliminar usuario"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Paginador personalizado con Tailwind -->
      <div
        class="py-3 px-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700"
      >
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            (click)="
              onPageChange({ pageIndex: currentPage - 1, pageSize: pageSize })
            "
            [disabled]="currentPage === 0"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Anterior
          </button>
          <button
            (click)="
              onPageChange({ pageIndex: currentPage + 1, pageSize: pageSize })
            "
            [disabled]="(currentPage + 1) * pageSize >= tableTotalItems"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Siguiente
          </button>
        </div>
        <div
          class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
        >
          <div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              Mostrando
              <span class="font-medium">{{
                tableData && tableData.length > 0
                  ? currentPage * pageSize + 1
                  : 0
              }}</span>
              a
              <span class="font-medium">{{
                tableData
                  ? getMinValue((currentPage + 1) * pageSize, tableTotalItems)
                  : 0
              }}</span>
              de
              <span class="font-medium">{{ tableTotalItems }}</span>
              resultados
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <!-- Selector de tamaño de página -->
            <div class="relative inline-block text-left">
              <select
                #pageSizeSelect
                [ngModel]="pageSize"
                (change)="onPageSizeChange(pageSizeSelect.value)"
                class="block w-full pl-3 pr-10 py-1 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="5">5 por página</option>
                <option value="8">8 por página</option>
                <option value="10">10 por página</option>
                <option value="20">20 por página</option>
                <option value="50">50 por página</option>
              </select>
            </div>

            <!-- Botones de paginación -->
            <nav
              class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
              aria-label="Pagination"
            >
              <button
                (click)="onPageChange({ pageIndex: 0, pageSize: pageSize })"
                [disabled]="currentPage === 0"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">Primera página</span>
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
              <button
                (click)="
                  onPageChange({
                    pageIndex: currentPage - 1,
                    pageSize: pageSize
                  })
                "
                [disabled]="currentPage === 0"
                class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">Anterior</span>
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>

              <!-- Números de página -->
              <ng-container
                *ngFor="let page of getPaginationRange(); let i = index"
              >
                <ng-container *ngIf="page !== '...'">
                  <button
                    (click)="
                      onPageChange({
                        pageIndex: getPageIndex(page),
                        pageSize: pageSize
                      })
                    "
                    [class]="
                      getPageIndex(page) === currentPage
                        ? 'relative inline-flex items-center px-4 py-2 border border-blue-500 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/30 text-sm font-medium text-blue-600 dark:text-blue-400'
                        : 'relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    "
                  >
                    {{ page }}
                  </button>
                </ng-container>
                <span
                  *ngIf="page === '...'"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  ...
                </span>
              </ng-container>

              <button
                (click)="
                  onPageChange({
                    pageIndex: currentPage + 1,
                    pageSize: pageSize
                  })
                "
                [disabled]="(currentPage + 1) * pageSize >= tableTotalItems"
                class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">Siguiente</span>
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
              <button
                (click)="
                  onPageChange({
                    pageIndex: getLastPageIndex(),
                    pageSize: pageSize
                  })
                "
                [disabled]="(currentPage + 1) * pageSize >= tableTotalItems"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">Última página</span>
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    *ngIf="tableData?.length === 0"
    class="mt-4 text-center p-8 bg-white dark:bg-gray-800 rounded-md shadow-sm"
  >
    <div class="flex flex-col items-center">
      <div class="text-5xl text-gray-300 dark:text-gray-600 mb-2">👤</div>
      <p class="text-gray-600 dark:text-gray-400">
        No hay usuarios registrados
      </p>
    </div>
  </div>
</section>