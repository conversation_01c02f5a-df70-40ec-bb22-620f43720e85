<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Midas Solutions</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <!-- External CSS libraries -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
    />
    <link rel="icon" type="image/png" href="assets/favicon.png" />
    <!-- Polyfills para SockJS -->
    <script>
      // Definir global para SockJS
      window.global = window;

      // Definir process para SockJS
      window.process = {
        env: { DEBUG: undefined },
        nextTick: function (fn) {
          setTimeout(fn, 0);
        },
      };

      // Definir Buffer para SockJS
      window.Buffer = window.Buffer || {};
    </script>
  </head>
  <body class="m-0 p-0 overflow-hidden">
    <app-root></app-root>
  </body>
</html>
