import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface IpPermitida {
  id?: number;
  ip: string;
  descripcion: string;
  fecha_expiracion: string;
  user_create_id?: number;
  is_active?: boolean;
}

export interface GenericResponse<T> {
  rpta: number;
  msg: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class IpPermitidasService {
  private baseUrl = `${environment.url}api/ip-permitidas`;

  constructor(private http: HttpClient) { }

  getAll(): Observable<GenericResponse<IpPermitida[]>> {
    return this.http.get<GenericResponse<IpPermitida[]>>(`${environment.urlVentas}ip-permitidas/todas/`);
  }

  create(ipPermitida: IpPermitida): Observable<GenericResponse<IpPermitida>> {
    return this.http.post<GenericResponse<IpPermitida>>(`${environment.urlVentas}ip-permitidas/crear/`, ipPermitida);
  }

  update(id: number, ipPermitida: IpPermitida): Observable<any> {
    // Asegurarse de que estamos enviando los datos correctos
    const updateData = {
      ...ipPermitida,
      id: id  // Asegurarse de que el ID esté incluido
    };
    return this.http.put(`${environment.urlVentas}ip-permitidas/editar/${id}/`, updateData);
  }

  delete(id: number, userId: number): Observable<GenericResponse<any>> {
    const body = { is_active: false }; // Enviamos el cambio de estado
    return this.http.patch<GenericResponse<any>>(
      `${environment.urlVentas}ip-permitidas/cambiarEstado/${id}/${userId}/`,
      body
    );
  }
}


