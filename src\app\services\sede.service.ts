import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '@src/environments/environment';
import { Sede, SedePaginadoResponse } from '@app/models/backend/sede/sede.model';
import { GenericResponse } from '@app/models/backend/generic-response';

export interface SedePagination {
  page: number;
  perPage: number;
  search?: string;
  column?: string;
  order?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SedeService {
  private apiUrl = `${environment.url}api/sedes`;
  
  // BehaviorSubject para manejar la paginación
  private paginationSubject = new BehaviorSubject<SedePagination>({
    page: 0,
    perPage: 10,
    search: '',
    column: 'id',
    order: 'asc'
  });

  pagination = this.paginationSubject.asObservable();

  constructor(private http: HttpClient) { }

  // Método para actualizar la paginación
  updatePagination(pagination: Partial<SedePagination>): void {
    this.paginationSubject.next({
      ...this.paginationSubject.getValue(),
      ...pagination
    });
  }

  // Obtener sedes paginadas
  getSedes(pagination: SedePagination): Observable<GenericResponse<SedePaginadoResponse>> {
    let params = new HttpParams()
      .set('page', pagination.page.toString())
      .set('size', pagination.perPage.toString())
      .set('column', pagination.column || 'id')
      .set('order', pagination.order || 'asc');

    if (pagination.search) {
      params = params.set('search', pagination.search);
    }

    return this.http.get<GenericResponse<SedePaginadoResponse>>(this.apiUrl, { params });
  }

  // Obtener una sede por ID
  getSedeById(id: number): Observable<GenericResponse<Sede>> {
    return this.http.get<GenericResponse<Sede>>(`${this.apiUrl}/${id}`);
  }

  // Crear una nueva sede
  createSede(sede: Sede): Observable<GenericResponse<Sede>> {
    return this.http.post<GenericResponse<Sede>>(this.apiUrl, sede);
  }

  // Actualizar una sede existente
  updateSede(id: number, sede: Sede): Observable<GenericResponse<Sede>> {
    return this.http.put<GenericResponse<Sede>>(`${this.apiUrl}/${id}`, sede);
  }

  // Eliminar una sede
  deleteSede(id: number): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.apiUrl}/${id}`);
  }

  // Obtener todas las sedes
  getAllSedes(): Observable<GenericResponse<Sede[]>> {
    return this.http.get<GenericResponse<Sede[]>>(`${this.apiUrl}/all`);
  }

  // Obtener sedes activas
  getSedesActivas(): Observable<GenericResponse<Sede[]>> {
    return this.http.get<GenericResponse<Sede[]>>(`${this.apiUrl}/activas`);
  }
}
