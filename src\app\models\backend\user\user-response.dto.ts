/**
 * DTO para respuestas de usuario desde el backend
 * Basado en la estructura que devuelve el backend en UserResponseDTO.java
 */
export interface UserResponseDTO {
    id: number;
    username: string;
    nombre: string;
    apellido: string;
    dni: string;
    telefono?: string;
    email?: string;
    fechaCreacion: string;
    fechaCese?: string;
    estado: string;
    role: string;
    sede?: string;
    sede_id?: number;
    coordinador?: {
        id: number;
        username: string;
        nombre: string;
        apellido: string;
    };
}

/**
 * DTO para mensajes WebSocket relacionados con usuarios
 */
export interface UserWebSocketDTO {
    type: 'CREATED' | 'UPDATED' | 'DELETED';
    user: UserResponseDTO;
}
