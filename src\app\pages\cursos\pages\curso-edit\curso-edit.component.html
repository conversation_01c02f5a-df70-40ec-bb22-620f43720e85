
<div class="  dark:text-white p-2" id="curso-edit-container">
  <div class="max-w-[80%] mx-auto">
    <!-- Encabezado profesional -->
    <div class="bg-indigo-600 dark:bg-indigo-800 rounded shadow mb-2 overflow-hidden">
      <div class="flex items-center p-2">
        <div class="flex-shrink-0 bg-white dark:bg-gray-800 p-1.5 rounded-full mr-3">
          <mat-icon class="text-indigo-600 dark:text-indigo-400">school</mat-icon>
        </div>
        <div>
          <h1 class="text-lg font-bold text-white">Editar Curso</h1>
          <p class="text-xs text-indigo-100 dark:text-indigo-200">Actualice la información del curso para mejorar su contenido</p>
        </div>
      </div>
    </div>

    <!-- Tarjeta principal -->
    <div class="bg-white dark:bg-gray-800 dark:text-white rounded shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Pestañas con estilo mejorado -->
      <!-- Las clases de Tailwind CSS para el modo oscuro se aplican directamente en los elementos -->
      <mat-tab-group [(selectedIndex)]="activeTab" (selectedIndexChange)="changeTab($event)"
                    animationDuration="200ms" class="dark:bg-gray-800">
        <!-- Pestaña de información básica del curso -->
        <mat-tab label="Información del Curso">
          <div class="p-3 bg-white dark:bg-gray-800 dark:text-white">
            <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-3">
              <!-- Información básica del curso -->
              <div class="bg-gray-50 dark:bg-gray-800/80 dark:text-white p-3 rounded border border-gray-200 dark:border-gray-700">
                <div class="flex items-center mb-2 text-indigo-600 dark:text-indigo-400">
                  <mat-icon class="mr-1 text-sm">info</mat-icon>
                  <h3 class="text-sm font-medium">Información Básica</h3>
                </div>

                <!-- Nombre del Curso -->
                <div class="mb-2">
                  <label for="nombre" class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <mat-icon class="mr-1 text-xs">title</mat-icon>
                    Nombre del Curso
                  </label>
                  <input type="text" id="nombre" formControlName="nombre"
                         class="block w-full px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                         placeholder="Ingrese el nombre del curso">
                  <div *ngIf="form.get('nombre')?.hasError('required') && form.get('nombre')?.touched"
                       class="mt-1 text-xs text-red-600 dark:text-red-400 flex items-center">
                    <mat-icon class="mr-1 text-xs">error</mat-icon>
                    El nombre del curso es obligatorio
                  </div>
                </div>

                <!-- Descripción -->
                <div class="mb-2">
                  <label for="descripcion" class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <mat-icon class="mr-1 text-xs">description</mat-icon>
                    Descripción
                  </label>
                  <textarea id="descripcion" formControlName="descripcion" rows="6"
                            class="block w-full px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Ingrese una descripción del curso"></textarea>
                </div>

                <!-- Estado -->
                <div class="mb-1">
                  <label for="estado" class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <mat-icon class="mr-1 text-xs">toggle_on</mat-icon>
                    Estado
                  </label>
                  <div class="relative">
                    <select id="estado" formControlName="estado"
                            class="outline-none block w-full px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white appearance-none">
                      <option value="A">Activo</option>
                      <option value="I">Inactivo</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-400">
                      <mat-icon class="text-xs">expand_more</mat-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Fechas del curso -->
              <div class="bg-gray-50 dark:bg-gray-800/80 dark:text-white p-3 rounded border border-gray-200 dark:border-gray-700">
                <div class="flex items-center mb-2 text-indigo-600 dark:text-indigo-400">
                  <mat-icon class="mr-1 text-sm">event</mat-icon>
                  <h3 class="text-sm font-medium">Fechas del Curso</h3>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <!-- Fecha de Inicio -->
                  <div>
                    <label for="fechaInicio" class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                      <mat-icon class="mr-1 text-xs">calendar_today</mat-icon>
                      Fecha de Inicio
                    </label>
                    <input type="date" id="fechaInicio" formControlName="fechaInicio"
                           class="block w-full px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  </div>

                  <!-- Fecha de Fin -->
                  <div>
                    <label for="fechaFin" class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                      <mat-icon class="mr-1 text-xs">event_busy</mat-icon>
                      Fecha de Fin
                    </label>
                    <input type="date" id="fechaFin" formControlName="fechaFin"
                           class="block w-full px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  </div>
                </div>
              </div>

              <!-- Video introductorio del curso -->
              <div class="bg-gray-50 dark:bg-gray-800/80 dark:text-white p-3 rounded border border-gray-200 dark:border-gray-700">
                <div class="flex items-center mb-2 text-indigo-600 dark:text-indigo-400">
                  <mat-icon class="mr-1 text-sm">videocam</mat-icon>
                  <h3 class="text-sm font-medium">Video Introductorio</h3>
                </div>

                <div class="flex items-center mb-2">
                  <button type="button" mat-raised-button color="primary" (click)="openFilesUpload()"
                          class="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded transition-colors">
                    <mat-icon class="mr-1 text-xs">cloud_upload</mat-icon>
                    <span>Subir desde Firebase</span>
                  </button>
                </div>

                <!-- Información del video -->
                <div *ngIf="videoPreviewUrl || form.get('videoUrl')?.value"
                     class="mb-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800 text-xs">
                  <div class="flex items-center">
                    <mat-icon class="text-green-500 dark:text-green-400 mr-1 text-xs">check_circle</mat-icon>
                    <span class="text-green-700 dark:text-green-300">Video cargado correctamente</span>
                  </div>

                  <div class="flex mt-1 space-x-2">
                    <button type="button" (click)="removeSelectedVideo()" *ngIf="videoPreviewUrl"
                            class="flex items-center text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 px-2 py-0.5 rounded text-xs">
                      <mat-icon class="mr-1 text-xs">delete</mat-icon> Eliminar
                    </button>

                    <button type="button" (click)="form.patchValue({videoUrl: ''})" *ngIf="form.get('videoUrl')?.value"
                            class="flex items-center text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 px-2 py-0.5 rounded text-xs">
                      <mat-icon class="mr-1 text-xs">delete</mat-icon> Eliminar
                    </button>
                  </div>
                </div>
              </div>

              <!-- Mensaje de error -->
              <div class="bg-red-50 dark:bg-red-900/20 p-2 rounded border border-red-200 dark:border-red-800 flex items-center text-xs" *ngIf="error">
                <mat-icon class="text-red-500 dark:text-red-400 mr-1 text-xs">error</mat-icon>
                <span class="text-red-700 dark:text-red-300">{{ error }}</span>
              </div>

              <!-- Botones de acción -->
              <div class="flex justify-end pt-2 border-t border-gray-200 dark:border-gray-700">
                <button type="button" (click)="onCancel()"
                        class="mr-2 px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors">
                  Cancelar
                </button>
                <button type="submit" [disabled]="form.invalid || loading"
                        class="flex items-center px-3 py-1 text-xs bg-indigo-600 hover:bg-indigo-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                  <mat-icon class="mr-1 text-xs">save</mat-icon>
                  <span *ngIf="!loading">Guardar</span>
                  <span *ngIf="loading">Guardando...</span>
                </button>
              </div>
            </form>
          </div>
        </mat-tab>

        <!-- Pestaña de módulos -->
        <mat-tab label="Módulos">
          <div class="p-3">
            <!-- Mostrar lista de módulos si no se está editando un módulo -->
            <div *ngIf="!showModuloForm" class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-modulo-list
                [cursoId]="cursoId"
                (editModulo)="onEditModulo($event)"
                (viewLecciones)="onViewLecciones($event)">
              </app-modulo-list>
            </div>

            <!-- Mostrar formulario de módulo si se está editando un módulo -->
            <div *ngIf="showModuloForm" class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-modulo-form
                [cursoId]="cursoId"
                [modulo]="selectedModulo"
                (saved)="onModuloSaved()"
                (cancelled)="onModuloCancelled()">
              </app-modulo-form>
            </div>
          </div>
        </mat-tab>

        <!-- Pestaña de secciones y lecciones -->
        <mat-tab label="Secciones">
          <div class="p-3">
            <!-- Mostrar mensaje si no se ha seleccionado un módulo -->
            <div *ngIf="!showSeccionList && !showSeccionForm && !showLeccionList && !showLeccionForm"
                 class="flex flex-col items-center justify-center py-6 bg-gray-50 dark:bg-gray-800/80 rounded border border-gray-200 dark:border-gray-700">
              <mat-icon class="text-3xl mb-2 text-indigo-500 dark:text-indigo-400">folder</mat-icon>
              <h3 class="text-sm font-medium text-gray-800 dark:text-white mb-1">No hay módulo seleccionado</h3>
              <p class="text-xs text-gray-600 dark:text-gray-400 text-center max-w-md mb-2">
                Seleccione un módulo para gestionar sus secciones
              </p>
              <button (click)="activeTab = 1" class="text-xs px-2 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded flex items-center">
                <mat-icon class="mr-1 text-xs">view_module</mat-icon>
                Ir a Módulos
              </button>
            </div>

            <!-- Mostrar lista de secciones si se ha seleccionado un módulo -->
            <div *ngIf="showSeccionList && !showSeccionForm && !showLeccionList && !showLeccionForm"
                 class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-seccion-list
                [modulo]="selectedModulo!"
                [editable]="true"
                (seccionSelected)="onSeccionSelected($event)"
                (editSeccion)="onEditSeccion($event)"
                (back)="onSeccionesBack()">
              </app-seccion-list>
            </div>

            <!-- Mostrar formulario de sección si se está editando una sección -->
            <div *ngIf="showSeccionForm && !showLeccionList && !showLeccionForm"
                 class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-seccion-form-inline
                [modulo]="selectedModulo!"
                [seccion]="selectedSeccion"
                (saved)="onSeccionSaved()"
                (cancelled)="onSeccionCancelled()">
              </app-seccion-form-inline>
            </div>

            <!-- Mostrar lista de lecciones si se ha seleccionado una sección -->
            <div *ngIf="showLeccionList && !showLeccionForm"
                 class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-leccion-list
                [seccion]="selectedSeccion!"
                (back)="onLeccionesBack()"
                (editLeccion)="onEditLeccion($event)">
              </app-leccion-list>
            </div>

            <!-- Mostrar formulario de lección si se está editando una lección -->
            <div *ngIf="showLeccionForm"
                 class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
              <app-leccion-form
                [seccion]="selectedSeccion!"
                [leccion]="selectedLeccion"
                (saved)="onLeccionSaved()"
                (cancelled)="onLeccionCancelled()">
              </app-leccion-form>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>
