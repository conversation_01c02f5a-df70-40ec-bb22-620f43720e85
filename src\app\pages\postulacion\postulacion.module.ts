import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PostulacionComponent } from './postulacion.component';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { PostulacionRoutingModule } from './postulacion-routing.module'; // 👈 FALTA ESTO

@NgModule({
  declarations: [
    PostulacionComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatTableModule,
    MatButtonModule,
    PostulacionRoutingModule
  ]
})
export class PostulacionModule { }
