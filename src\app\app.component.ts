import {
  Component,
  On<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild
} from '@angular/core';
import {
  NavigationEnd,
  Router,
  RouterOutlet
} from '@angular/router';
import { MatSidenav } from '@angular/material/sidenav';
import {
  trigger,
  transition,
  style,
  animate,
  query
} from '@angular/animations';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { BreakpointObserver } from '@angular/cdk/layout';
import { select, Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import * as fromRoot from './store';
import * as fromUser from './store/user';
import { NotificationService, ThemeService } from '@app/services';
import { SidenavStateService } from './shared/services/sidenav-state.service';
import { WebSocketService } from './services/websocket/WebSocketService';
import { UserStatusService } from './services/user-status.service';
import { MatDialog } from '@angular/material/dialog';
import { InactivityService } from './services/inactivity.service';
import { InactivityDialogComponent } from './components/inactivity-dialog.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  animations: [
    trigger('routeAnimations', [
      transition('* <=> *', [
        query(':enter', [style({ opacity: 0 })], { optional: true }),
        query(':enter', [animate('300ms ease-in-out', style({ opacity: 1 }))], { optional: true })
      ])
    ])
  ]
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('sidenav') sidenav!: MatSidenav;

  title = 'client-inmueble-app';
  showSpinner = false;
  isAdmin = false;
  isMobile = false;
  isSigningOut = false;
  private static readonly SLEEP_THRESHOLD_MS = 60 * 1000;
  private static readonly CHECK_INTERVAL_MS = 15 * 1000;

  user$!: Observable<fromUser.UserResponse | null>;
  isAuthorized$!: Observable<boolean>;
  isLoginRoute$!: Observable<boolean>;
  showMenu$!: Observable<boolean>;

  private subscriptions: Subscription[] = [];
  private lastCheck = Date.now();
  private suspendCheckIntervalId: any;
  private lastVisibleTime = Date.now();
  private visibilityHandler: any;
  private inactivitySub: Subscription | null = null;

  constructor(
    private fs: AngularFirestore,
    private notification: NotificationService,
    private store: Store<fromRoot.State>,
    public router: Router,
    private breakpointObserver: BreakpointObserver,
    private themeService: ThemeService,
    public sidenavStateService: SidenavStateService,
    private webSocketService: WebSocketService,
    private userStatusService: UserStatusService,
    private inactivityService: InactivityService,
    private dialog: MatDialog
  ) {
    this.checkToken();
  }

  ngOnInit(): void {
    this.initWebSocketMonitoring();
    this.initUserMonitoring();
    this.initRoutingMonitoring();
    this.initBreakpointMonitoring();
    this.initSuspendDetection();
    this.initInactivityDetection();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.suspendCheckIntervalId) clearInterval(this.suspendCheckIntervalId);
    if (this.visibilityHandler) document.removeEventListener('visibilitychange', this.visibilityHandler);
    this.webSocketService.cleanup();
    this.inactivityService.stopMonitoring();
    this.inactivitySub?.unsubscribe();
  }

  prepareRoute(outlet: RouterOutlet) {
    return outlet?.activatedRouteData?.['animation'] || '';
  }

  onSignOut(sidenav?: MatSidenav): void {
    if (this.isSigningOut) return;
    this.isSigningOut = true;

    try {
      this.inactivityService.stopMonitoring();
      this.inactivitySub?.unsubscribe();

      sidenav?.close();
      this.sidenavStateService.setSidenavState(false);
      if (this.webSocketService.isConnected()) this.webSocketService.disconnect();

      window.wsConnectionInitiated = false;
      localStorage.clear();
      this.store.dispatch(new fromUser.SignOut());
      this.router.navigate(['/auth/login']);
    } catch {
      this.notification.error('Error al cerrar sesión');
      this.router.navigate(['/auth/login']);
    } finally {
      setTimeout(() => (this.isSigningOut = false), 1000);
    }
  }

  private checkToken(): void {
    const token = localStorage.getItem('token');
    if (!token && !this.router.url.includes('/auth/login')) {
      this.router.navigate(['/auth/login']);
      setTimeout(() => this.notification.error('No hay sesión activa'), 100);
    }
  }

  private initWebSocketMonitoring(): void {
    const resumeSub = this.webSocketService.getMessagesByType('CONNECTION_RESUMED').subscribe(() => { });
    const restartSub = this.webSocketService.getMessagesByType('SERVER_RESTART_DETECTED').subscribe(() => { });
    this.subscriptions.push(resumeSub, restartSub);
  }

  private initUserMonitoring(): void {
  this.user$ = this.store.pipe(select(fromUser.getUser));
  this.isAuthorized$ = this.store.pipe(select(fromUser.getIsAuthorized));
  this.store.dispatch(new fromUser.Init());

  this.user$.subscribe(user => {
    this.isAdmin = !!user?.role && user.role.trim().toUpperCase() === 'ADMIN';
    if (this.router.url.includes('/auth/login') && this.webSocketService.isConnected()) {
      this.webSocketService.disconnect();
      window.wsConnectionInitiated = false;
    }

    // Redirección basada en el rol (solo si está en login)
    if (user && this.router.url === '/auth/login') {
      const role = user.role?.toUpperCase() ?? '';
      switch (role) {
        case 'COACHING':
          this.router.navigate(['/landing-contact']);
          break;
        default:
          this.router.navigate(['/home']);
          break;
      }
    }
  });

  this.store.pipe(
    select(fromUser.getIsAuthorized),
    filter(isAuth => isAuth)
  ).subscribe(() => {
    console.log('✅ Usuario autenticado. Activando monitoreo de inactividad.');
    this.inactivitySub?.unsubscribe(); // importante
    this.startInactivityDialogListener();
    this.inactivityService.startMonitoring();
  });
}

  private initRoutingMonitoring(): void {
    this.isLoginRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.router.url.includes('/auth/login'))
    );

    this.showMenu$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => !this.router.url.includes('/auth/login'))
    );

    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe((event: any) => {
      if (event.url && !event.url.includes('/auth/login')) {
        sessionStorage.setItem('lastUrl', event.url);
      }
    });
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      if (event.url.includes('/auth/login')) {
        this.sidenav?.close(); // 👈 Cierra el sidenav si está abierto
        this.sidenavStateService.setSidenavState(false); // Asegura sincronización de estado global
      }
    });

  }

  private initBreakpointMonitoring(): void {
    this.breakpointObserver.observe(['(max-width: 768px)']).subscribe(result => {
      this.isMobile = result.matches;
      this.sidenavStateService.setSidenavState(!this.isMobile);
    });
  }

  private initSuspendDetection(): void {
    this.suspendCheckIntervalId = setInterval(() => {
      const now = Date.now();
      const diff = now - this.lastCheck;

      if (diff > AppComponent.SLEEP_THRESHOLD_MS + AppComponent.CHECK_INTERVAL_MS) {
        console.warn('🛑 Posible suspensión detectada, cerrando sesión automáticamente...');
        this.notification.info('Sesión cerrada por suspensión del sistema', {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
          panelClass: ['info-snackbar', 'session-warning'],
        });
        this.onSignOut();
      }

      this.lastCheck = now;
    }, AppComponent.CHECK_INTERVAL_MS);
  }

  private initInactivityDetection(): void {
    this.visibilityHandler = () => {
      if (!document.hidden) {
        const now = Date.now();
        const diff = now - this.lastVisibleTime;
        if (diff > 30 * 60 * 1000) this.onSignOut(this.sidenav ?? null);
        this.lastVisibleTime = now;
      }
    };
    document.addEventListener('visibilitychange', this.visibilityHandler);
  }

 private startInactivityDialogListener(): void {
  this.inactivitySub?.unsubscribe();

  this.inactivitySub = this.inactivityService.onInactivityDetected$.subscribe(() => {
    // Detenemos el monitoreo para que no se emita nuevamente mientras el diálogo esté abierto
    this.inactivityService.stopMonitoring();

    const dialogRef = this.dialog.open(InactivityDialogComponent, {
      disableClose: true,
      width: '400px'
    });

    dialogRef.afterClosed().subscribe(userResponded => {
      if (userResponded) {
        console.log('🟢 Usuario está presente, reanudando monitoreo...');
        this.inactivityService.startMonitoring(); // solo se reinicia si respondió que está ahí
      } else {
        console.warn('🔴 Usuario inactivo. Cerrando sesión...');
        this.onSignOut();
      }
    });
  });
}


}