.calendar-modal {
  width: 100%;
  max-width: 500px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;

  .modal-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: #212529;
  }

  .btn-close {
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/0.8em auto no-repeat;
    border: 0;
    opacity: 0.5;
    cursor: pointer;

    &:hover {
      opacity: 0.75;
    }
  }
}

.modal-body {
  padding: 1rem;

  .form-group {
    margin-bottom: 1rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    font-size: 0.9rem;
    color: #212529;
  }

  .form-control, .form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      border-color: #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #adb5bd;
      opacity: 1;
    }
  }

  textarea.form-control {
    min-height: 60px;
    resize: vertical;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.5rem;
    margin-left: -0.5rem;
    margin-bottom: 1rem;

    .col-md-6 {
      flex: 0 0 50%;
      max-width: 50%;
      padding-right: 0.5rem;
      padding-left: 0.5rem;
    }
  }

  .input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;

    .form-control {
      position: relative;
      flex: 1 1 auto;
      width: 1%;
      min-width: 0;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-right: none;
    }

    .input-group-text {
      display: flex;
      align-items: center;
      padding: 0.375rem 0.75rem;
      font-size: 0.9rem;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      text-align: center;
      white-space: nowrap;
      background-color: #f8f9fa;
      border: 1px solid #ced4da;
      border-left: none;
      border-radius: 0 0.25rem 0.25rem 0;
    }
  }
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem 1rem;
  border-top: 1px solid #e9ecef;
  gap: 0.5rem;

  .btn {
    display: inline-block;
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &.btn-light {
      color: #212529;
      background-color: #f8f9fa;
      border-color: #f8f9fa;

      &:hover {
        background-color: #e2e6ea;
        border-color: #dae0e5;
      }
    }

    &.btn-success {
      color: #fff;
      background-color: #28a745;
      border-color: #28a745;

      &:hover {
        background-color: #218838;
        border-color: #1e7e34;
      }
    }

    &.btn-primary {
      color: #fff;
      background-color: #007bff;
      border-color: #007bff;

      &:hover {
        background-color: #0069d9;
        border-color: #0062cc;
      }
    }

    &.btn-danger {
      color: #fff;
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #c82333;
        border-color: #bd2130;
      }
    }
  }
}

/* Custom color classes */
.bg-purple {
  background-color: #6f42c1 !important;
  color: #fff !important;
}

/* Estilos para la vista previa del color */
.color-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border-radius: 0.25rem;
  font-weight: 500;
  margin-top: 0.25rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Estilos para los colores en el select */
select option {
  font-weight: normal;
  padding: 8px;
}

/* Colores para la vista previa */
.bg-primary {
  background-color: #007bff !important;
  color: #fff !important;
}

.bg-success {
  background-color: #28a745 !important;
  color: #fff !important;
}

.bg-danger {
  background-color: #dc3545 !important;
  color: #fff !important;
}

.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
  color: #fff !important;
}

.bg-secondary {
  background-color: #6c757d !important;
  color: #fff !important;
}

.bg-dark {
  background-color: #343a40 !important;
  color: #fff !important;
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  .color-preview {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  /* Colores para la vista previa en tema oscuro */
  .bg-primary {
    background-color: #1e88e5 !important;
  }

  .bg-success {
    background-color: #43a047 !important;
  }

  .bg-danger {
    background-color: #e53935 !important;
  }

  .bg-warning {
    background-color: #ffb300 !important;
  }

  .bg-info {
    background-color: #26c6da !important;
  }

  .bg-purple {
    background-color: #8e24aa !important;
  }

  .bg-secondary {
    background-color: #78909c !important;
  }

  .bg-dark {
    background-color: #455a64 !important;
  }
}
