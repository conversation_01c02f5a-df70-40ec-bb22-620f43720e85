import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@src/environments/environment';
import { User } from '@app/models/backend/user';
import { GenericResponse } from '@app/models/backend';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private baseUrl = environment.url + 'api/users';
  private userUrl = environment.url + 'api/user'; // URL correcta para el endpoint de listar usuarios

  constructor(private http: HttpClient) { }

  /**
   * Obtiene todos los usuarios con paginación
   * @param page Número de página (0-indexed)
   * @param size Tamaño de la página
   * @param sedeId ID de la sede (opcional)
   */
  getUsers(page: number = 0, size: number = 100, sedeId?: number): Observable<GenericResponse<any>> {
    // Construir la URL con los parámetros de paginación
    let url = `${this.userUrl}/listar?page=${page}&size=${size}`;

    // Añadir el parámetro de sede_id si existe
    if (sedeId) {
      url += `&sede_id=${sedeId}`;
    }

    return this.http.get<GenericResponse<any>>(url);
  }

  /**
   * Busca usuarios por query (nombre, username, email, dni, etc.)
   * @param query Término de búsqueda
   * @param page Número de página (0-indexed)
   * @param size Tamaño de la página
   * @param sedeId ID de la sede (opcional)
   */
  searchUsers(query: string, page: number = 0, size: number = 100, sedeId?: number): Observable<GenericResponse<any>> {
    // Construir la URL con los parámetros de búsqueda
    let url = `${this.userUrl}/buscar?page=${page}&size=${size}`;

    // Añadir el parámetro de búsqueda si existe
    if (query && query.trim() !== '') {
      url += `&query=${encodeURIComponent(query.trim())}`;
    }

    // Añadir el parámetro de sede_id si existe
    if (sedeId) {
      url += `&sede_id=${sedeId}`;
    }

    return this.http.get<GenericResponse<any>>(url);
  }

  /**
   * Obtiene un usuario por su ID
   * @param id ID del usuario
   */
  getUserById(id: number): Observable<GenericResponse<User>> {
    return this.http.get<GenericResponse<User>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene usuarios por rol
   * @param role Rol de los usuarios a buscar
   */
  getUsersByRole(role: string): Observable<GenericResponse<User[]>> {
    return this.http.get<GenericResponse<User[]>>(`${this.baseUrl}/role/${role}`);
  }
}
