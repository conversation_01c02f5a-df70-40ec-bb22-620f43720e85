<div class="modal-content">
  <div class="modal-header">
    <h2 class="modal-title">{{ dataModal.title }}</h2>
    <button type="button" class="btn-close" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>

  <div class="modal-body">
    <form [formGroup]="faqForm" autocomplete="off" (keydown.enter)="$event.preventDefault()">
      <!-- Tipo de usuario -->
      <div class="mb-3">
        <label class="form-label">Tipo de usuario *</label>
        <select class="form-select" formControlName="tipoUsuario">
          <option value="" disabled>Seleccione un tipo...</option>
          <option *ngFor="let tipo of tiposUsuario" [value]="tipo.value">{{ tipo.viewValue }}</option>
        </select>
      </div>

      <!-- Pregunta -->
      <div class="mb-3">
        <label class="form-label">Pregunta *</label>
        <textarea class="form-control" formControlName="pregunta" rows="3" placeholder="Ingrese su pregunta..."></textarea>
      </div>

      <!-- Respuesta -->
      <div class="mb-3">
        <label class="form-label">Respuesta</label>
        <div class="editor-toolbar">
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-bold"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-italic"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-underline"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-strikethrough"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-code-tags"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-quote-close"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-list-bulleted"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-format-list-numbered"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-link"></i></button>
          <button type="button" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-image"></i></button>
        </div>
        <textarea class="form-control" formControlName="respuesta" rows="6" placeholder="Escriba aquí..."></textarea>
      </div>

      <!-- Archivos -->
      <div class="mb-3">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Archivos Seleccionados ({{ uploadFiles.length }})</h6>
            <button type="button" class="btn btn-sm btn-link" data-bs-toggle="collapse" data-bs-target="#collapseFiles">
              <i class="mdi mdi-chevron-down"></i>
            </button>
          </div>
          <div id="collapseFiles" class="collapse show">
            <div class="card-body">
              <div class="file-upload-area border rounded p-4 text-center">
                <div class="upload-icon mb-3">
                  <i class="mdi mdi-cloud-upload" style="font-size: 3rem; color: #6c757d;"></i>
                </div>
                <p class="mb-3">Suelte los archivos aquí o haga clic para cargarlos.</p>
                <app-form-upload (emitFiles)="getUploadedFiles($event)"></app-form-upload>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button class="btn btn-success" (click)="saveData()">
      <i class="mdi mdi-content-save me-1"></i> Guardar
    </button>
    <button class="btn btn-secondary" (click)="modalRef.hide()">Cerrar</button>
  </div>
</div>