import {
  NotificationCategory,
  NotificationType,
} from './notification-types.enum';

/**
 * Modelo para las notificaciones en el frontend
 */
export interface Notification {
  id: number;
  recipientId?: number;
  senderId?: number;
  senderName: string;
  message: string;
  time?: string; // Texto formateado para mostrar (ej: "Hace 5 minutos")
  read: boolean;
  avatar?: string;
  type: NotificationType;
  category: NotificationCategory;
  createdAt: string; // Fecha ISO para ordenar
  data?: any; // Datos adicionales opcionales
}

/**
 * Respuesta paginada de notificaciones
 * Soporta tanto el formato personalizado como el formato de Spring Page
 */
export interface NotificationPageResponse {
  // Formato personalizado
  notifications?: Notification[];
  currentPage?: number;
  totalItems?: number;
  totalPages?: number;
  pageSize?: number;

  // Formato Spring Page
  content?: Notification[];
  number?: number; // Número de página actual (equivalente a currentPage)
  totalElements?: number; // Total de elementos (equivalente a totalItems)
  size?: number; // Tamaño de página (equivalente a pageSize)

  // Otras propiedades de Spring Page que podrían ser útiles
  first?: boolean; // Si es la primera página
  last?: boolean; // Si es la última página
  empty?: boolean; // Si la página está vacía
  numberOfElements?: number; // Número de elementos en la página actual
}
