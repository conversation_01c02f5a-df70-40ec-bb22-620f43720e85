<section
  class="flex flex-col justify-center items-center w-full h-full bg-[url('/assets/bg-login.webp')] bg-no-repeat bg-center bg-cover relative"
>
  <!-- Overlay para mejorar legibilidad en modo oscuro -->
  <div class="absolute inset-0 bg-black/20 dark:bg-black/50"></div>

  <div
    class="bg-white dark:bg-gray-900 p-6 shadow-lg dark:shadow-2xl dark:shadow-black/50 rounded-lg w-full lg:w-6/12 max-w-[500px] relative z-10 border dark:border-gray-700"
  >
    <div class="text-center mb-8">
      <img
        src="assets/logovector-MIDAS.svg"
        alt="Midas Solutions Center"
        height="50"
        class="mb-4 w-[220px] block mx-auto"
      />
      <div class="text-gray-900 dark:text-white text-3xl font-medium mb-4">
        INTRANET CRM MIDAS
      </div>
    </div>

    <form #f="ngForm" (ngSubmit)="loginUsuario(f)">
      <div>
        <!-- Usuario -->
        <label
          for="username"
          class="block text-gray-900 dark:text-white font-medium mb-2"
          >Usuario</label
        >
        <div class="mb-4 relative">
          <svg
            class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5.121 17.804A9.001 9.001 0 0112 15a9.001 9.001 0 016.879 2.804M15 10a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
          <input
            type="text"
            id="username"
            name="username"
            ngModel
            required
            class="pl-10 py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400 dark:focus:ring-gray-600"
            placeholder="Ingrese su usuario"
          />
        </div>

        <!-- Contraseña -->
        <label
          for="password"
          class="block text-gray-900 dark:text-white font-medium mb-2"
          >Contraseña</label
        >
        <div class="mb-4 relative">
          <svg
            class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 11c1.657 0 3 1.343 3 3v3H9v-3c0-1.657 1.343-3 3-3z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 11V9a5 5 0 00-10 0v2m2 4h6"
            />
          </svg>
          <input
            [type]="hidePassword ? 'password' : 'text'"
            id="password"
            name="password"
            ngModel
            required
            class="pl-10 pr-12 py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400 dark:focus:ring-gray-600"
            placeholder="Ingrese su contraseña"
          />
          <mat-icon
            class="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
            (click)="hidePassword = !hidePassword"
          >
            {{ hidePassword ? "visibility_off" : "visibility" }}
          </mat-icon>
        </div>

        <div class="flex justify-center">
          <button
            class="btn bg-[#3596f7] hover:bg-[#0148a4] dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-md px-6 py-2 w-[420px] max-w-full font-medium transition-colors duration-200 shadow-md dark:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
            type="submit"
          >
            Ingresar
          </button>
        </div>
      </div>
    </form>
  </div>

  <div class="mt-5 text-center relative z-10">
    <p
      class="m-0 text-xs text-white dark:text-gray-300 text-shadow drop-shadow-lg"
    >
      © {{ currentYear }} Midas Solutions Center. Creado por Desarrolladores
      Midas
    </p>
  </div>
</section>
