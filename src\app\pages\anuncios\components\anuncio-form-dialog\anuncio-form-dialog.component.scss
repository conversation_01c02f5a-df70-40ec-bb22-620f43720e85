.anuncio-form-dialog {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 400px;
  max-height: 90vh;
  overflow: hidden;
}

mat-dialog-title {
  margin: 0;
  padding: 12px 16px;
  background-color: #f5f5f5;
  color: #333;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;

  @media (min-width: 600px) {
    padding: 16px 24px;
    font-size: 20px;
    text-align: left;
  }
}

mat-dialog-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  @media (min-width: 600px) {
    padding: 24px;
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: flex-start;
    gap: 24px;
  }
}

.image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    width: 35%;
    margin-bottom: 0;
  }
}

.image-container {
  width: 100%;
  height: 160px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f9f9f9;
  transition: all 0.3s ease;

  @media (min-width: 600px) {
    height: 180px;
  }

  @media (min-width: 768px) {
    height: 200px;
  }

  &.has-image {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .placeholder-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;

    mat-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
      margin-bottom: 4px;

      @media (min-width: 600px) {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 8px;
      }
    }
  }
}

.upload-button {
  width: 100%;
  max-width: 180px;
  height: 36px;
  font-size: 14px;

  @media (min-width: 600px) {
    max-width: 200px;
  }

  mat-icon {
    margin-right: 4px;
  }
}

.form-fields {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 600px) {
    gap: 16px;
  }
}

.main-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 600px) {
    gap: 16px;
  }
}

.date-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 480px) {
    flex-direction: row;
    flex-wrap: wrap;

    mat-form-field {
      flex: 1;
      min-width: 140px;
    }
  }
}

.additional-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 480px) {
    flex-direction: row;
    flex-wrap: wrap;

    mat-form-field {
      flex: 1;
      min-width: 120px;
    }
  }
}

.full-width {
  width: 100%;
}

.sede-warning {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 4px;
  padding: 8px 12px;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: #f57c00;

  mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    margin-top: 2px;
  }

  span {
    flex: 1;
    line-height: 1.4;
  }
}

mat-form-field {
  font-size: 14px;

  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0.75em;
  }

  ::ng-deep .mat-form-field-infix {
    padding: 0.5em 0;
  }

  ::ng-deep .mat-form-field-label-wrapper {
    top: -1em;
  }
}

mat-dialog-actions {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  margin: 0;
  gap: 8px;

  @media (min-width: 600px) {
    padding: 16px 24px;
    gap: 12px;
  }

  button {
    min-width: 80px;

    @media (min-width: 600px) {
      min-width: 100px;
    }
  }
}

// Estilos para el tema oscuro
:host-context(.dark-theme) {
  mat-dialog-title {
    background-color: #0a1628;
    color: #ffffff;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .image-container {
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.05);

    &.has-image {
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .placeholder-image {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  mat-dialog-actions {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
