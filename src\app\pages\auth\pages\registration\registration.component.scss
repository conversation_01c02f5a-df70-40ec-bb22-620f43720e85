mat-form-field {
  width: 300px;
}

.upload-card {
  width: 450px;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 20px;
}

.form-container {
  margin-top: 15px;
}

.file-name {
  margin: 10px 0;
  font-weight: 500;
  color: #3f51b5;
}

.success-message {
  color: #4caf50;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.error-message {
  color: #f44336;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.success-message mat-icon, .error-message mat-icon {
  margin-right: 8px;
}

.format-info {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 15px;
  text-align: left;
  border-left: 4px solid #3f51b5;
}

.format-info h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #3f51b5;
}

.format-info ol {
  margin-top: 5px;
  margin-bottom: 5px;
  padding-left: 25px;
}

.format-info li {
  margin-bottom: 3px;
}

.format-info p {
  margin-top: 8px;
  margin-bottom: 0;
}

/* Estilos para tema oscuro */
:host-context(.dark-theme) {
  .upload-card {
    background-color: #2d2d2d;
    color: #e0e0e0;
  }

  .format-info {
    background-color: #3d3d3d;
    border-left-color: #7986cb;
  }

  .format-info h4 {
    color: #7986cb;
  }

  .file-name {
    color: #7986cb;
  }
}
