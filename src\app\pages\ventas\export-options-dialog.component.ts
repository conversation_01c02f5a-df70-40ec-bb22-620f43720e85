import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-export-options-dialog',
  templateUrl: './export-options-dialog.component.html',
  styleUrls: ['./export-options-dialog.component.scss']
})
export class ExportOptionsDialogComponent {
  exportForm: FormGroup;
  meses = [
    { value: 0, nombre: 'Enero' },
    { value: 1, nombre: 'Febrero' },
    { value: 2, nombre: 'Marzo' },
    { value: 3, nombre: 'Abril' },
    { value: 4, nombre: 'Mayo' },
    { value: 5, nombre: 'Junio' },
    { value: 6, nombre: 'Julio' },
    { value: 7, nombre: 'Agosto' },
    { value: 8, nombre: 'Septiembre' },
    { value: 9, nombre: 'Octubre' },
    { value: 10, nombre: 'Noviembre' },
    { value: 11, nombre: 'Diciembre' }
  ];
  
  anioActual = new Date().getFullYear();
  
  constructor(
    private dialogRef: MatDialogRef<ExportOptionsDialogComponent>,
    private fb: FormBuilder
  ) {
    this.exportForm = this.fb.group({
      tipoReporte: ['dia', Validators.required],
      fecha: [null, Validators.required],
      mes: [null],
      anio: [this.anioActual]
    });
    
    // Actualizar validadores cuando cambia el tipo de reporte
    this.exportForm.get('tipoReporte')?.valueChanges.subscribe(value => {
      if (value === 'dia') {
        this.exportForm.get('fecha')?.setValidators([Validators.required]);
        this.exportForm.get('mes')?.clearValidators();
      } else {
        this.exportForm.get('fecha')?.clearValidators();
        this.exportForm.get('mes')?.setValidators([Validators.required]);
      }
      this.exportForm.get('fecha')?.updateValueAndValidity();
      this.exportForm.get('mes')?.updateValueAndValidity();
    });
  }

  monthSelected(date: Date, datepicker: any): void {
    this.exportForm.patchValue({ fecha: date });
    datepicker.close();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onExport(): void {
    if (this.exportForm.valid) {
      const formValue = this.exportForm.value;
      
      // Si es reporte por mes, crear una fecha con el mes y año seleccionados
      if (formValue.tipoReporte === 'mes' && formValue.mes !== null) {
        const fecha = new Date(formValue.anio, formValue.mes, 1);
        formValue.fecha = fecha;
      }
      
      this.dialogRef.close(formValue);
    }
  }
}