/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,ts}"],
  darkMode: ["class", '[class*="dark-theme"]'], // Detectar tanto 'dark' como 'dark-theme'
  theme: {
    extend: {
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
      },
      colors: {
        // Colores personalizados para el tema claro
        light: {
          background: "#EEF1F9",
          text: "#000000",
          primary: "#0277bd",
          secondary: "#f44336",
        },
        // Colores personalizados para el tema oscuro
        dark: {
          background: "#0e1c33",
          text: "#ffffff",
          primary: "#1e4976",
          secondary: "#ff795d",
        },
      },
      // Animaciones personalizadas
      animation: {
        fadeIn: "fadeIn 0.5s ease-in-out",
        bounce: "bounce 1s infinite",
        "pulse-subtle": "pulseSubtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        bounce: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-5px)" },
        },
        pulseSubtle: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.85" },
        },
      },
    },
  },
  plugins: [],
};
