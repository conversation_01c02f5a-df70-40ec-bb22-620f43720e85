<div class="p-5 w-full max-w-full ">
  <!-- Encabezado -->
  <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 bg-white dark:bg-gray-900 shadow-md rounded-lg transition-all p-5">
    <div>
      <h2 class="text-xl font-semibold text-blue-600">Gráficos de Ventas</h2>
    </div>
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
      <button (click)="abrirModalMetas()"
        class="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow">
        <mat-icon>add_chart</mat-icon>
        Registrar Meta
      </button>
      <button routerLink="/ventas"
        class="flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium transition shadow">
        <mat-icon>arrow_back</mat-icon>
        Volver a Ventas
      </button>
    </div>
  </div>

  <!-- Filtros de Fecha -->
  <div class="flex flex-col gap-4 mb-6 bg-white dark:bg-gray-900 shadow-md rounded-lg transition-all p-5 w-full">
    <div class="w-full flex justify-between items-center p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
      <span class="font-medium">Filtros de Fecha</span>
    </div>

    <form [formGroup]="filterForm" class="flex flex-col gap-4 p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Fecha Inicio -->
        <div class="flex flex-col w-full gap-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Inicio</label>
          <input type="date" formControlName="fechaInicio"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            placeholder="Seleccione fecha inicio">
        </div>

        <!-- Fecha Fin -->
        <div class="flex flex-col w-full gap-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Fin</label>
          <input type="date" formControlName="fechaFin"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            placeholder="Seleccione fecha fin">
        </div>
      </div>

      <!-- Botones de acción -->
      <div class="flex justify-end gap-3 mt-4">
        <button type="button" (click)="resetearFiltros()"
          class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-md transition-colors">
          Resetear
        </button>
        <button type="button" (click)="aplicarFiltros()"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md transition-colors">
          Aplicar Filtros
        </button>
      </div>
    </form>
  </div>

  <!-- Área de Gráficos -->
  <div class="bg-white dark:bg-gray-900 shadow-md rounded-lg transition-all p-5 w-full">
    <!-- Spinner de carga -->
    <div *ngIf="loading" class="flex flex-col justify-center items-center h-[400px]">
      <mat-spinner diameter="50"></mat-spinner>
      <p class="mt-4 text-gray-600 dark:text-gray-400">Cargando datos del período seleccionado...</p>
      <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">Esto puede tomar unos momentos</p>
    </div>

    <!-- Contenedor de los gráficos -->
    <div *ngIf="!loading" class="flex flex-col gap-6">

      <!-- Contenedor para gráficos de barras -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Gráfico por Asesor -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm h-[350px]">
          <h3 class="text-center font-medium text-gray-700 dark:text-gray-300 mb-2">Top Asesores por Puntos</h3>
          <div class="h-[300px]">
            <canvas #chartAsesores></canvas>
          </div>
        </div>

        <!-- Gráfico por Coordinador -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm h-[350px]">
          <h3 class="text-center font-medium text-gray-700 dark:text-gray-300 mb-2">Top Coordinadores por Puntos</h3>
          <div class="h-[300px]">
            <canvas #chartCoordinadores></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Listado de Asesores -->
<div *ngIf="mostrarModalAsesores" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] flex flex-col">
    <!-- Cabecera del modal -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Listado de Coodinadores</h3>
      <button (click)="cerrarModalAsesores()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Contenido del modal - Listado de asesores -->
    <div class="p-4 overflow-auto flex-grow">
      <!-- Spinner de carga -->
      <div *ngIf="loadingAsesores" class="flex justify-center items-center h-40">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <!-- Mensaje si no hay asesores -->
      <div *ngIf="!loadingAsesores && (!asesores || asesores.length === 0)" class="text-center py-8 text-gray-500">
        <mat-icon class="text-4xl mb-2">person_off</mat-icon>
        <p>No se encontraron asesores.</p>
      </div>

      <!-- Buscador -->
      <div *ngIf="!loadingAsesores && asesores && asesores.length > 0" class="mb-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <mat-icon class="text-gray-500 dark:text-gray-400">search</mat-icon>
          </div>
          <input
            type="text"
            [value]="terminoBusqueda"
            (input)="buscarAsesores($event)"
            class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="Buscar por nombre, apellido, DNI o ciudad...">
        </div>
      </div>

      <!-- Tabla de asesores -->
      <div *ngIf="!loadingAsesores && asesores && asesores.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nombre</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">DNI</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ciudad</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Acciones</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let asesor of asesoresPaginados" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ asesor.codi_usuario }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ asesor.nombre }} {{ asesor.apellido }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ asesor.dni }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ asesor.nombreSede }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <button (click)="verMetasAsesor(asesor)" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button (click)="registrarMetaAsesor(asesor)" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
                  <mat-icon>add_circle</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Mensaje si no hay resultados de búsqueda -->
        <div *ngIf="asesoresFiltrados.length === 0" class="text-center py-8 text-gray-500">
          <mat-icon class="text-4xl mb-2">search_off</mat-icon>
          <p>No se encontraron asesores que coincidan con la búsqueda.</p>
        </div>

        <!-- Paginación -->
        <div *ngIf="asesoresFiltrados.length > 0" class="flex justify-between items-center mt-4">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Mostrando <span class="font-medium">{{ ((paginaActual - 1) * elementosPorPagina) + 1 }}</span> a
            <span class="font-medium">{{ Math.min(paginaActual * elementosPorPagina, asesoresFiltrados.length) }}</span> de
            <span class="font-medium">{{ asesoresFiltrados.length }}</span> asesores
          </div>

          <div class="flex space-x-1">
            <!-- Botón Anterior -->
            <button
              (click)="cambiarPagina(paginaActual - 1)"
              [disabled]="paginaActual === 1"
              class="px-3 py-1 rounded-md text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
              <mat-icon class="text-base">chevron_left</mat-icon>
            </button>

            <!-- Números de página -->
            <button
              *ngFor="let pagina of paginas"
              (click)="cambiarPagina(pagina)"
              [class.bg-blue-600]="pagina === paginaActual"
              [class.text-white]="pagina === paginaActual"
              [class.hover:bg-blue-700]="pagina === paginaActual"
              [class.bg-gray-200]="pagina !== paginaActual"
              [class.dark:bg-gray-700]="pagina !== paginaActual"
              [class.hover:bg-gray-300]="pagina !== paginaActual"
              [class.dark:hover:bg-gray-600]="pagina !== paginaActual"
              class="px-3 py-1 rounded-md text-sm font-medium">
              {{ pagina }}
            </button>

            <!-- Botón Siguiente -->
            <button
              (click)="cambiarPagina(paginaActual + 1)"
              [disabled]="paginaActual === totalPaginas"
              class="px-3 py-1 rounded-md text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
              <mat-icon class="text-base">chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Metas de un Asesor -->
<div *ngIf="mostrarModalMetas" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] flex flex-col">
    <!-- Cabecera del modal -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
        Metas de {{ asesorSeleccionado?.nombre }} {{ asesorSeleccionado?.apellido }}
      </h3>
      <button (click)="cerrarModalMetas()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Contenido del modal - Listado de metas -->
    <div class="p-4 overflow-auto flex-grow">
      <!-- Spinner de carga -->
      <div *ngIf="loadingMetas" class="flex justify-center items-center h-40">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <!-- Mensaje si no hay metas -->
      <div *ngIf="!loadingMetas && (!metas || metas.length === 0)" class="text-center py-8 text-gray-500">
        <mat-icon class="text-4xl mb-2">assignment_late</mat-icon>
        <p>No se encontraron metas para este asesor.</p>
      </div>

      <!-- Tabla de metas -->
      <div *ngIf="!loadingMetas && metas && metas.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Fecha Inicio</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Fecha Fin</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Meta</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Progreso</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Acciones</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let meta of metas"
              [class.bg-green-50]="esMetaActiva(meta)"
              [class.dark:bg-green-900]="esMetaActiva(meta)"
              [class.border-l-4]="esMetaActiva(meta)"
              [class.border-green-500]="esMetaActiva(meta)"
              class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ meta.id }}
                <span *ngIf="esMetaActiva(meta)" class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                  Activa
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ meta.fecha_inicio | date:'dd/MM/yyyy' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ meta.fecha_fin | date:'dd/MM/yyyy' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ meta.meta }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div [class.bg-blue-600]="!esMetaActiva(meta)"
                       [class.bg-green-600]="esMetaActiva(meta)"
                       class="h-2.5 rounded-full"
                       [style.width]="Math.min(100, (meta.resultado / meta.meta * 100)) + '%'"></div>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 block">
                  {{ meta.resultado }} / {{ meta.meta }} ({{ (meta.resultado / meta.meta * 100) | number:'1.0-0' }}%)
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <button (click)="eliminarMeta(meta)" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Registrar Meta -->
<div *ngIf="mostrarModalRegistroMeta" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md flex flex-col">
    <!-- Cabecera del modal -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
        Registrar Meta para {{ asesorSeleccionado?.nombre }} {{ asesorSeleccionado?.apellido }}
      </h3>
      <button (click)="cerrarModalRegistroMeta()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Contenido del modal - Formulario de registro -->
    <form [formGroup]="metaForm" (ngSubmit)="guardarMeta()" class="p-4">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Inicio</label>
        <input type="date" formControlName="fechaInicio"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
        <div *ngIf="metaForm.get('fechaInicio')?.invalid && metaForm.get('fechaInicio')?.touched" class="text-red-500 text-xs mt-1">
          La fecha de inicio es requerida
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Fin</label>
        <input type="date" formControlName="fechaFin"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
        <div *ngIf="metaForm.get('fechaFin')?.invalid && metaForm.get('fechaFin')?.touched" class="text-red-500 text-xs mt-1">
          La fecha de fin es requerida
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Meta (cantidad)</label>
        <input type="number" formControlName="cantidadMeta"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
        <div *ngIf="metaForm.get('cantidadMeta')?.invalid && metaForm.get('cantidadMeta')?.touched" class="text-red-500 text-xs mt-1">
          <span *ngIf="metaForm.get('cantidadMeta')?.errors?.['required']">La cantidad de meta es requerida</span>
          <span *ngIf="metaForm.get('cantidadMeta')?.errors?.['min']">La cantidad debe ser mayor a 0</span>
        </div>
      </div>

      <div class="flex justify-end gap-3 mt-6">
        <button type="button" (click)="cerrarModalRegistroMeta()"
          class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-md transition-colors">
          Cancelar
        </button>
        <button type="submit" [disabled]="metaForm.invalid || guardandoMeta"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">
          <mat-spinner *ngIf="guardandoMeta" diameter="20" class="!w-5 !h-5"></mat-spinner>
          <span>Guardar</span>
        </button>
      </div>
    </form>
  </div>
</div>