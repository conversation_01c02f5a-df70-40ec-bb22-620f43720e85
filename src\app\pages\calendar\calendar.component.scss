.app-calendar {
  height: 100%;
  width: 100%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;

  ::ng-deep {
    // Ocultar la barra de herramientas original
    .fc-header-toolbar {
      display: none !important;
    }
    .fc-toolbar-title {
      font-size: 1.25rem;
      margin: 0 0 0.5rem 0;
    }

    .fc-toolbar {
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      gap: 0.5rem;
      padding: 0 0.5rem;
    }

    .fc-button-primary {
      background-color: #3699ff;
      border-color: #3699ff;

      &:hover {
        background-color: #1e88e5;
        border-color: #1e88e5;
      }

      &:not(:disabled):active,
      &:not(:disabled).fc-button-active {
        background-color: #1565c0;
        border-color: #1565c0;
      }
    }

    .fc-event {
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 0.85em;

      .fc-event-title {
        font-weight: 500;
      }

      .fc-event-time {
        font-weight: 500;
        padding-right: 3px;
      }
    }

    /* Asegurarse de que los eventos tengan buen contraste */
    .fc-event.bg-primary,
    .fc-event.bg-info,
    .fc-event.bg-dark {
      color: #ffffff !important;
      .fc-event-title,
      .fc-event-time {
        color: #ffffff !important;
      }
    }

    .fc-event.bg-warning,
    .fc-event.bg-success,
    .fc-event.bg-light {
      color: #212529 !important;
      .fc-event-title,
      .fc-event-time {
        color: #212529 !important;
      }
    }

    .fc-daygrid-day-number {
      font-weight: 500;
      padding: 8px;
    }

    .fc-col-header-cell {
      font-weight: 600;
      padding: 10px 0;
    }

    .fc-timegrid-slot {
      height: 40px;
    }

    .fc-view-harness {
      margin: 0 1rem 1rem;
    }

    .fc-scrollgrid {
      border-radius: 0.5rem;
      overflow: hidden;
    }

    .fc-day {
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(54, 153, 255, 0.05);
      }
    }
  }
}

/* Custom color classes */
.bg-purple {
  background-color: #6f42c1 !important;
  color: #fff !important;
}

/* Estilos para los colores de eventos del calendario */
::ng-deep {
  /* Colores para los eventos del calendario */
  .fc-event.bg-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
  }

  .fc-event.bg-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
  }

  .fc-event.bg-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
  }

  .fc-event.bg-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
  }

  .fc-event.bg-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
  }

  .fc-event.bg-purple {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
  }

  .fc-event.bg-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
  }

  .fc-event.bg-dark {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
  }
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  .app-calendar {
    ::ng-deep {
      .fc-theme-standard .fc-scrollgrid,
      .fc-theme-standard td,
      .fc-theme-standard th {
        border-color: rgba(255, 255, 255, 0.1);
      }

      .fc-day {
        &:hover {
          background-color: rgba(54, 153, 255, 0.15);
        }
      }

      .fc-col-header-cell,
      .fc-daygrid-day-number,
      .fc-toolbar-title {
        color: #ffffff;
      }

      .fc-button-primary {
        background-color: #1e4976;
        border-color: #1e4976;

        &:hover {
          background-color: #26afe5;
          border-color: #26afe5;
        }
      }

      /* Colores para los eventos del calendario en tema oscuro */
      .fc-event.bg-primary {
        background-color: #1e88e5 !important;
        border-color: #1e88e5 !important;
      }

      .fc-event.bg-success {
        background-color: #43a047 !important;
        border-color: #43a047 !important;
      }

      .fc-event.bg-danger {
        background-color: #e53935 !important;
        border-color: #e53935 !important;
      }

      .fc-event.bg-warning {
        background-color: #ffb300 !important;
        border-color: #ffb300 !important;
      }

      .fc-event.bg-info {
        background-color: #26c6da !important;
        border-color: #26c6da !important;
      }

      .fc-event.bg-purple {
        background-color: #8e24aa !important;
        border-color: #8e24aa !important;
      }

      .fc-event.bg-secondary {
        background-color: #78909c !important;
        border-color: #78909c !important;
      }

      .fc-event.bg-dark {
        background-color: #455a64 !important;
        border-color: #455a64 !important;
      }
    }
  }

  .card {
    background-color: var(--sidenav-dark);
    border: none;
  }

  .card-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .card-title {
    color: #ffffff;
  }
}

.page-title {
  margin-bottom: 1.5rem;

  h2 {
    margin-bottom: 0.5rem;
  }

  .breadcrumb {
    padding: 0;
    margin: 0;
    background: transparent;
  }
}

::ng-deep .modal-dialog {
  display: flex !important;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

::ng-deep .modal-content {
  width: 100%;
}

::ng-deep .modal.show .modal-dialog {
  transform: none;
  max-width: 500px;
  margin: 1.75rem auto;
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  padding: 1rem;
  background-color: #fff;

  .modal-title {
    font-weight: 500;
    font-size: 1.1rem;
  }

  .btn-close {
    background-size: 0.8em;
    opacity: 0.5;
    &:hover {
      opacity: 0.75;
    }
  }
}

.modal-body {
  padding: 1rem;

  .form-label {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .form-control,
  .form-select {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    font-size: 0.9rem;

    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #adb5bd;
      opacity: 1;
    }
  }

  textarea.form-control {
    min-height: 60px;
  }

  .date-picker-group,
  .time-picker-group {
    .input-group-text {
      background-color: #f8f9fa;
      border: 1px solid #ced4da;
      border-left: none;
      padding: 0.375rem 0.75rem;
    }

    input[type="date"],
    input[type="time"] {
      border-right: none;
    }
  }
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;

  .btn {
    padding: 0.375rem 0.75rem;
    font-weight: 400;
    font-size: 0.9rem;
    border-radius: 0.25rem;

    &.btn-light {
      background-color: #f8f9fa;
      border-color: #f8f9fa;
      color: #212529;

      &:hover {
        background-color: #e2e6ea;
        border-color: #dae0e5;
      }
    }

    &.btn-success {
      background-color: #28a745;
      border-color: #28a745;

      &:hover {
        background-color: #218838;
        border-color: #1e7e34;
      }
    }

    &.btn-primary {
      background-color: #007bff;
      border-color: #007bff;

      &:hover {
        background-color: #0069d9;
        border-color: #0062cc;
      }
    }

    &.btn-danger {
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #c82333;
        border-color: #bd2130;
      }
    }
  }
}
