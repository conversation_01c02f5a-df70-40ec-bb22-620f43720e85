import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-page-title',
  template: `
    <div class="page-title-container">
      <h2 class="page-title">{{ title }}</h2>
      <div class="breadcrumb" *ngIf="breadcrumbItems && breadcrumbItems.length > 0">
        <span *ngFor="let item of breadcrumbItems; let i = index">
          <span *ngIf="i > 0"> / </span>
          <span [class.active]="item.active">{{ item.label }}</span>
        </span>
      </div>
    </div>
  `,
  styles: [`
    .page-title-container {
      margin-bottom: 1.5rem;
    }
    .page-title {
      font-size: 1.5rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
    .breadcrumb {
      font-size: 0.875rem;
      color: #6c757d;
    }
    .active {
      color: #2E74BB;
      font-weight: 500;
    }
  `]
})
export class PageTitleComponent {
  @Input() title: string = '';
  @Input() breadcrumbItems: Array<{label: string, active?: boolean}> = [];
}
