<div class="flex flex-col h-full max-h-[80vh] overflow-hidden">
  <!-- Header -->
  <div class="flex flex-col md:flex-row justify-between items-start md:items-center px-6 py-4 border-b border-gray-200 bg-gray-50 gap-3 md:gap-0">
    <div class="flex-1">
      <h2 mat-dialog-title class="m-0 text-2xl font-medium text-gray-800">{{ data.title || 'Explorador de Google Drive' }}</h2>
      <p class="mt-1 mb-0 text-sm text-gray-600" *ngIf="data.cliente">
        Cliente: {{ data.cliente.nombres }} {{ data.cliente.apellidos }}
        <span *ngIf="data.numeroMovil"> - {{ data.numeroMovil }}</span>
      </p>
      <div *ngIf="hasUploadedFiles && data.mode === 'upload'" class="flex items-center mt-2 px-3 py-2 bg-green-50 border border-green-500 rounded text-green-800 text-sm font-medium">
        <mat-icon class="mr-2 text-green-500 text-lg w-[18px] h-[18px]">check_circle</mat-icon>
        <span>{{ uploadedFilesCount }} archivo(s) subido(s) exitosamente</span>
      </div>
    </div>

    <div class="flex items-center gap-2 w-full md:w-auto justify-between md:justify-end">
      <!-- Botón de crear carpeta -->
      <button mat-icon-button
              matTooltip="Crear carpeta"
              (click)="createFolder()"
              [disabled]="loading">
        <mat-icon>create_new_folder</mat-icon>
      </button>

      <!-- Botón de subir archivos -->
      <button mat-icon-button
              matTooltip="Subir archivos"
              (click)="openFileUpload()"
              [disabled]="loading || uploadLoading">
        <mat-icon>cloud_upload</mat-icon>
      </button>

      <!-- Toggle de vista -->
      <mat-button-toggle-group [(value)]="viewMode" class="ml-4">
        <mat-button-toggle value="grid" matTooltip="Vista de cuadrícula">
          <mat-icon>grid_view</mat-icon>
        </mat-button-toggle>
        <mat-button-toggle value="list" matTooltip="Vista de lista">
          <mat-icon>view_list</mat-icon>
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>
  </div>

  <!-- Breadcrumbs -->
  <div class="px-6 py-3 bg-gray-100 border-b border-gray-200">
    <nav aria-label="breadcrumb">
      <ol class="flex items-center m-0 p-0 list-none">
        <li class="flex items-center"
            *ngFor="let breadcrumb of breadcrumbs; let last = last">
          <a *ngIf="!last"
             (click)="navigateToBreadcrumb(breadcrumb)"
             class="flex items-center text-blue-600 no-underline cursor-pointer px-2 py-1 rounded transition-colors duration-200 hover:bg-blue-50">
            <mat-icon *ngIf="breadcrumb.id === ''" class="mr-1 text-lg">home</mat-icon>
            {{ breadcrumb.name }}
          </a>
          <span *ngIf="last" class="flex items-center text-gray-800 font-medium">
            <mat-icon *ngIf="breadcrumb.id === ''" class="mr-1 text-lg">home</mat-icon>
            {{ breadcrumb.name }}
          </span>
          <mat-icon *ngIf="!last" class="mx-2 text-gray-600 text-lg">chevron_right</mat-icon>
        </li>
      </ol>
    </nav>
  </div>

  <!-- Barra de búsqueda -->
  <div class="px-6 py-4 bg-white">
    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Buscar archivos y carpetas</mat-label>
      <input matInput
             #searchInput
             (input)="onSearchChange($event)"
             placeholder="Escriba para buscar...">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <!-- Área de contenido principal -->
  <div class="flex-1 overflow-auto relative px-6 py-4"
       [class.bg-blue-50]="isDragOver"
       (dragover)="onDragOver($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)">

    <!-- Indicador de carga -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center h-48 text-gray-600">
      <mat-spinner diameter="50"></mat-spinner>
      <p class="mt-4 text-sm">Cargando archivos...</p>
    </div>

    <!-- Indicador de subida -->
    <div *ngIf="uploadLoading" class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
      <div class="flex flex-col items-center text-gray-600">
        <mat-spinner diameter="40"></mat-spinner>
        <p class="mt-4 text-sm">Subiendo archivos...</p>
      </div>
    </div>

    <!-- Lista de archivos - Vista de cuadrícula -->
    <div *ngIf="!loading && viewMode === 'grid'" class="grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] md:grid-cols-[repeat(auto-fill,minmax(150px,1fr))] gap-4 md:gap-3">
      <div *ngFor="let file of files"
           class="flex flex-col p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 bg-white relative group hover:shadow-md hover:border-blue-500"
           [class.border-blue-500]="isFileSelected(file)"
           [class.bg-blue-50]="isFileSelected(file)"
           (click)="onFileClick(file)"
           (dblclick)="onFileDoubleClick(file)">

        <div class="flex justify-center mb-3">
          <mat-icon class="text-5xl w-12 h-12 text-gray-600"
                    [class.text-orange-500]="file.isFolder"
                    [class.text-green-500]="getFileIcon(file) === 'image'"
                    [class.text-red-500]="getFileIcon(file) === 'videocam' || getFileIcon(file) === 'picture_as_pdf'"
                    [class.text-purple-500]="getFileIcon(file) === 'audiotrack'"
                    [class.text-blue-500]="getFileIcon(file) === 'description'"
                    [class.text-green-500]="getFileIcon(file) === 'table_chart'"
                    [class.text-orange-500]="getFileIcon(file) === 'slideshow'"
                    [class.text-amber-700]="getFileIcon(file) === 'archive'">
            {{ getFileIcon(file) }}
          </mat-icon>
        </div>

        <div class="text-center">
          <div class="font-medium mb-2 break-words line-clamp-2" [title]="file.name">{{ file.name }}</div>
          <div class="text-xs text-gray-600" *ngIf="!file.isFolder">
            <span class="block">{{ formatFileSize(file.size || 0) }}</span>
            <span class="block mt-0.5">{{ formatDate(file.modifiedTime) }}</span>
          </div>
        </div>

        <!-- Menú de acciones -->
        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1" (click)="$event.stopPropagation()">
          <!-- Botón de transcripción para archivos de audio -->
          <button *ngIf="isAudioFile(file)"
                  mat-icon-button
                  (click)="openTranscriptionDialog(file)"
                  matTooltip="Transcribir audio con IA"
                  class="text-indigo-500 hover:bg-indigo-50 hover:text-indigo-600">
            <mat-icon class="animate-pulse">record_voice_over</mat-icon>
          </button>

          <button mat-icon-button [matMenuTriggerFor]="fileMenu">
            <mat-icon>more_vert</mat-icon>
          </button>

          <mat-menu #fileMenu="matMenu">
            <button *ngIf="isAudioFile(file)" mat-menu-item (click)="openTranscriptionDialog(file)">
              <mat-icon>record_voice_over</mat-icon>
              <span>Transcribir Audio</span>
            </button>
            <button mat-menu-item (click)="previewFile(file)" *ngIf="!file.isFolder">
              <mat-icon>visibility</mat-icon>
              <span>Ver</span>
            </button>
            <button mat-menu-item (click)="downloadFile(file)" *ngIf="!file.isFolder">
              <mat-icon>download</mat-icon>
              <span>Descargar</span>
            </button>
            <button mat-menu-item (click)="deleteFile(file)">
              <mat-icon>delete</mat-icon>
              <span>Eliminar</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </div>

    <!-- Lista de archivos - Vista de lista -->
    <div *ngIf="!loading && viewMode === 'list'">
      <table mat-table [dataSource]="files" class="w-full">

        <!-- Columna de selección -->
        <ng-container matColumnDef="select" *ngIf="data.mode === 'select'">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox></mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let file">
            <mat-checkbox [checked]="isFileSelected(file)"
                         (change)="toggleFileSelection(file)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Columna de nombre -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Nombre</th>
          <td mat-cell *matCellDef="let file"
              (click)="onFileClick(file)"
              (dblclick)="onFileDoubleClick(file)"
              class="cursor-pointer">
            <div class="flex items-center">
              <mat-icon class="mr-3 text-gray-600"
                        [class.text-orange-500]="file.isFolder || getFileIcon(file) === 'folder'"
                        [class.text-green-500]="getFileIcon(file) === 'image' || getFileIcon(file) === 'table_chart'"
                        [class.text-red-500]="getFileIcon(file) === 'videocam' || getFileIcon(file) === 'picture_as_pdf'"
                        [class.text-purple-500]="getFileIcon(file) === 'audiotrack'"
                        [class.text-blue-500]="getFileIcon(file) === 'description'"
                        [class.text-orange-500]="getFileIcon(file) === 'slideshow'"
                        [class.text-amber-700]="getFileIcon(file) === 'archive'">
                {{ getFileIcon(file) }}
              </mat-icon>
              <span class="font-medium">{{ file.name }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Columna de tamaño -->
        <ng-container matColumnDef="size">
          <th mat-header-cell *matHeaderCellDef>Tamaño</th>
          <td mat-cell *matCellDef="let file">
            {{ file.isFolder ? '-' : formatFileSize(file.size || 0) }}
          </td>
        </ng-container>

        <!-- Columna de fecha de modificación -->
        <ng-container matColumnDef="modified">
          <th mat-header-cell *matHeaderCellDef>Modificado</th>
          <td mat-cell *matCellDef="let file">
            {{ formatDate(file.modifiedTime) }}
          </td>
        </ng-container>

        <!-- Columna de acciones -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Acciones</th>
          <td mat-cell *matCellDef="let file">
            <!-- Botón de transcripción para archivos de audio -->
            <button *ngIf="isAudioFile(file)"
                    mat-icon-button
                    (click)="openTranscriptionDialog(file)"
                    matTooltip="Transcribir audio con IA"
                    class="text-indigo-500 mr-2 hover:bg-indigo-50 hover:text-indigo-600">
              <mat-icon class="animate-pulse">record_voice_over</mat-icon>
            </button>

            <button mat-icon-button [matMenuTriggerFor]="fileMenu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #fileMenu="matMenu">
              <button *ngIf="isAudioFile(file)" mat-menu-item (click)="openTranscriptionDialog(file)">
                <mat-icon>record_voice_over</mat-icon>
                <span>Transcribir Audio</span>
              </button>
              <button mat-menu-item (click)="previewFile(file)" *ngIf="!file.isFolder">
                <mat-icon>visibility</mat-icon>
                <span>Ver</span>
              </button>
              <button mat-menu-item (click)="downloadFile(file)" *ngIf="!file.isFolder">
                <mat-icon>download</mat-icon>
                <span>Descargar</span>
              </button>
              <button mat-menu-item (click)="deleteFile(file)">
                <mat-icon>delete</mat-icon>
                <span>Eliminar</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="getDisplayedColumns()"></tr>
        <tr mat-row *matRowDef="let row; columns: getDisplayedColumns();"
            [class.bg-blue-50]="isFileSelected(row)"></tr>
      </table>
    </div>

    <!-- Mensaje cuando no hay archivos -->
    <div *ngIf="!loading && files.length === 0" class="flex flex-col items-center justify-center h-72 text-gray-600 text-center">
      <mat-icon class="text-6xl w-16 h-16 mb-4 text-gray-300">folder_open</mat-icon>
      <h3 class="m-0 mb-2 font-medium" *ngIf="!currentFolderId">No hay carpetas disponibles</h3>
      <h3 class="m-0 mb-2 font-medium" *ngIf="currentFolderId">No hay archivos en esta carpeta</h3>
      <p class="my-1 text-sm" *ngIf="searchQuery">No se encontraron elementos que coincidan con "{{ searchQuery }}"</p>
      <p class="my-1 text-sm" *ngIf="!searchQuery && !currentFolderId">No se encontraron carpetas en Google Drive. Puedes crear una nueva carpeta.</p>
      <p class="my-1 text-sm" *ngIf="!searchQuery && currentFolderId">Esta carpeta está vacía. Puedes subir archivos o crear subcarpetas.</p>
    </div>

    <!-- Overlay de drag & drop -->
    <div *ngIf="isDragOver" class="absolute inset-0 bg-blue-50 border-2 border-dashed border-blue-500 flex items-center justify-center z-[5]">
      <div class="text-center text-blue-600">
        <mat-icon class="text-6xl w-16 h-16 mb-4">cloud_upload</mat-icon>
        <h3 class="m-0 mb-2 font-medium">Suelta los archivos aquí</h3>
        <p class="m-0 text-sm">Los archivos se subirán a la carpeta actual</p>
      </div>
    </div>
  </div>

  <!-- Controles de paginación -->
  <div *ngIf="!loading && files.length > 0" class="flex flex-col md:flex-row justify-between items-center md:items-center px-6 py-3 border-t border-gray-200 bg-gray-50 gap-2 md:gap-0">
    <div class="text-sm text-gray-600 font-medium text-center md:text-left">
      <span>Página {{ currentPage + 1 }}</span>
      <span *ngIf="totalPages > 0"> de {{ totalPages }}</span>
      <span *ngIf="totalItems > 0"> ({{ totalItems }} elementos total)</span>
    </div>

    <div class="flex gap-1">
      <button mat-icon-button
              [disabled]="!hasPreviousPage"
              [class.opacity-40]="!hasPreviousPage"
              (click)="goToFirstPage()"
              matTooltip="Primera página">
        <mat-icon>first_page</mat-icon>
      </button>

      <button mat-icon-button
              [disabled]="!hasPreviousPage"
              [class.opacity-40]="!hasPreviousPage"
              (click)="previousPage()"
              matTooltip="Página anterior">
        <mat-icon>chevron_left</mat-icon>
      </button>

      <button mat-icon-button
              [disabled]="!hasNextPage"
              [class.opacity-40]="!hasNextPage"
              (click)="nextPage()"
              matTooltip="Página siguiente">
        <mat-icon>chevron_right</mat-icon>
      </button>
    </div>
  </div>

  <!-- Footer con acciones -->
  <div class="flex flex-col md:flex-row justify-between items-start md:items-center px-6 py-4 border-t border-gray-200 bg-gray-50 gap-3 md:gap-0" mat-dialog-actions>
    <div class="text-sm text-gray-600">
      <span class="mr-4" *ngIf="selectedFiles.length > 0">
        {{ selectedFiles.length }} archivo(s) seleccionado(s)
      </span>
      <span *ngIf="files.length > 0">
        {{ files.length }} elemento(s) en esta página
        <span *ngIf="!currentFolderId" class="italic text-gray-500 ml-1">(carpetas)</span>
        <span *ngIf="currentFolderId" class="italic text-gray-500 ml-1">(archivos y carpetas)</span>
      </span>
    </div>

    <div class="flex gap-2 w-full md:w-auto justify-end">
      <button mat-button (click)="cancel()">Cancelar</button>

      <button *ngIf="data.mode === 'upload'"
              mat-raised-button
              color="accent"
              (click)="closeAfterUpload()"
              matTooltip="Cerrar después de subir archivos">
        Finalizar
      </button>

      <button *ngIf="data.mode === 'select'"
              mat-raised-button
              color="primary"
              [disabled]="selectedFiles.length === 0"
              (click)="selectAndClose()">
        Seleccionar ({{ selectedFiles.length }})
      </button>
    </div>
  </div>

  <!-- Input oculto para selección de archivos -->
  <input #fileInput
         type="file"
         multiple
         style="display: none"
         [accept]="data.allowedTypes?.join(',')"
         (change)="onFileSelected($event)">
</div>
