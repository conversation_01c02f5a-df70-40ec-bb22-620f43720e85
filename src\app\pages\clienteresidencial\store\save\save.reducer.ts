import { ClienteConUsuarioDTO, ClienteResidencial } from '@app/models/backend/clienteresidencial';
import * as ClienteActions from './save.actions';
import { createReducer, on, Action } from '@ngrx/store';

export interface ClienteState {
  clientes: ClienteConUsuarioDTO[];
  selectedCliente: ClienteResidencial | null;
  loading: boolean;
  error: any;
  currentPage: number;
  totalItems: number;
  totalPages: number;
}

export const initialState: ClienteState = {
  clientes: [],
  selectedCliente: null,
  loading: false,
  error: null,
  currentPage: 0,
  totalItems: 0,
  totalPages: 0
};

const _clienteReducer = createReducer(
  initialState,
  on(ClienteActions.loadClientes, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(ClienteActions.loadClientesSuccess, (state, { clientes, currentPage, totalItems, totalPages }) => {
    // Asegurarse de que todos los clientes sean instancias válidas de ClienteConUsuarioDTO
    const clientesValidados = clientes.map(cliente => {
      // Si ya es una instancia válida, devolverla tal cual
      if (typeof cliente.getFechaCreacionFormatted === 'function') {
        return cliente;
      }
      // Si no, convertirla usando fromJson
      return ClienteConUsuarioDTO.fromJson(cliente);
    });

    return {
      ...state,
      clientes: clientesValidados,
      currentPage,
      totalItems,
      totalPages,
      loading: false
    };
  }),
  on(ClienteActions.loadClientesFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  on(ClienteActions.loadClienteByMobileSuccess, (state, { cliente }) => ({
    ...state,
    selectedCliente: cliente,
    error: null
  })),
  on(ClienteActions.loadClienteByMobileFailure, (state, { error }) => ({
    ...state,
    selectedCliente: null,
    error
  })),
  // Add these handlers for update actions
  on(ClienteActions.updateClient, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(ClienteActions.updateClientSuccess, (state, { client }) => {
    // Crear una nueva lista de clientes con los objetos ClienteConUsuarioDTO correctos
    const updatedClientes = state.clientes.map(c => {
      if (c.numeroMovil === client.movilContacto) {
        // Crear un nuevo objeto con los datos actualizados
        const updatedData = {
          ...c,
          // Actualizar cualquier propiedad relevante desde client
          // Por ejemplo, si el nombre del asesor cambia:
          // asesor: client.usuario?.nombre ? `${client.usuario.nombre} ${client.usuario.apellido}` : c.asesor
        };
        // Usar el método fromJson para crear un objeto ClienteConUsuarioDTO válido
        return ClienteConUsuarioDTO.fromJson(updatedData);
      }
      return c;
    });

    return {
      ...state,
      // Update the selected cliente with the updated data
      selectedCliente: client,
      // Usar la lista actualizada de clientes
      clientes: updatedClientes,
      loading: false
    };
  }),
  on(ClienteActions.updateClientFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  on(ClienteActions.loadClienteDetalleSuccess, (state, { cliente }) => ({
    ...state,
    selectedCliente: cliente,
    loading: false,
  })),

  on(ClienteActions.loadClienteDetalleFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),

  on(ClienteActions.clearClienteError, (state) => ({
    ...state,
    error: null
  })),

);

export function clienteReducer(state: ClienteState | undefined, action: Action) {
  return _clienteReducer(state, action);
}