import { Component, OnInit } from '@angular/core';
import { ContactoService } from '../../services/contacto.service';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-landing-contact',
  templateUrl: './landing-contact.component.html',
  styleUrls: ['./landing-contact.component.scss'],
})
export class LandingContactComponent implements OnInit {
  searchText: string = '';
  fechaDesde: string = '';
  fechaHasta: string = '';
  contactos: any[] = [];
  contactosFiltrados: any[] = [];

  constructor(private contactoService: ContactoService) {}

  ngOnInit(): void {
    // Obtener fecha actual en zona horaria de Perú (UTC-5)
    const limaTime = new Date().toLocaleString('en-US', {
      timeZone: 'America/Lima',
    });

    const hoy = new Date(limaTime);
    const fechaFormateada = hoy.toISOString().split('T')[0]; // yyyy-MM-dd

    this.fechaDesde = fechaFormateada;
    this.fechaHasta = fechaFormateada;

    this.contactoService.getContactos().subscribe(
      (res) => {
        this.contactos = res;
        this.filtrarContactos(); // Mostrar filtrados desde el inicio
      },
      (error) => {
        console.error('❌ Error al obtener contactos:', error);
      }
    );
  }

  filtrarContactos(): void {
    if (!Array.isArray(this.contactos)) {
      console.warn('❌ contactos no es un arreglo válido');
      this.contactosFiltrados = [];
      return;
    }

    const search = this.searchText.toLowerCase();
    const desde = this.fechaDesde;
    const hasta = this.fechaHasta;

    this.contactosFiltrados = this.contactos.filter((c) => {
      const nombre = c.nombres?.toLowerCase() || '';
      const correo = c.correo?.toLowerCase() || '';
      const fecha = c.fecha_registro;

      const coincideTexto = nombre.includes(search) || correo.includes(search);

      const coincideRangoFecha =
        (!desde && !hasta) ||
        (fecha && (!desde || fecha >= desde) && (!hasta || fecha <= hasta));

      return coincideTexto && coincideRangoFecha;
    });
  }

  filtrarPorFecha(): void {
    this.filtrarContactos();
  }

  exportarExcel(): void {
    const worksheet = XLSX.utils.json_to_sheet(this.contactosFiltrados);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Contactos');
    XLSX.writeFile(
      workbook,
      `contactos_${this.fechaDesde}_a_${this.fechaHasta}.xlsx`
    );
  }
}
