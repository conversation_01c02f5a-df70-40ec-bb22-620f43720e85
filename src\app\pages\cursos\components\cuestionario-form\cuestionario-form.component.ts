import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CuestionarioService } from '@app/services/cuestionario.service';
import { PreguntaService } from '@app/services/pregunta.service';
import { RespuestaService } from '@app/services/respuesta.service';
import { NotificationService } from '@app/services/notification/notification.service';
import { Cuestionario, CuestionarioCreateRequest, CuestionarioUpdateRequest } from '@app/models/backend/curso/cuestionario.model';
import { Pregunta, PreguntaCreateRequest, TipoPregunta } from '@app/models/backend/curso/pregunta.model';
import { RespuestaCreateRequest } from '@app/models/backend/curso/respuesta.model';

@Component({
  selector: 'app-cuestionario-form',
  templateUrl: './cuestionario-form.component.html',
  styleUrls: ['./cuestionario-form.component.scss']
})
export class CuestionarioFormComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  preguntasForm!: FormArray;
  loading = false;
  error: string | null = null;
  isEditMode = false;
  cuestionario: Cuestionario | null = null;

  // Enum para acceder desde el template
  TipoPregunta = TipoPregunta;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CuestionarioFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { leccionId: number, cuestionarioId?: number },
    private cuestionarioService: CuestionarioService,
    private preguntaService: PreguntaService,
    private respuestaService: RespuestaService,
    private notification: NotificationService
  ) {}

  ngOnInit(): void {
    this.initForm();

    if (this.data.cuestionarioId) {
      this.isEditMode = true;
      this.loadCuestionario();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.group({
      titulo: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', [Validators.maxLength(500)]],
      tiempoLimite: [30, [Validators.required, Validators.min(1)]],
      puntajeAprobacion: [70, [Validators.required, Validators.min(1), Validators.max(100)]],
      intentosMaximos: [3, [Validators.required, Validators.min(1)]],
      mostrarRespuestas: [true],
      aleatorizarPreguntas: [false],
      preguntas: this.fb.array([])
    });

    this.preguntasForm = this.form.get('preguntas') as FormArray;

    // Añadir al menos una pregunta por defecto
    this.addPregunta();
  }

  private loadCuestionario(): void {
    this.loading = true;

    this.cuestionarioService.getCuestionarioById(this.data.cuestionarioId!)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.cuestionario = response.data;
            this.populateForm();
          } else {
            this.error = response.msg || 'Error al cargar el cuestionario';
            this.notification.error(this.error);
          }
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Error al cargar el cuestionario. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al cargar cuestionario:', error);
        }
      });
  }

  private populateForm(): void {
    if (!this.cuestionario) return;

    this.form.patchValue({
      titulo: this.cuestionario.titulo,
      descripcion: this.cuestionario.descripcion,
      tiempoLimite: this.cuestionario.tiempoLimite,
      puntajeAprobacion: this.cuestionario.puntajeAprobacion,
      intentosMaximos: this.cuestionario.intentosMaximos,
      mostrarRespuestas: this.cuestionario.mostrarRespuestas,
      aleatorizarPreguntas: this.cuestionario.aleatorizarPreguntas
    });

    // Limpiar el array de preguntas
    while (this.preguntasForm.length) {
      this.preguntasForm.removeAt(0);
    }

    // Añadir las preguntas existentes
    if (this.cuestionario.preguntas && this.cuestionario.preguntas.length > 0) {
      this.cuestionario.preguntas.forEach(pregunta => {
        const preguntaGroup = this.createPreguntaGroup();
        preguntaGroup.patchValue({
          id: pregunta.id,
          enunciado: pregunta.enunciado,
          explicacion: pregunta.explicacion,
          puntaje: pregunta.puntaje,
          tipo: pregunta.tipo,
          orden: pregunta.orden
        });

        // Añadir las respuestas
        const respuestasArray = preguntaGroup.get('respuestas') as FormArray;

        // Limpiar el array de respuestas
        while (respuestasArray.length) {
          respuestasArray.removeAt(0);
        }

        // Añadir las respuestas existentes
        if (pregunta.respuestas && pregunta.respuestas.length > 0) {
          pregunta.respuestas.forEach((respuesta: any) => {
            const respuestaGroup = this.createRespuestaGroup();
            respuestaGroup.patchValue({
              id: respuesta.id,
              texto: respuesta.texto,
              esCorrecta: respuesta.esCorrecta,
              orden: respuesta.orden
            });
            respuestasArray.push(respuestaGroup);
          });
        }

        this.preguntasForm.push(preguntaGroup);
      });
    }
  }

  createPreguntaGroup(): FormGroup {
    return this.fb.group({
      id: [null],
      enunciado: ['', [Validators.required]],
      explicacion: [''],
      puntaje: [10, [Validators.required, Validators.min(1)]],
      tipo: [TipoPregunta.OPCION_MULTIPLE, [Validators.required]],
      orden: [this.preguntasForm ? this.preguntasForm.length + 1 : 1],
      respuestas: this.fb.array([
        this.createRespuestaGroup(),
        this.createRespuestaGroup()
      ])
    });
  }

  createRespuestaGroup(): FormGroup {
    return this.fb.group({
      id: [null],
      texto: ['', [Validators.required]],
      esCorrecta: [false],
      orden: [0]
    });
  }

  addPregunta(): void {
    this.preguntasForm.push(this.createPreguntaGroup());
  }

  removePregunta(index: number): void {
    this.preguntasForm.removeAt(index);
  }

  getRespuestas(preguntaIndex: number): FormArray {
    return this.preguntasForm.at(preguntaIndex).get('respuestas') as FormArray;
  }

  addRespuesta(preguntaIndex: number): void {
    const respuestas = this.getRespuestas(preguntaIndex);
    respuestas.push(this.createRespuestaGroup());
  }

  removeRespuesta(preguntaIndex: number, respuestaIndex: number): void {
    const respuestas = this.getRespuestas(preguntaIndex);
    respuestas.removeAt(respuestaIndex);
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.notification.error('Por favor, complete todos los campos requeridos');
      return;
    }

    this.loading = true;

    if (this.isEditMode) {
      this.updateCuestionario();
    } else {
      this.createCuestionario();
    }
  }

  private createCuestionario(): void {
    const formValue = this.form.value;

    const cuestionarioData: CuestionarioCreateRequest = {
      titulo: formValue.titulo,
      descripcion: formValue.descripcion,
      tiempoLimite: formValue.tiempoLimite,
      puntajeAprobacion: formValue.puntajeAprobacion,
      intentosMaximos: formValue.intentosMaximos,
      mostrarRespuestas: formValue.mostrarRespuestas,
      aleatorizarPreguntas: formValue.aleatorizarPreguntas,
      leccionId: this.data.leccionId
    };

    this.cuestionarioService.createCuestionario(cuestionarioData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            const cuestionarioId = response.data.id;
            this.createPreguntas(cuestionarioId);
          } else {
            this.loading = false;
            this.error = response.msg || 'Error al crear el cuestionario';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al crear el cuestionario. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al crear cuestionario:', error);
        }
      });
  }

  private updateCuestionario(): void {
    if (!this.cuestionario) return;

    const formValue = this.form.value;

    const cuestionarioData: CuestionarioUpdateRequest = {
      titulo: formValue.titulo,
      descripcion: formValue.descripcion,
      tiempoLimite: formValue.tiempoLimite,
      puntajeAprobacion: formValue.puntajeAprobacion,
      intentosMaximos: formValue.intentosMaximos,
      mostrarRespuestas: formValue.mostrarRespuestas,
      aleatorizarPreguntas: formValue.aleatorizarPreguntas
    };

    this.cuestionarioService.updateCuestionario(this.cuestionario.id, cuestionarioData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            // Actualizar las preguntas existentes y crear nuevas
            this.updatePreguntas();
          } else {
            this.loading = false;
            this.error = response.msg || 'Error al actualizar el cuestionario';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al actualizar el cuestionario. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al actualizar cuestionario:', error);
        }
      });
  }

  private createPreguntas(cuestionarioId: number): void {
    const preguntas = this.form.value.preguntas;
    let preguntasCreadas = 0;

    if (preguntas.length === 0) {
      this.finalizarCreacion();
      return;
    }

    preguntas.forEach((pregunta: any, index: number) => {
      const preguntaData: PreguntaCreateRequest = {
        enunciado: pregunta.enunciado,
        explicacion: pregunta.explicacion || '',
        puntaje: pregunta.puntaje,
        tipo: pregunta.tipo,
        orden: pregunta.orden || index + 1,
        cuestionarioId: cuestionarioId,
        respuestas: pregunta.respuestas.map((respuesta: any, respIndex: number) => ({
          texto: respuesta.texto,
          esCorrecta: respuesta.esCorrecta,
          orden: respuesta.orden || respIndex + 1,
          preguntaId: 0 // Se asignará en el backend
        }))
      };

      this.preguntaService.createPregunta(preguntaData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            preguntasCreadas++;

            if (preguntasCreadas === preguntas.length) {
              this.finalizarCreacion();
            }
          },
          error: (error) => {
            console.error('Error al crear pregunta:', error);
            preguntasCreadas++;

            if (preguntasCreadas === preguntas.length) {
              this.finalizarCreacion();
            }
          }
        });
    });
  }

  private updatePreguntas(): void {
    const preguntas = this.form.value.preguntas;
    let preguntasProcesadas = 0;

    if (preguntas.length === 0) {
      this.finalizarActualizacion();
      return;
    }

    preguntas.forEach((pregunta: any, index: number) => {
      if (pregunta.id) {
        // Actualizar pregunta existente
        const preguntaData = {
          enunciado: pregunta.enunciado,
          explicacion: pregunta.explicacion || '',
          puntaje: pregunta.puntaje,
          tipo: pregunta.tipo,
          orden: pregunta.orden || index + 1
        };

        this.preguntaService.updatePregunta(pregunta.id, preguntaData)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              // Actualizar respuestas
              this.updateRespuestas(pregunta);

              preguntasProcesadas++;
              if (preguntasProcesadas === preguntas.length) {
                this.finalizarActualizacion();
              }
            },
            error: (error) => {
              console.error('Error al actualizar pregunta:', error);
              preguntasProcesadas++;
              if (preguntasProcesadas === preguntas.length) {
                this.finalizarActualizacion();
              }
            }
          });
      } else {
        // Crear nueva pregunta
        const preguntaData: PreguntaCreateRequest = {
          enunciado: pregunta.enunciado,
          explicacion: pregunta.explicacion || '',
          puntaje: pregunta.puntaje,
          tipo: pregunta.tipo,
          orden: pregunta.orden || index + 1,
          cuestionarioId: this.cuestionario!.id,
          respuestas: pregunta.respuestas.map((respuesta: any, respIndex: number) => ({
            texto: respuesta.texto,
            esCorrecta: respuesta.esCorrecta,
            orden: respuesta.orden || respIndex + 1,
            preguntaId: 0 // Se asignará en el backend
          }))
        };

        this.preguntaService.createPregunta(preguntaData)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              preguntasProcesadas++;
              if (preguntasProcesadas === preguntas.length) {
                this.finalizarActualizacion();
              }
            },
            error: (error) => {
              console.error('Error al crear pregunta:', error);
              preguntasProcesadas++;
              if (preguntasProcesadas === preguntas.length) {
                this.finalizarActualizacion();
              }
            }
          });
      }
    });
  }

  private updateRespuestas(pregunta: any): void {
    // Implementación simplificada - en un caso real, deberías manejar la actualización y creación de respuestas
    // Este es un ejemplo básico
    console.log('Actualizando respuestas para la pregunta:', pregunta);
  }

  private finalizarCreacion(): void {
    this.loading = false;
    this.notification.success('Cuestionario creado exitosamente');
    this.dialogRef.close(true);
  }

  private finalizarActualizacion(): void {
    this.loading = false;
    this.notification.success('Cuestionario actualizado exitosamente');
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
