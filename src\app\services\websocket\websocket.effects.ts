import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { tap, map, mergeMap } from 'rxjs/operators';
import { EMPTY } from 'rxjs';
import { WebSocketService } from './WebSocketService';
import * as WebSocketActions from './websocket.actions';
import * as UsersActions from '../user/user.actions';

/**
 * Efectos para la gestión de WebSockets
 * Simplificado para manejar solo el estado de conexión de usuarios
 */
@Injectable()
export class WebSocketEffects {
  constructor(
    private actions$: Actions,
    private webSocketService: WebSocketService
  ) {}

  // Conectar WebSocket
  connectWebSocket$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WebSocketActions.connectWebSocket),
      tap(() => this.webSocketService.connect()),
      map(() => WebSocketActions.webSocketConnected())
    )
  );

  // Desconectar WebSocket
  disconnectWebSocket$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WebSocketActions.disconnectWebSocket),
      tap(() => this.webSocketService.disconnect()),
      map(() => WebSocketActions.webSocketDisconnected())
    )
  );

  // Manejar mensajes WebSocket
  handleWebSocketMessages$ = createEffect(() =>
    this.webSocketService.getMessages().pipe(
      map(message => {
        switch (message.type) {
          case 'USERS_STATUS_UPDATED':
            return UsersActions.usersStatusUpdatedWs({ usersStatus: message.payload });
          default:
            return WebSocketActions.webSocketMessageReceived({ messageType: message.type, messagePayload: message.payload });
        }
      })
    )
  );

  // Solicitar estado de usuarios por WebSocket
  requestUsersStatusWs$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UsersActions.requestUsersStatusWs),
      tap(() => this.webSocketService.requestUsersStatus()),
      mergeMap(() => EMPTY)
    ),
    { dispatch: false }
  );

  // Actualizar actividad del usuario
  updateUserActivity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UsersActions.updateUserActivity),
      tap(() => this.webSocketService.updateUserActivity()),
      mergeMap(() => EMPTY)
    ),
    { dispatch: false }
  );
}
