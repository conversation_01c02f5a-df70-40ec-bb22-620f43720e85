.notification-readers-modal {
  @apply bg-white dark:bg-gray-900 rounded-lg shadow-xl;
  min-width: 500px;
  max-width: 700px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-content {
  max-height: 60vh;
  overflow-y: auto;
}

/* Scrollbar personalizado */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.modal-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Animaciones */
.notification-readers-modal {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Hover effects para las filas de usuarios */
.notification-readers-modal .space-y-3 > div:hover {
  @apply bg-gray-100 dark:bg-gray-700 transition-colors duration-200;
}

/* Responsive */
@media (max-width: 640px) {
  .notification-readers-modal {
    min-width: 90vw;
    max-width: 90vw;
    margin: 1rem;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    @apply px-4;
  }
}
