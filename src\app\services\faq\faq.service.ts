import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, from, BehaviorSubject, throwError } from 'rxjs';
import { switchMap, map, shareReplay, catchError, take } from 'rxjs/operators';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { v4 as uuid } from 'uuid';

import { environment } from '@src/environments/environment';
import { Faq }  from '@app/models/backend/faq/faq.model';
import { FileFaq } from '@app/models/backend/faq/file-faq.model';
import { FaqRespuesta } from '@app/models/backend/faq/faq-respuesta.model';
import { WebSocketService } from '@app/services/websocket/WebSocketService';

@Injectable({ providedIn: 'root' })
export class FaqService {
  private readonly baseUrl = `${environment.url}api/faqs`;

  // Subjects para manejar actualizaciones en tiempo real
  private faqsUpdated = new BehaviorSubject<Faq[]>([]);
  private respuestasUpdated = new BehaviorSubject<{faqId: number, respuestas: FaqRespuesta[]}>({faqId: 0, respuestas: []});

  constructor(
    private http: HttpClient,
    private storage: AngularFireStorage,
    private webSocketService: WebSocketService
  ) {
    // Suscribirse a actualizaciones de FAQs por WebSocket
    this.subscribeToFaqsWebSocket();
  }

  /**
   * Suscribe a los eventos WebSocket relacionados con FAQs
   */
  private subscribeToFaqsWebSocket(): void {
    // Suscribirse a actualizaciones de FAQs
    this.webSocketService.getMessagesByType('FAQS_UPDATED')
      .subscribe(faqs => {

        if (Array.isArray(faqs)) {
          this.faqsUpdated.next(faqs);
        }
      });

    // Ya no nos suscribimos aquí a las respuestas porque ahora lo haremos dinámicamente
    // para cada FAQ específica cuando se soliciten sus respuestas
  }

  // Registro de suscripciones activas para evitar duplicados
  private activeSubscriptions: Set<number> = new Set();

  /**
   * Suscribe a actualizaciones de respuestas para una FAQ específica
   * @param faqId ID de la FAQ
   */
  private subscribeToFaqRespuestas(faqId: number): void {
    if (!faqId) return;

    // Verificar si ya estamos suscritos a este faqId
    if (this.activeSubscriptions.has(faqId)) {
      return;
    }

    // Verificar si el WebSocket está conectado
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();

      // Esperar a que se conecte antes de suscribirse
      setTimeout(() => this.subscribeToFaqRespuestas(faqId), 1000);
      return;
    }

    const topic = `/topic/faq-respuestas/${faqId}`;

    try {
      // Suscribirse al canal específico para esta FAQ
      this.webSocketService.subscribeToDynamicTopic(topic, `FAQ_RESPUESTAS_${faqId}`);

      // Escuchar mensajes de este canal específico
      this.webSocketService.getMessagesByDestination(topic)
        .subscribe({
          next: respuestas => {
            if (Array.isArray(respuestas)) {
              this.respuestasUpdated.next({
                faqId: faqId,
                respuestas: respuestas
              });
            }
          },
          error: () => {
            // Error al recibir respuestas
          }
        });

      // Registrar la suscripción como activa
      this.activeSubscriptions.add(faqId);
    } catch (error) {
      // Error al suscribirse a respuestas
    }
  }

  /**
   * Obtiene un Observable que emite cuando las FAQs se actualizan por WebSocket
   */
  getFaqsUpdates(): Observable<Faq[]> {
    return this.faqsUpdated.asObservable();
  }

  /**
   * Obtiene un Observable que emite cuando las respuestas de una FAQ se actualizan por WebSocket
   */
  getRespuestasUpdates(): Observable<{faqId: number, respuestas: FaqRespuesta[]}> {
    return this.respuestasUpdated.asObservable();
  }

  /* ---------- REST ---------- */
  getAll(): Observable<Faq[]> {
    return this.http.get<any>(this.baseUrl).pipe(
      map(response => {
        // Manejar la respuesta con formato {rpta, msg, data}
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1 && Array.isArray(response.data)) {
            return response.data;
          } else {

            return [];
          }
        }
        // Si es un array directamente
        return Array.isArray(response) ? response : [];
      })
    );
  }


  create(faq: Faq): Observable<Faq> {
    // Crear un nuevo objeto con los datos de la FAQ
    const newFaq = { ...faq };

    // Asegurarse de que se incluya el ID del usuario que crea la pregunta
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.id) {
          // En el backend, usuarioPregunta es un objeto User completo
          // Pero para la API, solo necesitamos enviar el ID
          newFaq.usuarioPreguntaId = user.id;

          // Estos campos son para mostrar en la UI, no se envían al backend
          newFaq.usuarioPreguntaNombre = `${user.nombre || ''} ${user.apellido || ''}`.trim();
        }
      } catch (error) {

      }
    }

    // Preparar el objeto para enviar al backend
    const faqToSend = {
      pregunta: newFaq.pregunta,
      respuesta: newFaq.respuesta || '',
      categoria: newFaq.categoria || null,
      esPublica: newFaq.esPublica !== undefined ? newFaq.esPublica : true,
      respondida: newFaq.respondida !== undefined ? newFaq.respondida : false,
      usuarioPreguntaId: newFaq.usuarioPreguntaId,
      archivos: newFaq.archivos || []
    };

    return this.http.post<any>(this.baseUrl, faqToSend).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1) {
            return response.data;
          } else {
            throw new Error(response.msg || 'Error al crear FAQ');
          }
        }
        return response;
      })
    );
  }

  update(id: number, faq: Faq): Observable<Faq> {
    // Crear un nuevo objeto con los datos de la FAQ
    const updatedFaq = { ...faq };

    // Verificar si estamos en modo respuesta (si hay respuesta pero no hay usuarioRespuestaId)
    const isResponseMode = updatedFaq.respuesta && updatedFaq.respuesta.trim() !== '' && !updatedFaq.usuarioRespuestaId;

    // Obtener datos del usuario actual
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.id) {
          // Si estamos en modo respuesta o si ya hay un usuarioRespuestaId, actualizarlo
          if (isResponseMode || updatedFaq.usuarioRespuestaId) {
            // Establecer el ID del usuario que responde
            updatedFaq.usuarioRespuestaId = user.id;

            // Establecer el nombre del usuario que responde
            const nombreCompleto = `${user.nombre || ''} ${user.apellido || ''}`.trim();
            updatedFaq.usuarioRespuestaNombre = nombreCompleto || user.username || 'Usuario ' + user.id;
          }
        }
      } catch (error) {
        // Error al parsear datos de usuario
      }
    }

    // Si la pregunta tiene respuesta, marcarla como respondida
    if (updatedFaq.respuesta && updatedFaq.respuesta.trim() !== '') {
      updatedFaq.respondida = true;
    }

    // Preparar el objeto para enviar al backend
    const faqToSend = {
      id: id,
      pregunta: updatedFaq.pregunta,
      respuesta: updatedFaq.respuesta || '',
      categoria: updatedFaq.categoria || null,
      esPublica: updatedFaq.esPublica !== undefined ? updatedFaq.esPublica : true,
      respondida: updatedFaq.respondida !== undefined ? updatedFaq.respondida : false,
      usuarioPreguntaId: updatedFaq.usuarioPreguntaId, // Mantener el ID del usuario que pregunta
      usuarioRespuestaId: updatedFaq.usuarioRespuestaId, // ID del usuario que responde
      archivos: updatedFaq.archivos || []
    };



    return this.http.put<any>(`${this.baseUrl}/${id}`, faqToSend).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1) {
            return response.data;
          } else {
            throw new Error(response.msg || 'Error al actualizar FAQ');
          }
        }
        return response;
      })
    );
  }

  remove(id: number): Observable<void> {
    return this.http.delete<any>(`${this.baseUrl}/${id}`).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response) {
          if (response.rpta !== 1) {
            throw new Error(response.msg || 'Error al eliminar FAQ');
          }
        }
        return;
      })
    );
  }
  incrementViews(id: number): Observable<Faq> {
    return this.http.patch<Faq>(`${this.baseUrl}/${id}/increment-views`, {});
  }

  // Método para cambiar el estado de una pregunta (abierta/cerrada)
  cambiarEstadoFaq(id: number, estado: 'ABIERTA' | 'CERRADA'): Observable<Faq> {
    // Verificar si el ID es válido
    if (!id || isNaN(id)) {
      return throwError(() => new Error('ID de pregunta inválido'));
    }

    // Verificar si el estado es válido
    if (estado !== 'ABIERTA' && estado !== 'CERRADA') {
      return throwError(() => new Error('Estado de pregunta inválido'));
    }

    // Usar solo WebSocket para cambiar el estado
    // Esto evita los errores HTTP que confunden al usuario
    if (this.webSocketService.isConnected()) {

      // Crear un observable para manejar la operación
      return new Observable<Faq>(observer => {
        try {
          // Enviar mensaje WebSocket para cambiar el estado
          this.webSocketService.sendMessage('/app/faqs.cambiar-estado', {
            faqId: id,
            estado: estado
          });

          // Obtener la FAQ actual para tener una referencia
          this.getById(id).pipe(take(1)).subscribe({
            next: (currentFaq) => {
              if (!currentFaq) {
                observer.error(new Error('No se pudo encontrar la pregunta'));
                return;
              }

              // Actualizar el estado localmente para una respuesta inmediata
              const updatedFaq = {...currentFaq, estado};

              // Actualizar la lista de FAQs para reflejar el cambio en la UI
              this.getAll().pipe(take(1)).subscribe(faqs => {
                const updatedFaqs = faqs.map(f => f.id === id ? updatedFaq : f);
                this.faqsUpdated.next(updatedFaqs);

                // Emitir la FAQ actualizada
                observer.next(updatedFaq);
                observer.complete();
              });

              // Establecer un timeout para verificar si el cambio se aplicó en el servidor
              setTimeout(() => {
                this.getById(id).pipe(take(1)).subscribe({
                  next: (serverFaq) => {
                    if (serverFaq && serverFaq.estado === estado) {
                      // El cambio se aplicó correctamente en el servidor

                      // Actualizar la lista de FAQs con los datos del servidor
                      this.getAll().pipe(take(1)).subscribe(faqs => {
                        this.faqsUpdated.next(faqs);
                      });
                    }
                  },
                  error: () => {
                    // No se pudo verificar el estado en el servidor
                  }
                });
              }, 2000);
            },
            error: () => {
              observer.error(new Error('No se pudo obtener la información de la pregunta'));
            }
          });
        } catch (error) {
          observer.error(new Error('Error al cambiar el estado de la pregunta'));
        }
      });
    } else {
      // Si el WebSocket no está conectado, intentar con HTTP como fallback
      return this.http.patch<any>(`${this.baseUrl}/${id}/estado/${estado}`, {}).pipe(
        map(response => {

          if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
            if (response.rpta === 1) {
              // Actualizar la lista de FAQs para reflejar el cambio en la UI
              this.getAll().pipe(take(1)).subscribe(faqs => {
                this.faqsUpdated.next(faqs);
              });

              return response.data;
            } else {
              throw new Error(response.msg || 'Error al cambiar estado de FAQ');
            }
          }

          return response;
        }),
        catchError(() => {
          return throwError(() => new Error('Error de conexión al servidor. Por favor, inténtelo de nuevo.'));
        })
      );
    }
  }

  // Método para obtener una FAQ por su ID
  getById(id: number): Observable<Faq> {
    return this.http.get<any>(`${this.baseUrl}/${id}`).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1) {
            return response.data;
          } else {
            throw new Error(response.msg || 'Error al obtener FAQ por ID');
          }
        }
        return response;
      }),
      catchError(() => {
        return throwError(() => new Error('No se pudo obtener la información de la pregunta'));
      })
    );
  }

  // Método para obtener preguntas por estado
  getPreguntasPorEstado(estado: 'ABIERTA' | 'CERRADA'): Observable<Faq[]> {
    return this.http.get<any>(`${this.baseUrl}/estado/${estado}`).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1 && Array.isArray(response.data)) {
            return response.data;
          } else {
            return [];
          }
        }
        return Array.isArray(response) ? response : [];
      })
    );
  }

  // Método para agregar una respuesta a una pregunta
  agregarRespuesta(faqId: number, respuesta: FaqRespuesta): Observable<FaqRespuesta> {

    // Asegurarse de que se incluya el ID del usuario que crea la respuesta
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.id) {
          respuesta.usuarioId = user.id;
          respuesta.usuarioNombre = `${user.nombre || ''} ${user.apellido || ''}`.trim();
        }
      } catch (error) {
        // Error al parsear datos de usuario
      }
    }

    // Preparar el objeto para enviar al backend
    const respuestaToSend = {
      contenido: respuesta.contenido,
      usuarioId: respuesta.usuarioId,
      archivos: respuesta.archivos || []
    };

    return this.http.post<any>(`${environment.url}api/faq-respuestas?faqId=${faqId}&usuarioId=${respuesta.usuarioId}`, respuestaToSend).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1) {
            // Ya no necesitamos solicitar todas las respuestas después de agregar una
            // porque el backend ya envía un mensaje WebSocket con todas las respuestas actualizadas

            // Emitimos el evento con la respuesta recién agregada para actualizar la UI inmediatamente
            // mientras esperamos la actualización por WebSocket
            // No emitimos el evento aquí porque podría causar una actualización parcial
            // El WebSocket enviará la lista completa de respuestas

            return response.data;
          } else {
            throw new Error(response.msg || 'Error al agregar respuesta');
          }
        }
        return response;
      })
    );
  }

  // Método para obtener las respuestas de una pregunta
  getRespuestasByFaqId(faqId: number): Observable<FaqRespuesta[]> {
    // Suscribirse al canal específico para esta FAQ
    this.subscribeToFaqRespuestas(faqId);

    return this.http.get<any>(`${environment.url}api/faq-respuestas/faq/${faqId}`).pipe(
      map(response => {
        if (response && typeof response === 'object' && 'rpta' in response && 'data' in response) {
          if (response.rpta === 1 && Array.isArray(response.data)) {
            return response.data;
          } else {
            return [];
          }
        }
        return Array.isArray(response) ? response : [];
      })
    );
  }

  // Cache de observables para evitar suscripciones duplicadas
  private realtimeResponsesCache: Map<number, Observable<FaqRespuesta[]>> = new Map();

  /**
   * Obtiene las respuestas de una pregunta con actualizaciones en tiempo real
   * @param faqId ID de la pregunta
   * @returns Observable que emite las respuestas actualizadas
   */
  getRespuestasByFaqIdRealtime(faqId: number): Observable<FaqRespuesta[]> {

    // Verificar si ya tenemos un observable en caché para este faqId
    if (this.realtimeResponsesCache.has(faqId)) {
      return this.realtimeResponsesCache.get(faqId)!;
    }

    // Asegurarnos de estar suscritos al canal específico
    this.subscribeToFaqRespuestas(faqId);

    // Crear un nuevo observable y guardarlo en caché
    const realtimeResponses$ = this.getRespuestasByFaqId(faqId).pipe(
      switchMap((initialResponses: FaqRespuesta[]) => {
        // Crear un observable que combine las respuestas iniciales con las actualizaciones
        return this.getRespuestasUpdates().pipe(
          // Filtramos solo las actualizaciones para esta FAQ
          map((update: {faqId: number, respuestas: FaqRespuesta[]}) => {
            if (update.faqId === faqId) {
              return update.respuestas;
            }
            return initialResponses;
          })
        );
      }),
      // Compartir la suscripción entre múltiples observadores
      shareReplay<FaqRespuesta[]>(1)
    ) as Observable<FaqRespuesta[]>;

    // Guardar en caché
    this.realtimeResponsesCache.set(faqId, realtimeResponses$);

    return realtimeResponses$;
  }

  /* ---------- Firebase ---------- */
  uploadFiles(files: File[]): Observable<FileFaq[]> {
    const uploads = files.map(file => {
      const path = `faqs/${uuid()}_${file.name}`;
      return from(this.storage.upload(path, file)).pipe(
        switchMap(() => this.storage.ref(path).getDownloadURL()),
        map(url => ({
          name: file.name,
          type: file.type,
          size: file.size,
          url,
        }))
      );
    });
    return forkJoin(uploads);   // Observable<FileFaq[]>
  }
}
