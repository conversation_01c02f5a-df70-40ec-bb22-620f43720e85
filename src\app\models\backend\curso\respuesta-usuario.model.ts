import { DetalleRespuestaUsuario } from './detalle-respuesta-usuario.model';

/**
 * Modelo que representa un intento de un usuario en un cuestionario
 */
export interface RespuestaUsuario {
  id: number;
  usuarioId: number;
  usuarioNombre: string;
  cuestionarioId: number;
  cuestionarioTitulo: string;
  fechaInicio: string;
  fechaFin: string;
  puntajeObtenido: number;
  porcentajeAprobacion: number;
  completado: boolean;
  aprobado: boolean;
  numeroIntento: number;
  fechaCreacion: string;
  fechaActualizacion: string;
  detallesRespuestas: DetalleRespuestaUsuario[];
}

/**
 * Modelo para iniciar un nuevo intento de cuestionario
 */
export interface RespuestaUsuarioCreateRequest {
  usuarioId: number;
  cuestionarioId: number;
}

/**
 * Modelo para responder una pregunta en un cuestionario
 */
export interface DetalleRespuestaUsuarioCreateRequest {
  respuestaUsuarioId?: number; // ID de la respuesta del usuario (se asigna automáticamente en el componente)
  preguntaId: number;
  respuestaId?: number; // Puede ser null en caso de respuesta de texto libre
  textoRespuesta?: string; // Texto de respuesta para preguntas de texto libre
}
