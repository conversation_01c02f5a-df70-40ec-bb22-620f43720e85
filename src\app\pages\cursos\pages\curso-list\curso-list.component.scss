/* Usando solo Tailwind CSS - Sin estilos SCSS personalizados */

/* Solo estilos mínimos necesarios para corregir problemas específicos */

/* Corrección para el menú desplegable después de abrir modales */
::ng-deep .mat-mdc-menu-panel {
  z-index: 1000 !important;
  background-color: #374151 !important; /* gray-700 */
  border: 1px solid #4b5563 !important; /* gray-600 */
  border-radius: 8px !important;
}

/* Estilos para los elementos del menú */
::ng-deep .mat-mdc-menu-item {
  color: #f9fafb !important; /* gray-50 */

  &:hover {
    background-color: #4b5563 !important; /* gray-600 */
  }

  .mat-icon {
    color: inherit !important;
  }
}

/* Asegurar que el botón de menú mantenga su posición */
::ng-deep .mat-mdc-menu-trigger {
  position: relative !important;
}

/* Prevenir problemas de z-index con modales */
::ng-deep .cdk-overlay-container {
  z-index: 1000 !important;
}

::ng-deep .cdk-overlay-backdrop {
  z-index: 999 !important;
}

::ng-deep .mat-mdc-dialog-container {
  z-index: 1001 !important;
}
