import { createAction, props } from '@ngrx/store';

// Acciones para la conexión WebSocket
export const connectWebSocket = createAction('[WebSocket] Connect');
export const disconnectWebSocket = createAction('[WebSocket] Disconnect');
export const webSocketConnected = createAction('[WebSocket] Connected');
export const webSocketDisconnected = createAction('[WebSocket] Disconnected');
export const webSocketError = createAction(
  '[WebSocket] Error',
  props<{ error: any }>()
);

// Acciones para mensajes WebSocket genéricos
export const webSocketMessageReceived = createAction(
  '[WebSocket] Message Received',
  props<{ messageType: string; messagePayload: any }>()
);
