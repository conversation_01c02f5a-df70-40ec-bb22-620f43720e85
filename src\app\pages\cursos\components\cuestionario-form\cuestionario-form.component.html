<div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-2xl overflow-hidden max-h-[90vh] overflow-y-auto border border-gray-100 dark:border-gray-700">
  <!-- Header con gradiente -->
  <div class="relative px-8 py-6 bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-700 dark:to-indigo-800">
    <div class="absolute inset-0 bg-black/10"></div>
    <div class="relative flex items-center space-x-3">
      <div class="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
        <mat-icon class="text-white text-2xl">quiz</mat-icon>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-white">{{ isEditMode ? 'Editar' : 'Crear' }} Cuestionario</h2>
        <p class="text-blue-100 text-sm mt-1">Configure las preguntas y parámetros de evaluación</p>
      </div>
    </div>
    <!-- Decorative elements -->
    <div class="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
    <div class="absolute bottom-0 right-8 w-20 h-20 bg-white/5 rounded-full translate-y-10"></div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center p-12 space-y-6">
    <div class="relative">
      <mat-spinner diameter="50" class="!text-blue-500"></mat-spinner>
      <div class="absolute inset-0 flex items-center justify-center">
        <mat-icon class="text-blue-500 text-lg animate-pulse">quiz</mat-icon>
      </div>
    </div>
    <div class="text-center">
      <p class="text-lg font-medium text-gray-700 dark:text-gray-200">{{ isEditMode ? 'Cargando cuestionario...' : 'Creando cuestionario...' }}</p>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Por favor espere un momento</p>
    </div>
  </div>

  <!-- Form Content -->
  <form [formGroup]="form" (ngSubmit)="onSubmit()" *ngIf="!loading" class="px-8 py-6">
    <mat-dialog-content>
      <!-- Información General Section -->
      <div class="mb-10">
        <div class="flex items-center space-x-3 mb-6">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl">
            <mat-icon class="text-white text-lg">info</mat-icon>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Información General</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Configure los datos básicos del cuestionario</p>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-100 dark:border-gray-700">
          <!-- Título -->
          <div class="mb-6">
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>
                <span class="flex items-center">
                  <mat-icon class="mr-2 text-lg">title</mat-icon>
                  Título del Cuestionario
                </span>
              </mat-label>
              <input matInput formControlName="titulo" placeholder="Ej: Evaluación de Conocimientos - Módulo 1"
                    class="dark:bg-gray-700 dark:text-white">
              <mat-hint>Máximo 100 caracteres</mat-hint>
              <mat-error *ngIf="form.get('titulo')?.hasError('required')">
                El título es obligatorio
              </mat-error>
              <mat-error *ngIf="form.get('titulo')?.hasError('maxlength')">
                El título no puede tener más de 100 caracteres
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Descripción -->
          <div class="mb-6">
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>
                <span class="flex items-center">
                  <mat-icon class="mr-2 text-lg">description</mat-icon>
                  Descripción
                </span>
              </mat-label>
              <textarea matInput formControlName="descripcion"
                       placeholder="Describe el propósito y contenido de este cuestionario..."
                       rows="4"
                       class="dark:bg-gray-700 dark:text-white"></textarea>
              <mat-hint>Máximo 500 caracteres</mat-hint>
              <mat-error *ngIf="form.get('descripcion')?.hasError('maxlength')">
                La descripción no puede tener más de 500 caracteres
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Configuración en grid -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Tiempo límite -->
            <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200 dark:border-orange-700/50">
              <div class="flex items-center mb-3">
                <mat-icon class="text-orange-600 dark:text-orange-400 mr-2">schedule</mat-icon>
                <span class="font-medium text-gray-800 dark:text-gray-200">Tiempo Límite</span>
              </div>
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Minutos</mat-label>
                <input matInput type="number" formControlName="tiempoLimite" min="1" placeholder="60"
                      class="dark:bg-gray-700 dark:text-white">
                <mat-hint>Tiempo máximo para completar</mat-hint>
                <mat-error *ngIf="form.get('tiempoLimite')?.hasError('required')">
                  El tiempo límite es obligatorio
                </mat-error>
                <mat-error *ngIf="form.get('tiempoLimite')?.hasError('min')">
                  Mínimo 1 minuto
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Puntaje de aprobación -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200 dark:border-green-700/50">
              <div class="flex items-center mb-3">
                <mat-icon class="text-green-600 dark:text-green-400 mr-2">grade</mat-icon>
                <span class="font-medium text-gray-800 dark:text-gray-200">Puntaje Mínimo</span>
              </div>
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Porcentaje (%)</mat-label>
                <input matInput type="number" formControlName="puntajeAprobacion" min="1" max="100" placeholder="70"
                      class="dark:bg-gray-700 dark:text-white">
                <mat-hint>Para aprobar el cuestionario</mat-hint>
                <mat-error *ngIf="form.get('puntajeAprobacion')?.hasError('required')">
                  El puntaje es obligatorio
                </mat-error>
                <mat-error *ngIf="form.get('puntajeAprobacion')?.hasError('min') || form.get('puntajeAprobacion')?.hasError('max')">
                  Entre 1 y 100
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Intentos máximos -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700/50">
              <div class="flex items-center mb-3">
                <mat-icon class="text-purple-600 dark:text-purple-400 mr-2">refresh</mat-icon>
                <span class="font-medium text-gray-800 dark:text-gray-200">Intentos Máximos</span>
              </div>
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Número de intentos</mat-label>
                <input matInput type="number" formControlName="intentosMaximos" min="1" placeholder="3"
                      class="dark:bg-gray-700 dark:text-white">
                <mat-hint>Veces que puede repetir</mat-hint>
                <mat-error *ngIf="form.get('intentosMaximos')?.hasError('required')">
                  Los intentos son obligatorios
                </mat-error>
                <mat-error *ngIf="form.get('intentosMaximos')?.hasError('min')">
                  Mínimo 1 intento
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Opciones adicionales -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700/50">
            <div class="flex items-center mb-4">
              <mat-icon class="text-blue-600 dark:text-blue-400 mr-2">settings</mat-icon>
              <span class="font-medium text-gray-800 dark:text-gray-200">Opciones Adicionales</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <mat-checkbox formControlName="mostrarRespuestas" class="text-gray-700 dark:text-gray-200">
                </mat-checkbox>
                <div>
                  <p class="font-medium text-gray-800 dark:text-gray-200">Mostrar respuestas correctas</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Al finalizar el cuestionario</p>
                </div>
              </div>

              <div class="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <mat-checkbox formControlName="aleatorizarPreguntas" class="text-gray-700 dark:text-gray-200">
                </mat-checkbox>
                <div>
                  <p class="font-medium text-gray-800 dark:text-gray-200">Aleatorizar preguntas</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Orden diferente para cada intento</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Preguntas Section -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl">
              <mat-icon class="text-white text-lg">quiz</mat-icon>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Preguntas del Cuestionario</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Añade y configura las preguntas de evaluación</p>
            </div>
          </div>
          <button type="button" mat-fab color="primary" (click)="addPregunta()"
                 class="!bg-gradient-to-r !from-green-500 !to-emerald-600 hover:!from-green-600 hover:!to-emerald-700 !shadow-lg hover:!shadow-xl !transition-all !duration-300"
                 aria-label="Añadir pregunta">
            <mat-icon class="!text-white !text-xl">add</mat-icon>
          </button>
        </div>

        <!-- Lista de preguntas -->
        <div formArrayName="preguntas" class="space-y-8">
          <div *ngFor="let preguntaGroup of preguntasForm.controls; let i = index"
               class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-300"
               [formGroupName]="i">

            <!-- Cabecera de pregunta -->
            <div class="flex justify-between items-center mb-6">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span class="text-white font-bold text-sm">{{ i + 1 }}</span>
                </div>
                <div>
                  <h4 class="text-lg font-bold text-gray-800 dark:text-gray-100">Pregunta {{ i + 1 }}</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Configure el contenido y opciones</p>
                </div>
              </div>
              <button type="button" mat-icon-button (click)="removePregunta(i)"
                     class="!text-red-500 hover:!bg-red-50 dark:hover:!bg-red-900/20 !w-10 !h-10 rounded-full !transition-all !duration-200"
                     aria-label="Eliminar pregunta">
                <mat-icon>delete_outline</mat-icon>
              </button>
            </div>

            <!-- Enunciado -->
            <div class="mb-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Enunciado</mat-label>
                <textarea matInput formControlName="enunciado" placeholder="Enunciado de la pregunta" rows="2"
                         class="dark:bg-gray-700 dark:text-white"></textarea>
                <mat-error *ngIf="preguntaGroup.get('enunciado')?.hasError('required')">
                  El enunciado es obligatorio
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Configuración de pregunta -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <!-- Tipo de pregunta -->
              <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700/50">
                <div class="flex items-center mb-3">
                  <mat-icon class="text-blue-600 dark:text-blue-400 mr-2">category</mat-icon>
                  <span class="font-medium text-gray-800 dark:text-gray-200">Tipo de Pregunta</span>
                </div>
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Seleccionar tipo</mat-label>
                  <mat-select formControlName="tipo" class="dark:bg-gray-700 dark:text-white">
                    <mat-option [value]="TipoPregunta.OPCION_MULTIPLE">
                      <div class="flex items-center">
                        <mat-icon class="mr-2 text-sm">radio_button_checked</mat-icon>
                        Opción múltiple (una respuesta)
                      </div>
                    </mat-option>
                    <mat-option [value]="TipoPregunta.SELECCION_MULTIPLE">
                      <div class="flex items-center">
                        <mat-icon class="mr-2 text-sm">check_box</mat-icon>
                        Selección múltiple (varias respuestas)
                      </div>
                    </mat-option>
                    <mat-option [value]="TipoPregunta.VERDADERO_FALSO">
                      <div class="flex items-center">
                        <mat-icon class="mr-2 text-sm">toggle_on</mat-icon>
                        Verdadero / Falso
                      </div>
                    </mat-option>
                    <mat-option [value]="TipoPregunta.TEXTO_LIBRE">
                      <div class="flex items-center">
                        <mat-icon class="mr-2 text-sm">text_fields</mat-icon>
                        Texto libre
                      </div>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <!-- Puntaje -->
              <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-xl p-4 border border-yellow-200 dark:border-yellow-700/50">
                <div class="flex items-center mb-3">
                  <mat-icon class="text-yellow-600 dark:text-yellow-400 mr-2">star</mat-icon>
                  <span class="font-medium text-gray-800 dark:text-gray-200">Puntaje</span>
                </div>
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Puntos</mat-label>
                  <input matInput type="number" formControlName="puntaje" min="1" placeholder="10"
                        class="dark:bg-gray-700 dark:text-white">
                  <mat-hint>Valor de la pregunta</mat-hint>
                  <mat-error *ngIf="preguntaGroup.get('puntaje')?.hasError('required')">
                    El puntaje es obligatorio
                  </mat-error>
                  <mat-error *ngIf="preguntaGroup.get('puntaje')?.hasError('min')">
                    Mínimo 1 punto
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Orden -->
              <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 rounded-xl p-4 border border-gray-200 dark:border-gray-700/50">
                <div class="flex items-center mb-3">
                  <mat-icon class="text-gray-600 dark:text-gray-400 mr-2">sort</mat-icon>
                  <span class="font-medium text-gray-800 dark:text-gray-200">Orden</span>
                </div>
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Posición</mat-label>
                  <input matInput type="number" formControlName="orden" min="1" placeholder="{{ i + 1 }}"
                        class="dark:bg-gray-700 dark:text-white">
                  <mat-hint>Orden de aparición</mat-hint>
                </mat-form-field>
              </div>
            </div>

            <!-- Explicación -->
            <div class="mb-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Explicación (opcional)</mat-label>
                <textarea matInput formControlName="explicacion"
                         placeholder="Explicación que se mostrará después de responder" rows="2"
                         class="dark:bg-gray-700 dark:text-white"></textarea>
              </mat-form-field>
            </div>

            <!-- Respuestas (excepto para texto libre) -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600"
                *ngIf="preguntaGroup.get('tipo')?.value !== TipoPregunta.TEXTO_LIBRE">

              <div class="flex justify-between items-center mb-3">
                <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300">Respuestas</h5>
                <button type="button" mat-mini-fab color="accent" (click)="addRespuesta(i)"
                       class="!bg-indigo-500 hover:!bg-indigo-600 !w-8 !h-8 !min-h-0" aria-label="Añadir respuesta">
                  <mat-icon class="!text-white !text-base">add</mat-icon>
                </button>
              </div>

              <!-- Lista de respuestas -->
              <div formArrayName="respuestas" class="space-y-3">
                <div *ngFor="let respuestaGroup of getRespuestas(i).controls; let j = index"
                     class="flex items-center gap-2 bg-white dark:bg-gray-800 p-2 rounded border border-gray-100 dark:border-gray-700"
                     [formGroupName]="j">

                  <!-- Texto de respuesta -->
                  <mat-form-field appearance="outline" class="flex-1 !m-0">
                    <mat-label>Texto de la respuesta</mat-label>
                    <input matInput formControlName="texto" placeholder="Texto de la respuesta"
                          class="dark:bg-gray-700 dark:text-white">
                    <mat-error *ngIf="respuestaGroup.get('texto')?.hasError('required')">
                      El texto es obligatorio
                    </mat-error>
                  </mat-form-field>

                  <!-- Checkbox de respuesta correcta -->
                  <mat-checkbox formControlName="esCorrecta" class="mx-2 text-gray-700 dark:text-gray-200">
                    Correcta
                  </mat-checkbox>

                  <!-- Botón eliminar respuesta -->
                  <button type="button" mat-icon-button color="warn" (click)="removeRespuesta(i, j)"
                         class="!text-red-500 hover:!bg-red-50 dark:hover:!bg-red-900/20" aria-label="Eliminar respuesta">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>

              <!-- Advertencia de respuestas mínimas -->
              <div *ngIf="getRespuestas(i).length < 2"
                   class="flex items-center mt-2 text-sm text-amber-600 dark:text-amber-400">
                <mat-icon class="!text-amber-500 !text-base mr-1">warning</mat-icon>
                <span>Debe añadir al menos 2 respuestas</span>
              </div>
            </div>

            <!-- Información para preguntas de texto libre -->
            <div *ngIf="preguntaGroup.get('tipo')?.value === TipoPregunta.TEXTO_LIBRE"
                 class="flex items-start p-3 mt-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md">
              <mat-icon class="!text-blue-500 mr-2 !text-base">info</mat-icon>
              <p class="text-sm">Las preguntas de texto libre no tienen respuestas predefinidas. El usuario podrá ingresar cualquier texto como respuesta.</p>
            </div>
          </div>
        </div>

        <!-- Estado sin preguntas -->
        <div *ngIf="preguntasForm.length === 0"
             class="flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <mat-icon class="!text-gray-400 dark:!text-gray-500 !text-4xl mb-2">help</mat-icon>
          <p class="text-gray-500 dark:text-gray-400">No hay preguntas. Haga clic en el botón "+" para añadir una pregunta.</p>
        </div>
      </div>

      <!-- Mensaje de error -->
      <div *ngIf="error"
           class="flex items-center p-3 mb-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md">
        <mat-icon class="!text-red-500 mr-2">error</mat-icon>
        <span>{{ error }}</span>
      </div>
    </mat-dialog-content>

    <!-- Acciones del formulario -->
    <mat-dialog-actions align="end" class="px-8 py-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <button type="button" mat-stroked-button (click)="onCancel()"
               class="!border-gray-300 dark:!border-gray-600 !text-gray-700 dark:!text-gray-300 hover:!bg-gray-100 dark:hover:!bg-gray-700 !px-6 !py-3 !rounded-xl !font-medium !transition-all !duration-200">
          <mat-icon class="mr-2">close</mat-icon>
          Cancelar
        </button>
        <button type="submit" mat-raised-button [disabled]="form.invalid || loading"
               class="!bg-gradient-to-r !from-blue-600 !to-indigo-600 hover:!from-blue-700 hover:!to-indigo-700 !text-white !px-8 !py-3 !rounded-xl !font-medium !shadow-lg hover:!shadow-xl !transition-all !duration-200 disabled:!opacity-50 disabled:!cursor-not-allowed">
          <mat-icon class="mr-2">{{ isEditMode ? 'update' : 'save' }}</mat-icon>
          {{ isEditMode ? 'Actualizar Cuestionario' : 'Guardar Cuestionario' }}
        </button>
      </div>
    </mat-dialog-actions>
  </form>
</div>
