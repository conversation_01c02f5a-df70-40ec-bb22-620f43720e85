<div class="anuncio-form-dialog">
  <h2 mat-dialog-title>{{ isEditing ? 'Editar Anuncio' : 'Crear Nuevo Anuncio' }}</h2>

  <mat-dialog-content>
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form-content">
        <!-- Sección de imagen -->
        <div class="image-section">
          <div class="image-container" [class.has-image]="photoLoaded">
            <img *ngIf="photoLoaded" [src]="photoLoaded" alt="Imagen del anuncio" class="preview-image">
            <div *ngIf="!photoLoaded" class="placeholder-image">
              <mat-icon>image</mat-icon>
              <span>Sin imagen</span>
            </div>
          </div>
          <button type="button" mat-raised-button color="primary" class="upload-button" (click)="openFilesUpload()">
            <mat-icon>cloud_upload</mat-icon>
            <span class="button-text">Subir Imagen</span>
          </button>
        </div>

        <!-- Sección de formulario -->
        <div class="form-fields">
          <!-- Campos principales -->
          <div class="main-fields">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Título del Anuncio</mat-label>
              <input matInput formControlName="titulo" placeholder="Ingrese el título">
              <mat-error *ngIf="form.get('titulo')?.hasError('required')">
                Debe ingresar un título
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Descripción</mat-label>
              <textarea matInput formControlName="descripcion" placeholder="Ingrese la descripción" rows="3"></textarea>
              <mat-error *ngIf="form.get('descripcion')?.hasError('required')">
                Debe ingresar una descripción
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Categoría</mat-label>
              <mat-select formControlName="categoria">
                <mat-option *ngFor="let cat of categorias" [value]="cat">{{cat}}</mat-option>
              </mat-select>
              <mat-error *ngIf="form.get('categoria')?.hasError('required')">
                Debe seleccionar una categoría
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Fechas de inicio y fin -->
          <div class="date-fields">
            <mat-form-field appearance="outline">
              <mat-label>Fecha de inicio</mat-label>
              <input matInput [matDatepicker]="pickerInicio" formControlName="fechaInicio">
              <mat-datepicker-toggle matSuffix [for]="pickerInicio"></mat-datepicker-toggle>
              <mat-datepicker #pickerInicio></mat-datepicker>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Fecha de fin</mat-label>
              <input matInput [matDatepicker]="pickerFin" formControlName="fechaFin">
              <mat-datepicker-toggle matSuffix [for]="pickerFin"></mat-datepicker-toggle>
              <mat-datepicker #pickerFin></mat-datepicker>
            </mat-form-field>
          </div>

          <!-- Orden, Estado y Sede -->
          <div class="additional-fields">
            <mat-form-field appearance="outline">
              <mat-label>Orden</mat-label>
              <input matInput type="number" formControlName="orden" min="0">
              <mat-hint>Menor número = mayor prioridad</mat-hint>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Estado</mat-label>
              <mat-select formControlName="estado">
                <mat-option *ngFor="let estado of estados" [value]="estado">{{estado}}</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Sede</mat-label>
              <mat-select formControlName="sedeId">
                <mat-option [value]="null">Todas las sedes</mat-option>
                <mat-option *ngFor="let sede of sedes" [value]="sede.id">{{sede.nombre}} (ID: {{sede.id}})</mat-option>
              </mat-select>
              <mat-hint>Sede destino del anuncio</mat-hint>
              <mat-error *ngIf="form.get('sedeId')?.hasError('required')">
                Debe seleccionar una sede
              </mat-error>
            </mat-form-field>

            <!-- Mensaje de advertencia sobre la sede -->
            <div *ngIf="form.get('sedeId')?.value" class="sede-warning">
              <mat-icon color="warn">warning</mat-icon>
              <span>Nota: La sede seleccionada se guardará correctamente, pero puede aparecer como "null" en la respuesta del backend debido a una limitación técnica.</span>
            </div>
          </div>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onClose()">Cancelar</button>
    <button mat-raised-button color="primary" [disabled]="form.invalid" (click)="onSubmit()">
      {{ isEditing ? 'Actualizar' : 'Publicar' }}
    </button>
  </mat-dialog-actions>
</div>
