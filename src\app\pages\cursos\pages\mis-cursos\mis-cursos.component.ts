import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Store, select } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';
import { Curso } from '@app/models/backend/curso/curso.model';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';
import { ModuloService } from '@app/services/modulo.service';
import { NotificationService } from '@app/services/notification/notification.service';

import { ProgresoUsuario } from '@app/models/backend/curso/progreso.model';
import { ArrayToDatePipe } from '@app/shared/pipes/array-to-date.pipe';
import { GenericResponse } from '@app/models/backend';

@Component({
  selector: 'app-mis-cursos',
  templateUrl: './mis-cursos.component.html',
  providers: [ArrayToDatePipe]
})
export class MisCursosComponent implements OnInit, OnDestroy {
  user: User | null = null;
  cursos: Curso[] = [];
  progresos: { [cursoId: number]: ProgresoUsuario } = {};
  loading = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private store: Store<fromRoot.State>,
    private cursoUsuarioService: CursoUsuarioService,
    private moduloService: ModuloService,
    private notification: NotificationService,
    private arrayToDatePipe: ArrayToDatePipe
  ) { }

  ngOnInit(): void {
    // Obtener el usuario actual
    this.store.pipe(
      select(fromUser.getUser),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
      if (user) {
        this.loadCursosAsignados(user.id);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadCursosAsignados(usuarioId: number): void {
    this.loading = true;
    this.error = null; // Limpiar errores anteriores
    this.cursos = []; // Limpiar cursos anteriores

    console.log(`Cargando cursos asignados para el usuario ID: ${usuarioId}`);

    // Usar el nuevo método que obtiene los cursos completos directamente
    this.cursoUsuarioService.getCursosCompletosByUsuarioId(usuarioId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<any[]>) => {
          console.log('Respuesta de getCursosCompletosByUsuarioId:', response);

          if (response.rpta === 1) {
            if (response.data && response.data.length > 0) {
              console.log(`Se cargaron ${response.data.length} cursos correctamente`);
              this.cursos = response.data;

              // Cargar los módulos y lecciones para cada curso
              this.loadModulosYLecciones();
            } else {
              // No hay cursos asignados (respuesta exitosa pero sin datos)
              console.log('No hay cursos asignados para este usuario');
              this.loading = false;
              // No establecemos error porque no es un error, simplemente no hay cursos
            }
          } else {
            this.loading = false;
            this.error = response.msg || 'Error al cargar cursos asignados';
            this.notification.error(this.error || 'Error desconocido');
            console.error('Error en la respuesta:', response);
          }
        },
        error: (error: any) => {
          this.loading = false;
          this.error = 'Error al cargar cursos asignados. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al cargar cursos asignados:', error);

          // Mostrar más detalles del error para depuración
          if (error.error) {
            console.error('Detalles del error:', error.error);
          }
          if (error.status) {
            console.error('Estado HTTP:', error.status);
            if (error.status === 404) {
              this.error = 'El servicio de cursos no está disponible. Por favor, contacte al administrador.';
              this.notification.error(this.error);
            } else if (error.status === 500) {
              this.error = 'Error interno del servidor al cargar cursos asignados. El equipo técnico ha sido notificado.';
              this.notification.error(this.error);
              console.error('Error 500 del servidor:', error);

              // Solución temporal: Mostrar un mensaje más amigable y sugerir alternativas
              setTimeout(() => {
                this.error = 'No se pudieron cargar tus cursos asignados. Por favor, intenta nuevamente más tarde o contacta a soporte técnico.';
                this.notification.success('Estamos trabajando para resolver este problema lo antes posible.');

                // Mostrar cursos de demostración para que el usuario pueda ver la interfaz
                this.mostrarCursosDemostracion();
              }, 3000);
            }
          }
        }
      });
  }



  /**
   * Carga los módulos y lecciones para cada curso
   */
  private loadModulosYLecciones(): void {
    if (this.cursos.length === 0) {
      console.log('No hay cursos para cargar módulos y lecciones');
      this.loading = false;
      return;
    }

    console.log(`Cargando módulos y lecciones para ${this.cursos.length} cursos`);

    // Contador para saber cuándo se han cargado todos los cursos
    let cursosCompletados = 0;

    // Para cada curso, cargar sus módulos
    this.cursos.forEach(curso => {
      // Si el curso ya tiene módulos, no es necesario cargarlos de nuevo
      if (curso.modulos && curso.modulos.length > 0) {
        cursosCompletados++;
        if (cursosCompletados === this.cursos.length) {
          this.loading = false;
          this.loadProgresos();
        }
        return;
      }

      // Cargar los módulos del curso
      this.moduloService.getModulosByCursoId(curso.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1 && response.data) {
              // Asignar los módulos al curso
              curso.modulos = response.data;
              console.log(`Módulos cargados para el curso ${curso.id}: ${curso.modulos.length} módulos`);
            } else {
              console.warn(`No se encontraron módulos para el curso ${curso.id}`);
              curso.modulos = [];
            }

            // Incrementar el contador de cursos completados
            cursosCompletados++;
            if (cursosCompletados === this.cursos.length) {
              this.loading = false;
              this.loadProgresos();
            }
          },
          error: (error) => {
            console.error(`Error al cargar módulos para el curso ${curso.id}:`, error);
            curso.modulos = [];

            // Incrementar el contador de cursos completados
            cursosCompletados++;
            if (cursosCompletados === this.cursos.length) {
              this.loading = false;
              this.loadProgresos();
            }
          }
        });
    });
  }

  private loadProgresos(): void {
    if (!this.user || this.cursos.length === 0) {
      console.log('No se pueden cargar progresos: usuario o cursos no disponibles');
      return;
    }

    console.log(`Generando datos de progreso para ${this.cursos.length} cursos`);

    try {
      this.cursos.forEach(curso => {
        // Para cursos de demostración, siempre mostramos 0% de progreso
        // En un caso real, obtendríamos el progreso real del backend
        this.progresos[curso.id] = {
          id: 0,
          usuario: this.user,
          curso: curso,
          porcentajeCompletado: 0, // Siempre 0% para mostrar el progreso real
          fechaInicio: new Date().toISOString(),
          fechaUltimoAcceso: new Date().toISOString(),
          estado: 'EN_PROGRESO',
          leccionesCompletadas: []
        };
      });

      console.log('Progresos generados correctamente:', this.progresos);
    } catch (error) {
      console.error('Error al generar datos de progreso:', error);
      // No mostramos notificación al usuario porque esto es una simulación
      // En una implementación real, podríamos manejar este error de otra manera
    }
  }

  getProgresoPorcentaje(cursoId: number): number {
    return this.progresos[cursoId]?.porcentajeCompletado || 0;
  }

  getProgresoColor(cursoId: number): string {
    const porcentaje = this.getProgresoPorcentaje(cursoId);

    if (porcentaje >= 75) {
      return '#4caf50'; // Verde
    } else if (porcentaje >= 50) {
      return '#ff9800'; // Naranja
    } else if (porcentaje >= 25) {
      return '#ffc107'; // Amarillo
    } else {
      return '#f44336'; // Rojo
    }
  }

  // URL de imagen de placeholder para cursos (usando una imagen local para evitar problemas de CORS)
  cursoPlaceholderUrl: string = 'assets/images/curso-placeholder.jpg';

  formatFecha(fecha: any): string {
    if (!fecha) return 'No disponible';

    const date = this.arrayToDatePipe.transform(fecha);
    return date ? date.toLocaleDateString() : 'No disponible';
  }

  continuarCurso(curso: Curso): void {
    // Si es un curso de demostración (ID negativo), mostrar un mensaje informativo
    if (curso.id < 0) {
      this.notification.success('Esta es una vista de demostración. Los cursos reales estarán disponibles pronto.');
      return;
    }

    // Desactivamos temporalmente la actualización de la última visualización
    // para evitar errores 500 que bloquean la experiencia del usuario
    /*
    if (curso.asignacion) {
      // Usar formato YYYY-MM-DD sin la Z al final para evitar errores
      const fecha = new Date().toISOString().split('T')[0];
      this.cursoUsuarioService.updateCursoUsuario(curso.asignacion.id, {
        ultimaVisualizacion: fecha
      }).pipe(takeUntil(this.destroy$)).subscribe();
    }
    */

    // Navegar a la vista detallada del curso
    this.router.navigate(['/cursos/detalle', curso.id]);
  }

  /**
   * Obtiene el número de módulos para un curso
   */
  getModulosCount(cursoId: number): number {
    const curso = this.cursos.find(c => c.id === cursoId);
    if (!curso || !curso.modulos) {
      return 0;
    }
    return curso.modulos.length;
  }

  /**
   * Obtiene el número total de lecciones para un curso
   */
  getLeccionesCount(cursoId: number): number {
    const curso = this.cursos.find(c => c.id === cursoId);
    if (!curso || !curso.modulos) {
      return 0;
    }

    let totalLecciones = 0;
    curso.modulos.forEach(modulo => {
      if (modulo.secciones) {
        modulo.secciones.forEach(seccion => {
          if (seccion.lecciones) {
            totalLecciones += seccion.lecciones.length;
          }
        });
      }
    });

    return totalLecciones;
  }

  /**
   * Muestra cursos de demostración cuando hay un error en el servidor
   * Esta es una solución temporal mientras se corrige el problema en el backend
   */
  private mostrarCursosDemostracion(): void {
    console.log('Mostrando cursos de demostración como solución temporal');

    // Limpiar el error para mostrar los cursos
    this.error = null;

    // Crear cursos de demostración
    this.cursos = [
      {
        id: -1, // ID negativo para indicar que es un curso de demostración
        nombre: 'Introducción al CRM (Demo)',
        descripcion: 'Este es un curso de demostración mientras solucionamos el problema técnico.',
        fechaInicio: [2023, 1, 1],
        fechaFin: [2023, 12, 31],
        estado: 'A',
        videoUrl: '',
        usuario: null,
        asignacion: {
          completado: false
        }
      },
      {
        id: -2,
        nombre: 'Gestión de Ventas (Demo)',
        descripcion: 'Curso de demostración sobre gestión de ventas en el sistema.',
        fechaInicio: [2023, 2, 15],
        fechaFin: [2023, 12, 31],
        estado: 'A',
        videoUrl: '',
        usuario: null,
        asignacion: {
          completado: false
        }
      }
    ];

    // Generar progresos simulados
    this.loadProgresos();

    // Mostrar notificación informativa
    this.notification.success('Se muestran cursos de demostración temporalmente');
  }
}
