<div class="flex items-center p-1">
  <div class="flex items-center mr-1">
    <span
      class="inline-block w-2.5 h-2.5 md:w-3 md:h-3 rounded-full"
      [ngClass]="{'bg-green-500 shadow-[0_0_4px_#22C55E] dark:bg-green-400 dark:shadow-[0_0_4px_#4ADE80]': isConnected, 'bg-red-500 shadow-[0_0_4px_#EF4444] dark:bg-red-400 dark:shadow-[0_0_4px_#F87171]': !isConnected}"
      [matTooltip]="isConnected ? 'Servidor conectado' : 'Servidor desconectado'">
      <div
        *ngIf="isConnected"
        class="absolute inset-0 bg-green-500 dark:bg-green-400 rounded-full animate-ping opacity-50 scale-150">
      </div>
    </span>
  </div>
  <button
    *ngIf="!isConnected"
    mat-icon-button
    class="w-6 h-6 md:w-7 md:h-7 flex items-center justify-center p-0 min-w-0 text-blue-600 dark:text-blue-400"
    (click)="reconnect()">
    <mat-icon class="text-[16px] md:text-[18px] h-[16px] w-[16px] md:h-[18px] md:w-[18px] m-0">refresh</mat-icon>
  </button>
</div>
