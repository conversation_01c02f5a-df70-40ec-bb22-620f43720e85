import { Component, OnD<PERSON>roy, OnInit, Injector } from '@angular/core';
import { FormBuilder } from '@angular/forms';
// Ya no necesitamos BsModalService ni BsModalRef
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import { FaqService } from '@app/services/faq/faq.service';
import * as FaqSelectors from '../../store/faq.selectors';
import * as FaqActions from '../../store/faq.actions';
import { Faq } from '@app/models/backend/faq/faq.model';
// Ya no necesitamos ModalFormComponent
import { FaqDialogComponent } from '../faq-dialog/faq-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-faq-list',
  templateUrl: './faq-list.component.html',
})
export class FaqListComponent implements OnInit, OnD<PERSON>roy {
  /* buscador + paginación  →  page y perPage no pueden ser null */
  pagination = this.fb.nonNullable.group({
    // 👈  nonNullable
    page: 1,
    perPage: 10,
    search: '',
  });
  countElements = [10, 25, 50];

  /** datos */
  loading$ = this.store.select(FaqSelectors.selectLoading);
  faqs$ = this.store.select(FaqSelectors.selectFaqs);
  paginationResult = { data: [] as Faq[], total: 0, from: 0, to: 0 };

  // Variable para almacenar si el usuario tiene permisos (ADMIN o PROGRAMADOR) (se calcula una sola vez)
  userHasPermission: boolean = false;

  // Datos de ejemplo para mostrar en caso de que no se puedan cargar los datos del backend
  exampleFaqs: Faq[] = [
    {
      id: 1,
      pregunta: '¿Cómo registro un nuevo cliente en el sistema?',
      respuesta:
        'Para registrar un nuevo cliente, dirígete al módulo "Home" y "Registrar Tipificacion", haz clic en el botón "Agregar Cliente" y completa los datos solicitados.',
      categoria: 'Clientes',
      tipoUsuario: 'ASESOR',
    },
    {
      id: 2,
      pregunta:
        '¿Dónde puedo ver el historial de mis ventas?',
      respuesta:
        'Accede al perfil "Ventas". Allí encontrarás todas las interacciones registradas.',
      categoria: 'Seguimiento',
      tipoUsuario: 'ASESOR',
    },
  ];

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private dialog: MatDialog,
    private injector: Injector
  ) {}

  ngOnInit(): void {
    // Verificar permisos una sola vez al iniciar el componente
    this.userHasPermission = this.checkUserHasPermission();

    this.listenStore();
    this.apiFaqListPagination();

    // Suscribirse a actualizaciones en tiempo real de FAQs
    this.subscribeToFaqUpdates();
  }

  /**
   * Suscribe a actualizaciones en tiempo real de FAQs
   */
  private subscribeToFaqUpdates(): void {
    // Importar el servicio FaqService si no está disponible
    const faqService = this.injector.get(FaqService);

    // Suscribirse a actualizaciones de FAQs
    faqService['faqsUpdated'].pipe(
      takeUntil(this.destroy$)
    ).subscribe(faqs => {

      // Actualizar la lista de FAQs
      const { page, perPage } = this.pagination.getRawValue();
      const start = (page - 1) * perPage;
      const end = start + perPage;

      this.paginationResult = {
        data: faqs.slice(start, end),
        total: faqs.length,
        from: start + 1,
        to: Math.min(end, faqs.length),
      };
    });
  }

  private listenStore() {
    this.faqs$.pipe(takeUntil(this.destroy$)).subscribe((faqs) => {
      // Si no hay datos del backend, usar los datos de ejemplo
      const faqsToUse = faqs && faqs.length > 0 ? faqs : this.exampleFaqs;

      const { page, perPage } = this.pagination.getRawValue(); // ya son number
      const start = (page - 1) * perPage;
      const end = start + perPage;

      this.paginationResult = {
        data: faqsToUse.slice(start, end),
        total: faqsToUse.length,
        from: start + 1,
        to: Math.min(end, faqsToUse.length),
      };
    });
  }

  apiFaqListPagination() {
    // Asegurarse de que los valores de paginación estén inicializados
    if (!this.pagination.value.page) {
      this.pagination.patchValue({ page: 1 });
    }
    if (!this.pagination.value.perPage) {
      this.pagination.patchValue({ perPage: 10 });
    }

    // Cargar las FAQs
    this.store.dispatch(FaqActions.loadFaqs());

    // Suscribirse a los cambios en las FAQs
    this.faqs$.subscribe();
  }

  /** refrescar lista */
  getPageRefresh() {
    this.apiFaqListPagination();
  }

  /** paginado → sin patchValue */
  getPage({ page }: { page: number }) {
    this.pagination.controls.page.setValue(page); // 👈 cambia solo esta línea
  }

  /** Verifica si el usuario tiene permisos (ADMIN o PROGRAMADOR) (se ejecuta una sola vez) */
  private checkUserHasPermission(): boolean {
    try {
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        return false;
      }

      const user = JSON.parse(userStr);
      const hasPermission = user && (user.role === 'ADMIN' || user.role === 'PROGRAMADOR');
      return hasPermission;
    } catch (error) {
      return false;
    }
  }

  /** Retorna el valor almacenado de userHasPermission (sin recalcular) */
  validateOptionsByUserId(): boolean {
    return this.userHasPermission;
  }

  /* abrir modal de alta / edición usando MatDialog */
  openModalForm(data?: Faq) {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    // Si estamos creando una nueva FAQ, asegurarnos de que sea un objeto nuevo
    const faqData = data ? { ...data } : undefined;

    const dialogRef = this.dialog.open(FaqDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      disableClose: true,
      data: {
        title: data
          ? 'Editar Pregunta y Respuesta'
          : 'Crear Pregunta y Respuesta',
        faq: faqData,
      },
      panelClass: ['modern-modal', isDarkTheme ? 'dark-theme' : ''],
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Recargar datos si es necesario
        this.apiFaqListPagination();
      }
    });
  }

  /* abrir modal para responder a una pregunta */
  openResponseForm(data: Faq) {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    // Crear una copia de los datos para no modificar el original
    const faqData = { ...data };

    const dialogRef = this.dialog.open(FaqDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      disableClose: true,
      data: {
        title: 'Responder Pregunta',
        faq: faqData,
        isResponseMode: true, // Modo especial para responder
      },
      panelClass: ['modern-modal', isDarkTheme ? 'dark-theme' : ''],
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Recargar datos si es necesario
        this.apiFaqListPagination();
      }
    });
  }

  /** opciones emitidas por card */
  getOptCardV1(evt: { option: string; data: Faq }) {

    if (evt.option === 'editar') {
      this.openModalForm(evt.data);
    }

    if (evt.option === 'eliminar') {
      // No necesitamos verificar permisos aquí porque ya se verificó en el botón
      // y solo los usuarios con rol ADMIN pueden ver y hacer clic en ese botón
      if (
        confirm('¿Está seguro que desea eliminar esta pregunta y respuesta?')
      ) {
        this.store.dispatch(FaqActions.deleteFaq({ id: evt.data.id! }));
      }
    }

    if (evt.option === 'responder') {
      this.openResponseForm(evt.data);
    }

    // Manejar cambio de estado (abrir/cerrar)
    if (evt.option === 'cerrar') {
      if (confirm('¿Está seguro que desea cerrar esta pregunta?')) {
        this.cambiarEstadoFaq(evt.data.id!, 'CERRADA');
      }
    }

    if (evt.option === 'abrir') {
      if (confirm('¿Está seguro que desea abrir esta pregunta?')) {
        this.cambiarEstadoFaq(evt.data.id!, 'ABIERTA');
      }
    }

    // Manejar evento de respuesta agregada
    if (evt.option === 'respuesta-agregada') {
      // Recargar datos para asegurarnos de tener la información actualizada
      this.apiFaqListPagination();
    }
  }

  /**
   * Cambia el estado de una pregunta (abierta/cerrada)
   */
  cambiarEstadoFaq(id: number, estado: 'ABIERTA' | 'CERRADA'): void {
    // Importar el servicio FaqService si no está disponible
    const faqService = this.injector.get(FaqService);

    // Mostrar un mensaje de confirmación al usuario
    const confirmMessage = estado === 'CERRADA'
      ? '¿Está seguro que desea cerrar esta pregunta? No se aceptarán más respuestas.'
      : '¿Está seguro que desea abrir esta pregunta? Se aceptarán nuevas respuestas.';

    if (!confirm(confirmMessage)) {
      return;
    }

    faqService.cambiarEstadoFaq(id, estado).subscribe({
      next: () => {
        // Mostrar mensaje de éxito
        const mensaje = estado === 'CERRADA'
          ? 'Pregunta cerrada exitosamente'
          : 'Pregunta abierta exitosamente';

        alert(mensaje);

        // Recargar datos
        this.apiFaqListPagination();
      },
      error: (error) => {
        // Mostrar mensaje de error
        alert(`Error al cambiar estado de la pregunta: ${error.message || 'Error desconocido'}`);

        // Recargar datos de todos modos para asegurar que la UI esté actualizada
        this.apiFaqListPagination();
      },
      complete: () => {
        // Asegurarse de que la UI esté actualizada después de un breve retraso
        setTimeout(() => {
          this.apiFaqListPagination();
        }, 500);
      }
    });
  }

  /** Métodos de paginación */
  previousPage() {
    const currentPage = this.pagination.value.page || 1;
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      this.pagination.patchValue({ page: newPage });
      this.getPage({ page: newPage });
    }
  }

  nextPage() {
    const currentPage = this.pagination.value.page || 1;
    const perPage = this.pagination.value.perPage || 10;
    if (currentPage * perPage < this.paginationResult.total) {
      const newPage = currentPage + 1;
      this.pagination.patchValue({ page: newPage });
      this.getPage({ page: newPage });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
