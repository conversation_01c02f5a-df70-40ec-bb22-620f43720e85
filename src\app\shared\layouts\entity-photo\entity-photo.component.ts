import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON>anitizer, SafeStyle } from '@angular/platform-browser';

@Component({
  selector: 'app-entity-photo',
  templateUrl: './entity-photo.component.html',
  styles: []
})
export class EntityPhotoComponent implements OnInit {
  @Input() photoURL! : string;

  constructor(
    private sanitizer: DomSanitizer,
  ) { }

  ngOnInit(): void {
  }

  get safePhotoURL(): SafeStyle | null {
    return this.photoURL ? this.sanitizer.bypassSecurityTrustStyle(`url(${this.photoURL})`) : null;
  }



}
