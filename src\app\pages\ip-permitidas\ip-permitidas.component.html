<div class="container">
  <mat-card>
 

    <mat-card-content>
      <div class="header-content">
        <h1>
       
          <ng-container  >
            IPS PERMITIDAS
          </ng-container>
        </h1>
        <div class="header-buttons">
          <button mat-raised-button color="primary" (click)="abrirDialog()">
            <mat-icon>add</mat-icon>
            Agregar IP
          </button>
        </div>
      </div>

      <div class="mat-elevation-z8">
        <div class="loading-overlay" *ngIf="loading">
          <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
        </div>
        
        <mat-table [dataSource]="dataSource">
          <!-- IP Column -->
          <ng-container matColumnDef="ip">
            <mat-header-cell *matHeaderCellDef>IP</mat-header-cell>
            <mat-cell *matCellDef="let element">{{element.ip}}</mat-cell>
          </ng-container>

          <!-- Descripción Column -->
          <ng-container matColumnDef="descripcion">
            <mat-header-cell *matHeaderCellDef>Descripción</mat-header-cell>
            <mat-cell *matCellDef="let element">
              <span class="description-cell" [matTooltip]="element.descripcion">
                {{element.descripcion}}
              </span>
            </mat-cell>
          </ng-container>

          <!-- Fecha de Expiración Column -->
          <ng-container matColumnDef="fecha_expiracion">
            <mat-header-cell *matHeaderCellDef>Fecha de Expiración</mat-header-cell>
            <mat-cell *matCellDef="let element">{{element.fecha_expiracion | date:'dd/MM/yyyy'}}</mat-cell>
          </ng-container>

          <!-- Estado Column -->
          <ng-container matColumnDef="is_active">
            <mat-header-cell *matHeaderCellDef>Estado</mat-header-cell>
            <mat-cell *matCellDef="let element">
              <mat-chip-list>
                <mat-chip [color]="element.is_active ? 'primary' : 'warn'" selected>
                  {{element.is_active ? 'Activo' : 'Inactivo'}}
                </mat-chip>
              </mat-chip-list>
            </mat-cell>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="acciones">
            <mat-header-cell *matHeaderCellDef>Acciones</mat-header-cell>
            <mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" (click)="abrirDialog(element)" matTooltip="Editar">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button [color]="element.is_active ? 'warn' : 'primary'" 
                      (click)="eliminarIpPermitida(element)"
                      [matTooltip]="element.is_active ? 'Inactivar' : 'Activar'">
                <mat-icon>{{element.is_active ? 'block' : 'check_circle'}}</mat-icon>
              </button>
            </mat-cell>
          </ng-container>

          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>

        <mat-paginator 
          [pageSizeOptions]="[5, 10, 25]"
          [pageSize]="10"
          showFirstLastButtons
          aria-label="Seleccionar página">
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>


