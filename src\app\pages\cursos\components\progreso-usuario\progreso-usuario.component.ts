import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { Subject, of } from 'rxjs';
import { takeUntil, catchError } from 'rxjs/operators';
import { ProgresoService } from '@app/services/progreso.service';
import { ProgresoUsuario, ProgresoLeccion } from '@app/models/backend/curso/progreso.model';
import { Curso } from '@app/models/backend/curso/curso.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { Store, select } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';

@Component({
  selector: 'app-progreso-usuario',
  templateUrl: './progreso-usuario.component.html',
  styleUrls: ['./progreso-usuario.component.scss']
})
export class ProgresoUsuarioComponent implements OnInit, OnDestroy {
  @Input() curso!: Curso;
  @Input() isDarkTheme: boolean = false;

  user: User | null = null;
  progreso: ProgresoUsuario | null = null;
  leccionesCompletadas: ProgresoLeccion[] = [];
  loading: boolean = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private progresoService: ProgresoService,
    private notification: NotificationService,
    private store: Store<fromRoot.State>
  ) { }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getCurrentUser(): void {
    this.store.pipe(
      select(fromUser.getUser),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
      if (this.user && this.curso) {
        this.loadProgreso();
      }
    });
  }

  private loadProgreso(): void {
    if (!this.user || !this.curso) {
      return;
    }

    this.loading = true;
    this.error = null;

    this.progresoService.getProgresoByCursoAndUsuario(this.curso.id, this.user.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.progreso = response.data;
            this.loadLeccionesCompletadas();
          } else {
            // Si no hay progreso, creamos uno nuevo
            this.createProgreso();
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar el progreso. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar progreso:', error);
        }
      });
  }

  private createProgreso(): void {
    if (!this.user || !this.curso) {
      return;
    }

    this.progresoService.createProgreso({
      usuarioId: this.user.id,
      cursoId: this.curso.id,
      estado: 'EN_PROGRESO'
    })
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.progreso = response.data;
          this.loading = false;
        } else {
          this.loading = false;
          this.error = response.msg || 'Error al crear el progreso';
          this.notification.error(this.error);
        }
      },
      error: (error) => {
        this.loading = false;
        this.error = 'Error al crear el progreso. Por favor, inténtelo de nuevo.';
        this.notification.error(this.error);
        console.error('Error al crear progreso:', error);
      }
    });
  }

  private loadLeccionesCompletadas(): void {
    if (!this.user || !this.curso) {
      this.loading = false;
      return;
    }

    this.progresoService.getLeccionesCompletadas(this.curso.id, this.user.id)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          this.loading = false;
          console.error('Error al cargar lecciones completadas:', error);
          // Si hay un error 500, probablemente el endpoint no existe o hay un problema en el backend
          // En este caso, simplemente continuamos con un array vacío
          return of({ rpta: 0, msg: 'Error al cargar lecciones completadas', data: [] });
        })
      )
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.leccionesCompletadas = response.data;
          } else {
            // Si la respuesta no es exitosa, inicializamos con un array vacío
            this.leccionesCompletadas = [];
          }
        }
      });
  }

  isLeccionCompletada(leccionId: number): boolean {
    return this.leccionesCompletadas.some(lc => lc.leccionId === leccionId);
  }

  getProgresoLabel(): string {
    if (!this.progreso) {
      return 'Sin iniciar';
    }

    switch (this.progreso.estado) {
      case 'EN_PROGRESO':
        return 'En progreso';
      case 'COMPLETADO':
        return 'Completado';
      case 'ABANDONADO':
        return 'Abandonado';
      default:
        return 'Desconocido';
    }
  }

  getProgresoColor(): string {
    if (!this.progreso) {
      return 'gray';
    }

    switch (this.progreso.estado) {
      case 'EN_PROGRESO':
        return '#3f51b5';
      case 'COMPLETADO':
        return '#4caf50';
      case 'ABANDONADO':
        return '#f44336';
      default:
        return 'gray';
    }
  }
}
