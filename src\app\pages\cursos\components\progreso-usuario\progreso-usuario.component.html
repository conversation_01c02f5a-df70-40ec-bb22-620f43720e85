<div class="progreso-container" [ngClass]="{'dark-theme': isDarkTheme}">
  <div class="progreso-header">
    <h3>Tu Progreso en el Curso</h3>
  </div>

  <div class="progreso-content">
    <app-spinner *ngIf="loading"></app-spinner>

    <div *ngIf="error" class="error-message">
      <mat-icon>error</mat-icon>
      <span>{{ error }}</span>
    </div>

    <div *ngIf="!loading && progreso" class="progreso-info">
      <div class="progreso-estado">
        <div class="estado-label">Estado:</div>
        <div class="estado-value" [style.color]="getProgresoColor()">
          {{ getProgresoLabel() }}
        </div>
      </div>

      <div class="progreso-porcentaje">
        <div class="porcentaje-label">Progreso:</div>
        <div class="porcentaje-bar">
          <div class="porcentaje-fill" [style.width.%]="progreso ? progreso.porcentajeCompletado : 0" [style.background-color]="getProgresoColor()"></div>
        </div>
        <div class="porcentaje-value">{{ progreso ? progreso.porcentajeCompletado : 0 }}%</div>
      </div>

      <div class="progreso-fechas">
        <div class="fecha-item">
          <mat-icon>calendar_today</mat-icon>
          <span>Inicio: {{ progreso && progreso.fechaInicio ? (progreso.fechaInicio | arrayToDate | date:'dd/MM/yyyy') : 'No disponible' }}</span>
        </div>
        <div class="fecha-item">
          <mat-icon>update</mat-icon>
          <span>Último acceso: {{ progreso && progreso.fechaUltimoAcceso ? (progreso.fechaUltimoAcceso | arrayToDate | date:'dd/MM/yyyy HH:mm') : 'No disponible' }}</span>
        </div>
        <div class="fecha-item" *ngIf="progreso && progreso.fechaCompletado">
          <mat-icon>check_circle</mat-icon>
          <span>Completado: {{ progreso.fechaCompletado | arrayToDate | date:'dd/MM/yyyy' }}</span>
        </div>
      </div>
    </div>

    <div *ngIf="!loading && !progreso" class="no-progreso">
      <mat-icon>school</mat-icon>
      <p>Aún no has comenzado este curso</p>
    </div>
  </div>
</div>
