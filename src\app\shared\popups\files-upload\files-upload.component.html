<div class="flex flex-col h-full">
  <div class="mb-3">
    <h3 class="text-lg font-medium">Subir archivos</h3>
  </div>

  <div class="flex-grow">
    <ng-container *ngIf="!files.length">
    <div class="flex flex-col items-center justify-center border-2 border-dashed border-blue-300 dark:border-blue-700 rounded p-5"
         appDropZone
         (hovered)="toggleHover($event)"
         [ngClass]="{'border-blue-500 dark:border-blue-500': isHovering}"
         (dropped)="onDrop($event)">
      <div class="text-gray-600 dark:text-gray-300" [ngClass]="{'opacity-30': isHovering}">Deja tus archivos aquí</div>
      <div [ngClass]="{'opacity-30': isHovering}">
        <label class="cursor-pointer">
          <input class="hidden" [multiple]="data.multiple" type="file" (change)="onDropFile($event)" />
          <span class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">seleccionar</span>
        </label>
      </div>
    </div>
    <div class="text-red-500 mt-2" *ngIf="isError">
      Seleccione un archivo
    </div>
  </ng-container>

  <ng-container *ngIf="files">
    <app-upload *ngFor="let file of files" [file]="file" (completed)="onUploadComplete($event)"></app-upload>
  </ng-container>
  </div>

  <div class="flex flex-row-reverse mt-3">
    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 dark:bg-blue-700 dark:hover:bg-blue-600" (click)="onComplete()" *ngIf="filesURLs.length">Completado</button>
    <button class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 mr-2" (click)="onClose()" *ngIf="!filesURLs.length">Cerrar</button>
  </div>
</div>
