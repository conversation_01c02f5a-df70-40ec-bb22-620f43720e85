<div class="bg-white rounded-xl shadow-xl w-full max-w-2xl mx-auto">
  <!-- Título compacto -->
  <div class="bg-blue-100 text-gray-800 px-6 py-3 rounded-t-xl border-b">
    <h2 class="text-lg font-semibold">
      {{ isNewData ? "Crear manual" : "Editar manual" }}
    </h2>
  </div>

  <div class="px-6 pt-4 pb-6 sm:px-8">
    <form [formGroup]="manualForm" autocomplete="off" class="space-y-4">
      <input type="hidden" formControlName="id" />

      <!-- Nombre -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Nombre</label
        >
        <input
          type="text"
          formControlName="nombre"
          placeholder="Ingrese el nombre"
          class="w-full h-11 px-4 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none"
        />
        <p
          *ngIf="form['nombre'].errors?.['required']"
          class="text-sm text-red-600 mt-1"
        >
          El nombre es requerido
        </p>
        <p
          *ngIf="form['nombre'].errors?.['maxlength']"
          class="text-sm text-red-600 mt-1"
        >
          No debe exceder 50 caracteres
        </p>
      </div>

      <!-- Tipo -->
      <div>
        <label class="block text-sm font-semibold text-gray-700 mb-1"
          >Tipo</label
        >
        <select
          formControlName="tipo"
          class="w-full h-12 px-4 border border-gray-300 rounded-lg shadow-sm bg-white focus:ring-2 focus:ring-blue-400 focus:outline-none"
        >
          <option value="">Seleccione un tipo</option>
          <option value="S">Manual de Software</option>
          <option value="B">Gestión de Backlog</option>
          <option value="M">Vodafone Micropyme</option>
          <option value="R">Vodafone Residencial</option>
          <option value="T">Tarifario</option>
          <option value="I">Informativo</option>
          <option value="O">Otro</option>
        </select>
        <p
          *ngIf="form['tipo'].errors?.['required']"
          class="text-sm text-red-600 mt-1"
        >
          El tipo es requerido
        </p>
      </div>

      <!-- Sede -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Sede
          <span class="text-xs text-gray-500"
            >(Opcional - Dejar vacío para manual global)</span
          >
        </label>
        <select
          formControlName="sedeId"
          class="w-full h-11 px-4 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none"
          [disabled]="loadingSedes"
        >
          <option value="">Manual global (todas las sedes)</option>
          <option *ngFor="let sede of sedes" [value]="sede.id">
            {{ sede.nombre }}
          </option>
        </select>
        <div *ngIf="loadingSedes" class="flex items-center mt-1">
          <div
            class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"
          ></div>
          <span class="text-xs text-gray-500">Cargando sedes...</span>
        </div>
      </div>

      <!-- Estado -->
      <div>
        <label class="block text-sm font-semibold text-gray-700 mb-1"
          >Estado</label
        >
        <div class="flex items-center">
          <label class="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              formControlName="isActive"
              class="sr-only peer"
            />
            <div
              class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
            ></div>
            <span
              class="ms-3 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {{ form["isActive"].value ? "Activo" : "Inactivo" }}
            </span>
          </label>
        </div>
      </div>

      <!-- Archivo -->
      <div
        class="p-5 bg-gray-50 border border-dashed border-gray-300 rounded-lg"
      >
        <label class="block text-sm font-semibold text-gray-700 mb-3"
          >Archivo</label
        >

        <!-- Archivo actual -->
        <div
          *ngIf="form['archivo']?.value && !uploadFiles.length"
          class="flex justify-between items-center mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200"
        >
          <div class="flex items-center gap-2 text-sm text-gray-700">
            <i class="fa-solid fa-file-pdf text-red-500 text-lg"></i>
            <span class="font-medium">Archivo actual:</span>
            <span class="text-gray-600 truncate max-w-[150px]">{{
              getFileName(form["archivo"].value)
            }}</span>
            <a
              [href]="getFileUrl(form['archivo'].value)"
              target="_blank"
              class="text-blue-600 underline hover:text-blue-800 flex items-center gap-1"
            >
              <i class="fa-solid fa-eye"></i> Ver
            </a>
          </div>
          <button
            type="button"
            (click)="openFileSelector()"
            class="text-sm px-3 py-1 border border-blue-500 text-blue-600 rounded hover:bg-blue-50 flex items-center gap-2"
          >
            <i class="fa-solid fa-rotate"></i> Reemplazar
          </button>
        </div>

        <!-- Dropzone -->
        <div
          *ngIf="!uploadFiles.length && !form['archivo']?.value"
          class="p-6 border border-dashed border-gray-400 rounded-lg text-center bg-white cursor-pointer hover:bg-blue-50"
          (click)="openFileSelector()"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)"
          [class.bg-blue-100]="isDragging"
        >
          <i class="fa-solid fa-cloud-arrow-up text-3xl text-gray-400 mb-2"></i>
          <p class="text-sm text-gray-600 mb-3">
            Haz clic o arrastra el archivo aquí
          </p>
          <button
            type="button"
            class="text-sm px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
          >
            Seleccionar archivo
          </button>
        </div>

        <!-- Input de archivo oculto (siempre presente) -->
        <input
          type="file"
          id="file"
          #fileInput
          (change)="onFileSelected(fileInput)"
          class="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
        />

        <!-- Archivo seleccionado -->
        <div
          *ngIf="uploadFiles.length > 0"
          class="mt-4 flex items-center justify-between bg-white p-3 rounded border border-gray-200"
        >
          <div class="flex items-center gap-2">
            <i class="fa-solid fa-file-lines text-gray-600"></i>
            <span class="text-sm">{{ uploadFiles[0].name }}</span>
          </div>
          <button
            type="button"
            (click)="removeSelectedFile()"
            class="text-red-500 hover:text-red-700"
          >
            <i class="fa-solid fa-xmark"></i>
          </button>
        </div>

        <div class="mt-3 text-xs text-gray-500">
          <p>Formatos permitidos: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT</p>
          <p>Tamaño máximo: 5MB</p>
        </div>
      </div>
    </form>

    <!-- Acciones -->
    <div class="flex justify-end gap-4 mt-8">
      <button
        type="button"
        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md"
        (click)="onCancel()"
      >
        Cancelar
      </button>

      <button
        type="button"
        (click)="saveData()"
        [disabled]="manualForm.invalid"
        class="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
      >
        {{ isNewData ? "Agregar" : "Actualizar" }}
      </button>
    </div>
  </div>
</div>
