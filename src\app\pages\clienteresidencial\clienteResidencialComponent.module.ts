import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClienteResidencialListModule } from './pages/clienteresidencial-list/clienteResidencial-list.module';
import { ClienteResidencialRoutingModule } from './clienteResidencialComponent-routing.module';
import { GraficosSedePageModule } from './pages/graficos-sede-page/graficos-sede-page.module';
import { SharedComponentsModule } from './components/shared/shared-components.module';

@NgModule({
  imports: [
    CommonModule,
    ClienteResidencialRoutingModule,
    ClienteResidencialListModule,
    GraficosSedePageModule,
    SharedComponentsModule,
  ],
  exports: [
    ClienteResidencialListModule,
    GraficosSedePageModule,
    ClienteResidencialRoutingModule,
  ],
})
export class ClienteResidencialComponentModule {}
