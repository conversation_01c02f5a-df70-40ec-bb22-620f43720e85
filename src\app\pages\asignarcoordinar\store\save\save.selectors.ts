import { createFeatureSelector, createSelector } from '@ngrx/store';
import { CoordinadorState } from './save.reducer';

// Selecciona el slice "coordinador" del state global
export const selectCoordinadorState = createFeatureSelector<CoordinadorState>('coordinador');

export const selectPaginationMetadata = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => ({
    totalItems: state.totalItems,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    size: state.size,
    hasNext: state.hasNext,
    hasPrevious: state.hasPrevious,
  })
);


export const selectExcelBlob = createSelector(
  selectCoordinadorState, // asegúrate que sea el feature correcto
  (state) => state.excelBlob
);

// Selectores con la convención "select"
export const selectAsesoresConClientes = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.asesoresConClientes
);

export const selectCoordinadorLoading = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.loading
);

export const selectCoordinadorError = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.error
);

// Además, si necesitas los "get", los exportamos también:
export const getAllCoordinadores = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.coordinadores
);

export const getAsesoresDisponibles = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.asesoresDisponibles
);

export const getClientesDeAsesor = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.clientesDeAsesor
);

export const getSelectedAsesorId = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.selectedAsesorId
);

export const getCoordinadorLoading = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.loading
);

export const getCoordinadorError = createSelector(
  selectCoordinadorState,
  (state: CoordinadorState) => state.error
);
export const selectClienteSeleccionado = createSelector(
  selectCoordinadorState, // <- tu feature state
  (state) => state.selectedCliente
);

