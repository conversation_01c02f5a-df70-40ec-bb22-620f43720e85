<div class="p-2">
  <div class="flex justify-between items-center mb-2">
    <h3 class="text-sm font-medium text-indigo-600 dark:text-indigo-400 flex items-center">
      <mat-icon class="mr-1 text-sm">view_module</mat-icon>
      Mó<PERSON>los del Curso
    </h3>
    <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editModulo.emit(null)">
      <mat-icon class="mr-1 text-xs">add</mat-icon> Nuevo
    </button>
  </div>

  <div class="relative min-h-[50px]">
    <app-spinner *ngIf="loading"></app-spinner>

    <div *ngIf="error" class="flex items-center text-red-600 dark:text-red-400 text-xs mb-2">
      <mat-icon class="mr-1 text-xs">error</mat-icon>
      <span>{{ error }}</span>
    </div>

    <div *ngIf="!loading && !error && modulos.length === 0" class="flex flex-col items-center py-3 text-center">
      <mat-icon class="text-indigo-500 dark:text-indigo-400 mb-1">school</mat-icon>
      <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">No hay módulos disponibles</p>
      <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editModulo.emit(null)">
        <mat-icon class="mr-1 text-xs">add</mat-icon> Crear Módulo
      </button>
    </div>

    <div cdkDropList (cdkDropListDropped)="onDrop($event)" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" *ngIf="modulos.length > 0">
      <div *ngFor="let modulo of modulos" class="modulo-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl relative p-4 cursor-move transition-all duration-300 hover:-translate-y-1" cdkDrag>
        <!-- Drag handle -->
        <div class="absolute top-3 right-3 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors" cdkDragHandle>
          <mat-icon class="text-lg">drag_indicator</mat-icon>
        </div>

        <!-- Header con título y estado -->
        <div class="mb-3 pr-8">
          <div class="flex items-start justify-between mb-2">
            <h4 class="text-base font-semibold text-gray-800 dark:text-white line-clamp-2 leading-tight">
              {{ modulo.titulo || modulo.nombre }}
            </h4>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
              Orden: {{ modulo.orden }}
            </span>
            <div class="text-sm px-2 py-1 rounded-full font-medium"
                 [ngClass]="{'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300': modulo.estado === 'A',
                            'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300': modulo.estado === 'I'}">
              {{ modulo.estado === 'A' ? 'Activo' : 'Inactivo' }}
            </div>
          </div>
        </div>

        <!-- Descripción -->
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3 min-h-[60px]">
          {{ modulo.descripcion }}
        </p>

        <!-- Estadísticas -->
        <div class="flex items-center justify-center space-x-6 mb-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg box-border">
          <div class="text-center">
            <div class="flex items-center justify-center mb-1">
              <mat-icon class="text-indigo-600 dark:text-indigo-400 text-lg">folder</mat-icon>
            </div>
            <span class="text-lg font-bold text-gray-800 dark:text-white">{{ modulo.secciones?.length || 0 }}</span>
            <p class="text-xs text-gray-500 dark:text-gray-400">Secciones</p>
          </div>

          <div class="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>

          <div class="text-center">
            <div class="flex items-center justify-center mb-1">
              <mat-icon class="text-green-600 dark:text-green-400 text-lg">video_library</mat-icon>
            </div>
            <span class="text-lg font-bold text-gray-800 dark:text-white">{{ getTotalLecciones(modulo) }}</span>
            <p class="text-xs text-gray-500 dark:text-gray-400">Lecciones</p>
          </div>
        </div>

        <!-- Acciones -->
        <div class="flex justify-between space-x-2 border-t border-gray-100 dark:border-gray-700 pt-3">
          <button
            class="flex-1 text-sm px-3 py-2 text-indigo-600 hover:bg-indigo-50 dark:text-indigo-400 dark:hover:bg-indigo-900/20 rounded-lg flex items-center justify-center transition-colors font-medium"
            (click)="onViewLecciones(modulo)"
            matTooltip="Ver secciones del módulo">
            <mat-icon class="mr-1 text-sm">folder</mat-icon>
            Secciones
          </button>

          <button
            class="flex-1 text-sm px-3 py-2 text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20 rounded-lg flex items-center justify-center transition-colors font-medium"
            (click)="onEditModulo(modulo)"
            matTooltip="Editar módulo">
            <mat-icon class="mr-1 text-sm">edit</mat-icon>
            Editar
          </button>

          <button
            class="flex-1 text-sm px-3 py-2 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded-lg flex items-center justify-center transition-colors font-medium"
            (click)="onDeleteModulo(modulo.id)"
            matTooltip="Eliminar módulo">
            <mat-icon class="mr-1 text-sm">delete</mat-icon>
            Eliminar
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
