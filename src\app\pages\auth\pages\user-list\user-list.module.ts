import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { UserListRoutingModule } from './user-list-routing.module';
import { UserListComponent } from './user-list.component';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSidenavModule } from '@angular/material/sidenav';

// O<PERSON>s módulos que uses (por ejemplo, FlexLayout, SpinnerModule, etc.)
import { FlexLayoutModule } from '@angular/flex-layout';
import { SpinnerModule } from '@app/shared/indicators';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { EditUserDialogComponent } from './EditUserDialogComponent';
import { MatDialogModule } from '@angular/material/dialog';
import { ConfirmDialogComponent } from './ConfirmDialogComponent';
import { MatPaginatorModule } from '@angular/material/paginator';
import { DataTableModule } from '@app/shared/components';
import { SedeNombrePipe } from '@app/pipes/sede-nombre.pipe';
import { RegistrationIndividualModule } from '@app/pages/auth/pages/registrationindividual/registrationindividual.module';

@NgModule({
  declarations: [
    UserListComponent,
    EditUserDialogComponent,
    ConfirmDialogComponent,
    SedeNombrePipe
  ],
  imports: [
    CommonModule,
    UserListRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatCardModule,
    FlexLayoutModule,
    SpinnerModule,
    MatDividerModule,
    MatTableModule,
    MatProgressBarModule,
    MatSidenavModule,
    MatSelectModule,
    MatDialogModule,
    MatPaginatorModule,
    MatCheckboxModule,
    DataTableModule,
    RegistrationIndividualModule
  ],
  providers: [DatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class UserListModule { }
