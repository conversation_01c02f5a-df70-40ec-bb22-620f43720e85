<div class="cuestionario-container">
  <!-- Pantalla de carga -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Cargando cuestionario...</p>
  </div>

  <!-- Mensaje de error -->
  <div *ngIf="error" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
  </div>

  <!-- Información del cuestionario (antes de iniciar) -->
  <div *ngIf="!loading && !error && cuestionario && !iniciado && !finalizado" class="cuestionario-info">
    <h2>{{ cuestionario.titulo }}</h2>
    <div class="cuestionario-descripcion">
      <p>{{ cuestionario.descripcion }}</p>
    </div>

    <div class="cuestionario-detalles">
      <div class="detalle-item">
        <mat-icon>help</mat-icon>
        <span>{{ cuestionario.preguntas.length || 0 }} preguntas</span>
      </div>
      <div class="detalle-item" *ngIf="cuestionario.tiempoLimite">
        <mat-icon>timer</mat-icon>
        <span>Tiempo límite: {{ cuestionario.tiempoLimite }} minutos</span>
      </div>
      <div class="detalle-item">
        <mat-icon>grade</mat-icon>
        <span>Puntaje mínimo para aprobar: {{ cuestionario.puntajeAprobacion }}%</span>
      </div>
      <div class="detalle-item">
        <mat-icon>repeat</mat-icon>
        <span>Intentos máximos: {{ cuestionario.intentosMaximos }}</span>
      </div>
    </div>

    <div class="cuestionario-acciones">
      <button mat-raised-button color="primary" [disabled]="!puedeIntentar" (click)="iniciarCuestionario()">
        <mat-icon>play_arrow</mat-icon>
        Iniciar Cuestionario
      </button>
      <p *ngIf="!puedeIntentar" class="intentos-agotados">
        <mat-icon color="warn">warning</mat-icon>
        Has agotado todos los intentos disponibles para este cuestionario.
      </p>
    </div>
  </div>

  <!-- Cuestionario en progreso -->
  <div *ngIf="!loading && !error && iniciado && !finalizado" class="cuestionario-progreso">
    <!-- Barra de progreso -->
    <div class="progreso-header">
      <div class="progreso-info">
        <span>Pregunta {{ preguntaIndex + 1 }} de {{ preguntas.length }}</span>
        <mat-progress-bar mode="determinate" [value]="getPorcentajeProgreso()"></mat-progress-bar>
      </div>

      <!-- Temporizador -->
      <div *ngIf="tiempoTotal > 0" class="tiempo-restante">
        <mat-icon>timer</mat-icon>
        <span>{{ formatTiempo(tiempoRestante) }}</span>
        <mat-progress-bar mode="determinate" [value]="getPorcentajeTiempo()" [color]="getColorTiempo()"></mat-progress-bar>
      </div>
    </div>

    <!-- Pregunta actual -->
    <div *ngIf="preguntaActual" class="pregunta-container">
      <h3 class="pregunta-enunciado">{{ preguntaActual.enunciado }}</h3>

      <form [formGroup]="respuestaForm" (ngSubmit)="responderPregunta()">
        <!-- Opciones según el tipo de pregunta -->
        <div [ngSwitch]="preguntaActual.tipo" class="opciones-container">

          <!-- Opción múltiple (una sola respuesta) -->
          <div *ngSwitchCase="TipoPregunta.OPCION_MULTIPLE" class="opcion-multiple">
            <mat-radio-group formControlName="respuestaId" class="opciones-radio-group">
              <mat-radio-button *ngFor="let respuesta of preguntaActual.respuestas" [value]="respuesta.id" class="opcion-item">
                {{ respuesta.texto }}
              </mat-radio-button>
            </mat-radio-group>
          </div>

          <!-- Selección múltiple (varias respuestas) -->
          <div *ngSwitchCase="TipoPregunta.SELECCION_MULTIPLE" class="seleccion-multiple">
            <mat-selection-list formControlName="respuestasIds" class="opciones-checkbox-group">
              <mat-list-option *ngFor="let respuesta of preguntaActual.respuestas" [value]="respuesta.id" class="opcion-item">
                {{ respuesta.texto }}
              </mat-list-option>
            </mat-selection-list>
          </div>

          <!-- Verdadero/Falso -->
          <div *ngSwitchCase="TipoPregunta.VERDADERO_FALSO" class="verdadero-falso">
            <mat-button-toggle-group formControlName="verdaderoFalso" class="verdadero-falso-group">
              <mat-button-toggle [value]="true">Verdadero</mat-button-toggle>
              <mat-button-toggle [value]="false">Falso</mat-button-toggle>
            </mat-button-toggle-group>
          </div>

          <!-- Texto libre -->
          <div *ngSwitchCase="TipoPregunta.TEXTO_LIBRE" class="texto-libre">
            <mat-form-field appearance="outline" class="texto-libre-field">
              <mat-label>Tu respuesta</mat-label>
              <textarea matInput formControlName="textoRespuesta" rows="4" placeholder="Escribe tu respuesta aquí"></textarea>
            </mat-form-field>
          </div>
        </div>

        <div class="pregunta-acciones">
          <button mat-raised-button color="primary" type="submit">
            <mat-icon>check</mat-icon>
            Responder
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Resultados del cuestionario -->
  <div *ngIf="!loading && !error && finalizado && respuestaUsuario" class="cuestionario-resultados">
    <h2>Resultados del Cuestionario</h2>

    <div class="resultado-header" [ngClass]="{'aprobado': respuestaUsuario.aprobado, 'reprobado': !respuestaUsuario.aprobado}">
      <mat-icon *ngIf="respuestaUsuario.aprobado">check_circle</mat-icon>
      <mat-icon *ngIf="!respuestaUsuario.aprobado">cancel</mat-icon>
      <h3>{{ respuestaUsuario.aprobado ? '¡Aprobado!' : 'No Aprobado' }}</h3>
    </div>

    <div class="resultado-detalles">
      <div class="detalle-item">
        <span class="detalle-label">Puntaje obtenido:</span>
        <span class="detalle-valor">{{ respuestaUsuario.puntajeObtenido }} puntos</span>
      </div>
      <div class="detalle-item">
        <span class="detalle-label">Porcentaje de aprobación:</span>
        <span class="detalle-valor">{{ respuestaUsuario.porcentajeAprobacion }}%</span>
      </div>
      <div class="detalle-item">
        <span class="detalle-label">Intento:</span>
        <span class="detalle-valor">{{ respuestaUsuario.numeroIntento }} de {{ cuestionario?.intentosMaximos }}</span>
      </div>
      <div class="detalle-item">
        <span class="detalle-label">Fecha de inicio:</span>
        <span class="detalle-valor">{{ respuestaUsuario.fechaInicio | date:'dd/MM/yyyy HH:mm' }}</span>
      </div>
      <div class="detalle-item">
        <span class="detalle-label">Fecha de finalización:</span>
        <span class="detalle-valor">{{ respuestaUsuario.fechaFin | date:'dd/MM/yyyy HH:mm' }}</span>
      </div>
    </div>

    <!-- Mostrar respuestas si está configurado -->
    <div *ngIf="cuestionario?.mostrarRespuestas" class="respuestas-detalle">
      <h3>Detalle de Respuestas</h3>

      <div *ngFor="let detalle of respuestaUsuario.detallesRespuestas" class="respuesta-item"
           [ngClass]="{'correcta': detalle.esCorrecta, 'incorrecta': !detalle.esCorrecta}">
        <div class="respuesta-pregunta">
          <mat-icon *ngIf="detalle.esCorrecta">check_circle</mat-icon>
          <mat-icon *ngIf="!detalle.esCorrecta">cancel</mat-icon>
          <span>{{ detalle.preguntaEnunciado }}</span>
        </div>
        <div class="respuesta-texto">
          <span class="respuesta-label">Tu respuesta:</span>
          <span class="respuesta-valor">{{ detalle.textoRespuesta || detalle.respuestaTexto }}</span>
        </div>
      </div>
    </div>

    <div class="resultado-acciones">
      <button mat-raised-button color="primary" (click)="reiniciarCuestionario()" *ngIf="puedeIntentar">
        <mat-icon>replay</mat-icon>
        Intentar Nuevamente
      </button>
    </div>
  </div>
</div>
