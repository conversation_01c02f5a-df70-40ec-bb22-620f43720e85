import { Component, OnInit, OnD<PERSON>roy, OnChanges, SimpleChanges, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LeccionService } from '@app/services/leccion.service';
import { Leccion, TipoLeccion } from '@app/models/backend/curso/leccion.model';
import { Modulo } from '@app/models/backend/curso/modulo.model';
import { Seccion } from '@app/models/backend/curso/seccion.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatDialog } from '@angular/material/dialog';
import { CuestionarioService } from '@app/services/cuestionario.service';
import { CuestionarioFormComponent } from '../cuestionario-form/cuestionario-form.component';

@Component({
  selector: 'app-leccion-list',
  templateUrl: './leccion-list.component.html',
  styleUrls: ['./leccion-list.component.scss']
})
export class LeccionListComponent implements OnInit, OnDestroy, OnChanges {
  @Input() modulo?: Modulo;
  @Input() seccion?: Seccion;
  @Input() isDarkTheme: boolean = false;
  @Output() editLeccion = new EventEmitter<Leccion | null>();
  @Output() viewLeccion = new EventEmitter<Leccion>();
  @Output() back = new EventEmitter<void>();

  lecciones: Leccion[] = [];
  loading: boolean = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  // Enum para acceder desde el template
  TipoLeccion = TipoLeccion;

  constructor(
    private leccionService: LeccionService,
    private notification: NotificationService,
    private dialog: MatDialog,
    private cuestionarioService: CuestionarioService
  ) { }

  ngOnInit(): void {
    this.loadLecciones();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Detectar cambios en modulo o seccion
    if (changes['modulo'] || changes['seccion']) {
      // Limpiar lecciones anteriores inmediatamente
      this.lecciones = [];
      this.error = null;

      // Cargar nuevas lecciones
      this.loadLecciones();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Método centralizado para cargar lecciones
   */
  private loadLecciones(): void {
    if (this.seccion?.id) {
      this.loadLeccionesBySeccion();
    } else if (this.modulo?.id) {
      this.loadLeccionesByModulo();
    }
  }

  loadLeccionesByModulo(): void {
    if (!this.modulo?.id) return;

    this.loading = true;
    this.error = null;

    this.leccionService.getLeccionesByModuloId(this.modulo.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.lecciones = response.data;
          } else {
            this.error = response.msg || 'Error al cargar las lecciones';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar las lecciones. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar lecciones:', error);
        }
      });
  }

  loadLeccionesBySeccion(): void {
    if (!this.seccion?.id) return;

    this.loading = true;
    this.error = null;

    this.leccionService.getLeccionesBySeccionId(this.seccion.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.lecciones = response.data;
          } else {
            this.error = response.msg || 'Error al cargar las lecciones';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar las lecciones. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar lecciones:', error);
        }
      });
  }

  onEditLeccion(leccion: Leccion): void {
    this.editLeccion.emit(leccion);
  }

  onViewLeccion(leccion: Leccion): void {
    this.viewLeccion.emit(leccion);
  }

  onDeleteLeccion(id: number): void {
    if (confirm('¿Está seguro de que desea eliminar esta lección? Esta acción no se puede deshacer.')) {
      this.leccionService.deleteLeccion(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.notification.success('Lección eliminada exitosamente');
              if (this.seccion?.id) {
                this.loadLeccionesBySeccion();
              } else if (this.modulo?.id) {
                this.loadLeccionesByModulo();
              }
            } else {
              this.error = response.msg || 'Error al eliminar la lección';
              this.notification.error(this.error);

              // Si el error está relacionado con dependencias, mostrar un mensaje más específico
              if (response.msg && (
                  response.msg.includes('cuestionario') ||
                  response.msg.includes('progreso') ||
                  response.msg.includes('dependencias')
                )) {
                this.notification.success('La lección tiene cuestionarios o progreso de usuarios asociados. Estos datos también serán eliminados.');
              }
            }
          },
          error: (error) => {
            let errorMsg = 'Error al eliminar la lección. Por favor, inténtelo de nuevo.';

            // Intentar extraer un mensaje más específico del error
            if (error.error && error.error.msg) {
              errorMsg = error.error.msg;
            } else if (error.message) {
              errorMsg = error.message;
            }

            this.error = errorMsg;
            this.notification.error(this.error);
            console.error('Error al eliminar lección:', error);
          }
        });
    }
  }

  onDrop(event: CdkDragDrop<Leccion[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    // Actualizar el orden localmente
    moveItemInArray(this.lecciones, event.previousIndex, event.currentIndex);

    // Actualizar el orden en el servidor
    const leccionIds = this.lecciones.map(leccion => leccion.id);

    if (this.seccion?.id) {
      this.leccionService.reordenarLeccionesEnSeccion(this.seccion.id, leccionIds)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.notification.success('Orden de lecciones actualizado');
            } else {
              this.error = response.msg || 'Error al reordenar las lecciones';
              this.notification.error(this.error);
              this.loadLeccionesBySeccion(); // Recargar para restaurar el orden original
            }
          },
          error: (error) => {
            this.error = 'Error al reordenar las lecciones. Por favor, inténtelo de nuevo.';
            this.notification.error(this.error);
            console.error('Error al reordenar lecciones:', error);
            this.loadLeccionesBySeccion(); // Recargar para restaurar el orden original
          }
        });
    } else if (this.modulo?.id) {
      this.leccionService.reordenarLecciones(this.modulo.id, leccionIds)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.notification.success('Orden de lecciones actualizado');
            } else {
              this.error = response.msg || 'Error al reordenar las lecciones';
              this.notification.error(this.error);
              this.loadLeccionesByModulo(); // Recargar para restaurar el orden original
            }
          },
          error: (error) => {
            this.error = 'Error al reordenar las lecciones. Por favor, inténtelo de nuevo.';
            this.notification.error(this.error);
            console.error('Error al reordenar lecciones:', error);
            this.loadLeccionesByModulo(); // Recargar para restaurar el orden original
          }
        });
    }
  }

  onBack(): void {
    this.back.emit();
  }

  getDuracionFormateada(duracion?: number): string {
    if (!duracion) {
      return 'No especificada';
    }

    const horas = Math.floor(duracion / 60);
    const minutos = duracion % 60;

    if (horas > 0) {
      return `${horas}h ${minutos}m`;
    } else {
      return `${minutos} minutos`;
    }
  }

  /**
   * Abre el diálogo para gestionar el cuestionario asociado a la lección
   */
  gestionarCuestionario(leccion: Leccion): void {
    if (!leccion || !leccion.id) {
      this.notification.error('No se puede gestionar el cuestionario para esta lección');
      return;
    }

    // Verificar si ya existe un cuestionario para esta lección
    this.cuestionarioService.getCuestionarioByLeccionId(leccion.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          let cuestionarioId: number | undefined;

          if (response.rpta === 1 && response.data) {
            cuestionarioId = response.data.id;
          }

          // Abrir el diálogo para crear o editar el cuestionario
          const dialogRef = this.dialog.open(CuestionarioFormComponent, {
            width: '900px',
            data: {
              leccionId: leccion.id,
              cuestionarioId: cuestionarioId
            },
            disableClose: true
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              this.notification.success('Cuestionario guardado exitosamente');
            }
          });
        },
        error: (error) => {
          this.notification.error('Error al verificar el cuestionario existente');
          console.error('Error al verificar cuestionario:', error);
        }
      });
  }
}
