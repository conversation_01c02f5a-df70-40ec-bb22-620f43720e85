import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Direccion {
  id?: number;
  codigoVenta: string;
  direccion: string;
}

@Injectable({
  providedIn: 'root'
})
export class DireccionService {
  private apiUrl = 'https://apisozarusac.com/ventas/api/direcciones/';

  constructor(private http: HttpClient) {}

  obtenerDireccionesPorVenta(codigoVenta: string): Observable<Direccion[]> {
    return this.http.get<Direccion[]>(`${this.apiUrl}${codigoVenta}/`);
  }

  actualizarDirecciones(codigoVenta: string, direcciones: string[]): Observable<any> {
    const direccionesFormateadas = direcciones.map(direccion => ({
      codigoVenta,
      direccion
    }));
    return this.http.put(`${this.apiUrl}${codigoVenta}/`, direccionesFormateadas);
  }

  crearDireccion(direccion: Direccion): Observable<any> {
    return this.http.post(this.apiUrl, direccion);
  }

  eliminarDireccionesPorVenta(codigoVenta: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}eliminar/${codigoVenta}/`);
  }
}



