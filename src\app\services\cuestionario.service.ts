import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend';
import { Cuestionario, CuestionarioCreateRequest, CuestionarioUpdateRequest } from '@app/models/backend/curso/cuestionario.model';

@Injectable({
  providedIn: 'root'
})
export class CuestionarioService {
  private baseUrl = environment.url + 'api/cuestionarios';
  private cuestionariosSubject = new BehaviorSubject<Cuestionario[]>([]);
  public cuestionarios$ = this.cuestionariosSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene todos los cuestionarios
   */
  getAllCuestionarios(): Observable<GenericResponse<Cuestionario[]>> {
    return this.http.get<GenericResponse<Cuestionario[]>>(this.baseUrl).pipe(
      map(response => {
        if (response.data) {
          this.cuestionariosSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene un cuestionario por su ID
   */
  getCuestionarioById(id: number): Observable<GenericResponse<Cuestionario>> {
    return this.http.get<GenericResponse<Cuestionario>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene un cuestionario por el ID de la lección
   */
  getCuestionarioByLeccionId(leccionId: number): Observable<GenericResponse<Cuestionario>> {
    return this.http.get<GenericResponse<Cuestionario>>(`${this.baseUrl}/leccion/${leccionId}`);
  }

  /**
   * Crea un nuevo cuestionario
   */
  createCuestionario(cuestionario: CuestionarioCreateRequest): Observable<GenericResponse<Cuestionario>> {
    return this.http.post<GenericResponse<Cuestionario>>(this.baseUrl, cuestionario);
  }

  /**
   * Actualiza un cuestionario existente
   */
  updateCuestionario(id: number, cuestionario: CuestionarioUpdateRequest): Observable<GenericResponse<Cuestionario>> {
    return this.http.put<GenericResponse<Cuestionario>>(`${this.baseUrl}/${id}`, cuestionario);
  }

  /**
   * Elimina un cuestionario
   */
  deleteCuestionario(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }
}
