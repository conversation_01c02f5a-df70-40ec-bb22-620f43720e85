import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ModuloService } from '@app/services/modulo.service';
import { Modulo } from '@app/models/backend/curso/modulo.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-modulo-list',
  templateUrl: './modulo-list.component.html',
  styleUrls: ['./modulo-list.component.scss']
})
export class ModuloListComponent implements OnInit, OnDestroy {
  @Input() cursoId!: number;
  @Input() isDarkTheme: boolean = false;
  @Output() editModulo = new EventEmitter<Modulo | null>();
  @Output() viewLecciones = new EventEmitter<Modulo>();

  modulos: Modulo[] = [];
  loading: boolean = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private moduloService: ModuloService,
    private notification: NotificationService
  ) { }

  ngOnInit(): void {
    if (this.cursoId) {
      this.loadModulos();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadModulos(): void {
    this.loading = true;
    this.error = null;

    this.moduloService.getModulosByCursoId(this.cursoId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.modulos = response.data;
          } else {
            this.error = response.msg || 'Error al cargar los módulos';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar los módulos. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar módulos:', error);
        }
      });
  }

  onEditModulo(modulo: Modulo): void {
    this.editModulo.emit(modulo);
  }

  onViewLecciones(modulo: Modulo): void {
    this.viewLecciones.emit(modulo);
  }

  onDeleteModulo(id: number): void {
    if (confirm('¿Está seguro de que desea eliminar este módulo? Esta acción no se puede deshacer.')) {
      this.moduloService.deleteModulo(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.notification.success('Módulo eliminado exitosamente');
              this.loadModulos();
            } else {
              this.error = response.msg || 'Error al eliminar el módulo';
              this.notification.error(this.error);
            }
          },
          error: (error) => {
            this.error = 'Error al eliminar el módulo. Por favor, inténtelo de nuevo.';
            this.notification.error(this.error);
            console.error('Error al eliminar módulo:', error);
          }
        });
    }
  }

  /**
   * Calcula el total de lecciones en todas las secciones de un módulo
   */
  getTotalLecciones(modulo: Modulo): number {
    if (!modulo.secciones) {
      return 0;
    }

    return modulo.secciones.reduce((total, seccion) => {
      return total + (seccion.lecciones?.length || 0);
    }, 0);
  }

  onDrop(event: CdkDragDrop<Modulo[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    // Actualizar el orden localmente
    moveItemInArray(this.modulos, event.previousIndex, event.currentIndex);

    // Actualizar el orden en el servidor
    const moduloIds = this.modulos.map(modulo => modulo.id);
    this.moduloService.reordenarModulos(this.cursoId, moduloIds)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.notification.success('Orden de módulos actualizado');
          } else {
            this.error = response.msg || 'Error al reordenar los módulos';
            this.notification.error(this.error);
            this.loadModulos(); // Recargar para restaurar el orden original
          }
        },
        error: (error) => {
          this.error = 'Error al reordenar los módulos. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al reordenar módulos:', error);
          this.loadModulos(); // Recargar para restaurar el orden original
        }
      });
  }
}
