import { Faq } from '@app/models/backend/faq/faq.model';
import { createAction, props } from '@ngrx/store';

export const loadFaqs        = createAction('[FAQ] Load');
export const loadFaqsSuccess = createAction('[FAQ] Load success', props<{ faqs: Faq[] }>());
export const loadFaqsFailure = createAction('[FAQ] Load failure', props<{ error: any }>());

export const createFaq        = createAction('[FAQ] Create',  props<{ faq: Faq }>());
export const createFaqSuccess = createAction('[FAQ] Create success', props<{ faq: Faq }>());
export const createFaqFailure = createAction('[FAQ] Create failure', props<{ error: any }>());

export const updateFaq        = createAction('[FAQ] Update',  props<{ id: number; faq: Faq }>());
export const updateFaqSuccess = createAction('[FAQ] Update success', props<{ faq: Faq }>());
export const updateFaqFailure = createAction('[FAQ] Update failure', props<{ error: any }>());

export const deleteFaq        = createAction('[FAQ] Delete',  props<{ id: number }>());
export const deleteFaqSuccess = createAction('[FAQ] Delete success', props<{ id: number }>());
export const deleteFaqFailure = createAction('[FAQ] Delete failure', props<{ error: any }>());

// Acción disparada por WebSocketService al recibir /topic/faqs
export const faqsUpdatedWs = createAction('[FAQ] Update list (WS)', props<{ faqs: Faq[] }>());
