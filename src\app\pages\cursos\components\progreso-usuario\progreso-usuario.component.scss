.progreso-container {
  padding: 20px;
  
  &.dark-theme {
    background-color: #1e1e1e;
    color: #ffffff;
  }
}

.progreso-header {
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #3f51b5;
    
    .dark-theme & {
      color: #7986cb;
    }
  }
}

.progreso-content {
  position: relative;
  min-height: 100px;
}

.progreso-info {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  
  .dark-theme & {
    background-color: #2d2d2d;
  }
}

.progreso-estado {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  .estado-label {
    font-weight: 500;
    margin-right: 10px;
  }
  
  .estado-value {
    font-weight: 700;
    font-size: 18px;
  }
}

.progreso-porcentaje {
  margin-bottom: 20px;
  
  .porcentaje-label {
    font-weight: 500;
    margin-bottom: 5px;
  }
  
  .porcentaje-bar {
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
    
    .dark-theme & {
      background-color: #424242;
    }
    
    .porcentaje-fill {
      height: 100%;
      border-radius: 5px;
    }
  }
  
  .porcentaje-value {
    text-align: right;
    font-weight: 500;
  }
}

.progreso-fechas {
  .fecha-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    mat-icon {
      margin-right: 10px;
      color: #757575;
      
      .dark-theme & {
        color: #bdbdbd;
      }
    }
  }
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin: 15px 0;
  
  mat-icon {
    margin-right: 8px;
  }
}

.no-progreso {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #3f51b5;
    
    .dark-theme & {
      color: #7986cb;
    }
  }
  
  p {
    color: #757575;
    
    .dark-theme & {
      color: #bdbdbd;
    }
  }
}
