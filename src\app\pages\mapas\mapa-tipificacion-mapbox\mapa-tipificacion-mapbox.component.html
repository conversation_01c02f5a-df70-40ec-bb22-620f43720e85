<div class="container-fluid max-w-[95%] lg:max-w-[90%] mx-auto p-2">
  <mat-card class="w-full shadow-lg rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
    <mat-card-header class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/30 dark:to-purple-900/20 p-4">
      <div class="flex items-center w-full">
        <div class="flex justify-center items-center w-10 h-10 rounded-full bg-indigo-500 dark:bg-indigo-600 shadow-md mr-3">
          <mat-icon class="text-white">public</mat-icon>
        </div>
        <mat-card-title class="text-xl font-semibold text-gray-800 dark:text-white">Mapa de Tipificación Mapbox</mat-card-title>
      </div>
    </mat-card-header>

    <mat-card-content class="p-4 space-y-4">
      <!-- Sección de datos faltantes -->
      <div *ngIf="datosFaltantes" class="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm">
        <div class="flex items-start">
          <div class="flex-shrink-0 mt-0.5">
            <div class="flex justify-center items-center w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-800">
              <mat-icon class="text-yellow-600 dark:text-yellow-300 text-lg">info</mat-icon>
            </div>
          </div>
          <div class="ml-3">
            <h3 class="text-base font-medium text-yellow-800 dark:text-yellow-300 mb-1">Información incompleta</h3>
            <p class="text-sm text-yellow-700 dark:text-yellow-400 mb-3">
              Para mostrar una ubicación precisa en el mapa, necesitamos completar los siguientes datos:
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <!-- Provincia (si falta) -->
              <div *ngIf="camposFaltantes.includes('provincia')" class="flex flex-col w-full gap-2">
            <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Provincia
            </label>
            <div class="relative">
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="provincia"
                placeholder="Seleccione provincia"
                (focus)="onProvinciaFocus()"
              />
              <div *ngIf="cargandoProvincias" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                <mat-spinner diameter="16"></mat-spinner>
              </div>
            </div>

            <!-- Lista de provincias -->
            <div *ngIf="mostrarProvincias && provinciasCatastro.length > 0" class="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
              <div
                *ngFor="let prov of provinciasCatastro"
                class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                (click)="seleccionarProvincia(prov)"
              >
                {{ prov.Denominacion }}
              </div>
            </div>
          </div>

          <!-- Municipio (si falta) -->
          <div *ngIf="camposFaltantes.includes('municipio')" class="flex flex-col w-full gap-2">
            <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Municipio
            </label>
            <div class="relative">
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="municipio"
                placeholder="Seleccione municipio"
                [disabled]="!provincia"
                (focus)="onMunicipioFocus()"
              />
              <div *ngIf="cargandoMunicipios" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                <mat-spinner diameter="16"></mat-spinner>
              </div>
            </div>

            <!-- Lista de municipios -->
            <div *ngIf="municipiosCatastro.length > 0" class="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
              <div
                *ngFor="let mun of municipiosCatastro"
                class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                (click)="seleccionarMunicipio(mun)"
              >
                {{ mun.Denominacion }}
              </div>
            </div>
          </div>

          <!-- Vía (si falta) -->
          <div *ngIf="camposFaltantes.includes('vía')" class="flex flex-col w-full gap-2">
            <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Vía
            </label>
            <div class="relative">
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="via"
                placeholder="Seleccione vía"
                [disabled]="!municipio"
                (focus)="onViaFocus()"
              />
              <div *ngIf="cargandoVias" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                <mat-spinner diameter="16"></mat-spinner>
              </div>
            </div>

            <!-- Lista de vías -->
            <div *ngIf="viasCatastro.length > 0" class="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
              <div
                *ngFor="let v of viasCatastro"
                class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                (click)="seleccionarVia(v)"
              >
                {{ v.DenominacionCompleta }}
              </div>
            </div>
          </div>
        </div>

            <div class="mt-4 flex justify-end">
              <button
                mat-raised-button
                color="primary"
                (click)="completarDatos()"
                class="px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-md shadow-md hover:shadow-lg transition-all duration-200 flex items-center"
              >
                <mat-icon class="mr-2">search</mat-icon>
                <span>Buscar en el mapa</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Mensaje de error de geocodificación -->
      <div *ngIf="errorGeocoding" class="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm">
        <div class="flex items-start">
          <div class="flex-shrink-0 mt-0.5">
            <div class="flex justify-center items-center w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-800">
              <mat-icon class="text-yellow-600 dark:text-yellow-300 text-lg">warning</mat-icon>
            </div>
          </div>
          <div class="ml-3">
            <h4 class="text-base font-medium text-yellow-800 dark:text-yellow-300 mb-1">Aviso de geocodificación</h4>
            <p class="text-sm text-yellow-700 dark:text-yellow-400">{{ mensajeError }}</p>
            <p class="text-sm text-yellow-700 dark:text-yellow-400 mt-2">Se están utilizando coordenadas aproximadas. Esto puede ocurrir cuando la dirección contiene caracteres especiales o formatos no estándar.</p>
            <p class="text-sm text-yellow-700 dark:text-yellow-400 mt-1">Para mejorar la precisión, el sistema limpia automáticamente los paréntesis y otros caracteres especiales antes de realizar la búsqueda.</p>
          </div>
        </div>
      </div>

      <!-- Mapa -->
      <div class="relative mt-4">
        <div *ngIf="cargando && !inDialog" class="absolute inset-0 flex items-center justify-center bg-white/70 dark:bg-gray-800/70 z-10 rounded-lg">
          <div class="flex flex-col items-center">
            <mat-spinner diameter="48" color="accent"></mat-spinner>
            <p class="mt-3 text-sm font-medium text-gray-600 dark:text-gray-300">Cargando mapa...</p>
          </div>
        </div>
        <div [id]="mapContainerId" class="h-[80vh] w-full rounded-lg border-2 border-indigo-200 dark:border-indigo-800 shadow-inner" style="position: relative; z-index: 0;"></div>
      </div>
    </mat-card-content>

    <mat-card-actions class="p-4 flex justify-between bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
      <button
        mat-button
        (click)="volver()"
        class="px-4 py-2 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 flex items-center shadow-sm hover:shadow"
      >
        <mat-icon class="mr-2">arrow_back</mat-icon>
        <span>Volver al formulario</span>
      </button>
    </mat-card-actions>
  </mat-card>
</div>
