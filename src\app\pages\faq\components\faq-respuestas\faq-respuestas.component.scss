.faq-respuestas-container {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e3e6f0;
}

.respuestas-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #4e73df;
  margin-bottom: 1rem;
}

// Estilos para el spinner de carga
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(78, 115, 223, 0.1);
    border-radius: 50%;
    border-top-color: #4e73df;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  p {
    color: #858796;
    font-size: 0.9rem;
  }
}

// Estilos para mensajes de error
.error-container {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

// Estilos para la lista de respuestas
.respuestas-list {
  margin-bottom: 1.5rem;
}

.respuesta-item {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
  padding: 1rem;
  margin-bottom: 1rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.respuesta-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e3e6f0;
}

.respuesta-user {
  display: flex;
  align-items: center;
  
  .user-name {
    font-weight: 600;
    color: #4e73df;
  }
  
  .user-role {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    color: #858796;
  }
}

.respuesta-date {
  font-size: 0.8rem;
  color: #858796;
}

.respuesta-content {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #5a5c69;
  margin-bottom: 0.75rem;
  white-space: pre-line;
}

// Estilos para archivos adjuntos
.respuesta-archivos {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e3e6f0;
}

.archivos-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #5a5c69;
  margin-bottom: 0.5rem;
}

.archivos-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.archivo-item {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.25rem;
  padding: 0.5rem;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #eaecf4;
    transform: translateY(-2px);
  }
}

.archivo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #4e73df;
}

.archivo-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

.archivo-name {
  font-size: 0.85rem;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Estilos para el mensaje de no respuestas
.no-respuestas {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
  padding: 1rem;
  text-align: center;
  color: #858796;
  font-style: italic;
}

// Estilos para el formulario de respuesta
.respuesta-form-container {
  margin-top: 1.5rem;
  padding: 1.25rem;
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
}

.form-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4e73df;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
  font-size: 0.95rem;
  color: #5a5c69;
  
  &:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
  }
}

.form-error {
  color: #e74a3b;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.95rem;
  line-height: 1.5;
  border-radius: 0.35rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  
  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

.btn-primary {
  color: #fff;
  background-color: #4e73df;
  border-color: #4e73df;
  
  &:hover:not(:disabled) {
    background-color: #2e59d9;
    border-color: #2653d4;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
  }
}

// Estilos para el mensaje de pregunta cerrada
.pregunta-cerrada {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
  padding: 1rem;
  text-align: center;
  color: #858796;
  font-style: italic;
  margin-top: 1rem;
}

// Estilos para tema oscuro
:host-context(.dark-theme) {
  .faq-respuestas-container {
    border-top-color: rgba(77, 171, 245, 0.2);
  }
  
  .respuestas-title {
    color: rgba(77, 171, 245, 0.9);
  }
  
  .loading-container {
    .spinner {
      border-color: rgba(77, 171, 245, 0.1);
      border-top-color: rgba(77, 171, 245, 0.9);
    }
    
    p {
      color: rgba(255, 255, 255, 0.7);
    }
  }
  
  .error-container {
    background-color: rgba(248, 215, 218, 0.1);
    color: #f8d7da;
  }
  
  .respuesta-item {
    background-color: rgba(77, 171, 245, 0.05);
    border-color: rgba(77, 171, 245, 0.2);
  }
  
  .respuesta-header {
    border-bottom-color: rgba(77, 171, 245, 0.2);
  }
  
  .respuesta-user {
    .user-name {
      color: rgba(77, 171, 245, 0.9);
    }
    
    .user-role {
      color: rgba(255, 255, 255, 0.6);
    }
  }
  
  .respuesta-date {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .respuesta-content {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .respuesta-archivos {
    border-top-color: rgba(77, 171, 245, 0.2);
  }
  
  .archivos-title {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .archivo-item {
    background-color: rgba(77, 171, 245, 0.05);
    border-color: rgba(77, 171, 245, 0.2);
    
    &:hover {
      background-color: rgba(77, 171, 245, 0.1);
    }
  }
  
  .archivo-link {
    color: rgba(77, 171, 245, 0.9);
  }
  
  .no-respuestas,
  .pregunta-cerrada {
    background-color: rgba(77, 171, 245, 0.05);
    border-color: rgba(77, 171, 245, 0.2);
    color: rgba(255, 255, 255, 0.7);
  }
  
  .respuesta-form-container {
    background-color: rgba(77, 171, 245, 0.05);
    border-color: rgba(77, 171, 245, 0.2);
  }
  
  .form-title {
    color: rgba(77, 171, 245, 0.9);
  }
  
  .form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(77, 171, 245, 0.2);
    color: rgba(255, 255, 255, 0.9);
    
    &:focus {
      border-color: rgba(77, 171, 245, 0.4);
      box-shadow: 0 0 0 0.2rem rgba(77, 171, 245, 0.25);
    }
  }
  
  .form-error {
    color: #f8d7da;
  }
  
  .btn-primary {
    background-color: rgba(77, 171, 245, 0.8);
    border-color: rgba(77, 171, 245, 0.8);
    
    &:hover:not(:disabled) {
      background-color: rgba(77, 171, 245, 0.9);
      border-color: rgba(77, 171, 245, 0.9);
    }
    
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(77, 171, 245, 0.25);
    }
  }
}
