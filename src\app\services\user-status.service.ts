import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { WebSocketService } from './websocket/WebSocketService';
import { GeneralService } from './general.service';

@Injectable({
  providedIn: 'root',
})
export class UserStatusService {
  private userStatusSubject = new BehaviorSubject<Map<number, string>>(
    new Map()
  );
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  private messageSubscription: Subscription | null = null;
  private connectionSubscription: Subscription | null = null;
  private static instance: UserStatusService;
  private lastStatusUpdate = new Map<
    number,
    { status: string; timestamp: number }
  >();
  private readonly STATUS_UPDATE_THROTTLE = 5000; // 5 segundos entre actualizaciones
  private isInitialized = false;

  constructor(
    private websocketService: WebSocketService,
    private generalService: GeneralService
  ) {
    if (UserStatusService.instance) {
      return UserStatusService.instance;
    }
    UserStatusService.instance = this;
  }

  /**
   * Inicializa las suscripciones a WebSocket
   */
  private initSubscriptions(): void {
    if (this.isInitialized) {
      return;
    }

    this.clearSubscriptions();
    this.isInitialized = true;

    this.messageSubscription = this.websocketService
      .getMessages()
      .subscribe((message) => {
        if (message.type === 'USERS_STATUS_UPDATED') {
          this.updateUserStatus(message.payload);
        } else if (message.type === 'CONNECTION_CLOSED') {
          this.markAllUsersOffline();
        } else if (message.type === 'USER_DISCONNECTED') {
          this.handleUserDisconnection(message.payload.userId);
        }
      });

    this.connectionSubscription = this.websocketService
      .getConnectionStatus()
      .subscribe((connected) => {
        this.connectionStatusSubject.next(connected);
        if (!connected) {
          this.markAllUsersOffline();
        } else if (!window.location.href.includes('/auth/login')) {
          this.requestUserStatuses();
        }
      });
  }

  private handleUserDisconnection(userId: number): void {
    const currentMap = this.userStatusSubject.value;
    if (currentMap.has(userId)) {
      const newMap = new Map(currentMap);
      newMap.set(userId, 'OFFLINE');
      this.userStatusSubject.next(newMap);

      const currentUserId = this.generalService.getUserId();
      if (currentUserId && userId === currentUserId) {
        localStorage.setItem('userStatus', 'OFFLINE');
      }
    }
  }

  private updateUserStatus(userStatus: any): void {
    const currentMap = this.userStatusSubject.value;
    const newMap = new Map(currentMap);
    let hasChanges = false;

    const processStatus = (status: any) => {
      const userId = status.userId;
      const normalizedStatus = this.normalizeStatus(status);
      const lastUpdate = this.lastStatusUpdate.get(userId);
      const now = Date.now();

      if (
        !lastUpdate ||
        lastUpdate.status !== normalizedStatus ||
        now - lastUpdate.timestamp > this.STATUS_UPDATE_THROTTLE
      ) {
        newMap.set(userId, normalizedStatus);
        this.lastStatusUpdate.set(userId, {
          status: normalizedStatus,
          timestamp: now,
        });
        hasChanges = true;
      }
    };

    if (Array.isArray(userStatus)) {
      userStatus.forEach(processStatus);
    } else {
      processStatus(userStatus);
    }

    if (hasChanges) {
      this.userStatusSubject.next(newMap);
      this.updateLocalStorage(newMap);
    }
  }

  private normalizeStatus(status: any): string {
    return status.status === 'ONLINE' || status.online === true
      ? 'ONLINE'
      : 'OFFLINE';
  }

  private updateLocalStorage(statusMap: Map<number, string>): void {
    const currentUserId = this.generalService.getUserId();
    if (currentUserId) {
      const currentUserStatus = statusMap.get(currentUserId) || 'OFFLINE';
      localStorage.setItem('userStatus', currentUserStatus);
    }
  }

  /**
   * Limpia las suscripciones existentes
   */
  private clearSubscriptions(): void {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
      this.messageSubscription = null;
    }

    if (this.connectionSubscription) {
      this.connectionSubscription.unsubscribe();
      this.connectionSubscription = null;
    }
  }

  /**
   * Solicita el estado actual de todos los usuarios
   */
  requestUserStatuses(): void {
    // Verificar si estamos en la página de login
    if (window.location.href.includes('/auth/login')) {
      return;
    }

    // Verificar si hay token antes de solicitar el estado de los usuarios
    const token = localStorage.getItem('token');
    if (!token) {
      return;
    }

    this.websocketService.sendMessage('/app/users.status');
  }

  /**
   * Marca a todos los usuarios como desconectados
   */
  private markAllUsersOffline(): void {
    const currentMap = this.userStatusSubject.value;
    const newMap = new Map<number, string>();

    // Copiar el mapa actual pero cambiar todos los estados a OFFLINE
    currentMap.forEach((_, userId) => {
      newMap.set(userId, 'OFFLINE');
    });

    this.userStatusSubject.next(newMap);

    // También actualizar el estado en localStorage
    localStorage.setItem('userStatus', 'OFFLINE');
  }

  /**
   * Conecta un usuario
   * @param userId ID del usuario
   */
  connectUser(userId: number): void {
    this.websocketService.sendMessage('/app/user.connect', { userId });
  }

  /**
   * Desconecta un usuario
   * @param userId ID del usuario
   */
  disconnectUser(userId: number): void {
    this.websocketService.sendMessage('/app/user.disconnect', { userId });
  }

  /**
   * Actualiza la actividad de un usuario
   * @param userId ID del usuario
   */
  updateUserActivity(userId: number): void {
    this.websocketService.sendMessage('/app/user.activity', { userId });
  }

  /**
   * Obtiene el estado de un usuario
   * @param userId ID del usuario
   * @returns Observable con el estado del usuario
   */
  getUserStatus(userId: number): Observable<string> {
    return new Observable((observer) => {
      // Emitir el estado actual
      const currentStatus =
        this.userStatusSubject.value.get(userId) || 'OFFLINE';
      observer.next(currentStatus);

      // Suscribirse a cambios futuros
      const subscription = this.userStatusSubject.subscribe((map) => {
        observer.next(map.get(userId) || 'OFFLINE');
      });

      // Limpiar suscripción al desuscribirse
      return () => subscription.unsubscribe();
    });
  }

  /**
   * Obtiene el mapa completo de estados de usuarios
   * @returns Observable con el mapa de estados
   */
  getAllUserStatuses(): Observable<Map<number, string>> {
    return this.userStatusSubject.asObservable();
  }

  /**
   * Obtiene el estado de conexión con el servidor
   * @returns Observable con el estado de conexión
   */
  getConnectionStatus(): Observable<boolean> {
    return this.connectionStatusSubject.asObservable();
  }

  /**
   * Actualiza explícitamente el estado del usuario actual
   * @param online Estado de conexión (true = conectado, false = desconectado)
   */
  updateCurrentUserStatus(online: boolean): void {
    const userId = this.generalService.getUserId();
    if (!userId) {
      return;
    }

    // Actualizar el estado en localStorage
    const status = online ? 'ONLINE' : 'OFFLINE';
    localStorage.setItem('userStatus', status);

    // Actualizar el mapa de estados
    const currentMap = this.userStatusSubject.value;
    currentMap.set(userId, status);
    this.userStatusSubject.next(new Map(currentMap));



    // Enviar mensaje al servidor
    if (this.websocketService.isConnected()) {
      if (online) {
        this.connectUser(userId);
      } else {
        this.disconnectUser(userId);
      }
    }
  }

  /**
   * Limpia las suscripciones al destruir el servicio
   */
  ngOnDestroy(): void {
    this.clearSubscriptions();
  }
}
