import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface TranscriptionRequest {
  audio_file: File;
  whisper_model?: 'tiny' | 'base' | 'small' | 'medium' | 'large' | 'turbo';
  device?: 'cpu' | 'gpu';
  target_language?: string;
  call_id?: string;
  call_type?: 'inbound' | 'outbound' | 'internal';
  caller_phone?: string;
  agent_id?: string;
  call_datetime?: string;
}

export interface TranscriptionResponse {
  success: boolean;
  id?: number;
  call_id?: string;
  transcription?: string;
  mood?: string;
  mood_confidence?: number;
  sentiment_score?: number;
  processing_time?: number;
  word_count?: number;
  call_duration?: number;
  language_detected?: string;
  audio_quality_score?: number;
  model_used?: string;
  device_used?: string;
  file_size_mb?: number;
  created_at?: string;
  call_datetime?: string;
  warnings?: Array<{
    type: string;
    message: string;
    audio_duration?: string;
    recommendation?: string;
  }>;
  performance_metrics?: {
    processing_speed: string;
    words_per_second: string;
  };
  message?: string;
  error?: string;
}

export interface TranscriptionProgress {
  status: 'uploading' | 'processing' | 'analyzing' | 'completed' | 'error';
  progress: number;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class TranscriptionService {
  private readonly baseUrl = 'https://apisozarusac.com/BackendTranscriptor/api';

  constructor(private http: HttpClient) {}

  /**
   * Transcribe un archivo de audio con análisis de estado de ánimo
   * @param request Datos de la transcripción
   */
  transcribeAudio(request: TranscriptionRequest): Observable<TranscriptionResponse> {
    const formData = new FormData();

    // LOG: Información del archivo antes de agregarlo al FormData
    console.log('=== SERVICIO TRANSCRIPCIÓN - PREPARANDO FORMDATA ===');
    console.log('Archivo recibido:', {
      name: request.audio_file.name,
      size: request.audio_file.size,
      type: request.audio_file.type,
      lastModified: request.audio_file.lastModified
    });

    // Archivo de audio (requerido)
    formData.append('audio_file', request.audio_file);

    // Parámetros opcionales con valores por defecto
    const whisperModel = request.whisper_model || 'base';
    const device = request.device || 'cpu';
    const targetLanguage = request.target_language || 'es';
    const callType = request.call_type || 'inbound';

    formData.append('whisper_model', whisperModel);
    formData.append('device', device);
    formData.append('target_language', targetLanguage);
    formData.append('call_type', callType);

    // Parámetros opcionales
    if (request.call_id) {
      formData.append('call_id', request.call_id);
    }

    if (request.caller_phone) {
      formData.append('caller_phone', request.caller_phone);
    }

    if (request.agent_id) {
      formData.append('agent_id', request.agent_id);
    }

    if (request.call_datetime) {
      formData.append('call_datetime', request.call_datetime);
    }

    // LOG: Mostrar todos los campos del FormData
    console.log('FormData campos enviados:');
    console.log('- whisper_model:', whisperModel);
    console.log('- device:', device);
    console.log('- target_language:', targetLanguage);
    console.log('- call_type:', callType);
    console.log('- call_id:', request.call_id || 'No enviado');
    console.log('- caller_phone:', request.caller_phone || 'No enviado');
    console.log('- agent_id:', request.agent_id || 'No enviado');
    console.log('- call_datetime:', request.call_datetime || 'No enviado');
    console.log('- audio_file: [File object]');

    // LOG: Resumen de campos enviados
    console.log('Resumen de campos en FormData:');
    console.log('Total de campos:', 8 + (request.call_id ? 1 : 0) + (request.caller_phone ? 1 : 0) + (request.agent_id ? 1 : 0) + (request.call_datetime ? 1 : 0));
    console.log('URL destino:', `${this.baseUrl}/transcribe/`);
    console.log('================================================');

    const headers = new HttpHeaders({
      // No establecer Content-Type para FormData, el navegador lo hará automáticamente
    });

    return this.http.post<TranscriptionResponse>(`${this.baseUrl}/transcribe/`, formData, { headers });
  }

  /**
   * Verifica si un archivo es de audio válido para transcripción
   * @param file Archivo a verificar
   */
  isValidAudioFile(file: File): boolean {
    const validTypes = [
      'audio/mpeg',      // MP3
      'audio/wav',       // WAV
      'audio/ogg',       // OGG
      'audio/flac',      // FLAC
      'audio/mp4',       // M4A
      'audio/aac',       // AAC
      'audio/x-ms-wma',  // WMA
      'audio/opus',      // OPUS
      'video/webm',      // WebM
      'audio/amr',       // AMR (WhatsApp)
      'video/3gpp'       // 3GP
    ];

    return validTypes.includes(file.type) || this.isValidAudioExtension(file.name);
  }

  /**
   * Verifica la extensión del archivo si el tipo MIME no es reconocido
   * @param fileName Nombre del archivo
   */
  private isValidAudioExtension(fileName: string): boolean {
    const validExtensions = [
      '.mp3', '.wav', '.ogg', '.flac', '.m4a', 
      '.aac', '.wma', '.opus', '.webm', '.amr', '.3gp'
    ];

    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return validExtensions.includes(extension);
  }

  /**
   * Obtiene el tamaño máximo recomendado para archivos de audio (en bytes)
   */
  getMaxFileSize(): number {
    return 100 * 1024 * 1024; // 100MB
  }

  /**
   * Formatea el tiempo de duración en formato legible
   * @param seconds Duración en segundos
   */
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Obtiene el color asociado a un estado de ánimo
   * @param sentiment Estado de ánimo
   */
  getSentimentColor(sentiment: string): string {
    const sentimentColors: { [key: string]: string } = {
      'muy_enojado': '#d32f2f',
      'enojado': '#f44336',
      'frustrado': '#ff5722',
      'molesto': '#ff9800',
      'neutral': '#9e9e9e',
      'satisfecho': '#4caf50',
      'feliz': '#8bc34a',
      'muy_feliz': '#cddc39',
      'emocionado': '#ffeb3b',
      'confundido': '#9c27b0',
      'preocupado': '#673ab7',
      'calmado': '#2196f3'
    };

    return sentimentColors[sentiment.toLowerCase()] || '#9e9e9e';
  }

  /**
   * Obtiene el icono asociado a un estado de ánimo
   * @param sentiment Estado de ánimo
   */
  getSentimentIcon(sentiment: string): string {
    const sentimentIcons: { [key: string]: string } = {
      'muy_enojado': 'sentiment_very_dissatisfied',
      'enojado': 'sentiment_dissatisfied',
      'frustrado': 'sentiment_dissatisfied',
      'molesto': 'sentiment_neutral',
      'neutral': 'sentiment_neutral',
      'satisfecho': 'sentiment_satisfied',
      'feliz': 'sentiment_satisfied',
      'muy_feliz': 'sentiment_very_satisfied',
      'emocionado': 'sentiment_very_satisfied',
      'confundido': 'help_outline',
      'preocupado': 'sentiment_dissatisfied',
      'calmado': 'sentiment_satisfied'
    };

    return sentimentIcons[sentiment.toLowerCase()] || 'sentiment_neutral';
  }

  /**
   * Traduce el estado de ánimo al español
   * @param sentiment Estado de ánimo en inglés
   */
  translateSentiment(sentiment: string): string {
    const translations: { [key: string]: string } = {
      'very_angry': 'Muy Enojado',
      'angry': 'Enojado',
      'frustrated': 'Frustrado',
      'annoyed': 'Molesto',
      'neutral': 'Neutral',
      'satisfied': 'Satisfecho',
      'happy': 'Feliz',
      'very_happy': 'Muy Feliz',
      'excited': 'Emocionado',
      'confused': 'Confundido',
      'worried': 'Preocupado',
      'calm': 'Calmado'
    };

    return translations[sentiment.toLowerCase()] || sentiment;
  }

  /**
   * Genera un ID único para la llamada
   * @param clientPhone Teléfono del cliente
   */
  generateCallId(clientPhone?: string): string {
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '');
    const random = Math.random().toString(36).substring(2, 8);
    const phone = clientPhone ? `_${clientPhone.slice(-4)}` : '';
    
    return `CALL_${timestamp}${phone}_${random}`.toUpperCase();
  }

  /**
   * Valida los parámetros de transcripción
   * @param request Solicitud de transcripción
   */
  validateTranscriptionRequest(request: TranscriptionRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validar archivo
    if (!request.audio_file) {
      errors.push('El archivo de audio es requerido');
    } else {
      if (!this.isValidAudioFile(request.audio_file)) {
        errors.push('Formato de audio no soportado');
      }
      
      if (request.audio_file.size > this.getMaxFileSize()) {
        errors.push('El archivo es demasiado grande (máximo 100MB)');
      }
    }

    // Validar teléfono si se proporciona
    if (request.caller_phone && !/^\+?[\d\s\-\(\)]+$/.test(request.caller_phone)) {
      errors.push('Formato de teléfono inválido');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
