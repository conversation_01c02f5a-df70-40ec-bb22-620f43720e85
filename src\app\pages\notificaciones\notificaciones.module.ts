import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';

import { ListadoNotificacionesComponent } from './listado-notificaciones.component';
import { EnviarNotificacionComponent } from './enviar-notificacion.component';
import { NotificationReadersComponent } from '@app/components/notificaciones/notification-readers/notification-readers.component';
import { NotificationReadersModalComponent } from '@app/components/notificaciones/notification-readers-modal/notification-readers-modal.component';

import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';

const routes: Routes = [
  {
    path: '',
    component: ListadoNotificacionesComponent,
  },
];

@NgModule({
  declarations: [
    ListadoNotificacionesComponent,
    EnviarNotificacionComponent,
    NotificationReadersComponent,
    NotificationReadersModalComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonToggleModule,
    MatTooltipModule,
    MatDividerModule,
  ],
})
export class NotificacionesModule {}
