import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MapExampleComponent } from './map-example.component';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MapModule } from '../../../components/map/map.module';
import { MapExampleRoutingModule } from './map-example-routing.module';

@NgModule({
  declarations: [
    MapExampleComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    MapExampleRoutingModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MapModule
  ]
})
export class MapExampleModule { }
