import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Store, select } from '@ngrx/store';
import { MatDialog } from '@angular/material/dialog';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';
import { Curso } from '@app/models/backend/curso/curso.model';
import { Modulo } from '@app/models/backend/curso/modulo.model';
import { Seccion } from '@app/models/backend/curso/seccion.model';
import { Leccion, TipoLeccion } from '@app/models/backend/curso/leccion.model';
import { CursoUsuario } from '@app/models/backend/curso/curso-usuario.model';
import { CursoService } from '@app/services/curso.service';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';
import { ModuloService } from '@app/services/modulo.service';
import { SeccionService } from '@app/services/seccion.service';
import { LeccionService } from '@app/services/leccion.service';
import { ProgresoService } from '@app/services/progreso.service';
import { NotificationService } from '@app/services/notification/notification.service';
import { CuestionarioService } from '@app/services/cuestionario.service';
import { HttpClient } from '@angular/common/http';

import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { CuestionarioFormComponent } from '../../components/cuestionario-form/cuestionario-form.component';

@Component({
  selector: 'app-curso-detalle',
  templateUrl: './curso-detalle.component.html',
  styleUrls: ['./curso-detalle.component.css']
})
export class CursoDetalleComponent implements OnInit, OnDestroy {
  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;
  @ViewChild('videoIntroductorio') videoIntroductorio!: ElementRef<HTMLVideoElement>;

  cursoId: number;
  user: User | null = null;
  curso: Curso | null = null;
  modulos: Modulo[] = [];
  leccionActual: Leccion | null = null;
  cursoUsuario: CursoUsuario | null = null;

  // Índices para navegación entre lecciones
  moduloActualIndex: number = -1;
  leccionActualIndex: number = -1;

  loading = true;
  loadingLeccion = false;
  error: string | null = null;

  videoUrl: SafeResourceUrl | null = null;
  subtitlesUrl: string | null = null;
  subtitlesContent: string | null = null;
  haySubtitulos: boolean = false;

  // Control de progreso
  porcentajeCompletado = 0;
  leccionesCompletadas: number[] = [];

  // Progreso por módulo
  progresosPorModulo: { [moduloId: number]: number } = {};

  // URL de imagen de placeholder para cursos (usando una imagen local para evitar problemas de CORS)
  cursoPlaceholderUrl: string = 'assets/images/curso-placeholder.jpg';

  // Control de visualización
  mostrarBienvenida: boolean = true;
  mostrarVideo: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store<fromRoot.State>,
    private cursoService: CursoService,
    private cursoUsuarioService: CursoUsuarioService,
    private moduloService: ModuloService,
    private seccionService: SeccionService,
    private leccionService: LeccionService,
    private progresoService: ProgresoService,
    private notification: NotificationService,
    private sanitizer: DomSanitizer,
    private http: HttpClient,
    private dialog: MatDialog,
    private cuestionarioService: CuestionarioService
  ) {
    this.cursoId = +this.route.snapshot.paramMap.get('id')!;
  }

  ngOnInit(): void {
    // Obtener el usuario actual
    this.store.pipe(
      select(fromUser.getUser),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
      if (user) {
        this.loadCursoDetalle();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadCursoDetalle(): void {
    if (!this.user) {
      this.router.navigate(['/auth/login']);
      return;
    }

    this.loading = true;
    this.error = null;

    // Obtener el curso completo con la asignación
    this.cursoUsuarioService.getCursosCompletosByUsuarioId(this.user.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            // Buscar el curso específico por ID
            const cursoEncontrado = response.data.find(c => c.id === this.cursoId);

            if (cursoEncontrado) {
              this.curso = cursoEncontrado;
              this.cursoUsuario = cursoEncontrado.asignacion;

              // Depurar información del curso para diagnóstico
              this.depurarInformacionCurso();

              // Cargar los módulos del curso
              this.loadModulos();

              // Actualizar la última visualización
              this.actualizarUltimaVisualizacion();
            } else {
              this.loading = false;
              this.error = 'No tienes acceso a este curso o no existe';
              this.notification.error(this.error);
              this.router.navigate(['/cursos/mis-cursos']);
            }
          } else {
            this.loading = false;
            this.error = response.msg || 'Error al cargar el curso';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar el curso. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar curso:', error);
        }
      });
  }

  loadModulos(): void {
    if (!this.curso) return;

    this.moduloService.getModulosByCursoId(this.curso.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;

          if (response.rpta === 1 && response.data) {
            this.modulos = response.data;

            // Ordenar los módulos por orden
            this.modulos.sort((a, b) => a.orden - b.orden);

            // Depurar información de los módulos y lecciones
            this.depurarInformacionModulosYLecciones();

            // Ya no cargamos automáticamente la primera lección
            // Mostramos la pantalla de bienvenida en su lugar
            this.mostrarBienvenida = true;
            this.leccionActual = null;

            // Cargar el progreso del usuario en este curso
            this.loadProgreso();
          } else {
            this.error = 'No se encontraron módulos para este curso';
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar los módulos del curso';
          this.notification.error(this.error);
          console.error('Error al cargar módulos:', error);
        }
      });
  }

  /**
   * Muestra el video introductorio del curso
   */
  mostrarVideoIntroductorio(): void {
    this.mostrarBienvenida = false;
    this.mostrarVideo = true;
    this.leccionActual = null;
  }

  /**
   * Oculta el video introductorio y vuelve a la pantalla de bienvenida
   */
  ocultarVideoIntroductorio(): void {
    this.mostrarVideo = false;
    this.mostrarBienvenida = true;
  }

  cargarLeccion(leccion: Leccion): void {
    this.loadingLeccion = true;
    this.leccionActual = leccion;
    this.mostrarBienvenida = false;
    this.mostrarVideo = false;

    // Actualizar índices para navegación
    this.actualizarIndicesNavegacion(leccion);

    // Si la lección es de tipo VIDEO, cargar el video
    if (!leccion.tipoLeccion || leccion.tipoLeccion === TipoLeccion.VIDEO) {
      // Asignar VIDEO como tipo por defecto si no está especificado
      if (!leccion.tipoLeccion) {
        leccion.tipoLeccion = TipoLeccion.VIDEO;
      }

      // Intentar obtener la URL del video de la lección o del curso
      let videoUrlSource = '';

      // Primero intentamos con la URL de la lección
      if (leccion.videoUrl && leccion.videoUrl.trim() !== '') {
        console.log('Usando URL del video de la lección:', leccion.videoUrl);
        videoUrlSource = leccion.videoUrl;
      }
      // Si no hay URL en la lección, intentamos con la URL del curso
      else if (this.curso && this.curso.videoUrl && this.curso.videoUrl.trim() !== '') {
        console.log('Usando URL del video del curso como fallback:', this.curso.videoUrl);
        videoUrlSource = this.curso.videoUrl;
      }

      // Procesar la URL del video si existe
      if (videoUrlSource) {
        console.log('URL del video original:', videoUrlSource);

        // Asegurarse de que la URL sea válida
        let videoUrl = videoUrlSource;

        // Si la URL no comienza con http o https, asumimos que es una ruta relativa
        if (!videoUrl.startsWith('http://') && !videoUrl.startsWith('https://')) {
          videoUrl = `${window.location.origin}/${videoUrl.startsWith('/') ? videoUrl.substring(1) : videoUrl}`;
        }

        // Para la reproducción directa, no necesitamos sanitizar las URLs
        console.log('URL del video procesada para reproducción directa:', videoUrl);
        this.videoUrl = videoUrl;
      } else {
        console.log('No hay URL de video disponible ni en la lección ni en el curso');
        this.videoUrl = null;
      }

      // Asignar la URL de los subtítulos
      if (leccion.subtitlesUrl && leccion.subtitlesUrl.trim() !== '') {
        console.log('URL de subtítulos original:', leccion.subtitlesUrl);

        // Asegurarse de que la URL sea válida
        let subtitlesUrl = leccion.subtitlesUrl;

        // Para URLs de tipo blob, necesitamos manejarlas de manera especial
        if (subtitlesUrl.startsWith('blob:')) {
          console.log('URL de subtítulos tipo blob detectada, usando archivo de subtítulos alternativo');

          // Intentar cargar subtítulos desde el backend si es posible
          if (this.leccionActual && this.leccionActual.id) {
            this.cargarSubtitulosDesdeBackend(this.leccionActual.id);
          } else {
            // Si no hay ID de lección, usar subtítulos por defecto
            this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (contenido) => {
                  console.log('Usando subtítulos por defecto desde archivo local');
                  this.procesarContenidoSubtitulos(contenido);
                },
                error: () => {
                  console.error('No se pudo cargar los subtítulos por defecto');
                  this.subtitlesUrl = null;
                  this.loadingLeccion = false;
                }
              });
          }
          return; // Salimos porque la URL se asignará en el callback
        }
      // Si la URL no comienza con http o https, asumimos que es una ruta relativa
      else if (!subtitlesUrl.startsWith('http://') && !subtitlesUrl.startsWith('https://')) {
        subtitlesUrl = `${window.location.origin}/${subtitlesUrl.startsWith('/') ? subtitlesUrl.substring(1) : subtitlesUrl}`;
        console.log('URL de subtítulos procesada como ruta relativa:', subtitlesUrl);

        // Intentar cargar el archivo local
        this.http.get(subtitlesUrl, { responseType: 'text' })
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (contenido) => {
              console.log('Subtítulos cargados desde ruta relativa');
              this.procesarContenidoSubtitulos(contenido);
            },
            error: (error) => {
              console.error('Error al cargar subtítulos desde ruta relativa:', error);
              // Si falla, intentar con subtítulos por defecto
              this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                  next: (contenido) => {
                    console.log('Usando subtítulos por defecto tras error en ruta relativa');
                    this.procesarContenidoSubtitulos(contenido);
                  },
                  error: () => {
                    console.error('No se pudo cargar los subtítulos por defecto');
                    this.subtitlesUrl = null;
                    this.loadingLeccion = false;
                  }
                });
            }
          });
        return; // Salimos porque la URL se asignará en el callback
      }
      // Si es una URL de Firebase, descargar los subtítulos y crear una URL local
      else if (subtitlesUrl.includes('firebasestorage.googleapis.com')) {
        console.log('URL de Firebase detectada, descargando subtítulos...');
        this.descargarSubtitulosDesdeFirebase(subtitlesUrl);
        return; // Salimos porque la URL se asignará en el callback de descarga
      }
      // Para otras URLs externas, intentar descargar directamente
      else {
        console.log('URL externa detectada, intentando descargar subtítulos...');
        this.http.get(subtitlesUrl, { responseType: 'text' })
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (contenido) => {
              console.log('Subtítulos descargados correctamente desde URL externa');
              this.procesarContenidoSubtitulos(contenido);
            },
            error: (error) => {
              console.error('Error al descargar subtítulos desde URL externa:', error);
              // Si falla, intentar con subtítulos por defecto
              this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                  next: (contenido) => {
                    console.log('Usando subtítulos por defecto tras error en URL externa');
                    this.procesarContenidoSubtitulos(contenido);
                  },
                  error: () => {
                    console.error('No se pudo cargar los subtítulos por defecto');
                    this.subtitlesUrl = null;
                    this.loadingLeccion = false;
                  }
                });
            }
          });
        return; // Salimos porque la URL se asignará en el callback
      }
    } else {
      console.log('No hay URL de subtítulos disponible para esta lección');
      this.subtitlesUrl = null;
    }

    }

    // Si la lección no es de tipo VIDEO, simplemente completamos la carga
    this.loadingLeccion = false;

    // Ya no marcamos automáticamente como completada la lección de tipo VIDEO
    // El usuario debe hacer clic en el botón "Marcar como completada"
  }

  /**
   * Actualiza los índices para la navegación entre lecciones
   */
  private actualizarIndicesNavegacion(leccion: Leccion): void {
    // Buscar el módulo y la sección que contiene esta lección
    for (let i = 0; i < this.modulos.length; i++) {
      const modulo = this.modulos[i];
      if (modulo.secciones) {
        for (let seccion of modulo.secciones) {
          if (seccion.lecciones) {
            for (let j = 0; j < seccion.lecciones.length; j++) {
              if (seccion.lecciones[j].id === leccion.id) {
                this.moduloActualIndex = i;
                this.leccionActualIndex = j;
                return;
              }
            }
          }
        }
      }
    }

    // Si no se encontró, resetear los índices
    this.moduloActualIndex = -1;
    this.leccionActualIndex = -1;
  }

  /**
   * Verifica si una sección está completada (todas sus lecciones están completadas)
   */
  isSeccionCompleted(seccion: Seccion): boolean {
    if (!seccion.lecciones || seccion.lecciones.length === 0) {
      return false;
    }

    return seccion.lecciones.every(leccion => this.esLeccionCompletada(leccion.id));
  }

  /**
   * Verifica si hay una lección anterior disponible
   */
  hayLeccionAnterior(): boolean {
    if (this.moduloActualIndex < 0 || this.leccionActualIndex < 0) {
      return false;
    }

    // Obtener el módulo actual
    const moduloActual = this.modulos[this.moduloActualIndex];

    // Si no hay secciones, no hay lección anterior
    if (!moduloActual.secciones || moduloActual.secciones.length === 0) {
      return false;
    }

    // Buscar la sección y lección actual
    let seccionActualIndex = -1;
    let leccionActualIndexEnSeccion = -1;

    for (let i = 0; i < moduloActual.secciones.length; i++) {
      const seccion = moduloActual.secciones[i];
      if (seccion.lecciones) {
        for (let j = 0; j < seccion.lecciones.length; j++) {
          if (seccion.lecciones[j].id === this.leccionActual?.id) {
            seccionActualIndex = i;
            leccionActualIndexEnSeccion = j;
            break;
          }
        }
        if (seccionActualIndex >= 0) break;
      }
    }

    // Si no se encontró la sección o lección actual, no hay lección anterior
    if (seccionActualIndex < 0 || leccionActualIndexEnSeccion < 0) {
      return false;
    }

    // Si no es la primera lección de la sección, hay una lección anterior
    if (leccionActualIndexEnSeccion > 0) {
      return true;
    }

    // Si es la primera lección de la sección, verificar si hay secciones anteriores con lecciones
    if (moduloActual.secciones) {
      for (let i = seccionActualIndex - 1; i >= 0; i--) {
        const seccion = moduloActual.secciones[i];
        if (seccion && seccion.lecciones && seccion.lecciones.length > 0) {
          return true;
        }
      }
    }

    // Si es la primera lección de la primera sección, verificar si hay módulos anteriores con secciones y lecciones
    for (let i = this.moduloActualIndex - 1; i >= 0; i--) {
      const modulo = this.modulos[i];
      if (modulo.secciones) {
        for (let j = modulo.secciones.length - 1; j >= 0; j--) {
          const seccion = modulo.secciones[j];
          if (seccion && seccion.lecciones && seccion.lecciones.length > 0) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Verifica si hay una lección siguiente disponible
   */
  hayLeccionSiguiente(): boolean {
    if (this.moduloActualIndex < 0 || this.leccionActualIndex < 0) {
      return false;
    }

    // Obtener el módulo actual
    const moduloActual = this.modulos[this.moduloActualIndex];

    // Si no hay secciones, no hay lección siguiente
    if (!moduloActual.secciones || moduloActual.secciones.length === 0) {
      return false;
    }

    // Buscar la sección y lección actual
    let seccionActualIndex = -1;
    let leccionActualIndexEnSeccion = -1;

    for (let i = 0; i < moduloActual.secciones.length; i++) {
      const seccion = moduloActual.secciones[i];
      if (seccion.lecciones) {
        for (let j = 0; j < seccion.lecciones.length; j++) {
          if (seccion.lecciones[j].id === this.leccionActual?.id) {
            seccionActualIndex = i;
            leccionActualIndexEnSeccion = j;
            break;
          }
        }
        if (seccionActualIndex >= 0) break;
      }
    }

    // Si no se encontró la sección o lección actual, no hay lección siguiente
    if (seccionActualIndex < 0 || leccionActualIndexEnSeccion < 0) {
      return false;
    }

    const seccionActual = moduloActual.secciones[seccionActualIndex];

    // Si no es la última lección de la sección, hay una lección siguiente
    if (seccionActual.lecciones && leccionActualIndexEnSeccion < seccionActual.lecciones.length - 1) {
      return true;
    }

    // Si es la última lección de la sección, verificar si hay secciones siguientes con lecciones
    if (moduloActual.secciones) {
      for (let i = seccionActualIndex + 1; i < moduloActual.secciones.length; i++) {
        const seccion = moduloActual.secciones[i];
        if (seccion && seccion.lecciones && seccion.lecciones.length > 0) {
          return true;
        }
      }
    }

    // Si es la última lección de la última sección, verificar si hay módulos siguientes con secciones y lecciones
    for (let i = this.moduloActualIndex + 1; i < this.modulos.length; i++) {
      const modulo = this.modulos[i];
      if (modulo.secciones) {
        for (let j = 0; j < modulo.secciones.length; j++) {
          const seccion = modulo.secciones[j];
          if (seccion && seccion.lecciones && seccion.lecciones.length > 0) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Carga la lección anterior
   */
  cargarLeccionAnterior(): void {
    if (!this.hayLeccionAnterior()) {
      return;
    }

    const moduloActual = this.modulos[this.moduloActualIndex];

    // Buscar la sección y lección actual
    let seccionActualIndex = -1;
    let leccionActualIndexEnSeccion = -1;

    if (moduloActual.secciones) {
      for (let i = 0; i < moduloActual.secciones.length; i++) {
        const seccion = moduloActual.secciones[i];
        if (seccion.lecciones) {
          for (let j = 0; j < seccion.lecciones.length; j++) {
            if (seccion.lecciones[j].id === this.leccionActual?.id) {
              seccionActualIndex = i;
              leccionActualIndexEnSeccion = j;
              break;
            }
          }
          if (seccionActualIndex >= 0) break;
        }
      }
    }

    // Si no se encontró la sección o lección actual, no hacer nada
    if (seccionActualIndex < 0 || leccionActualIndexEnSeccion < 0) {
      return;
    }

    // Si no es la primera lección de la sección actual
    if (leccionActualIndexEnSeccion > 0 && moduloActual.secciones) {
      const seccion = moduloActual.secciones[seccionActualIndex];
      if (seccion && seccion.lecciones && seccion.lecciones.length > leccionActualIndexEnSeccion - 1) {
        this.cargarLeccion(seccion.lecciones[leccionActualIndexEnSeccion - 1]);
        return;
      }
    }

    // Si es la primera lección de la sección pero no es la primera sección
    if (seccionActualIndex > 0 && moduloActual.secciones) {
      const seccionAnterior = moduloActual.secciones[seccionActualIndex - 1];
      if (seccionAnterior.lecciones && seccionAnterior.lecciones.length > 0) {
        // Cargar la última lección de la sección anterior
        this.cargarLeccion(seccionAnterior.lecciones[seccionAnterior.lecciones.length - 1]);
        return;
      }
    }

    // Si es la primera lección de la primera sección del módulo actual pero hay módulos anteriores
    if (this.moduloActualIndex > 0) {
      const moduloAnterior = this.modulos[this.moduloActualIndex - 1];
      if (moduloAnterior.secciones && moduloAnterior.secciones.length > 0) {
        const ultimaSeccion = moduloAnterior.secciones[moduloAnterior.secciones.length - 1];
        if (ultimaSeccion.lecciones && ultimaSeccion.lecciones.length > 0) {
          // Cargar la última lección de la última sección del módulo anterior
          this.cargarLeccion(ultimaSeccion.lecciones[ultimaSeccion.lecciones.length - 1]);
        }
      }
    }
  }

  /**
   * Carga la lección siguiente
   */
  cargarLeccionSiguiente(): void {
    if (!this.hayLeccionSiguiente()) {
      return;
    }

    const moduloActual = this.modulos[this.moduloActualIndex];

    // Buscar la sección y lección actual
    let seccionActualIndex = -1;
    let leccionActualIndexEnSeccion = -1;

    if (moduloActual.secciones) {
      for (let i = 0; i < moduloActual.secciones.length; i++) {
        const seccion = moduloActual.secciones[i];
        if (seccion.lecciones) {
          for (let j = 0; j < seccion.lecciones.length; j++) {
            if (seccion.lecciones[j].id === this.leccionActual?.id) {
              seccionActualIndex = i;
              leccionActualIndexEnSeccion = j;
              break;
            }
          }
          if (seccionActualIndex >= 0) break;
        }
      }
    }

    // Si no se encontró la sección o lección actual, no hacer nada
    if (seccionActualIndex < 0 || leccionActualIndexEnSeccion < 0) {
      return;
    }

    if (!moduloActual.secciones) {
      return;
    }

    const seccionActual = moduloActual.secciones[seccionActualIndex];

    // Si no es la última lección de la sección actual
    if (seccionActual.lecciones && leccionActualIndexEnSeccion < seccionActual.lecciones.length - 1) {
      this.cargarLeccion(seccionActual.lecciones[leccionActualIndexEnSeccion + 1]);
      return;
    }

    // Si es la última lección de la sección pero no es la última sección del módulo
    if (seccionActualIndex < moduloActual.secciones.length - 1) {
      const seccionSiguiente = moduloActual.secciones[seccionActualIndex + 1];
      if (seccionSiguiente.lecciones && seccionSiguiente.lecciones.length > 0) {
        // Cargar la primera lección de la sección siguiente
        this.cargarLeccion(seccionSiguiente.lecciones[0]);
        return;
      }
    }

    // Si es la última lección de la última sección del módulo actual pero hay módulos siguientes
    if (this.moduloActualIndex < this.modulos.length - 1) {
      const moduloSiguiente = this.modulos[this.moduloActualIndex + 1];
      if (moduloSiguiente.secciones && moduloSiguiente.secciones.length > 0) {
        const primeraSeccion = moduloSiguiente.secciones[0];
        if (primeraSeccion.lecciones && primeraSeccion.lecciones.length > 0) {
          // Cargar la primera lección de la primera sección del módulo siguiente
          this.cargarLeccion(primeraSeccion.lecciones[0]);
        }
      }
    }
  }

  loadProgreso(): void {
    if (!this.user || !this.curso) return;

    this.progresoService.getLeccionesCompletadasIds(this.curso.id, this.user.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.leccionesCompletadas = response.data;
            this.calcularPorcentajeCompletado();
          }
        },
        error: (error) => {
          console.error('Error al cargar progreso:', error);
        }
      });
  }

  calcularPorcentajeCompletado(): void {
    // Contar el total de lecciones en todos los módulos
    let totalLecciones = 0;
    this.modulos.forEach(modulo => {
      if (modulo.secciones) {
        modulo.secciones.forEach(seccion => {
          if (seccion.lecciones) {
            totalLecciones += seccion.lecciones.length;
          }
        });
      }
    });

    // Calcular el porcentaje
    if (totalLecciones > 0) {
      // Asegurarse de que el porcentaje no exceda el 100%
      const porcentaje = Math.min(
        Math.round((this.leccionesCompletadas.length / totalLecciones) * 100),
        100
      );

      this.porcentajeCompletado = porcentaje;

      // Actualizar el porcentaje en el cursoUsuario
      if (this.cursoUsuario) {
        this.cursoUsuario.porcentajeCompletado = this.porcentajeCompletado;

        // Si el porcentaje es 100%, marcar como completado
        if (this.porcentajeCompletado === 100 && !this.cursoUsuario.completado) {
          this.marcarCursoComoCompletado();
        }
      }

      // Calcular el progreso por módulo
      this.calcularProgresoPorModulo();
    } else {
      // Si no hay lecciones, el porcentaje es 0
      this.porcentajeCompletado = 0;
    }
  }

  /**
   * Calcula el porcentaje de progreso para cada módulo
   */
  calcularProgresoPorModulo(): void {
    this.progresosPorModulo = {};

    this.modulos.forEach(modulo => {
      if (modulo.secciones && modulo.secciones.length > 0) {
        // Contar el total de lecciones en todas las secciones del módulo
        let totalLeccionesEnModulo = 0;
        let leccionesCompletadasEnModulo = 0;

        modulo.secciones.forEach(seccion => {
          if (seccion.lecciones && seccion.lecciones.length > 0) {
            totalLeccionesEnModulo += seccion.lecciones.length;

            seccion.lecciones.forEach(leccion => {
              if (this.esLeccionCompletada(leccion.id)) {
                leccionesCompletadasEnModulo++;
              }
            });
          }
        });

        // Calcular el porcentaje para este módulo
        if (totalLeccionesEnModulo > 0) {
          const porcentajeModulo = Math.round((leccionesCompletadasEnModulo / totalLeccionesEnModulo) * 100);
          this.progresosPorModulo[modulo.id] = porcentajeModulo;
        } else {
          // Si el módulo no tiene lecciones, su progreso es 0
          this.progresosPorModulo[modulo.id] = 0;
        }
      } else {
        // Si el módulo no tiene secciones, su progreso es 0
        this.progresosPorModulo[modulo.id] = 0;
      }
    });
  }

  /**
   * Obtiene el porcentaje de progreso para un módulo específico
   */
  getModuloProgreso(modulo: Modulo): number {
    return this.progresosPorModulo[modulo.id] || 0;
  }

  /**
   * Método que se llama cuando se completa un cuestionario
   * @param aprobado Indica si el cuestionario fue aprobado
   */
  onCuestionarioCompleted(aprobado: boolean): void {
    if (!this.leccionActual || !this.user) return;

    // Marcar la lección como completada si el cuestionario fue aprobado
    if (aprobado) {
      this.actualizarProgresoLeccion(this.leccionActual.id);
      this.notification.success('¡Felicidades! Has completado el cuestionario correctamente');
    } else {
      this.notification.error('No has aprobado el cuestionario. Puedes intentarlo nuevamente.');
    }
  }

  puedeEditarCurso(): boolean {
    // Verificar si el usuario tiene permisos para editar el curso
    // Por ejemplo, si es administrador o creador del curso
    return this.user?.role === 'ADMIN' || this.user?.role === 'PROGRAMADOR';
  }

  gestionarCuestionario(leccionId: number): void {
    // Primero, verificar si ya existe un cuestionario para esta lección
    this.cuestionarioService.getCuestionarioByLeccionId(leccionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          let cuestionarioId: number | undefined;

          if (response.rpta === 1 && response.data) {
            cuestionarioId = response.data.id;
          }

          // Abrir el diálogo para crear o editar el cuestionario
          const dialogRef = this.dialog.open(CuestionarioFormComponent, {
            width: '900px',
            data: {
              leccionId: leccionId,
              cuestionarioId: cuestionarioId
            },
            disableClose: true
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              // Si se creó o actualizó el cuestionario, recargar la lección actual
              this.cargarLeccionPorId(leccionId);
            }
          });
        },
        error: (error: any) => {
          console.error('Error al verificar cuestionario:', error);
          this.notification.error('Error al verificar el cuestionario. Por favor, inténtelo de nuevo.');
        }
      });
  }

  // Método auxiliar para cargar una lección por su ID
  private cargarLeccionPorId(leccionId: number): void {
    this.leccionService.getLeccionById(leccionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.cargarLeccion(response.data);
          } else {
            this.notification.error('Error al cargar la lección');
          }
        },
        error: (error) => {
          console.error('Error al cargar lección:', error);
          this.notification.error('Error al cargar la lección');
        }
      });
  }

  actualizarProgresoLeccion(leccionId: number, tiempoVisto: number = 0): void {
    if (!this.user) return;

    // Verificar si la lección ya está completada
    const yaCompletada = this.leccionesCompletadas.includes(leccionId);

    if (!yaCompletada) {
      // Usar el método actualizado con el tiempo visto
      this.progresoService.marcarLeccionComoVista(leccionId, this.user.id, tiempoVisto)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              // Añadir a las lecciones completadas
              this.leccionesCompletadas.push(leccionId);
              this.calcularPorcentajeCompletado();
              this.notification.success('Lección marcada como completada');
              console.log('Lección marcada como completada correctamente');
            }
          },
          error: (error) => {
            console.error('Error al actualizar progreso de lección:', error);
            this.notification.error('No se pudo marcar la lección como completada');

            // Si hay un error 500, podría ser un problema con el formato de los datos
            // Intentar con un enfoque alternativo
            if (error.status === 500) {
              // Crear un objeto de progreso con los campos mínimos necesarios
              const progresoMinimo = {
                leccionId: leccionId,
                usuarioId: this.user!.id, // Usar el operador ! para indicar que user no es null
                completado: true
              };

              // Intentar directamente con el método completarLeccion
              this.progresoService.completarLeccion(progresoMinimo)
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                  next: (retryResponse) => {
                    if (retryResponse.rpta === 1) {
                      this.leccionesCompletadas.push(leccionId);
                      this.calcularPorcentajeCompletado();
                      this.notification.success('Lección marcada como completada');
                      console.log('Lección marcada como completada en segundo intento');
                    }
                  },
                  error: (retryError) => {
                    console.error('Error en segundo intento de actualizar progreso:', retryError);
                    this.notification.error('No se pudo marcar la lección como completada');
                  }
                });
            }
          }
        });
    }
  }

  /**
   * Desmarca una lección como completada
   */
  desmarcarProgresoLeccion(leccionId: number): void {
    if (!this.user) return;

    // Verificar si la lección está completada
    const yaCompletada = this.leccionesCompletadas.includes(leccionId);

    if (yaCompletada) {
      // Usar el método para desmarcar la lección
      this.progresoService.desmarcarLeccionComoCompletada(leccionId, this.user.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              // Eliminar de las lecciones completadas
              const index = this.leccionesCompletadas.indexOf(leccionId);
              if (index > -1) {
                this.leccionesCompletadas.splice(index, 1);
              }
              this.calcularPorcentajeCompletado();
              this.notification.success('Lección desmarcada como completada');
              console.log('Lección desmarcada como completada correctamente');
            }
          },
          error: (error) => {
            console.error('Error al desmarcar progreso de lección:', error);
            this.notification.error('No se pudo desmarcar la lección como completada');
          }
        });
    }
  }

  marcarCursoComoCompletado(): void {
    if (!this.user || !this.curso || !this.cursoUsuario) return;

    // Formatear la fecha correctamente para el backend
    // Usar formato YYYY-MM-DD sin la Z al final
    const fecha = new Date().toISOString().split('T')[0];

    this.cursoUsuarioService.updateCursoUsuario(this.cursoUsuario.id, {
      completado: true,
      fechaCompletado: fecha
    }).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.notification.success('¡Felicidades! Has completado el curso');
            this.cursoUsuario!.completado = true;
            console.log('Curso marcado como completado correctamente');
          }
        },
        error: (error) => {
          console.error('Error al marcar curso como completado:', error);
          this.notification.error('No se pudo marcar el curso como completado. Inténtalo de nuevo más tarde.');
        }
      });
  }

  actualizarUltimaVisualizacion(): void {
    // Esta función es opcional y no crítica para la funcionalidad principal
    // Si hay problemas con el backend, simplemente no actualizamos la fecha
    // para evitar errores que bloqueen la experiencia del usuario

    // Comentamos esta funcionalidad temporalmente hasta resolver el problema en el backend
    console.log('Actualización de última visualización desactivada temporalmente');

    /*
    if (!this.cursoUsuario) return;

    // Formatear la fecha correctamente para el backend
    // Usar formato YYYY-MM-DD sin la Z al final
    const fecha = new Date().toISOString().split('T')[0];

    this.cursoUsuarioService.updateCursoUsuario(this.cursoUsuario.id, {
      ultimaVisualizacion: fecha
    }).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            console.log('Última visualización actualizada correctamente');
          }
        },
        error: (error) => {
          console.error('Error al actualizar última visualización:', error);
        }
      });
    */
  }

  esLeccionCompletada(leccionId: number): boolean {
    return this.leccionesCompletadas.includes(leccionId);
  }

  getProgresoColor(): string {
    if (this.porcentajeCompletado < 30) return '#f44336'; // Rojo
    if (this.porcentajeCompletado < 70) return '#ff9800'; // Naranja
    return '#4caf50'; // Verde
  }

  isModuloExpanded(modulo: any): boolean {
    if (!modulo || !modulo.secciones || !this.leccionActual) {
      return false;
    }

    return modulo.secciones.some((seccion: any) =>
      seccion.lecciones && seccion.lecciones.some((leccion: any) =>
        leccion.id === this.leccionActual?.id
      )
    );
  }

  volver(): void {
    this.router.navigate(['/cursos/mis-cursos']);
  }

  /**
   * Método para depurar información del curso y sus lecciones
   * Útil para diagnosticar problemas con videos y contenido
   */
  depurarInformacionCurso(): void {
    if (!this.curso) {
      console.log('No hay curso para depurar');
      return;
    }

    console.log('=== INFORMACIÓN DE DEPURACIÓN DEL CURSO ===');
    console.log(`ID del curso: ${this.curso.id}`);
    console.log(`Nombre del curso: ${this.curso.nombre}`);
    console.log(`URL del video del curso: ${this.curso.videoUrl || 'No disponible'}`);

    if (this.curso.videoUrl) {
      console.log('El curso tiene una URL de video que puede usarse como fallback para lecciones sin video');
    } else {
      console.log('ADVERTENCIA: El curso no tiene URL de video para usar como fallback');
    }

    console.log('=== FIN DE INFORMACIÓN DE DEPURACIÓN ===');
  }

  /**
   * Método para depurar información de los módulos y lecciones
   * Útil para diagnosticar problemas con videos y contenido
   */
  depurarInformacionModulosYLecciones(): void {
    if (!this.modulos || this.modulos.length === 0) {
      console.log('No hay módulos para depurar');
      return;
    }

    console.log('=== INFORMACIÓN DE DEPURACIÓN DE MÓDULOS, SECCIONES Y LECCIONES ===');
    console.log(`Total de módulos: ${this.modulos.length}`);

    this.modulos.forEach((modulo, indexModulo) => {
      console.log(`\nMódulo ${indexModulo + 1}: ${modulo.titulo || modulo.nombre}`);
      console.log(`ID del módulo: ${modulo.id}`);

      if (!modulo.secciones || modulo.secciones.length === 0) {
        console.log('ADVERTENCIA: Este módulo no tiene secciones');
        return;
      }

      console.log(`Total de secciones en este módulo: ${modulo.secciones.length}`);

      modulo.secciones.forEach((seccion, indexSeccion) => {
        console.log(`\n  Sección ${indexSeccion + 1}: ${seccion.titulo}`);
        console.log(`  ID de la sección: ${seccion.id}`);

        if (!seccion.lecciones || seccion.lecciones.length === 0) {
          console.log('  ADVERTENCIA: Esta sección no tiene lecciones');
          return;
        }

        console.log(`  Total de lecciones en esta sección: ${seccion.lecciones.length}`);

        seccion.lecciones.forEach((leccion, indexLeccion) => {
          console.log(`\n    Lección ${indexLeccion + 1}: ${leccion.nombre}`);
          console.log(`    ID de la lección: ${leccion.id}`);
          console.log(`    URL del video: ${leccion.videoUrl || 'No disponible'}`);
          console.log(`    URL de subtítulos: ${leccion.subtitlesUrl || 'No disponible'}`);

          if (!leccion.videoUrl) {
            console.log('    ADVERTENCIA: Esta lección no tiene URL de video');
          }
        });
      });
    });

    console.log('\n=== FIN DE INFORMACIÓN DE DEPURACIÓN DE MÓDULOS, SECCIONES Y LECCIONES ===');
  }

  /**
   * Carga los subtítulos desde el backend para una lección específica
   * @param leccionId ID de la lección
   */
  cargarSubtitulosDesdeBackend(leccionId: number): void {
    console.log('Intentando cargar subtítulos desde el backend para la lección ID:', leccionId);

    // Usar el servicio de lecciones para obtener los subtítulos como Blob
    this.leccionService.getSubtitulosBlob(leccionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (blob) => {
          console.log('Subtítulos cargados correctamente desde el backend');

          // Crear una URL para el blob
          const url = URL.createObjectURL(blob);

          // Asignar la URL a los subtítulos
          this.subtitlesUrl = url;

          console.log('URL de subtítulos generada:', url);

          // Completar la carga
          this.loadingLeccion = false;
        },
        error: (error) => {
          console.error('Error al cargar subtítulos desde el backend:', error);

          // Si hay un error, intentar usar un archivo de subtítulos estático
          this.subtitlesUrl = 'assets/subtitles/default-subtitles.vtt';

          // Completar la carga
          this.loadingLeccion = false;
        }
      });
  }

  /**
   * Descarga los subtítulos desde Firebase y crea una URL local
   * @param firebaseUrl URL de Firebase Storage
   */
  descargarSubtitulosDesdeFirebase(firebaseUrl: string): void {
    console.log('Descargando subtítulos desde Firebase usando el proxy:', firebaseUrl);

    // Extraer el nombre del archivo para crear un subtítulo por defecto si todo falla
    const nombreArchivo = firebaseUrl.split('/').pop()?.split('?')[0] || '';
    console.log('Nombre del archivo de subtítulos:', nombreArchivo);

    // Verificar si el archivo es .vtt o .txt
    const esVtt = nombreArchivo.toLowerCase().endsWith('.vtt');
    const esTxt = nombreArchivo.toLowerCase().endsWith('.txt');

    console.log('Tipo de archivo detectado:', esVtt ? 'VTT' : esTxt ? 'TXT' : 'Desconocido');

    // Crear subtítulos por defecto en caso de que todo falle
    const crearSubtitulosPorDefecto = () => {
      console.log('Creando subtítulos por defecto');
      const contenidoDefault = 'WEBVTT\n\n1\n00:00:00.000 --> 00:00:05.000\nSubtítulos no disponibles para este video.\n\n2\n00:00:05.000 --> 00:00:10.000\nPor favor, contacte al administrador si necesita subtítulos.\n\n';
      const blob = new Blob([contenidoDefault], { type: 'text/vtt' });
      const url = URL.createObjectURL(blob);
      this.subtitlesUrl = url;
      this.actualizarSubtitulosEnVideo();
      this.loadingLeccion = false;
    };

    // Intentar cargar desde el backend primero si hay ID de lección
    if (this.leccionActual && this.leccionActual.id) {
      console.log('Intentando cargar subtítulos desde el backend usando el ID de lección');
      this.leccionService.getSubtitulosBlob(this.leccionActual.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (blob) => {
            console.log('Subtítulos cargados correctamente desde el backend');
            const url = URL.createObjectURL(blob);
            this.subtitlesUrl = url;
            this.actualizarSubtitulosEnVideo();
            this.loadingLeccion = false;
          },
          error: (error) => {
            console.error('Error al cargar subtítulos desde el backend, intentando con el proxy de URL:', error);

            // Si falla el backend, intentar usando el proxy con la URL directa
            this.leccionService.getSubtitulosByUrl(firebaseUrl)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (blob) => {
                  console.log('Subtítulos descargados correctamente usando el proxy de URL');
                  const url = URL.createObjectURL(blob);
                  this.subtitlesUrl = url;
                  this.actualizarSubtitulosEnVideo();
                  this.loadingLeccion = false;
                },
                error: (error) => {
                  console.error('Error al descargar subtítulos usando el proxy de URL:', error);

                  // Si falla el proxy, intentar con archivo local
                  this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
                    .pipe(takeUntil(this.destroy$))
                    .subscribe({
                      next: (contenido) => {
                        console.log('Usando subtítulos por defecto desde archivo local');
                        this.procesarContenidoSubtitulos(contenido);
                      },
                      error: () => {
                        console.error('No se pudo cargar ni siquiera los subtítulos por defecto');
                        crearSubtitulosPorDefecto();
                      }
                    });
                }
              });
          }
        });
    } else {
      // Si no hay ID de lección, intentar usando el proxy con la URL directa
      this.leccionService.getSubtitulosByUrl(firebaseUrl)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (blob) => {
            console.log('Subtítulos descargados correctamente usando el proxy de URL');
            const url = URL.createObjectURL(blob);
            this.subtitlesUrl = url;
            this.actualizarSubtitulosEnVideo();
            this.loadingLeccion = false;
          },
          error: (error) => {
            console.error('Error al descargar subtítulos usando el proxy de URL:', error);

            // Si falla el proxy, intentar con archivo local
            this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (contenido) => {
                  console.log('Usando subtítulos por defecto desde archivo local');
                  this.procesarContenidoSubtitulos(contenido);
                },
                error: () => {
                  console.error('No se pudo cargar ni siquiera los subtítulos por defecto');
                  crearSubtitulosPorDefecto();
                }
              });
          }
        });
    }
  }

  /**
   * Descarga un archivo de texto y lo convierte a formato WebVTT
   * @param url URL del archivo de texto
   */
  private descargarYConvertirTxtAVtt(url: string): void {
    console.log('Descargando archivo de texto para convertir a VTT:', url);

    this.http.get(url, { responseType: 'text' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (texto) => {
          console.log('Archivo de texto descargado correctamente, convirtiendo a VTT');
          // Convertir el texto a formato WebVTT
          const vttContent = this.convertirTextoAVtt(texto);

          // Crear un Blob con el contenido VTT
          const blob = new Blob([vttContent], { type: 'text/vtt' });

          // Crear una URL para el blob
          const blobUrl = URL.createObjectURL(blob);

          // Asignar la URL a los subtítulos
          this.subtitlesUrl = blobUrl;

          console.log('Archivo TXT convertido a VTT y asignado como URL de subtítulos');

          // Actualizar los subtítulos en el reproductor de video
          this.actualizarSubtitulosEnVideo();

          // Completar la carga
          this.loadingLeccion = false;
        },
        error: (error) => {
          console.error('Error al descargar el archivo de texto:', error);
          // Intentar con método alternativo
          this.cargarSubtitulosDirectamente(url);
        }
      });
  }

  /**
   * Intenta cargar los subtítulos directamente desde la URL proporcionada
   * Si falla, intenta con métodos alternativos
   */
  private cargarSubtitulosDirectamente(url: string): void {
    console.log('Intentando cargar subtítulos directamente desde URL:', url);

    // Determinar si es un archivo .txt basado en la URL
    const esTxt = url.toLowerCase().includes('.txt');

    if (esTxt) {
      console.log('URL parece ser un archivo de texto, descargando y convirtiendo a VTT');
      this.descargarYConvertirTxtAVtt(url);
      return;
    }

    // Para archivos VTT o SRT, intentar cargar directamente
    // Crear un elemento de video temporal para probar la carga de subtítulos
    const tempVideo = document.createElement('video');
    const tempTrack = document.createElement('track');

    tempTrack.kind = 'subtitles';
    tempTrack.src = url;
    tempTrack.srclang = 'es';
    tempTrack.label = 'Español';
    tempTrack.default = true;

    tempVideo.appendChild(tempTrack);

    // Establecer un timeout para detectar si la carga falla
    const timeout = setTimeout(() => {
      console.log('Timeout al cargar subtítulos, intentando descargar el contenido directamente');
      this.descargarContenidoSubtitulos(url);
    }, 3000);

    // Evento para cuando los subtítulos se cargan correctamente
    tempTrack.addEventListener('load', () => {
      clearTimeout(timeout);
      console.log('Subtítulos cargados correctamente de forma directa');

      // Asignar la URL a los subtítulos
      this.subtitlesUrl = url;

      // Actualizar los subtítulos en el reproductor de video
      this.actualizarSubtitulosEnVideo();

      // Completar la carga
      this.loadingLeccion = false;
    });

    // Evento para cuando hay un error al cargar los subtítulos
    tempTrack.addEventListener('error', () => {
      clearTimeout(timeout);
      console.log('Error al cargar subtítulos directamente, intentando descargar el contenido');

      // Intentar descargar el contenido directamente
      this.descargarContenidoSubtitulos(url);
    });
  }

  /**
   * Descarga el contenido de los subtítulos directamente y lo procesa
   */
  private descargarContenidoSubtitulos(url: string): void {
    console.log('Descargando contenido de subtítulos directamente:', url);

    this.http.get(url, { responseType: 'text' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (contenido) => {
          console.log('Contenido de subtítulos descargado correctamente');

          // Procesar el contenido según su formato
          if (contenido.trim().startsWith('WEBVTT')) {
            console.log('Contenido en formato WebVTT');
            this.procesarContenidoSubtitulos(contenido);
          } else if (contenido.includes('-->')) {
            console.log('Contenido con formato de tiempo, convirtiendo a WebVTT');
            const vttContent = this.convertirTextoAVtt(contenido);
            this.procesarContenidoSubtitulos(vttContent);
          } else {
            console.log('Contenido en formato de texto plano, convirtiendo a WebVTT');
            const vttContent = this.convertirTextoAVtt(contenido);
            this.procesarContenidoSubtitulos(vttContent);
          }
        },
        error: (error) => {
          console.error('Error al descargar contenido de subtítulos:', error);

          // Intentar cargar desde el backend como último recurso
          if (this.leccionActual && this.leccionActual.id) {
            console.log('Intentando cargar subtítulos desde el backend');
            this.cargarSubtitulosDesdeBackend(this.leccionActual.id);
          } else {
            // Si todo falla, usar subtítulos por defecto
            console.log('Usando subtítulos por defecto');
            this.extraerContenidoSubtitulos(url);
          }
        }
      });
  }

  /**
   * Intenta extraer el contenido de los subtítulos usando un proxy o método alternativo
   */
  private extraerContenidoSubtitulos(url: string): void {
    console.log('Intentando extraer contenido de subtítulos usando método alternativo');

    // Intentar usar un archivo local con el mismo nombre
    const nombreArchivo = url.split('/').pop()?.split('?')[0] || 'subtitles-es.vtt';
    const rutaLocal = `assets/subtitles/${nombreArchivo}`;

    console.log('Intentando cargar subtítulos desde ruta local:', rutaLocal);

    // Verificar si el archivo existe localmente
    this.http.get(rutaLocal, { responseType: 'text' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (contenido: string) => {
          console.log('Subtítulos cargados desde archivo local');
          this.procesarContenidoSubtitulos(contenido);
        },
        error: () => {
          console.log('No se encontró archivo local, usando subtítulos por defecto');
          // Usar subtítulos por defecto
          this.http.get('assets/subtitles/default-subtitles.vtt', { responseType: 'text' })
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (contenido: string) => {
                console.log('Usando subtítulos por defecto');
                this.procesarContenidoSubtitulos(contenido);
              },
              error: () => {
                console.error('No se pudo cargar ni siquiera los subtítulos por defecto');
                this.subtitlesUrl = null;
                this.loadingLeccion = false;
              }
            });
        }
      });
  }

  /**
   * Procesa el contenido de los subtítulos y lo almacena para su uso
   */
  private procesarContenidoSubtitulos(contenido: string): void {
    // Verificar que el contenido sea válido para VTT
    if (!contenido.trim().startsWith('WEBVTT')) {
      console.log('Añadiendo encabezado WEBVTT al archivo de subtítulos');
      contenido = 'WEBVTT\n\n' + contenido;
    }

    // Almacenar el contenido de los subtítulos
    this.subtitlesContent = contenido;

    // Indicar que hay subtítulos disponibles
    this.haySubtitulos = true;

    console.log('Contenido de subtítulos procesado y listo para usar');

    // Si el video ya está cargado, aplicar los subtítulos
    if (this.videoPlayer && this.videoPlayer.nativeElement) {
      this.aplicarSubtitulosAlVideo();
    }

    // Completar la carga
    this.loadingLeccion = false;
  }

  /**
   * Método que se ejecuta cuando el video se ha cargado
   */
  onVideoLoaded(): void {
    console.log('Video cargado, aplicando subtítulos si están disponibles');
    if (this.subtitlesContent) {
      this.aplicarSubtitulosAlVideo();
    }
    // Ya no marcamos automáticamente como completada la lección
  }

  /**
   * Aplica los subtítulos al video usando la API TextTrack
   */
  private aplicarSubtitulosAlVideo(): void {
    if (!this.videoPlayer || !this.videoPlayer.nativeElement || !this.subtitlesContent) {
      console.log('No se pueden aplicar subtítulos: video o contenido no disponible');
      return;
    }

    const video = this.videoPlayer.nativeElement;

    // Limpiar cualquier track existente
    while (video.textTracks.length > 0) {
      const track = video.textTracks[0];
      if (track.mode) {
        track.mode = 'disabled';
      }

      // Intentar eliminar el track si es posible
      const trackElem = video.querySelector('track');
      if (trackElem) {
        video.removeChild(trackElem);
      }
    }

    try {
      // Crear un nuevo track programáticamente
      const track = video.addTextTrack('subtitles', 'Español', 'es');

      // Usar nuestro parser manual para procesar el contenido VTT
      this.parseVTTManually(this.subtitlesContent, track);

      // Mostrar los subtítulos
      track.mode = 'showing';
      console.log('Subtítulos aplicados correctamente al video');
    } catch (error) {
      console.error('Error al aplicar subtítulos programáticamente:', error);

      // Método alternativo: crear un elemento track y usar blob URL
      // Esto podría causar el error de seguridad en algunos navegadores
      this.aplicarSubtitulosConElementoTrack();
    }
  }

  /**
   * Método alternativo para parsear VTT manualmente si el parser no está disponible
   */
  private parseVTTManually(vttContent: string, track: TextTrack): void {
    const lines = vttContent.split('\n');
    let inCue = false;
    let cueStart = 0;
    let cueEnd = 0;
    let cueText = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Saltar líneas vacías y la cabecera WEBVTT
      if (line === '' || line === 'WEBVTT') continue;

      // Buscar líneas con formato de tiempo "00:00:00.000 --> 00:00:00.000"
      if (line.includes('-->')) {
        const times = line.split('-->').map(t => t.trim());
        cueStart = this.parseTimeString(times[0]);
        cueEnd = this.parseTimeString(times[1]);
        inCue = true;
        cueText = '';
      }
      // Si estamos dentro de un cue, acumular el texto
      else if (inCue) {
        if (line === '') {
          // Fin del cue, añadirlo al track
          if (cueText) {
            track.addCue(new VTTCue(cueStart, cueEnd, cueText));
          }
          inCue = false;
        } else {
          // Añadir línea al texto del cue
          cueText += (cueText ? '\n' : '') + line;
        }
      }
    }

    // Añadir el último cue si quedó pendiente
    if (inCue && cueText) {
      track.addCue(new VTTCue(cueStart, cueEnd, cueText));
    }
  }

  /**
   * Convierte un string de tiempo en formato "00:00:00.000" a segundos
   */
  private parseTimeString(timeString: string): number {
    const parts = timeString.split(':');
    let seconds = 0;

    if (parts.length === 3) {
      // Formato hh:mm:ss.ms
      seconds = parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60;
      const secParts = parts[2].split('.');
      seconds += parseInt(secParts[0]);
      if (secParts.length > 1) {
        seconds += parseInt(secParts[1]) / 1000;
      }
    } else if (parts.length === 2) {
      // Formato mm:ss.ms
      seconds = parseInt(parts[0]) * 60;
      const secParts = parts[1].split('.');
      seconds += parseInt(secParts[0]);
      if (secParts.length > 1) {
        seconds += parseInt(secParts[1]) / 1000;
      }
    }

    return seconds;
  }

  /**
   * Método alternativo para aplicar subtítulos usando un elemento track
   * Esto podría causar el error de seguridad en algunos navegadores
   */
  private aplicarSubtitulosConElementoTrack(): void {
    if (!this.videoPlayer || !this.videoPlayer.nativeElement || !this.subtitlesContent) return;

    const video = this.videoPlayer.nativeElement;

    // Crear un Blob con el contenido
    const blob = new Blob([this.subtitlesContent], { type: 'text/vtt' });

    // Crear una URL para el blob
    const url = URL.createObjectURL(blob);

    // Guardar la URL para limpiarla después
    this.subtitlesUrl = url;

    // Crear un elemento track
    const track = document.createElement('track');
    track.kind = 'subtitles';
    track.label = 'Español';
    track.srclang = 'es';
    track.src = url;
    track.default = true;

    // Añadir el track al video
    video.appendChild(track);

    // Activar los subtítulos
    setTimeout(() => {
      if (track.track) {
        track.track.mode = 'showing';
      }
    }, 100);

    console.log('Subtítulos aplicados con elemento track (método alternativo)');
  }

  /**
   * Convierte texto plano a formato WebVTT
   * Soporta diferentes formatos de entrada:
   * 1. Formato SRT: con números de índice, tiempos y texto
   * 2. Formato simple: solo tiempos y texto
   * 3. Formato de texto plano: solo texto (se asignan tiempos automáticamente)
   * @param texto Texto a convertir
   * @returns Texto en formato WebVTT
   */
  private convertirTextoAVtt(texto: string): string {
    // Encabezado WebVTT
    let vttContent = 'WEBVTT\n\n';

    // Verificar si el texto ya está en formato VTT
    if (texto.trim().startsWith('WEBVTT')) {
      console.log('El texto ya está en formato WebVTT');
      return texto;
    }

    // Dividir el texto en líneas
    const lines = texto.split('\n');
    let index = 1;

    // Detectar el formato del texto
    const tieneFormatoTiempo = lines.some(line => line.includes('-->'));

    if (tieneFormatoTiempo) {
      // Procesar texto con formato de tiempo (SRT o similar)
      console.log('Procesando texto con formato de tiempo');

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // Buscar líneas con el formato de tiempo "00:00:00,000 --> 00:00:00,000"
        if (line.includes('-->')) {
          // Añadir el índice del subtítulo
          vttContent += index + '\n';
          index++;

          // Convertir el formato de tiempo de SRT (00:00:00,000) a VTT (00:00:00.000)
          const timeRange = line.replace(/,/g, '.');
          vttContent += timeRange + '\n';

          // Añadir el texto del subtítulo (puede estar en múltiples líneas)
          let subtitleText = '';
          i++;
          while (i < lines.length && lines[i].trim() !== '') {
            subtitleText += lines[i] + '\n';
            i++;
          }

          vttContent += subtitleText + '\n';
        }
      }
    } else {
      // Si no tiene formato de tiempo, crear subtítulos simples
      console.log('Texto sin formato de tiempo, creando subtítulos simples');

      // Agrupar el texto en bloques de 1-2 líneas para cada subtítulo
      const duracionPorSubtitulo = 5; // segundos
      let tiempoInicio = 0;

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim() === '') continue;

        // Calcular tiempos de inicio y fin
        const tiempoFin = tiempoInicio + duracionPorSubtitulo;

        // Formatear tiempos en formato VTT (00:00:00.000)
        const inicioFormateado = this.formatearTiempo(tiempoInicio);
        const finFormateado = this.formatearTiempo(tiempoFin);

        // Añadir el índice y el rango de tiempo
        vttContent += index + '\n';
        vttContent += `${inicioFormateado} --> ${finFormateado}\n`;

        // Añadir el texto del subtítulo (máximo 2 líneas)
        let subtitleText = lines[i] + '\n';
        if (i + 1 < lines.length && lines[i + 1].trim() !== '') {
          subtitleText += lines[i + 1] + '\n';
          i++;
        }

        vttContent += subtitleText + '\n';

        // Incrementar el índice y el tiempo de inicio
        index++;
        tiempoInicio = tiempoFin;
      }
    }

    return vttContent;
  }

  /**
   * Formatea un tiempo en segundos a formato VTT (00:00:00.000)
   * @param segundos Tiempo en segundos
   * @returns Tiempo formateado
   */
  private formatearTiempo(segundos: number): string {
    const horas = Math.floor(segundos / 3600);
    const minutos = Math.floor((segundos % 3600) / 60);
    const segs = Math.floor(segundos % 60);
    const milisegundos = Math.floor((segundos % 1) * 1000);

    return `${horas.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}:${segs.toString().padStart(2, '0')}.${milisegundos.toString().padStart(3, '0')}`;
  }

  /**
   * Actualiza los subtítulos en el reproductor de video
   * Este método se llama cuando cambia la URL de los subtítulos
   */
  actualizarSubtitulosEnVideo(): void {
    // Esperar a que el DOM se actualice
    setTimeout(() => {
      if (this.videoPlayer && this.videoPlayer.nativeElement) {
        const video = this.videoPlayer.nativeElement;

        // Buscar el elemento track de subtítulos
        const tracks = video.getElementsByTagName('track');

        if (tracks.length > 0) {
          // Forzar la recarga de los subtítulos
          const track = tracks[0];

          // Actualizar el src del track si es necesario
          if (this.subtitlesUrl && track.src !== this.subtitlesUrl) {
            console.log('Actualizando URL de subtítulos en el elemento track:', this.subtitlesUrl);
            track.src = this.subtitlesUrl;
          }

          // Desactivar y volver a activar los subtítulos para forzar la recarga
          if (track.track) {
            track.track.mode = 'disabled';
            setTimeout(() => {
              if (track.track) {
                track.track.mode = 'showing';
                console.log('Subtítulos activados en modo "showing"');
              }
            }, 100);
          }

          // Forzar la recarga del elemento track
          const currentTime = video.currentTime;
          video.textTracks[0].mode = 'showing';

          // Intentar forzar la recarga del video sin interrumpir la reproducción
          const isPlaying = !video.paused;
          if (isPlaying) {
            video.pause();
            setTimeout(() => {
              video.currentTime = currentTime;
              video.play().catch(e => console.error('Error al reanudar reproducción:', e));
            }, 200);
          } else {
            video.currentTime = currentTime;
          }

          console.log('Subtítulos actualizados en el reproductor de video');
        } else {
          console.log('No se encontraron elementos track en el video');

          // Si no hay elementos track, podríamos crear uno dinámicamente
          if (this.subtitlesUrl) {
            console.log('Creando elemento track dinámicamente');
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.label = 'Español';
            track.srclang = 'es';
            track.src = this.subtitlesUrl;
            track.default = true;

            video.appendChild(track);

            // Activar los subtítulos
            setTimeout(() => {
              if (track.track) {
                track.track.mode = 'showing';
                console.log('Subtítulos dinámicos activados');
              }
            }, 100);
          }
        }
      } else {
        console.log('El reproductor de video no está disponible');
      }
    }, 500);
  }

  /**
   * Sanitiza la URL del PDF para que pueda ser usada en un iframe
   * @param url URL del PDF
   * @returns URL sanitizada
   */
  getSafePdfUrl(url: string): SafeResourceUrl {
    // Verificar si la URL ya está sanitizada para evitar bucles
    if (typeof url === 'object') {
      console.log('URL ya sanitizada, evitando bucle');
      return url;
    }

    // Si la URL no comienza con http o https, asumimos que es una ruta relativa
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `${window.location.origin}/${url.startsWith('/') ? url.substring(1) : url}`;
    }

    // Para URLs de Firebase, usar un enfoque diferente para evitar bucles
    if (url.includes('firebasestorage.googleapis.com')) {
      console.log('URL de Firebase detectada, usando enfoque especial para evitar bucles');

      // Eliminar cualquier parámetro _t existente para evitar acumulación
      if (url.includes('_t=')) {
        url = url.replace(/[\?&]_t=\d+/g, '');
      }

      // Asegurarse de que la URL tenga el formato correcto después de eliminar _t
      if (url.includes('?') && !url.includes('&')) {
        // Si solo queda un ? sin parámetros, eliminarlo
        url = url.replace('?', '');
      }

      // No añadir parámetro de tiempo para Firebase, ya que puede causar problemas
      console.log('URL de Firebase procesada:', url);
    } else {
      // Para otras URLs, añadir parámetro para evitar caché
      if (url.includes('?')) {
        url = `${url}&_t=${new Date().getTime()}`;
      } else {
        url = `${url}?_t=${new Date().getTime()}`;
      }
      console.log('URL no Firebase procesada:', url);
    }

    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  /**
   * Divide una cadena de URLs separadas por comas en un array de URLs individuales
   * @param pdfUrlString Cadena de URLs separadas por comas
   * @returns Array de URLs individuales
   */
  getPdfUrls(pdfUrlString: string): string[] {
    if (!pdfUrlString) return [];

    // Dividir la cadena por comas y filtrar valores vacíos
    return pdfUrlString.split(',').filter(url => url && url.trim() !== '');
  }

  /**
   * Abre el PDF directamente en una nueva pestaña
   * @param url URL del PDF
   * @param event Evento del clic (opcional)
   */
  openPdfInNewTab(url: string, event?: Event): void {
    // Prevenir la propagación del evento si se proporciona
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Si la URL no comienza con http o https, asumimos que es una ruta relativa
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `${window.location.origin}/${url.startsWith('/') ? url.substring(1) : url}`;
    }

    // Para URLs de Firebase, limpiar cualquier parámetro _t existente
    if (url.includes('firebasestorage.googleapis.com') && url.includes('_t=')) {
      url = url.replace(/[\?&]_t=\d+/g, '');

      // Asegurarse de que la URL tenga el formato correcto después de eliminar _t
      if (url.includes('?') && !url.includes('&')) {
        // Si solo queda un ? sin parámetros, eliminarlo
        url = url.replace('?', '');
      }

      console.log('Abriendo URL de Firebase limpia:', url);
    }

    // Abrir en nueva pestaña
    window.open(url, '_blank');

    // Ya no marcamos automáticamente como completada la lección al abrir un PDF
    // El usuario debe hacer clic en el botón "Marcar como completada"
  }
}

