import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EncuestaService } from '@app/services/encuesta.service';
import { Encuesta } from '@app/models/backend/encuesta/encuesta.model';

@Component({
  selector: 'app-encuesta-detalle',
  templateUrl: './encuesta-detalle.component.html',
  styleUrls: ['./encuesta-detalle.component.scss'],
})
export class EncuestaDetalleComponent implements OnInit {
  encuestaId: number;
  encuesta: Encuesta | null = null;
  loading = false;
  error = false;

  constructor(
    private route: ActivatedRoute,
    private encuestaService: EncuestaService
  ) {}

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    this.encuestaId = idParam ? +idParam : 0;
    this.loadEncuesta();
  }

  loadEncuesta(): void {
    this.loading = true;
    this.encuestaService.getCompleta(this.encuestaId).subscribe({
      next: (response) => {
        this.encuesta = response.data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar encuesta:', error);
        this.loading = false;
        this.error = true;
      },
    });
  }
}
