<div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden max-w-3xl mx-auto w-full">
  <!-- Título del diálogo -->
  <div class="bg-gray-100 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-4 px-6">
    <h2 class="text-xl font-bold text-center text-gray-800 dark:text-white">Editar Usuario</h2>
  </div>

  <!-- Contenido del diálogo -->
  <div class="flex-1 p-6 overflow-auto">
    <form class="space-y-4">
      <!-- Fila 1: Nombre y Apellido -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Nombre -->
        <div class="flex flex-col space-y-1">
          <label for="nombre" class="text-sm font-medium text-gray-700 dark:text-gray-300">Nombre</label>
          <input
            type="text"
            id="nombre"
            [(ngModel)]="editingUser.nombre"
            name="nombre"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <!-- Apellido -->
        <div class="flex flex-col space-y-1">
          <label for="apellido" class="text-sm font-medium text-gray-700 dark:text-gray-300">Apellido</label>
          <input
            type="text"
            id="apellido"
            [(ngModel)]="editingUser.apellido"
            name="apellido"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <!-- Fila 2: Username y Sede -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Username -->
        <div class="flex flex-col space-y-1">
          <label for="username" class="text-sm font-medium text-gray-700 dark:text-gray-300">Username</label>
          <input
            type="text"
            id="username"
            [(ngModel)]="editingUser.username"
            name="username"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <!-- Selector de Sede -->
        <div class="flex flex-col space-y-1">
          <label for="sede" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sede</label>
          <select
            id="sede"
            [(ngModel)]="editingUser.sede_id"
            name="sede_id"
            (ngModelChange)="onSedeChange($event)"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option *ngIf="loading" value="">Cargando sedes...</option>
            <option *ngIf="!loading" value="" disabled>Seleccione una sede</option>
            <option *ngFor="let sede of sedes" [value]="sede.id">{{ sede.nombre }}</option>
          </select>
          <p *ngIf="loading" class="text-xs text-gray-500 dark:text-gray-400">Cargando sedes...</p>
        </div>
      </div>

      <!-- Fila 3: Sede Actual (solo lectura) -->
      <div class="flex flex-col space-y-1">
        <label for="sedeActual" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sede Actual</label>
        <input
          type="text"
          id="sedeActual"
          [value]="sedes | sedeNombre: (editingUser.sede_id || undefined)"
          readonly
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white cursor-not-allowed"
        />
      </div>

      <!-- Fila 4: Teléfono y Email -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Teléfono -->
        <div class="flex flex-col space-y-1">
          <label for="telefono" class="text-sm font-medium text-gray-700 dark:text-gray-300">Teléfono</label>
          <input
            type="tel"
            id="telefono"
            [(ngModel)]="editingUser.telefono"
            name="telefono"
            pattern="[0-9]{0,9}"
            maxlength="9"
            (keypress)="validateNumberInput($event)"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <!-- Email -->
        <div class="flex flex-col space-y-1">
          <label for="email" class="text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
          <input
            type="email"
            id="email"
            [(ngModel)]="editingUser.email"
            name="email"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <!-- Fila 5: Estado y Role -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Estado -->
        <div class="flex flex-col space-y-1">
          <label for="estado" class="text-sm font-medium text-gray-700 dark:text-gray-300">Estado</label>
          <select
            id="estado"
            [(ngModel)]="editingUser.estado"
            name="estado"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="A">Activo</option>
            <option value="I">Inactivo</option>
          </select>
        </div>

        <!-- Role -->
        <div class="flex flex-col space-y-1">
          <label for="role" class="text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
          <select
            id="role"
            [(ngModel)]="editingUser.role"
            name="role"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option *ngFor="let role of roles" [value]="role">
              {{ role === 'BACKOFFICETRAMITADOR' ? 'BACKOFFICE TRAMITADOR' :
                 role === 'BACKOFFICESEGUIMIENTO' ? 'BACKOFFICE SEGUIMIENTO' : role }}
            </option>
          </select>
        </div>
      </div>

      <!-- Opción para cambiar contraseña -->
      <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
        <div class="flex items-center mb-2">
          <input
            type="checkbox"
            id="changePassword"
            [(ngModel)]="changePassword"
            name="changePassword"
            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <label for="changePassword" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Cambiar contraseña
          </label>
        </div>

        <!-- Campo de contraseña (visible solo si se marca la casilla) -->
        <div *ngIf="changePassword" class="mt-3">
          <div class="flex flex-col space-y-1">
            <label for="password" class="text-sm font-medium text-gray-700 dark:text-gray-300">Nueva contraseña</label>
            <div class="relative">
              <input
                [type]="hidePassword ? 'password' : 'text'"
                id="password"
                [formControl]="password"
                placeholder="Ingrese nueva contraseña"
                autocomplete="new-password"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
              />
              <button
                type="button"
                (click)="hidePassword = !hidePassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400"
              >
                <mat-icon>{{ hidePassword ? "visibility_off" : "visibility" }}</mat-icon>
              </button>
            </div>
            <div class="flex justify-between">
              <p *ngIf="password.invalid && password.touched" class="text-xs text-red-500">
                La contraseña debe tener al menos 7 caracteres
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Mínimo 7 caracteres</p>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Acciones del diálogo -->
  <div class="bg-gray-50 dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
    <button
      (click)="onCancel()"
      class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      Cancelar
    </button>
    <button
      (click)="onSave()"
      class="px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center space-x-2"
    >
      <mat-icon>save</mat-icon>
      <span>Guardar</span>
    </button>
  </div>
</div>
