import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '@src/environments/environment';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { GenericResponse } from '@app/models/backend/generic-response';
import { Leccion, LeccionCreateRequest, LeccionUpdateRequest } from '@app/models/backend/curso/leccion.model';
import { finalize, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class LeccionService {
  private baseUrl = environment.url + 'api/lecciones';
  private leccionesSubject = new BehaviorSubject<Leccion[]>([]);
  public lecciones$ = this.leccionesSubject.asObservable();

  constructor(
    private http: HttpClient,
    private storage: AngularFireStorage
  ) {}

  /**
   * Obtiene todas las lecciones de una sección
   */
  getLeccionesBySeccionId(seccionId: number): Observable<GenericResponse<Leccion[]>> {
    return this.http.get<GenericResponse<Leccion[]>>(`${this.baseUrl}/seccion/${seccionId}`).pipe(
      map(response => {
        if (response.data) {
          this.leccionesSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene todas las lecciones de un módulo (a través de sus secciones)
   */
  getLeccionesByModuloId(moduloId: number): Observable<GenericResponse<Leccion[]>> {
    return this.http.get<GenericResponse<Leccion[]>>(`${this.baseUrl}/modulo/${moduloId}`).pipe(
      map(response => {
        if (response.data) {
          this.leccionesSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene una lección por su ID
   */
  getLeccionById(id: number): Observable<GenericResponse<Leccion>> {
    return this.http.get<GenericResponse<Leccion>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea una nueva lección
   * Si se proporciona un archivo de video, subtítulos o PDFs, primero los sube a Firebase
   */
  createLeccion(leccion: LeccionCreateRequest, videoFile?: File, subtitlesFile?: File, pdfFiles?: File[]): Observable<GenericResponse<Leccion>> {
    // Si hay un archivo de video, subtítulos o PDFs, primero los subimos a Firebase
    if (videoFile || subtitlesFile || (pdfFiles && pdfFiles.length > 0)) {
      return this.uploadFilesAndCreateLeccion(leccion, videoFile, subtitlesFile, pdfFiles);
    }

    // Si no hay archivos, simplemente creamos la lección
    return this.http.post<GenericResponse<Leccion>>(this.baseUrl, leccion);
  }

  /**
   * Actualiza una lección existente
   * Si se proporciona un archivo de video, subtítulos o PDFs, primero los sube a Firebase
   */
  updateLeccion(id: number, leccion: LeccionUpdateRequest, videoFile?: File, subtitlesFile?: File, pdfFiles?: File[]): Observable<GenericResponse<Leccion>> {
    // Asegurarnos de que el campo tipoLeccion se envíe correctamente
    console.log('Actualizando lección con tipo:', leccion.tipoLeccion);

    // Si hay un archivo de video, subtítulos o PDFs, primero los subimos a Firebase
    if (videoFile || subtitlesFile || (pdfFiles && pdfFiles.length > 0)) {
      return this.uploadFilesAndUpdateLeccion(id, leccion, videoFile, subtitlesFile, pdfFiles);
    }

    // Si no hay archivos, simplemente actualizamos la lección
    return this.http.put<GenericResponse<Leccion>>(`${this.baseUrl}/${id}`, leccion);
  }

  /**
   * Elimina una lección
   */
  deleteLeccion(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Reordena las lecciones de un módulo
   */
  reordenarLecciones(moduloId: number, leccionIds: number[]): Observable<GenericResponse<Leccion[]>> {
    return this.http.put<GenericResponse<Leccion[]>>(`${this.baseUrl}/modulo/${moduloId}/reordenar`, { leccionIds });
  }

  /**
   * Reordena las lecciones de una sección
   */
  reordenarLeccionesEnSeccion(seccionId: number, leccionIds: number[]): Observable<GenericResponse<Leccion[]>> {
    return this.http.put<GenericResponse<Leccion[]>>(`${this.baseUrl}/seccion/${seccionId}/reordenar`, { leccionIds });
  }

  /**
   * Obtiene los subtítulos de una lección
   * @param leccionId ID de la lección
   * @returns Observable con la respuesta genérica que contiene el texto de los subtítulos
   */
  getSubtitulos(leccionId: number): Observable<GenericResponse<string>> {
    return this.http.get<GenericResponse<string>>(`${this.baseUrl}/${leccionId}/subtitulos`, { responseType: 'text' as 'json' });
  }

  /**
   * Obtiene los subtítulos de una lección como un archivo Blob
   * @param leccionId ID de la lección
   * @returns Observable con el Blob de los subtítulos
   */
  getSubtitulosBlob(leccionId: number): Observable<Blob> {
    // Usar el nuevo proxy de subtítulos para evitar problemas de CORS
    return this.http.get(`${environment.url}api/subtitulos-proxy/leccion/${leccionId}`, { responseType: 'blob' });
  }

  /**
   * Obtiene los subtítulos directamente desde una URL de Firebase usando el proxy
   * @param url URL de Firebase Storage
   * @returns Observable con el Blob de los subtítulos
   */
  getSubtitulosByUrl(url: string): Observable<Blob> {
    // Usar el nuevo proxy de subtítulos para evitar problemas de CORS
    return this.http.get(`${environment.url}api/subtitulos-proxy/url?url=${encodeURIComponent(url)}`, { responseType: 'blob' });
  }

  /**
   * Sube archivos (video, subtítulos y/o PDFs) a Firebase y luego crea la lección
   */
  private uploadFilesAndCreateLeccion(leccion: LeccionCreateRequest, videoFile?: File, subtitlesFile?: File, pdfFiles?: File[]): Observable<GenericResponse<Leccion>> {
    return new Observable<GenericResponse<Leccion>>(observer => {
      // Arreglo para almacenar las tareas de carga
      const uploadTasks: Promise<string>[] = [];

      // Si hay un archivo de video, subirlo a Firebase
      if (videoFile) {
        const videoUploadPromise = this.uploadFileToFirebase(videoFile, 'lecciones/videos');
        uploadTasks.push(videoUploadPromise);
      }

      // Si hay un archivo de subtítulos, subirlo a Firebase
      if (subtitlesFile) {
        const subtitlesUploadPromise = this.uploadFileToFirebase(subtitlesFile, 'lecciones/subtitulos');
        uploadTasks.push(subtitlesUploadPromise);
      }

      // Si hay archivos PDF, subirlos a Firebase
      const pdfUrls: string[] = [];
      if (pdfFiles && pdfFiles.length > 0) {
        // Crear promesas para subir cada archivo PDF
        const pdfUploadPromises = pdfFiles.map(pdfFile =>
          this.uploadFileToFirebase(pdfFile, 'lecciones/pdfs')
            .then(url => {
              pdfUrls.push(url);
              return url;
            })
        );

        // Añadir todas las promesas al array de tareas
        uploadTasks.push(...pdfUploadPromises);
      }

      // Esperar a que todas las cargas se completen
      Promise.all(uploadTasks)
        .then(urls => {
          // Asignar las URLs a la lección
          if (videoFile) {
            leccion.videoUrl = urls.shift() || '';
          }

          if (subtitlesFile) {
            leccion.subtitlesUrl = urls.shift() || '';
          }

          // Si hay URLs de PDFs, asignarlas a la lección
          if (pdfUrls.length > 0) {
            leccion.pdfUrl = pdfUrls.join(',');
          }

          // Crear la lección con las URLs de los archivos
          this.http.post<GenericResponse<Leccion>>(this.baseUrl, leccion).subscribe({
            next: (response) => {
              observer.next(response);
              observer.complete();
            },
            error: (error) => {
              observer.error(error);
            }
          });
        })
        .catch(error => {
          observer.error({
            rpta: 0,
            msg: 'Error al subir los archivos a Firebase',
            data: null,
            errors: error
          });
        });
    });
  }

  /**
   * Sube archivos (video, subtítulos y/o PDFs) a Firebase y luego actualiza la lección
   */
  private uploadFilesAndUpdateLeccion(id: number, leccion: LeccionUpdateRequest, videoFile?: File, subtitlesFile?: File, pdfFiles?: File[]): Observable<GenericResponse<Leccion>> {
    return new Observable<GenericResponse<Leccion>>(observer => {
      // Arreglo para almacenar las tareas de carga
      const uploadTasks: Promise<string>[] = [];

      // Si hay un archivo de video, subirlo a Firebase
      if (videoFile) {
        const videoUploadPromise = this.uploadFileToFirebase(videoFile, 'lecciones/videos');
        uploadTasks.push(videoUploadPromise);
      }

      // Si hay un archivo de subtítulos, subirlo a Firebase
      if (subtitlesFile) {
        const subtitlesUploadPromise = this.uploadFileToFirebase(subtitlesFile, 'lecciones/subtitulos');
        uploadTasks.push(subtitlesUploadPromise);
      }

      // Si hay archivos PDF, subirlos a Firebase
      const pdfUrls: string[] = [];
      if (pdfFiles && pdfFiles.length > 0) {
        // Crear promesas para subir cada archivo PDF
        const pdfUploadPromises = pdfFiles.map(pdfFile =>
          this.uploadFileToFirebase(pdfFile, 'lecciones/pdfs')
            .then(url => {
              pdfUrls.push(url);
              return url;
            })
        );

        // Añadir todas las promesas al array de tareas
        uploadTasks.push(...pdfUploadPromises);
      }

      // Esperar a que todas las cargas se completen
      Promise.all(uploadTasks)
        .then(urls => {
          // Asignar las URLs a la lección
          if (videoFile) {
            leccion.videoUrl = urls.shift() || '';
          }

          if (subtitlesFile) {
            leccion.subtitlesUrl = urls.shift() || '';
          }

          // Si hay URLs de PDFs, asignarlas a la lección
          if (pdfUrls.length > 0) {
            leccion.pdfUrl = pdfUrls.join(',');
          }

          // Asegurarnos de que el campo tipoLeccion se envíe correctamente
          console.log('Actualizando lección con archivos y tipo:', leccion.tipoLeccion);

          // Actualizar la lección con las URLs de los archivos
          this.http.put<GenericResponse<Leccion>>(`${this.baseUrl}/${id}`, leccion).subscribe({
            next: (response) => {
              observer.next(response);
              observer.complete();
            },
            error: (error) => {
              observer.error(error);
            }
          });
        })
        .catch(error => {
          observer.error({
            rpta: 0,
            msg: 'Error al subir los archivos a Firebase',
            data: null,
            errors: error
          });
        });
    });
  }

  /**
   * Sube un archivo a Firebase y devuelve la URL de descarga
   * @param file Archivo a subir
   * @param basePath Ruta base en Firebase Storage
   * @returns Promesa con la URL de descarga
   */
  private uploadFileToFirebase(file: File, basePath: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      // Crear un nombre único para el archivo
      const filePath = `${basePath}/${new Date().getTime()}_${file.name}`;
      const fileRef = this.storage.ref(filePath);
      const task = this.storage.upload(filePath, file);

      // Observar el progreso de la carga
      task.percentageChanges().subscribe(percentage => {
        console.log(`Progreso de carga de ${file.name}: ${percentage}%`);
      });

      // Cuando la carga se complete, obtener la URL
      task.snapshotChanges().pipe(
        finalize(() => {
          fileRef.getDownloadURL().subscribe({
            next: (url) => {
              console.log(`Archivo ${file.name} subido correctamente. URL: ${url}`);
              resolve(url);
            },
            error: (error) => {
              console.error(`Error al obtener la URL de descarga para ${file.name}:`, error);
              reject(error);
            }
          });
        })
      ).subscribe({
        error: (error) => {
          console.error(`Error al subir el archivo ${file.name}:`, error);
          reject(error);
        }
      });
    });
  }
}
