.mat-dialog-container {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  padding: 0;
}

/* Título del modal */
[mat-dialog-title] {
  background-color: #3f51b5;
  color: white;
  margin: 0;
  padding: 20px 24px;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Contenido del modal */
mat-dialog-content {
  padding: 24px;
  margin: 0;
}

/* Grupo de radio buttons */
.radio-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  gap: 12px;
}

.mat-radio-button {
  font-size: 16px;
}

/* Campos de formulario */
.full-width {
  width: 100%;
  margin-top: 16px;
}

/* Selector de mes y año */
.mes-selector {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  
  mat-form-field {
    flex: 1;
  }
}

/* Botones de acción */
mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #f9f9f9;
}

button[mat-button] {
  padding: 8px 16px;
  font-weight: 500;
}

button[mat-raised-button] {
  padding: 8px 20px;
  font-weight: 500;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

/* Animación para el cambio entre opciones */
ng-container {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos responsivos */
@media (min-width: 600px) {
  .mat-dialog-container {
    min-width: 450px;
  }
}

@media (max-width: 599px) {
  .mat-dialog-container {
    width: 100%;
    max-width: 100%;
    border-radius: 0;
  }
  
  [mat-dialog-title] {
    font-size: 18px;
    padding: 16px 20px;
  }
  
  mat-dialog-content {
    padding: 16px 20px;
  }
  
  mat-dialog-actions {
    padding: 12px 20px;
  }
  
  .mes-selector {
    flex-direction: column;
    gap: 8px;
  }
}