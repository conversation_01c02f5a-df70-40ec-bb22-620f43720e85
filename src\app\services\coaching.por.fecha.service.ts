import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CoachingPorFechaService {
  private apiUrl = 'https://apisozarusac.com/Coaching/api/frases-por-rango/';

  constructor(private http: HttpClient) {}

  obtenerFrasesPorRango(desde: string, hasta: string): Observable<any[]> {
    const body = { desde, hasta };
    return this.http.post<any[]>(this.apiUrl, body);
  }
}
