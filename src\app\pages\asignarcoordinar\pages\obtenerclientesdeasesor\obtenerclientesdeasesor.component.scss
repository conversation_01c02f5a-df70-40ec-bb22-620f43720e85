/* Users Container */
.users-container {
  padding: 1.5rem;
  min-height: calc(100vh - 64px);
  width: 100%;
  box-sizing: border-box;
}

/* Header section */
.header-section {
  background-color: white;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.05);
  margin-bottom: 1.5rem;

  h1 {
    margin: 0;
    font-size: 1.75rem;
    color: #2c3e50;
    font-weight: 600;
  }
}

/* Search section */
.search-section {
  background-color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.05);
  margin-bottom: 1.5rem;

  mat-form-field {
    margin-right: 1rem;
  }

  button {
    height: 51px;
    margin-top: -0.8rem;
    min-width: 100px;

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

/* Table styles */
.table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.05);
  overflow-x: auto; /* IMPORTANTE para scroll horizontal */
  margin-bottom: 1.5rem;
  width: 100%;
}

.mat-table.responsive-table {
  width: 100%;
  background: transparent;
  min-width: 100%;
  table-layout: fixed;

  @media screen and (min-width: 1400px) {
    /* Para pantallas extra grandes, optimizamos el espacio */
    min-width: 100%;
    width: 100%;
  }

  @media screen and (min-width: 992px) and (max-width: 1399px) {
    /* Para pantallas grandes, aseguramos que se vean todas las columnas */
    min-width: 100%;
    width: 100%;
  }

  @media screen and (min-width: 768px) and (max-width: 991px) {
    /* Para pantallas medianas, aseguramos que se vean al menos 2 elementos por fila */
    min-width: 900px;
    width: 100%;
    table-layout: auto; /* Permitir que las columnas se ajusten automáticamente */
  }

  @media screen and (max-width: 767px) {
    min-width: 700px;
  }

  /* Ajustes específicos para cada columna */
  [matColumnDef="dni"] {
    @media screen and (min-width: 1200px) {
      width: 10%;
    }
  }

  [matColumnDef="nombres"] {
    @media screen and (min-width: 1200px) {
      width: 20%;
    }
  }

  [matColumnDef="fechaIngresado"] {
    @media screen and (min-width: 1200px) {
      width: 20%;
    }
  }

  [matColumnDef="numeroMovil"] {
    @media screen and (min-width: 1200px) {
      width: 15%;
    }
  }

  [matColumnDef="accion"] {
    @media screen and (min-width: 1200px) {
      width: 15%;
    }
  }

  .mat-header-cell {
    background-color: #f4f5f7;
    color: #5e6c84;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 1rem;

    @media screen and (min-width: 1400px) {
      /* Más espacio en pantallas grandes */
      padding: 1.25rem 1rem;
      font-size: 0.95rem;
    }

    @media screen and (max-width: 767px) {
      padding: 0.75rem 0.5rem;
      font-size: 0.8rem;
    }
  }

  .mat-cell {
    padding: 1rem;
    font-size: 0.875rem;
    color: #172b4d;

    @media screen and (min-width: 1400px) {
      /* Más espacio en pantallas grandes */
      padding: 1.25rem 1rem;
      font-size: 0.95rem;
    }

    @media screen and (max-width: 767px) {
      padding: 0.75rem 0.5rem;
      font-size: 0.8rem;
    }
  }

  /* Estilos para los botones de acción */
  [matColumnDef="accion"] .mat-cell button {
    @media screen and (min-width: 1400px) {
      /* Botones más grandes en pantallas grandes */
      padding: 0 1rem;
      height: 40px;
      line-height: 40px;
      font-size: 1rem;

      mat-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        margin-right: 6px;
      }
    }

    @media screen and (max-width: 767px) {
      padding: 0 0.5rem;
      height: 32px;
      line-height: 32px;
      font-size: 0.8rem;

      mat-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
        margin-right: 4px;
      }
    }
  }
}

/* Pagination controls */
.pagination-controls {
  padding: 1rem;
  background-color: white;
  border-top: 0.0625rem solid #e9ecef;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;

  button {
    min-width: 100px;
  }

  span {
    color: #172b4d;
  }
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    color: #6b7280;
  }

  p {
    margin: 0;
    font-size: 1rem;
    color: #6b7280;
  }
}

/* Empty state en modo oscuro */
:host-context(body.dark-theme) .empty-state {
  background-color: #1a2035 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;

  p {
    color: rgba(255, 255, 255, 0.7) !important;
  }
}



/* Spinner overlay */
.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(3px);
}

/* Spinner overlay en modo oscuro */
:host-context(body.dark-theme) .spinner-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Responsive adjustments */
@media screen and (max-width: 1200px) {
  .search-section {
    flex-wrap: wrap;

    mat-form-field {
      flex: 1 1 calc(50% - 1rem);
      margin-bottom: 1rem;
    }

    button {
      flex: 0 0 auto;
    }
  }
}

@media screen and (max-width: 960px) {
  .header-section h1 {
    font-size: 1.5rem;
  }

  .search-section {
    mat-form-field {
      flex: 1 1 calc(50% - 1rem);
    }
  }
}

@media screen and (max-width: 768px) {
  .users-container {
    padding: 1rem;
  }

  .search-section {
    padding: 1rem;
    flex-direction: column;

    mat-form-field {
      flex: 1 1 100%;
      margin-right: 0;
      margin-bottom: 1rem;
    }

    button {
      width: 100%;
      margin: 0.5rem 0;
      height: 45px;
    }
  }

  .pagination-controls {
    flex-direction: column;
    gap: 0.5rem;

    button {
      width: 100%;
    }

    span {
      order: -1;
    }
  }
}

@media screen and (max-width: 480px) {
  .users-container {
    padding: 0.75rem;
  }

  .header-section {
    padding: 1rem;

    h1 {
      font-size: 1.25rem;
    }
  }

  .search-section {
    padding: 0.75rem;
  }

  .mat-table {
    .mat-header-cell,
    .mat-cell {
      padding: 0.75rem;
      font-size: 0.813rem;
    }
  }
}

mat-sidenav-content {
  background-color: #DAE2F1;
}

/* Estilos para tema oscuro */
.dark-theme-header-container {
  background-color: #1e1e1e;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark-theme-text {
  color: white !important;
}

.dark-theme-card {
  background-color: #1a2035 !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Estilos para el título y subtítulo */
:host ::ng-deep .dark-theme-card {
  h2 {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  p {
    color: rgba(255, 255, 255, 0.8) !important;
  }
}

/* Estilos para el buscador y botones de acción */
.data-table-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.dark-theme-table {
  background-color: #1a2035 !important;
}

/* Estilos para el paginador */
.mat-paginator {
  background: transparent;
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* Estilos para el paginador en modo oscuro */
.dark-theme-paginator {
  background-color: #1a2035 !important;
  color: white !important;

  .mat-paginator-container {
    color: white !important;
  }

  .mat-paginator-page-size-label,
  .mat-paginator-range-label {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .mat-paginator-navigation-previous,
  .mat-paginator-navigation-next,
  .mat-paginator-navigation-first,
  .mat-paginator-navigation-last {
    color: white !important;

    &[disabled] {
      color: rgba(255, 255, 255, 0.3) !important;
    }
  }

  .mat-select-value,
  .mat-select-arrow {
    color: white !important;
  }

  .mat-paginator-icon {
    fill: white !important;
  }
}

.dark-theme-header {
  background-color: #111827 !important;
  color: white !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark-theme-cell {
  color: rgba(255, 255, 255, 0.9) !important;
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

/* Estilos para las filas en hover */
:host ::ng-deep .dark-theme-table {
  .mat-row:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }
}

.dark-theme-button {
  color: white !important;
  background-color: #1e4976 !important;

  &:hover {
    background-color: #2c5a8a !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  }
}

.dark-theme-button-outline {
  border-color: #64b5f6 !important;
  color: #64b5f6 !important;
  background-color: transparent !important;

  &:hover {
    background-color: rgba(100, 181, 246, 0.1) !important;
  }
}

.dark-theme-modal {
  background-color: #1a2035 !important;
  color: white !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4) !important;
}

.dark-theme-modal-header {
  background-color: #111827 !important;
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

.dark-theme-modal-body {
  color: white !important;
  background-color: #1a2035 !important;
}

.dark-theme-modal-footer {
  background-color: #111827 !important;
  border-top-color: rgba(255, 255, 255, 0.1) !important;
}

.dark-theme-detail-group {
  background-color: #1e2746 !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Estilos para el campo de búsqueda en tema oscuro */
:host ::ng-deep .dark-theme-card {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3);
  }

  .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: rgba(255, 255, 255, 0.5);
  }

  .mat-form-field-appearance-outline .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7);
  }

  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #64b5f6;
  }

  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: #64b5f6;
  }

  .mat-input-element {
    color: white;
  }

  .mat-form-field-suffix .mat-icon {
    color: rgba(255, 255, 255, 0.7);
  }

  /* Estilos para los botones de acción en modo oscuro */
  .action-button {
    &.filter-button {
      background-color: #1e4976 !important;
      color: white !important;

      &:hover {
        background-color: #2c5a8a !important;
      }
    }

    &.clear-button {
      color: #ef9a9a !important;
      border-color: #ef9a9a !important;

      &:hover {
        background-color: rgba(239, 154, 154, 0.1) !important;
      }
    }

    &.excel-button {
      background-color: #1e4976 !important;
      color: white !important;

      &:hover {
        background-color: #2c5a8a !important;
      }
    }

    &.pdf-button {
      background-color: #1e4976 !important;
      color: white !important;

      &:hover {
        background-color: #2c5a8a !important;
      }
    }
  }
}

/* Estilos para el modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;

  &.show {
    display: flex;
  }
}

.modal-content {
  background-color: #fff;
  padding: 0;
  border-radius: 8px;
  width: 95%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;

  h2 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    color: #1976d2;
  }

  .header-actions {
    display: flex;
    gap: 4px;
    align-items: center;

    button {
      height: 32px;
      width: 32px;
      line-height: 32px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      mat-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        line-height: 18px;
        color: #1976d2;
      }
    }
  }
}

.modal-body {
  padding: 10px;
  font-size: 0.85rem;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;

  .action-button {
    min-width: 100px;
    font-size: 0.85rem;
    line-height: 36px;
    padding: 0 16px;
    height: 36px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      margin-right: 6px;
    }

    span {
      font-weight: 500;
      letter-spacing: 0.3px;
    }

    &.close-button {
      background-color: #f44336;
      color: white;

      &:hover {
        background-color: #e53935;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  @media screen and (max-width: 480px) {
    flex-direction: column;

    .action-button {
      width: 100%;
    }
  }
}

/* Estilos para las secciones del formulario */
.form-header {
  background-color: #4caf50; /* Color verde */
  color: white;
  padding: 5px;
  margin: 5px 0;
  font-weight: bold;
  text-align: center;
  font-size: 0.9rem;
}

.form-section {
  margin-bottom: 10px;
  border: 1px solid #eee;
  overflow: hidden;
}

.form-row {
  display: flex;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
  min-height: 24px;

  &:last-child {
    border-bottom: none;
  }

  &:nth-child(even) {
    background-color: #f8f9fa;
  }

  &:nth-child(odd) {
    background-color: white;
  }
}

.form-label {
  width: 40%;
  min-width: 150px;
  font-weight: 500;
  color: #333;
  font-size: 0.8rem;
  padding: 4px 8px;
}

.form-value {
  width: 60%;
  color: #333;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-left: 1px solid #eee;
  text-indent: 5px;
}

/* Estilos para los valores de autorización y fútbol */
.status-inline {
  display: flex;
  align-items: center;
  padding: 0;

  mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    margin-right: 4px;

    &:first-child {
      color: #4CAF50;
    }

    &:nth-child(2) {
      color: #F44336;
    }

    &:nth-child(3) {
      color: #9E9E9E;
    }
  }

  span {
    font-weight: 500;
    font-size: 13px;
    line-height: 1;
  }

  &.status-yes span {
    color: #2E7D32;
  }

  &.status-no span {
    color: #C62828;
  }

  &.status-undefined span {
    color: #616161;
    font-style: italic;
  }
}

/* Mantener compatibilidad con clases antiguas */
.authorization-value {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    line-height: 18px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.text-container {
  font-size: 13px;
}

/* Estilos para estados de carga y error */
.loading-text {
  text-align: center;
  padding: 20px;
  color: #666;
}

.error-container {
  text-align: center;
  padding: 20px;
  color: #f44336;
}

.error-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  margin-bottom: 16px;
}

/* Estilos para tema oscuro */
.dark-theme-modal {
  background-color: #1a2035;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);

  h2 {
    color: #64b5f6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .header-actions button mat-icon {
    color: #64b5f6 !important;
  }

  .form-header {
    background-color: #1e4976;
    color: white;
    border-radius: 2px;
  }

  .form-section {
    background-color: #1a2035;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .form-row {
    border-bottom-color: rgba(255, 255, 255, 0.1);

    &:nth-child(even) {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &:nth-child(odd) {
      background-color: transparent;
    }

    .form-label {
      color: #bdbdbd;
      border-right-color: rgba(255, 255, 255, 0.1);
      font-weight: 500;
    }

    .form-value {
      color: #ffffff;
      border-left-color: rgba(255, 255, 255, 0.1);

      .status-inline {
        mat-icon {
          &:first-child {
            color: #81C784;
          }

          &:nth-child(2) {
            color: #EF5350;
          }

          &:nth-child(3) {
            color: #BDBDBD;
          }
        }

        span {
          color: #E0E0E0;
        }

        &.status-yes span {
          color: #81C784;
        }

        &.status-no span {
          color: #EF9A9A;
        }

        &.status-undefined span {
          color: #E0E0E0;
        }
      }

      /* Compatibilidad con clases antiguas */
      mat-icon {
        &[style*="color: green"] {
          color: #81C784 !important;
        }

        &[style*="color: red"] {
          color: #EF5350 !important;
        }

        &[style*="color: gray"] {
          color: #BDBDBD !important;
        }
      }

      &.authorization-value {
        .text-container {
          color: #ffffff;
        }

        .icon-container {
          mat-icon {
            &[style*="color: green"] {
              color: #81C784 !important;
            }

            &[style*="color: red"] {
              color: #EF5350 !important;
            }

            &[style*="color: gray"] {
              color: #BDBDBD !important;
            }
          }
        }
      }
    }
  }

  .modal-header {
    background-color: #111827;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .modal-body {
    background-color: #1a2035;
  }

  .modal-footer {
    background-color: #111827;
    border-top-color: rgba(255, 255, 255, 0.1);

    .action-button {
      &.close-button {
        background-color: #c62828 !important;
        color: white !important;

        &:hover {
          background-color: #d32f2f !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
        }
      }
    }
  }

  /* Estilos para estados de carga y error en modo oscuro */
  .loading-text {
    color: #bdbdbd !important;
  }

  .error-container {
    color: #ef9a9a !important;

    .error-icon {
      color: #ef5350 !important;
    }

    .error-text {
      color: #ef9a9a !important;
    }

    button {
      background-color: #1e4976 !important;
      color: white !important;

      &:hover {
        background-color: #2c5a8a !important;
      }
    }
  }
}
/* Animación para el modal */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-1.25rem); /* 20px / 16px = 1.25rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cliente-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(18rem, 1fr)); /* Columnas responsivas */
  gap: 1rem; /* Espacio entre los cuadros */
  padding: 1rem;

  .detail-group {
    background-color: #f8f9fa; /* Fondo gris claro */
    border-radius: 0.5rem; /* Bordes redondeados */
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05); /* Sombra suave */

    h4 {
      margin: 0 0 0.75rem 0; /* Margen inferior */
      font-size: 1rem;
      font-weight: 600;
      color: #333;
      border-bottom: 0.0625rem solid #e0e0e0; /* Línea divisoria */
      padding-bottom: 0.5rem;
    }

    p {
      margin: 0.5rem 0; /* Espaciado entre párrafos */
      font-size: 0.875rem;
      color: #555;

      strong {
        font-weight: 600;
        color: #000;
        display: inline-block;
        min-width: 10rem; /* Ancho mínimo para alinear los textos */
        margin-right: 0.5rem; /* Espacio entre el strong y el texto */
      }
    }
  }
}

/* Estilos para el botón de cierre */
.close {
  font-size: 1.5rem;
  cursor: pointer;
}
/* Estilo mejorado para los botones de acciones en el filtro */
.search-section {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 1rem;
}

/* Contenedor de botones de acción */
.action-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  width: 100%;
}

/* Estilos para el formulario compacto */
.compact-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.search-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  flex: 1;

  @media (max-width: 1200px) {
    gap: 8px;
  }
}

.compact-field {
  width: 150px;
  margin: 0;

  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }

  ::ng-deep .mat-form-field-infix {
    padding: 0.5em 0;
    width: auto;
    border-top: 0;
  }

  ::ng-deep .mat-form-field-flex {
    height: 40px;
    align-items: center;
  }

  ::ng-deep .mat-form-field-suffix {
    top: 0;
    align-self: center;
  }

  @media (max-width: 1200px) {
    width: calc(50% - 6px);
  }

  @media (max-width: 768px) {
    width: 100%;
  }
}

.filter-buttons {
  display: flex;
  gap: 8px;
  align-items: flex-start;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-end;
  }

  @media (max-width: 480px) {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

/* Estilo común para todos los botones de acción */
.action-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 36px !important;
  min-width: 90px !important;
  padding: 0 12px !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  letter-spacing: 0.25px !important;
  text-transform: none !important;
  box-shadow: none !important;
  white-space: nowrap !important;
  line-height: 36px !important;

  mat-icon {
    font-size: 18px !important;
    height: 18px !important;
    width: 18px !important;
    margin-right: 6px !important;
    line-height: 18px !important;
  }

  span {
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
}

/* Estilos específicos para cada tipo de botón */
.filter-button {
  background-color: #3f51b5 !important;
  color: white !important;

  &:hover {
    background-color: #303f9f !important;
  }
}

.clear-button {
  color: #f44336 !important;
  border-color: #f44336 !important;

  &:hover {
    background-color: rgba(244, 67, 54, 0.08) !important;
  }
}

.excel-button {
  background-color: #ff4081 !important;
  color: white !important;

  &:hover {
    background-color: #f50057 !important;
  }
}

.pdf-button {
  background-color: #1976d2 !important;
  color: white !important;

  &:hover {
    background-color: #1565c0 !important;
  }

  &[disabled] {
    background-color: rgba(0, 0, 0, 0.12) !important;
    color: rgba(0, 0, 0, 0.38) !important;
  }
}

/* Responsive para diferentes tamaños de pantalla */
@media screen and (min-width: 768px) {
  .action-button {
    min-width: 100px !important;
  }
}

@media screen and (max-width: 480px) {
  .action-buttons-container {
    flex-direction: column;
  }

  .action-button {
    width: 100% !important;
  }
}

/* Estilos específicos para la impresión de PDF */
#clienteDetalle {
  /* Eliminar espacios innecesarios */
  padding: 0 !important;
  margin: 0 !important;

  /* Asegurar que el contenido comience desde arriba */
  .form-header:first-child {
    margin-top: 0 !important;
  }

  /* Hacer que las secciones sean más compactas */
  .form-section {
    margin-bottom: 5px !important;
  }

  /* Reducir el espacio entre filas */
  .form-row {
    min-height: 20px !important;
    padding: 1px 0 !important;
  }

  /* Reducir el tamaño de los encabezados */
  .form-header {
    padding: 3px !important;
    margin: 3px 0 !important;
    font-size: 0.85rem !important;
  }

  /* Ajustar el tamaño de texto en las etiquetas y valores */
  .form-label, .form-value {
    font-size: 0.75rem !important;
    padding: 2px 6px !important;
  }
}

