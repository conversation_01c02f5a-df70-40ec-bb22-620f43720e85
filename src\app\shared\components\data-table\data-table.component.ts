import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-data-table',
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.scss']
})
export class DataTableComponent implements OnInit {
  @Input() showHeader: boolean = true;
  @Input() columns: any[] = [];
  @Input() data: any[] = [];
  @Input() loading: boolean = false;
  @Input() totalItems: number = 0;
  @Input() pageSize: number = 10;
  @Input() pageSizeOptions: number[] = [5, 10, 25, 50, 100];
  @Input() showPagination: boolean = true;
  @Input() showSearch: boolean = true;
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() allowAdd: boolean = true;
  @Input() allowEdit: boolean = true;
  @Input() allowDelete: boolean = true;
  @Input() allowRefresh: boolean = true;
  @Input() allowExport: boolean = false;
  @Input() addButtonText: string = 'Agregar nuevo';
  @Input() emptyStateText: string = 'Sin registros.';
  @Input() searchPlaceholder: string = 'Buscar...';

  @Output() add = new EventEmitter<void>();
  @Output() edit = new EventEmitter<any>();
  @Output() delete = new EventEmitter<any>();
  @Output() refresh = new EventEmitter<void>();
  @Output() export = new EventEmitter<void>();
  @Output() pageChange = new EventEmitter<any>();
  @Output() search = new EventEmitter<string>();
  @Output() rowClick = new EventEmitter<any>();
  @Output() actionClick = new EventEmitter<{action: string, row: any}>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  dataSource = new MatTableDataSource<any>([]);
  displayedColumns: string[] = [];
  searchText: string = '';

  constructor() { }

  ngOnInit(): void {
    this.displayedColumns = this.columns.map(col => col.property);

    // Agregar columna de acciones si se permite editar o eliminar
    if (this.allowEdit || this.allowDelete) {
      this.displayedColumns.push('actions');
    }

    this.updateDataSource();
  }

  ngOnChanges(): void {
    this.updateDataSource();
  }

  updateDataSource(): void {
    if (this.data) {
      this.dataSource.data = this.data;

      if (this.paginator) {
        this.dataSource.paginator = this.paginator;
      }

      if (this.sort) {
        this.dataSource.sort = this.sort;
      }
    }
  }

  onAddClick(): void {
    this.add.emit();
  }

  onEditClick(row: any): void {
    this.edit.emit(row);
  }

  onDeleteClick(row: any): void {
    this.delete.emit(row);
  }

  onRefreshClick(): void {
    this.refresh.emit();
  }

  onExportClick(): void {
    this.export.emit();
  }

  onPageChange(event: any): void {
    this.pageChange.emit(event);
  }

  onSearchChange(event: any): void {
    this.search.emit(event.target.value);
  }

  onRowClick(row: any): void {
    this.rowClick.emit(row);
  }

  onActionClick(action: string, row: any): void {
    this.actionClick.emit({action, row});
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  clearSearch(): void {
    this.searchText = '';
    this.dataSource.filter = '';
    this.search.emit('');

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
