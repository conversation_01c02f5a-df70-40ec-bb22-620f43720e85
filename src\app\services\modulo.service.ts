import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { Modulo, ModuloCreateRequest, ModuloUpdateRequest } from '@app/models/backend/curso/modulo.model';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ModuloService {
  private baseUrl = environment.url + 'api/modulos';
  private modulosSubject = new BehaviorSubject<Modulo[]>([]);
  public modulos$ = this.modulosSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene todos los módulos de un curso
   */
  getModulosByCursoId(cursoId: number): Observable<GenericResponse<Modulo[]>> {
    return this.http.get<GenericResponse<Modulo[]>>(`${this.baseUrl}/curso/${cursoId}`).pipe(
      map(response => {
        if (response.data) {
          this.modulosSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene un módulo por su ID
   */
  getModuloById(id: number): Observable<GenericResponse<Modulo>> {
    return this.http.get<GenericResponse<Modulo>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea un nuevo módulo
   */
  createModulo(modulo: ModuloCreateRequest): Observable<GenericResponse<Modulo>> {
    return this.http.post<GenericResponse<Modulo>>(this.baseUrl, modulo);
  }

  /**
   * Actualiza un módulo existente
   */
  updateModulo(id: number, modulo: ModuloUpdateRequest): Observable<GenericResponse<Modulo>> {
    return this.http.put<GenericResponse<Modulo>>(`${this.baseUrl}/${id}`, modulo);
  }

  /**
   * Elimina un módulo
   */
  deleteModulo(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Reordena los módulos de un curso
   */
  reordenarModulos(cursoId: number, moduloIds: number[]): Observable<GenericResponse<Modulo[]>> {
    return this.http.put<GenericResponse<Modulo[]>>(`${this.baseUrl}/curso/${cursoId}/reordenar`, { moduloIds });
  }
}
