import { <PERSON><PERSON><PERSON> } from './modulo.model';
import { Leccion } from './leccion.model';

/**
 * Modelo que representa una sección de un módulo
 */
export interface Seccion {
  id: number;
  titulo: string;
  descripcion: string;
  orden: number;
  estado: string; // A: Activo, I: Inactivo
  modulo: Modulo | null;
  moduloId?: number;
  lecciones?: Leccion[];
  fechaCreacion?: string;
  fechaActualizacion?: string;
  completado?: boolean; // Indica si todas las lecciones están completadas
}

/**
 * Modelo para crear una nueva sección
 */
export interface SeccionCreateRequest {
  titulo: string;
  descripcion: string;
  orden?: number;
  moduloId: number;
  estado?: string;
}

/**
 * Modelo para actualizar una sección existente
 */
export interface SeccionUpdateRequest {
  titulo?: string;
  descripcion?: string;
  orden?: number;
  estado?: string;
}
