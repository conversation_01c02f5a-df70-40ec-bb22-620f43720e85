<!-- src/app/pages/anuncios-recientes/anuncios-recientes.component.html -->
<div class="px-4">
  <!-- Carga -->
  <ng-template #loadingTemplate>
    <div
      class="flex flex-col items-center justify-center py-8 px-5 text-center"
    >
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"
      ></div>
      <p class="text-gray-600 dark:text-gray-300 text-sm">
        Cargando anuncios...
      </p>
    </div>
  </ng-template>

  <!-- Contenido principal -->
  <ng-container *ngIf="!loading; else loadingTemplate">
    <ng-container *ngIf="anunciosFiltrados$ | async as anuncios">
      <!-- Cabecera y buscador -->
      <div
        *ngIf="anuncios.length > 0"
        class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700 gap-3"
      >
        <h3
          class="text-xl font-bold text-gray-700 dark:text-white relative flex items-center gap-2.5 after:content-[''] after:absolute after:left-0 after:-bottom-2 after:w-10 after:h-0.5 after:bg-gradient-to-r after:from-blue-500 after:to-cyan-400 after:rounded-sm"
        >
          Anuncios recientes
          <ng-container *ngIf="!isAdmin; else adminBadge">
            <span
              *ngIf="userSedeId"
              class="ml-2 text-xs font-medium bg-green-500 text-white px-2 py-1 rounded-xl inline-flex items-center"
            >
              <i class="fas fa-map-marker-alt mr-1"></i>{{ userSedeName }}
            </span>
          </ng-container>
          <ng-template #adminBadge>
            <span
              class="ml-2 text-xs font-medium bg-blue-500 text-white px-2 py-1 rounded-xl"
            >
              <i class="fas fa-globe mr-1"></i> Todas las sedes
            </span>
          </ng-template>
        </h3>

        <div
          class="relative w-full md:w-1/3 shadow-sm rounded-lg overflow-hidden"
        >
          <input
            type="text"
            [(ngModel)]="searchTerm"
            placeholder="Buscar"
            class="w-full py-3 px-4 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-gray-50 dark:bg-gray-700 dark:text-white focus:outline-none focus:border-blue-500"
            (input)="searchTermSubject.next(searchTerm)"
          />
          <button
            *ngIf="searchTerm"
            (click)="clearSearch()"
            class="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 p-1.5"
          >
            <i class="fas fa-times"></i>
          </button>
          <button
            (click)="searchTermSubject.next(searchTerm)"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 p-1.5"
          >
            <i class="fas fa-search"></i>
          </button>
        </div>
      </div>

      <!-- Mensaje “no hay resultados” -->
      <div
        *ngIf="anuncios.length === 0 && searchTerm"
        class="flex flex-col items-center justify-center py-10 px-5 text-center bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm my-5"
      >
        <i
          class="fas fa-search text-2xl text-gray-400 dark:text-gray-500 mb-4"
        ></i>
        <p class="text-gray-600 dark:text-gray-300 mb-5">
          No se encontraron anuncios que coincidan con "<strong
            class="text-gray-800 dark:text-white"
            >{{ searchTerm }}</strong
          >"
        </p>
        <button
          (click)="clearSearch()"
          class="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium"
        >
          Limpiar búsqueda
        </button>
      </div>

      <!-- Grid de 2 columnas -->
      <div
        *ngIf="anuncios.length > 0"
        class="grid grid-cols-1 sm:grid-cols-2 gap-6"
      >
        <div
          *ngFor="let anuncio of anuncios"
          class="group cursor-pointer"
          (click)="
            openImageViewer(anuncio.imagenUrl || pictureDefault, anuncio.titulo)
          "
        >
          <div
            class="flex flex-col bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg"
            style="height: 700px"
          >
            <!-- Cabecera interna -->
            <div class="p-4">
              <div
                *ngIf="anuncio.categoria"
                class="inline-block bg-blue-500 text-white text-xs font-bold py-1 px-2 rounded-full mb-2"
              >
                {{ anuncio.categoria }}
              </div>
              <h4
                class="text-lg font-semibold text-gray-800 dark:text-white mb-1"
              >
                {{ anuncio.titulo }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                {{ anuncio.descripcion }}
              </p>
            </div>
            <!-- Imagen expandida -->
            <div class="flex-1 relative">
              <img
                [src]="anuncio.imagenUrl || pictureDefault"
                alt="Anuncio"
                class="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Paginador -->
      <div *ngIf="totalElements > 0" class="flex justify-center mt-8">
        <mat-paginator
          [length]="totalElements"
          [pageSize]="maxAnuncios"
          [pageSizeOptions]="[6, 12, 18, 24]"
          [pageIndex]="currentPage"
          (page)="onPageChange($event)"
          showFirstLastButtons
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 w-full max-w-2xl"
        >
        </mat-paginator>
      </div>
    </ng-container>
  </ng-container>
</div>
