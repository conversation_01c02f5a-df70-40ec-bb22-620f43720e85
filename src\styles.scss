/* You can add global styles to this file, and also import other style files */
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos personalizados para Material Angular en modo oscuro ahora están integrados directamente */
html,
body {
  height: 100%;
  margin: 0 auto;
  padding: 0;
}

/* ya lo tienes: quita padding/margin y forzamos full‐screen */
.fullscreen-dialog .mat-dialog-container {
  padding: 0 !important;
  margin: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Eliminar padding para componentes de curso */
.curso-dialog .mat-dialog-container {
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* AHORA forzamos que la imagen llene el contenedor sin deformarse */
.fullscreen-dialog img {
  display: block;
  width: 100vw !important;
  height: 100vh !important;
  object-fit: contain;
}

/* Estilos para el panel del mat-select de modelos Whisper */
.whisper-model-panel {
  .mat-option {
    height: auto !important;
    min-height: 60px !important;
    line-height: 1.2 !important;
    padding: 8px 16px !important;

    .mat-option-text {
      white-space: normal !important;
      line-height: 1.2 !important;
    }
  }

  .mat-option:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
  }

  .mat-option.mat-selected {
    background-color: rgba(63, 81, 181, 0.1) !important;
  }
}

/* Estilos para snackbar de advertencia de transcripción */
.warning-snackbar {
  background-color: #ff9800 !important; /* Color naranja de advertencia */
  color: white !important;

  .mat-simple-snackbar-action {
    color: white !important;
    font-weight: bold !important;
    border: 1px solid white !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
  }

  .mat-simple-snackbar-action:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}
