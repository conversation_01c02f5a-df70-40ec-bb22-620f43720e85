import { User } from '@app/models/backend/user';
import { Curso } from './curso.model';

/**
 * Modelo para la asignación de cursos a usuarios
 */
export interface CursoUsuario {
  id: number;
  cursoId: number;
  curso?: Curso;
  usuarioId: number;
  usuario?: User;
  fechaAsignacion: string | any[];
  estado: string; // A: Activo, I: Inactivo
  completado: boolean;
  fechaCompletado?: string | any[];
  porcentajeCompletado: number;
  ultimaVisualizacion?: string | any[];
}

/**
 * Modelo para crear una nueva asignación de curso a usuario
 */
export interface CursoUsuarioCreateRequest {
  cursoId: number;
  usuarioId: number;
  estado?: string;
}

/**
 * Modelo para actualizar una asignación de curso a usuario
 */
export interface CursoUsuarioUpdateRequest {
  estado?: string;
  completado?: boolean;
  fechaCompletado?: string;
  porcentajeCompletado?: number;
  ultimaVisualizacion?: string;
}

/**
 * Modelo para la respuesta de asignación masiva de cursos
 */
export interface AsignacionMasivaResponse {
  totalAsignados: number;
  usuariosAsignados: number[];
  errores: string[];
}
