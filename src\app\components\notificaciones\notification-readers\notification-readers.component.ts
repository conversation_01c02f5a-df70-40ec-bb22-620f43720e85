import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import {
  NotificationReadUser,
  NotificationReadersResponse,
} from '@app/models/notification-read-user.model';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';
import { NotificationReadersModalComponent } from '../notification-readers-modal/notification-readers-modal.component';

@Component({
  selector: 'app-notification-readers',
  templateUrl: './notification-readers.component.html',
  styleUrls: ['./notification-readers.component.css'],
})
export class NotificationReadersComponent implements OnInit, OnDestroy {
  @Input() notificationId!: number;
  @Input() notificationTitle: string = '';
  @Input() showCount: boolean = true;
  @Input() maxAvatars: number = 5;

  readers: NotificationReadUser[] = [];
  readCount: number = 0;
  loading: boolean = false;
  private subscriptions: Subscription[] = [];

  constructor(
    private notificacionesService: NotificacionesWsService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    if (this.notificationId) {
      this.loadReaders();
      this.subscribeToUpdates();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  private loadReaders(): void {
    this.loading = true;

    // Cargar usuarios que han leído
    this.subscriptions.push(
      this.notificacionesService
        .obtenerUsuariosQueHanLeido(this.notificationId)
        .subscribe({
          next: (readers) => {
            this.readers = readers;
            this.loading = false;
          },
          error: (error) => {
            console.error('Error al cargar usuarios que han leído:', error);
            this.loading = false;
          },
        })
    );

    // Cargar contador de lecturas
    this.subscriptions.push(
      this.notificacionesService
        .obtenerContadorLecturas(this.notificationId)
        .subscribe({
          next: (count) => {
            this.readCount = count;
          },
          error: (error) => {
            console.error('Error al cargar contador de lecturas:', error);
          },
        })
    );
  }

  private subscribeToUpdates(): void {
    // Suscribirse a actualizaciones en tiempo real
    this.subscriptions.push(
      this.notificacionesService
        .suscribirseAActualizacionesLecturas(this.notificationId)
        .subscribe({
          next: (response: NotificationReadersResponse) => {
            this.readers = response.readers;
            this.readCount = response.readCount;
          },
          error: (error) => {
            console.error('Error en actualizaciones de lecturas:', error);
          },
        })
    );
  }

  get displayedReaders(): NotificationReadUser[] {
    return this.readers.slice(0, this.maxAvatars);
  }

  get remainingCount(): number {
    return Math.max(0, this.readers.length - this.maxAvatars);
  }

  getAvatarUrl(reader: NotificationReadUser): string {
    // Generar avatar basado en las iniciales del usuario
    const initials = this.getInitials(reader);
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      initials
    )}&background=random&color=fff&size=32`;
  }

  private getInitials(reader: NotificationReadUser): string {
    const nombreCompleto = this.getNombreCompleto(reader);
    const words = nombreCompleto.trim().split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return nombreCompleto.substring(0, 2).toUpperCase();
  }

  /**
   * Obtiene el nombre completo del usuario
   */
  getNombreCompleto(reader: NotificationReadUser): string {
    if (reader.nombre && reader.apellido) {
      return `${reader.nombre} ${reader.apellido}`;
    } else if (reader.nombre) {
      return reader.nombre;
    } else if (reader.apellido) {
      return reader.apellido;
    } else {
      return reader.userName;
    }
  }

  formatReadTime(readAt: string): string {
    const date = new Date(readAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) {
      return 'Ahora mismo';
    } else if (diffMinutes < 60) {
      return `Hace ${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''}`;
    } else if (diffMinutes < 1440) {
      const hours = Math.floor(diffMinutes / 60);
      return `Hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMinutes / 1440);
      return `Hace ${days} día${days > 1 ? 's' : ''}`;
    }
  }

  trackByUserId(_index: number, reader: NotificationReadUser): number {
    return reader.userId;
  }

  onImageError(event: any, reader: NotificationReadUser): void {
    // Si falla la carga de la imagen, usar avatar por defecto
    event.target.src = this.getAvatarUrl(reader);
  }

  /**
   * Abre el modal para mostrar todos los usuarios que han leído
   */
  openReadersModal(): void {
    this.dialog.open(NotificationReadersModalComponent, {
      width: '700px',
      maxWidth: '90vw',
      data: {
        notificationId: this.notificationId,
        notificationTitle: this.notificationTitle || 'Notificación',
      },
    });
  }
}
