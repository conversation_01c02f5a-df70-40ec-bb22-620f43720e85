import { Component, OnInit, OnDestroy } from '@angular/core';
import * as fromRoot from '@app/store';
import { select, Store } from '@ngrx/store';
import { Observable, Subscription, timer, BehaviorSubject } from 'rxjs';
import { switchMap, take, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import * as fromList from '../../store/save';
import { AnuncioResponse } from '../../store/save';
import { MatDialog } from '@angular/material/dialog';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { AnuncioWsService } from '@app/services/anuncio-ws/anuncio-ws.service';

import { ImageViewerComponent } from '../../components/image-viewer/image-viewer.component';
import { AnuncioFormDialogComponent } from '../../components/anuncio-form-dialog/anuncio-form-dialog.component';

@Component({
  selector: 'app-anuncios-list',
  templateUrl: './anuncios-list.component.html',
  styleUrls: ['./anuncios-list.component.scss'],
})
export class AnunciosListComponent implements OnInit, OnDestroy {
  currentPage$ = this.store.pipe(select(fromList.getCurrentPage));
  totalPages$ = this.store.pipe(select(fromList.getTotalPages));
  totalElements$ = this.store.pipe(select(fromList.getTotalElements));
  anuncios$!: Observable<AnuncioResponse[] | null>;
  loading$!: Observable<boolean | null>;
  searchTerm: string = '';
  pageSize: number = 8; // Tamaño de página que coincide con el backend

  // Estado de carga y error con BehaviorSubject para evitar parpadeos
  private loadingSubject = new BehaviorSubject<boolean>(false);
  loading = this.loadingSubject.asObservable().pipe(
    // Usar debounceTime para evitar cambios rápidos de estado que causan parpadeo
    debounceTime(300),
    distinctUntilChanged()
  );

  private errorSubject = new BehaviorSubject<{
    error: boolean;
    message: string;
  }>({ error: false, message: '' });
  error = this.errorSubject.asObservable().pipe(
    // Usar debounceTime para evitar cambios rápidos de estado que causan parpadeo
    debounceTime(300),
    distinctUntilChanged(
      (prev, curr) => prev.error === curr.error && prev.message === curr.message
    )
  );

  // Indicador de si se está usando el respaldo HTTP
  usingHttpFallback: boolean = false;

  // Suscripciones para limpiar al destruir el componente
  private subscriptions: Subscription[] = [];

  // Bandera para evitar solicitudes duplicadas
  private cargaEnProgreso = false;

  pictureDefault: string =
    'https://firebasestorage.googleapis.com/v0/b/edificacion-app.appspot.com/o/image%2F1637099019171_O5986058_0.jpg?alt=media&token=0a146233-d63b-4702-b28d-6eaddf5e207a';

  constructor(
    private store: Store<fromRoot.State>,
    private dialog: MatDialog,
    private webSocketService: WebSocketService,
    private anuncioWsService: AnuncioWsService
  ) {
    this.loading$ = this.store.pipe(select(fromList.getLoading));
    this.anuncios$ = this.store.pipe(select(fromList.getAnuncios));
  }

  ngOnInit(): void {
    // Inicializar el servicio de anuncios WebSocket si no está inicializado
    if (!this.anuncioWsService.isInitialized()) {
      this.anuncioWsService.setupSubscriptions();
    }

    // Suscribirse al estado de conexión WebSocket
    this.subscriptions.push(
      this.webSocketService
        .getConnectionStatus()
        .pipe(
          // Usar debounceTime para evitar cambios rápidos de estado
          debounceTime(300),
          distinctUntilChanged()
        )
        .subscribe((connected) => {
          if (connected) {
            // Si la conexión WebSocket se establece, cargar anuncios
            if (!this.cargaEnProgreso) {
              // Obtener la página actual del store
              this.currentPage$.pipe(take(1)).subscribe((currentPage) => {
                // Cargar anuncios con la página actual o la primera página si no hay página actual
                this.cargarAnuncios(currentPage || 0);
              });
            }
          } else {
            // Si se pierde la conexión, mostrar un indicador
            this.errorSubject.next({
              error: true,
              message: 'Conexión perdida. Reconectando...',
            });
          }
        })
    );

    // Suscribirse a los anuncios para detectar cuando llegan
    this.subscriptions.push(
      this.anuncios$
        .pipe(
          // Usar debounceTime para evitar cambios rápidos de estado
          debounceTime(300),
          distinctUntilChanged((prev, curr) => {
            // Comparar longitudes para determinar si ha cambiado
            return (prev?.length || 0) === (curr?.length || 0);
          })
        )
        .subscribe((anuncios) => {
          if (anuncios && anuncios.length > 0) {
            // Si hay anuncios, ocultar cualquier mensaje de error
            this.errorSubject.next({ error: false, message: '' });
            this.loadingSubject.next(false);
          } else if (anuncios && anuncios.length === 0) {
            // Si no hay anuncios pero la respuesta es un array vacío (no null),
            // también ocultar el indicador de carga
            this.loadingSubject.next(false);
          }
        })
    );

    // Suscribirse a cambios en la página actual
    this.subscriptions.push(
      this.currentPage$.pipe(distinctUntilChanged()).subscribe((page) => {
        console.log('Página actual en el store:', page);
      })
    );

    // Cargar anuncios inicialmente
    // Obtener la página actual del store
    this.currentPage$.pipe(take(1)).subscribe((currentPage) => {
      // Cargar anuncios con la página actual o la primera página si no hay página actual
      this.cargarAnuncios(currentPage || 0);
    });
  }

  eliminarAnuncio(id: number): void {
    if (confirm('¿Está seguro que desea eliminar este anuncio?')) {
      this.store.dispatch(new fromList.Delete(id));
    }
  }

  openEditarDialog(anuncio: any): void {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    // Determinar el ancho del diálogo basado en el tamaño de la pantalla
    const screenWidth = window.innerWidth;
    const dialogWidth = screenWidth < 600 ? '95%' : '90%';
    const dialogMaxWidth = screenWidth < 600 ? '100vw' : '800px';

    const dialogRef = this.dialog.open(AnuncioFormDialogComponent, {
      width: dialogWidth,
      maxWidth: dialogMaxWidth,
      disableClose: false,
      autoFocus: true,
      restoreFocus: true,
      data: { anuncio },
      panelClass: [
        'modern-modal',
        'anuncio-edit-dialog',
        'responsive-dialog',
        isDarkTheme ? 'dark-theme' : '',
      ],
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Usar el método mejorado para cargar anuncios
        this.cargarAnuncios();
      }
    });
  }

  onPageChange(page: number, size?: number): void {
    // Mostrar indicador de carga inmediatamente
    this.loadingSubject.next(true);

    // Actualizar el tamaño de página si se proporciona
    if (size && size !== this.pageSize) {
      this.pageSize = size;
      console.log(`Cambiando tamaño de página a: ${size}`);
    }

    console.log(`Cambiando a página: ${page}`);

    // Actualizar el store con la nueva página actual
    // Obtener los valores actuales del store
    let totalElements = 0;
    let totalPages = 0;

    // Obtener los valores actuales usando pipe(take(1))
    this.totalElements$
      .pipe(take(1))
      .subscribe((total) => (totalElements = total || 0));
    this.totalPages$
      .pipe(take(1))
      .subscribe((total) => (totalPages = total || 0));

    // Actualizar el store con la nueva página actual
    this.store.dispatch(
      new fromList.ReadSuccess(
        [], // No modificar los anuncios actuales
        totalElements,
        totalPages,
        page // Actualizar la página actual
      )
    );

    // Usar el método mejorado para cargar anuncios con la página y tamaño especificados
    // Forzar la recarga para asegurarnos de que se actualice la lista
    this.cargarAnuncios(page, true);
  }

  openImageViewer(imageUrl: string, title: string): void {
    // en quien abra el diálogo:
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl, title },
      panelClass: 'custom-image-viewer-dialog',
      backdropClass: 'custom-image-viewer-backdrop',
      // opcional: ancho/alto
      maxWidth: '90vw',
      maxHeight: '90vh',
    });
  }

  /**
   * Formatea una fecha para mostrarla en formato legible
   * @param dateString Fecha en formato ISO o array
   * @returns Fecha formateada como DD/MM/YYYY
   */
  formatDate(dateString: string): string {
    if (!dateString) return 'No definida';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Fecha inválida';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error('Error al formatear fecha:', error);
      return 'Error en fecha';
    }
  }

  openNuevoAnuncioDialog(): void {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    // Determinar el ancho del diálogo basado en el tamaño de la pantalla
    const screenWidth = window.innerWidth;
    const dialogWidth = screenWidth < 600 ? '95%' : '90%';
    const dialogMaxWidth = screenWidth < 600 ? '100vw' : '800px';

    const dialogRef = this.dialog.open(AnuncioFormDialogComponent, {
      width: dialogWidth,
      maxWidth: dialogMaxWidth,
      disableClose: false,
      hasBackdrop: true,
      autoFocus: true,
      restoreFocus: true,
      panelClass: [
        'modern-modal',
        'anuncio-edit-dialog',
        'responsive-dialog',
        isDarkTheme ? 'dark-theme' : '',
      ],
    });

    // Antes de abrir el diálogo, asegurarse de que estamos suscritos al tópico de nuevos anuncios
    try {
      this.webSocketService.subscribeToDynamicTopic(
        '/topic/anuncios/new',
        'TOPIC'
      );
      console.log(
        'AnunciosListComponent: Suscrito explícitamente al tópico /topic/anuncios/new'
      );
    } catch (error) {
      console.error(
        'Error al suscribirse a tópico /topic/anuncios/new:',
        error
      );
    }

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // No recargar inmediatamente la lista, ya que el WebSocket se encargará de actualizar
        // la lista cuando reciba la notificación de nuevo anuncio
        console.log(
          'Anuncio creado exitosamente. Esperando notificación WebSocket...'
        );

        // Si estamos en una página diferente a la primera, volver a la primera página
        // para ver el nuevo anuncio cuando llegue
        this.currentPage$.pipe(take(1)).subscribe((currentPage) => {
          if (currentPage !== 0) {
            console.log(
              'Cambiando a la primera página para ver el nuevo anuncio'
            );
            this.cargarAnuncios(0);
          } else {
            // Si ya estamos en la primera página, forzar una recarga después de un breve retraso
            // para asegurarnos de que el anuncio se muestre
            setTimeout(() => {
              console.log('Forzando recarga de anuncios en la primera página');
              this.cargarAnuncios(0, true);
            }, 1000);
          }
        });
      }
    });
  }

  /**
   * Carga los anuncios usando exclusivamente WebSocket
   * @param page Número de página a cargar
   * @param forzar Si es true, fuerza la recarga incluso si ya hay una carga en progreso
   */
  cargarAnuncios(page: number = 0, forzar: boolean = false): void {
    // Evitar solicitudes duplicadas a menos que se fuerce la recarga
    if (this.cargaEnProgreso && !forzar) {
      console.log('Carga en progreso, ignorando solicitud para página:', page);
      return;
    }

    console.log(
      'Cargando anuncios para página:',
      page,
      forzar ? '(forzado)' : ''
    );

    this.cargaEnProgreso = true;
    this.loadingSubject.next(true);
    this.errorSubject.next({ error: false, message: '' });
    this.usingHttpFallback = false;

    // Verificar si el servicio de anuncios está inicializado
    if (!this.anuncioWsService.isInitialized()) {
      this.anuncioWsService.setupSubscriptions();
    }

    // Intentar conectar WebSocket si no está conectado
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();
    }

    // Asegurarse de que estamos suscritos al tópico de nuevos anuncios
    try {
      this.webSocketService.subscribeToDynamicTopic(
        '/topic/anuncios/new',
        'TOPIC'
      );
      console.log(
        'AnunciosListComponent: Suscrito explícitamente al tópico /topic/anuncios/new desde cargarAnuncios'
      );
    } catch (error) {
      console.error(
        'Error al suscribirse a tópico /topic/anuncios/new:',
        error
      );
    }

    // Solicitar todos los anuncios a través de WebSocket
    // Usar el tamaño de página dinámico y forzar la solicitud para asegurar que se actualice la lista
    // al cambiar de página
    this.anuncioWsService.requestAnuncios(page, this.pageSize, true);

    // Verificar si los anuncios se cargan correctamente después de un tiempo
    this.subscriptions.push(
      timer(5000)
        .pipe(
          // Aumentar el tiempo de espera a 5 segundos
          switchMap(() => this.anuncios$),
          take(1)
        )
        .subscribe({
          next: (anuncios) => {
            this.loadingSubject.next(false);
            this.cargaEnProgreso = false;

            // Solo mostrar mensaje de error si realmente no hay anuncios
            // y el WebSocket está conectado
            if (
              (!anuncios || anuncios.length === 0) &&
              this.webSocketService.isConnected()
            ) {
              console.log('No se recibieron anuncios para la página:', page);
              this.errorSubject.next({
                error: true,
                message: 'No hay anuncios disponibles para esta página.',
              });
            } else {
              console.log(
                `Recibidos ${anuncios?.length || 0} anuncios para la página:`,
                page
              );
            }
          },
          error: (err) => {
            console.error('Error al cargar anuncios:', err);
            this.loadingSubject.next(false);
            this.cargaEnProgreso = false;
            this.errorSubject.next({
              error: true,
              message:
                'Error al cargar anuncios. Por favor, intente nuevamente.',
            });
          },
        })
    );
  }

  /**
   * Recarga manualmente los anuncios
   */
  recargarAnuncios(): void {
    // Obtener la página actual
    this.currentPage$.pipe(take(1)).subscribe((page) => {
      this.cargarAnuncios(page || 0);
    });
  }

  /**
   * Limpia las suscripciones al destruir el componente
   */
  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
