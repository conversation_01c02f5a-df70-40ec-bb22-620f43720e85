import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CursosComponent } from './cursos.component';
import { CursoListComponent } from './pages/curso-list/curso-list.component';
import { CursoCreateComponent } from './pages/curso-create/curso-create.component';
import { CursoEditComponent } from './pages/curso-edit/curso-edit.component';
import { MisCursosComponent } from './pages/mis-cursos/mis-cursos.component';
import { CursoDetalleComponent } from './pages/curso-detalle/curso-detalle.component';
import { AuthGuard } from '@app/guards/auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: CursosComponent,
    children: [
      {
        path: '',
        component: CursoListComponent
      },
      {
        path: 'crear',
        component: CursoCreateComponent,
        canActivate: [AuthGuard]
      },
      {
        path: 'editar/:id',
        component: CursoEditComponent,
        canActivate: [AuthGuard]
      },
      {
        path: 'mis-cursos',
        component: MisCursosComponent,
        canActivate: [AuthGuard]
      },
      {
        path: 'detalle/:id',
        component: CursoDetalleComponent,
        canActivate: [AuthGuard]
      },
      {
        path: '**',
        redirectTo: '',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CursosRoutingModule { }
