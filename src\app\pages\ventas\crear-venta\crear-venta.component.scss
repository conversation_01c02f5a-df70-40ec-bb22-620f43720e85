/***************************************************
 * SECCIÓN PRINCIPAL DEL FORM
 ***************************************************/
 .form-section {
    max-width: 80%;
    margin: 3rem auto;
    padding: 0 1rem;
  }

  /***************************************************
   * CARDS
   ***************************************************/
  .main-card {
    margin-bottom: 3rem;   /* Separación entre tarjetas */
    position: relative;    /* Para el icono flotante */
    border-radius: 20px;   /* Borde redondeado en la tarjeta */
    overflow: visible;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
    border: 0.0625rem solid #dce3ec;

    /* Ajustar padding interno */
    ::ng-deep .mat-card-content {
      padding: 2rem !important;
    }
  }

  /***************************************************
   * ÍCONO FLOTANTE (CONTENEDOR CUADRADO)
   ***************************************************/
  .icon-container {
    position: absolute;
    top: -2rem;
    left: 0.935rem;
    width: 3.5rem;
    height: 3.5rem;

    background-color: #fff;
    border: 0.125rem solid #26AFE5; /* Borde #006a66 */
    border-radius: 0.5rem; /* Esquinas redondeadas */
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.15);

    display: flex;
    justify-content: center;
    align-items: center;
  }

  .icono-flotante {
    font-size: 1.5rem;
    color: #26AFE5; /* Color del ícono */
  }

  /***************************************************
   * TÍTULOS DE SECCIÓN
   ***************************************************/
  .section-title {
    /* Si quieres que sean negro, pónselo aquí.
       Pero si no, #006a66 para distinguir.
       Cambiar a #000 si deseas título negro.
    */
    color: #2E74BB;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0.5rem 0;
  }

  /***************************************************
   * BORDE Y LABEL DE LOS CAMPOS (#006a66)
   ***************************************************/
  /* 1) El contorno outline normal, SIEMPRE #006a66 */
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #26AFE5 !important;
    border-radius: 0.5rem;
    box-shadow: 0.5rem 0.5rem 0px 0px #F5F7FB;
    border-radius: 0.75rem;

  }
  /* 2) El "trazo grueso" (cuando enfoca) también #26AFE5 */
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #26AFE5 !important;
    box-shadow: 0.5rem 0.5rem 0px 0px #F5F7FB;
    border-radius: 0.75rem;

  }
  /* 3) Etiquetas (label) en negro */
  ::ng-deep .mat-form-field-label {
    color: #000 !important;
    font-weight: 500;
    transform: translateY(0) scale(1) !important;
    transition: transform 0.2s ease-in-out !important;
  }

  ::ng-deep .mat-form-field.mat-focused .mat-form-field-label,
  ::ng-deep .mat-form-field.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.28125em) scale(0.75) !important;
    color: #26AFE5 !important;
  }

  /* 4) Quitar cualquier hover/focus que cambie color a otro */
  ::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-hover:hover
    .mat-form-field-outline-thick {
    color: #26AFE5 !important;
  }
  .form-section mat-card{
    background-color:  #FFF !important;
  }

  ::ng-deep .mat-input-element {
    caret-color: #000; /* Asegúrate de que el cursor sea visible */
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #26AFE5; /* Color del borde cuando no está enfocado */
  }

  ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
    color: #26AFE5; /* Color del borde cuando está enfocado */
  }
  /***************************************************
   * TEXTO INTERNO DE LOS INPUTS (placeholder/typed)
   ***************************************************/
  /* Normalmente Angular Material deja el texto en negro,
     pero si necesitas forzar:
  */
  ::ng-deep .mat-input-element,
  ::ng-deep .mat-select-value-text {
    color: #000 !important; /* Texto real que escribe el usuario */
  }

  /***************************************************
   * QUITAR PADDINGS EXTRAS
   ***************************************************/
  ::ng-deep .mat-form-field-wrapper {
    padding: 0 !important;
  }
  ::ng-deep .mat-form-field-subscript-wrapper {
    margin: 0;
  }
  ::ng-deep .mat-form-field-flex {
    border-radius: 0.5rem;
  }

  /***************************************************
   * SEPARADOR (HR)
   ***************************************************/
  .title-separator {
    border: none;
    border-top: 0.0625rem solid #ccc;
    margin-bottom: 1rem;
  }

  /***************************************************
   * GRID 3x3 Y GRID 2x2
   ***************************************************/
  .grid-three-columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;

    @media screen and (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  .grid-two-columns {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
    width: 100%;
  }

  // Asegura una transición suave cuando aparecen/desaparecen los campos
  .datos-cliente-content {
    .mat-form-field {
      transition: all 0.3s ease;
    }
  }

  /***************************************************
   * MÓVILES A PORTAR
   ***************************************************/
  .moviles-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1rem 0;

    button[mat-stroked-button] {
      grid-column: 1;
      height: fit-content;
      margin-top: 1rem;
    }
  }

  /***************************************************
   * CHECKBOXES
   ***************************************************/
  .checkboxes-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.2rem 0;
  }

  /***************************************************
   * DATEPICKER
   ***************************************************/
  .datepicker-container {
    margin-top: 0.3rem;

    label {
      display: block;
      margin-bottom: 0.2rem;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }

  /***************************************************
   * BOTÓN DE REGISTRAR
   ***************************************************/
  .button-row {
    margin-top: 1rem;
    text-align: right;
  }
  .btn-registrar {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 600;
  }

  /***************************************************
   * FOOTER / CERRAR SESIÓN
   ***************************************************/
   .footer-section {
    max-width: 78%;
    margin: 0.5rem auto;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 1.5rem;
  }


  /***************************************************
   * FOOTER / CERRAR SESIÓN
   ***************************************************/
   .mat-icon{
    height: 1.5rem;
    width: 1.5rem;
   }



   .date-selector-field {
    width: 100%;

    ::ng-deep {
      .mat-form-field-flex {
        align-items: center;
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 0.5rem;
      }

      .date-selector-container {
        display: flex;
        gap: 10px;
        margin-top: 1rem;

        mat-select {
          flex: 1;
          width: 100%;

          .mat-select-trigger {
            display: flex;
            align-items: center;
            height: 100%;
          }

          .mat-select-value {
            color: #000;
            font-weight: 500;
          }

          .mat-select-arrow {
            color: #26AFE5;
          }
        }
      }

      .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      .mat-form-field-infix {
        padding: 0.5rem 0;
        border-top: 0;

      }

      .mat-form-field-outline {
        height: 3.5rem;
        border-radius: 0.5rem;
      }
    }
  }

  .header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: nowrap; /* Evita que los elementos se envuelvan */
  }

  .numero-agente-field {
    width: 200px;
    margin-left: 1rem;
    flex-shrink: 0; /* Evita que el campo se encoja */
  }

  /* Media queries para responsividad */
  @media screen and (max-width: 1024px) {
    .form-section {
      max-width: 98%;
      margin: 2rem auto;
      padding: 0 0.5rem;
    }

    .grid-three-columns {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 0.75rem;
    }

    .grid-two-columns {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 0.75rem;
    }

    .main-card {
      margin-bottom: 2rem;
    }

    .icon-container {
      top: -1.5rem;
      left: 0.75rem;
      width: 3rem;
      height: 3rem;
    }

    .icono-flotante {
      font-size: 1.25rem;
    }

    ::ng-deep .mat-card-content {
      padding: 1.25rem !important;
      overflow: hidden;
    }

    .section-title {
      font-size: 1.1rem;
    }

    .header-row {
      flex-direction: row; /* Mantiene la dirección en fila */
      align-items: center;
    }

    .numero-agente-field {
      width: 180px; /* Ligeramente más pequeño en tablets */
      margin-left: 1rem;
    }

    ::ng-deep .mat-form-field {
      width: 100%;
      min-width: 0;
    }

    ::ng-deep .mat-form-field-infix {
      width: 100% !important;
      min-width: 0 !important;
    }

    ::ng-deep .mat-form-field-wrapper {
      margin: 0;
      padding: 0 !important;
    }
  }

  @media screen and (max-width: 768px) {
    .form-section {
      max-width: 100%;
      margin: 1rem auto;
      padding: 0 0.25rem;
    }

    .grid-three-columns,
    .grid-two-columns {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .main-card {
      margin-bottom: 1rem;
      border-radius: 15px;
      overflow: hidden;
    }

    .icon-container {
      top: -1.25rem;
      left: 0.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }

    .icono-flotante {
      font-size: 1.1rem;
    }

    ::ng-deep .mat-card-content {
      padding: 1.25rem !important;
    }

    .section-title {
      font-size: 1rem;
      flex: 1; /* Permite que el título se ajuste */
      min-width: 0; /* Permite que el texto se ajuste */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    ::ng-deep .mat-form-field {
      width: 100%;
    }

    ::ng-deep .mat-form-field-infix {
      min-width: 100%;
      width: 100%;
    }

    .moviles-container {
      .movil-field {
        width: 100%;
      }
    }

    .button-row {
      text-align: center;
    }

    .btn-registrar {
      width: 100%;
      max-width: 300px;
    }

    .numero-agente-field {
      width: 150px; /* Más pequeño en móviles */
      margin-left: 0.5rem;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
      box-shadow: 0.25rem 0.25rem 0px 0px #F5F7FB;
    }
  }

  @media screen and (max-width: 480px) {
    .form-section {
      margin: 0.5rem auto;
      padding: 0 0.5rem;
    }

    .main-card {
      margin-bottom: 1rem;
      border-radius: 15px;
    }

    .icon-container {
      top: -1rem;
      left: 0.25rem;
      width: 2rem;
      height: 2rem;
    }

    .icono-flotante {
      font-size: 1rem;
    }

    ::ng-deep .mat-card-content {
      padding: 1rem !important;
    }

    .section-title {
      font-size: 0.9rem;
    }

    .title-separator {
      margin-bottom: 0.75rem;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
      border-radius: 0.5rem;
    }

    ::ng-deep .mat-form-field-label {
      font-size: 0.875rem;
    }

    ::ng-deep .mat-input-element {
      font-size: 0.875rem !important;
    }

    .checkboxes-row {
      font-size: 0.875rem;
    }

    .datepicker-container {
      label {
        font-size: 0.75rem;
      }
    }

    .btn-add-movil {
      width: 100%;
      margin-top: 0.5rem;
    }

    .footer-section {
      text-align: center;
      padding: 0 0.5rem;
    }

    .footer-section button {
      width: 100%;
      max-width: 300px;
    }
  }

  /* Ajustes para el alineamiento vertical de las etiquetas */
  ::ng-deep .mat-form-field {
    .mat-form-field-wrapper {
      padding-bottom: 0;
      margin: 0;
    }

    .mat-form-field-flex {
      background-color: #fff;
      border-radius: 0.5rem;
      min-height: 48px;
      align-items: center;
    }

    .mat-form-field-infix {
      padding: 0.5rem 0;
      border-top: 0;
      width: 100% !important;
      min-width: 0 !important;
      display: flex;
      align-items: center;
    }

    .mat-form-field-label {
      transform: translateY(0) scale(1) !important;
      transition: transform 0.2s ease-in-out !important;
    }

    &.mat-focused .mat-form-field-label,
    &.mat-form-field-should-float .mat-form-field-label {
      transform: translateY(-1.28125em) scale(0.75) !important;
      color: #26AFE5 !important;
    }

    /* Ajuste específico para campos con error */
    &.mat-form-field-invalid {
      .mat-form-field-label {
        transform: translateY(-1.28125em) scale(0.75) !important;
        margin-top: 0.5em;
      }
    }

    /* Ajuste para el espacio del error */
    .mat-form-field-subscript-wrapper {
      margin-top: 0.25em;
      min-height: 0;
    }

    /* Ajuste para el contenedor del input */
    .mat-form-field-infix {
      padding: 0.5rem 0;
      border-top: 0;
      min-height: 48px;
      display: flex;
      align-items: center;
    }

    /* Ajuste para el contenedor del label */
    .mat-form-field-label-wrapper {
      top: 0;
      padding-top: 0;
    }
  }

  /* Ajuste específico para campos con select */
  ::ng-deep .mat-select-trigger {
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  /* Ajuste para el contenedor del input en campos con error */
  ::ng-deep .mat-form-field-invalid {
    .mat-form-field-infix {
      min-height: 48px;
    }
  }

  /* Ajuste para mantener consistencia en todos los campos */
  ::ng-deep .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      height: 48px;
    }

    .mat-form-field-infix {
      padding: 0.5rem 0;
      border-top: 0;
    }
  }

  /* Ajuste para el espacio del error */
  ::ng-deep .mat-form-field-subscript-wrapper {
    margin-top: 0.25em;
    min-height: 0;
  }

  /* Ajuste para mantener la consistencia en campos con iconos */
  ::ng-deep .mat-form-field-suffix {
    top: 0;
    bottom: 0;
    margin: auto;
    height: 100%;
    display: flex;
    align-items: center;
  }

  /* Mejoras en la accesibilidad */
  ::ng-deep .mat-form-field-label {
    color: rgba(0, 0, 0, 0.87) !important;
  }

  ::ng-deep .mat-form-field.mat-focused {
    .mat-form-field-label {
      color: #26AFE5 !important;
    }
  }

  /* Ajustes para el datepicker */
  ::ng-deep .mat-datepicker-toggle {
    color: #26AFE5;
  }

  ::ng-deep .mat-datepicker-content {
    .mat-calendar {
      width: 100%;
      max-width: 300px;
    }
  }

  /* Mejoras en los botones */
  .btn-registrar {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
  }

  /* Ajustes para los checkboxes */
  ::ng-deep .mat-checkbox {
    .mat-checkbox-layout {
      margin-bottom: 0;
    }
    .mat-checkbox-label {
      line-height: 1;
      min-height: auto;
    }
  }

  /* Ajuste para contenedores de inputs */
  .datos-cliente-content,
  .datos-servicio-content,
  .datos-informacion-content {
    width: 100%;
    overflow: hidden;
  }

  /* Estilos específicos para la tarjeta de información */
  .datos-informacion {
    .datos-informacion-content {
      .info-section {
        margin-bottom: 0.5rem;

        /* Ajuste específico para los íconos en los campos */
        .mat-form-field {
          ::ng-deep .mat-form-field-suffix {
            position: relative;
            top: 0;
            bottom: 0;
            margin: auto;
            height: 100%;
            display: flex;
            align-items: center;

            .mat-icon {
              position: relative;
              top: 0;
              margin: auto;
              height: 24px;
              width: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .observation-field {
        width: 100%;
        margin-bottom: 0.75rem;

        textarea {
          min-height: 60px;
          resize: vertical;
        }

        ::ng-deep .mat-form-field-suffix {
          position: relative;
          top: 0;
          bottom: 0;
          margin: auto;
          height: 100%;
          display: flex;
          align-items: center;

          .mat-icon {
            position: relative;
            top: 0;
            margin: auto;
            height: 24px;
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #26AFE5;
          }
        }
      }

      .subsection-title {
        color: #2E74BB;
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0.25rem 0;
      }

      .checkbox-container {
        display: flex;
        flex-direction: column;
        gap: 0.35rem;
        padding: 0;
        margin-bottom: 0.5rem;

        .custom-checkbox {
          display: flex;
          align-items: center;
          background-color: #f8f9fa;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          margin: 0;
          height: 32px;

          ::ng-deep .mat-checkbox-layout {
            margin-bottom: 0;
          }

          ::ng-deep .mat-checkbox-inner-container {
            height: 16px;
            width: 16px;
            margin: 2px 8px 2px 2px;
          }

          .checkbox-label {
            margin-left: 0.25rem;
            font-size: 0.85rem;
            color: #495057;
            line-height: 1;
          }

          &.confirmation-checkbox {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;

            .checkbox-label {
              color: #495057;
              font-weight: 500;
            }
          }
        }
      }

      .button-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 0.75rem;

        .btn-registrar {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1.25rem;
          height: 36px;
          min-height: 36px;

          .mat-icon {
            font-size: 1.1rem;
            height: 1.1rem;
            width: 1.1rem;
          }
        }
      }
    }
  }

  /* Media queries para la tarjeta de información */
  @media screen and (max-width: 768px) {
    .datos-informacion {
      .datos-informacion-content {
        .info-grid {
          grid-template-columns: 1fr;
          gap: 0.5rem;
          margin-bottom: 0.5rem;
        }

        .observation-field {
          margin-bottom: 0.5rem;
        }

        .checkbox-container {
          gap: 0.25rem;

          .custom-checkbox {
            padding: 0.2rem 0.4rem;
            height: 28px;

            .checkbox-label {
              font-size: 0.8rem;
            }
          }
        }

        .button-container {
          margin-top: 0.5rem;
        }
      }
    }
  }

  /* Estilos para el tema oscuro */
  :host-context(body.dark-theme) {
    .crear-venta-container, .form-section {
      background-color: var(--background-dark) !important;

      h1, h2, h3, h4, h5, h6 {
        color: #ffffff !important;
      }

      .section-title {
        color: #ffffff !important;
        background-color: #1e4976 !important;
        padding: 8px 12px !important;
        border-radius: 4px !important;
      }

      .mat-card {
        background-color: var(--sidenav-dark) !important;
        color: #ffffff !important;
      }

      .mat-card-title, .mat-card-subtitle, .mat-card-content {
        color: #ffffff !important;
      }

      /* Estilos para los campos de formulario */
      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
        color: rgba(255, 255, 255, 0.3) !important;
        box-shadow: none !important;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
        color: #1e4976 !important;
        box-shadow: none !important;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
        color: #1e4976 !important;
      }

      ::ng-deep .mat-input-element, ::ng-deep .mat-select-value-text {
        color: #ffffff !important;
      }

      ::ng-deep .mat-select-arrow {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      /* Estilos para los radio buttons */
      .mat-radio-button .mat-radio-label-content {
        color: #ffffff !important;
      }

      .mat-radio-outer-circle {
        border-color: rgba(255, 255, 255, 0.5) !important;
      }

      .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
        border-color: #1e4976 !important;
      }

      .mat-radio-button.mat-accent .mat-radio-inner-circle {
        background-color: #1e4976 !important;
      }

      /* Estilos para los contenedores */
      .direccion-container, .datos-promocion-container, .datos-cliente-container {
        background-color: var(--sidenav-dark) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }

      /* Estilos para los botones */
      .add-direccion-button-container button,
      .footer-section button {
        color: #ffffff !important;

        &.mat-primary {
          background-color: #1e4976 !important;
        }

        &.mat-warn {
          background-color: #c62828 !important;
        }

        mat-icon {
          color: #ffffff !important;
        }
      }

      /* Estilos para los datepickers */
      .mat-datepicker-toggle {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      input[type="date"], input[type="time"] {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;

        &::-webkit-calendar-picker-indicator {
          filter: invert(1) !important;
        }
      }

      /* Estilos para los selectores */
      ::ng-deep .mat-select-panel {
        background-color: var(--sidenav-dark) !important;

        .mat-option {
          color: #ffffff !important;

          &:hover:not(.mat-option-disabled) {
            background-color: rgba(255, 255, 255, 0.1) !important;
          }

          &.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {
            background-color: rgba(30, 73, 118, 0.5) !important;

            &:hover {
              background-color: rgba(30, 73, 118, 0.7) !important;
            }
          }
        }
      }

      /* Estilos para los checkboxes */
      .mat-checkbox-label {
        color: #ffffff !important;
      }

      .mat-checkbox-frame {
        border-color: rgba(255, 255, 255, 0.5) !important;
      }

      .mat-checkbox-checked .mat-checkbox-background {
        background-color: #1e4976 !important;
      }
    }
  }

  @media screen and (max-width: 480px) {
    .datos-informacion {
      .datos-informacion-content {
        .subsection-title {
          font-size: 0.9rem;
          margin: 0.35rem 0;
        }

        .observation-field textarea {
          min-height: 50px;
        }

        .checkbox-container {
          gap: 0.35rem;
        }
      }
    }
  }

  /* Ajustes específicos para los íconos en los campos de información */
  .datos-informacion {
    .datos-informacion-content {
      .info-section {
        .mat-form-field {
          ::ng-deep .mat-form-field-suffix {
            top: 0;
            display: flex;
            align-items: center;
            height: 100%;

            .mat-icon {
              margin: 0;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }

  /* Estilos específicos para los selectores */
  ::ng-deep .mat-select-panel {
    background: white;
    border-radius: 8px !important;
    margin-top: 4px;

    .mat-option {
      height: 48px;
      line-height: 48px;
      padding: 0 16px;
      font-size: 14px;

      &:hover:not(.mat-option-disabled) {
        background: rgba(38, 175, 229, 0.04) !important;
      }

      &.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {
        background: rgba(38, 175, 229, 0.12);

        &:hover {
          background: rgba(38, 175, 229, 0.16) !important;
        }
      }
    }
  }

  ::ng-deep .mat-select-arrow {
    color: #26AFE5;
  }

  ::ng-deep .mat-form-field.mat-focused {
    .mat-select-arrow {
      color: #26AFE5;
    }
  }

  /* Ajustes para el campo de permanencia */
  .mat-form-field {
    &.permanencia-field {
      .mat-select-value {
        font-weight: 500;
      }
    }
  }

  /* Ajustes para el datepicker de permanencia */
  .datepicker-container {
    margin-top: 8px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    label {
      color: #495057;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .mat-datepicker-toggle {
      color: #26AFE5;
    }
  }

  /* Ajuste adicional para el alineamiento del texto del select */
  ::ng-deep .mat-select-value {
    padding-top: 2px;
  }


  .grid-single-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.datos-promocion-content .mat-form-field {
    width: 100%;
}

textarea.mat-input-element {
    resize: vertical;
    min-height: 8rem;
}
.grid-doble-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}
.grid-triple-column {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.direcciones-content {
  .direccion-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    .full-width {
      flex: 1;
    }
  }
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.tipo-cliente-selector {
    margin-bottom: 20px;

    mat-card {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
}

.radio-group-container {
    display: flex;
    align-items: center;
    padding: 10px;

    .radio-group-label {
        font-size: 16px;
        font-weight: 500;
        margin-right: 20px;
        color: #333;
    }
}

.radio-group {
    display: flex;
    gap: 20px;

    mat-radio-button {
        .radio-content {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
                color: #1976d2;
            }

            span {
                font-size: 14px;
            }
        }
    }
}

::ng-deep {
    .mat-radio-button.mat-radio-checked .mat-radio-outer-circle {
        border-color: #1976d2;
    }

    .mat-radio-button .mat-radio-inner-circle {
        background-color: #1976d2;
    }
}
 