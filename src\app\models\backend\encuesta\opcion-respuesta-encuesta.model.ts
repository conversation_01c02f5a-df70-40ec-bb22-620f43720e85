/**
 * Modelo que representa una opción de respuesta para una pregunta de encuesta
 */
export interface OpcionRespuestaEncuesta {
  id: number;
  texto: string;
  orden: number;
  valor?: number;
  preguntaId: number;
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
  
  // Estadísticas
  cantidadRespuestas?: number;
  porcentajeRespuestas?: number;
}
