import { createReducer, on } from '@ngrx/store';
import { UserStatus } from '@app/models/backend/user/user-status.model';
import * as UserActions from './user.actions';

export interface UserStatusState {
  usersStatus: UserStatus[];
  loading: boolean;
  error: string | null;
}

export const initialState: UserStatusState = {
  usersStatus: [],
  loading: false,
  error: null
};

export const userStatusReducer = createReducer(
  initialState,
  
  on(UserActions.requestUsersStatusWs, state => ({
    ...state,
    loading: true
  })),
  
  on(UserActions.usersStatusUpdatedWs, (state, { usersStatus }) => ({
    ...state,
    usersStatus,
    loading: false
  })),
  
  on(UserActions.updateUserActivity, state => ({
    ...state,
    loading: true
  }))
);
