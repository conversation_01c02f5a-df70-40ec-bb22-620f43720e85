import { Injectable } from '@angular/core';
import { UserStatusService } from './user-ws/user-status.service';
import { WebSocketService } from './websocket/WebSocketService';

@Injectable({
  providedIn: 'root'
})
export class UserStatusLoggerService {
  private static connectionLogShown = false;
  private static lastOnlineCount = 0;
  private initialized = false;

  constructor(
    private userStatusService: UserStatusService,
    private webSocketService: WebSocketService
  ) {}

  /**
   * Inicializa el servicio de logs de estado de usuarios
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    // Suscribirse a los cambios en el estado de todos los usuarios
    this.userStatusService.getUsersStatus().subscribe(users => {
      // Reducir logs - solo mostrar una vez por sesión o cuando cambia el número de usuarios
      const onlineUsers = users.filter(user => user.online);
      const onlineCount = onlineUsers.length;

      // Mostrar la lista de usuarios conectados de manera más eficiente
      // Solo mostrar información detallada cuando cambia el número de usuarios
      const lastCount = UserStatusLoggerService.lastOnlineCount || 0;

      if (lastCount !== onlineCount) {
        // Actualizar el contador
        UserStatusLoggerService.lastOnlineCount = onlineCount;
      }

      // No necesitamos actualizar variables de estado ya que siempre mostramos los logs
    });

    // Suscribirse a los eventos de conexión/desconexión del WebSocket
    this.webSocketService.getConnectionStatus().subscribe(connected => {
      if (connected) {
        // Reducir logs - solo mostrar una vez por sesión
        if (!UserStatusLoggerService.connectionLogShown) {
          UserStatusLoggerService.connectionLogShown = true;
        }
      } else {
        // Resetear los flags de logs para que se muestren nuevamente al reconectar
        UserStatusLoggerService.connectionLogShown = false;
      }
    });

    this.initialized = true;
  }

  /**
   * Resetea los flags de logs
   */
  resetLogs(): void {
    UserStatusLoggerService.connectionLogShown = false;
  }
}
