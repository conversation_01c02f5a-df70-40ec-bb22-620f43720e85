import { Component, OnInit, OnDestroy } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import * as UsersActions from '@app/services/user/user.actions';
import * as WebSocketActions from '@app/services/websocket/websocket.actions';
import * as fromUser from '@app/store/user';
import { Router } from '@angular/router';
import { UserStatusService } from '@app/services/user-ws/user-status.service';

@Component({
  selector: 'app-user-status',
  templateUrl: './user-status.component.html'
})
export class UserStatusComponent implements OnInit, OnDestroy {
  isOnline: boolean = false;
  private subscription = new Subscription();

  constructor(
    private store: Store,
    private router: Router,
    private userStatusService: UserStatusService
  ) {}

  ngOnInit(): void {
    // Verificar si estamos en la página de login
    if (this.router.url.includes('/auth/login')) {
      console.log('UserStatusComponent: En página de login, no se inicializará estado de usuario');
      return;
    }

    // Suscribirse al estado del usuario actual desde el servicio
    this.subscription.add(
      this.userStatusService.getCurrentUserStatus().subscribe(
        (online: boolean) => {
          this.isOnline = online;

          // Actualizar el estado en el store y localStorage para mantener compatibilidad
          const status = online ? 'ONLINE' : 'OFFLINE';
          this.store.dispatch(new fromUser.UpdateUserStatus(status));
          localStorage.setItem('userStatus', status);
        }
      )
    );

    // Actualizar actividad del usuario cada 5 minutos (solo si no estamos en login)
    const activityInterval = setInterval(() => {
      // No actualizar actividad si estamos en la página de login
      if (!this.router.url.includes('/auth/login')) {
        // Actualizar actividad a través del servicio
        this.userStatusService.updateCurrentUserStatus(true);

        // También mantener la compatibilidad con el store
        this.store.dispatch(UsersActions.updateUserActivity());
      }
    }, 300000); // 5 minutos

    // Limpiar intervalo al destruir componente
    this.subscription.add(() => clearInterval(activityInterval));
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
