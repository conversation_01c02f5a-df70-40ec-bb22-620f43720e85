import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';
import { Notificacion } from '@app/models/notificacion.model';
import { Router } from '@angular/router';

@Component({
  selector: 'app-notificaciones',
  templateUrl: './notificaciones.component.html',
})
export class NotificacionesComponent implements OnInit, OnDestroy {
  notificaciones: Notificacion[] = [];
  contadorNoLeidas = 0;
  cargando = false;
  private subscripciones: Subscription[] = [];

  constructor(
    private notificacionesService: NotificacionesWsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    console.log('Inicializando NotificacionesComponent');

    // Limpiar cualquier suscripción existente
    this.subscripciones.forEach((sub) => sub.unsubscribe());
    this.subscripciones = [];

    // Verificar si el servicio ya está inicializado
    if (!this.notificacionesService.isInitialized()) {
      console.log(
        'Inicializando servicio de notificaciones desde el componente'
      );
      // Configurar suscripciones WebSocket
      this.notificacionesService.setupSubscriptions();
    } else {
      console.log('Servicio de notificaciones ya inicializado');
    }

    // Establecer el estado de carga inicial
    this.cargando = true;

    // Suscribirse a las notificaciones
    this.subscripciones.push(
      this.notificacionesService
        .getNotificaciones()
        .subscribe((notificaciones) => {
          console.log(
            'NotificacionesComponent: Recibidas notificaciones:',
            notificaciones.length
          );
          this.notificaciones = notificaciones;
        })
    );

    // Suscribirse al contador de no leídas
    this.subscripciones.push(
      this.notificacionesService.getContadorNoLeidas().subscribe((contador) => {
        console.log('NotificacionesComponent: Contador actualizado:', contador);
        this.contadorNoLeidas = contador;
      })
    );

    // Suscribirse al estado de carga
    this.subscripciones.push(
      this.notificacionesService.getCargando().subscribe((cargando) => {
        // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
          console.log('NotificacionesComponent: Estado de carga:', cargando);
          this.cargando = cargando;
        }, 0);
      })
    );

    // Solicitar notificaciones después de un pequeño retraso
    setTimeout(() => {
      if (this.notificaciones.length === 0 && !this.cargando) {
        console.log('NotificacionesComponent: Solicitando notificaciones...');
        this.solicitarNotificaciones();
      }
    }, 500);
  }

  ngOnDestroy(): void {
    console.log('Destruyendo NotificacionesComponent');
    // Cancelar todas las suscripciones
    this.subscripciones.forEach((sub) => sub.unsubscribe());

    // No destruir el servicio aquí, ya que es un singleton y puede ser usado por otros componentes
    // Solo lo haríamos si estamos seguros de que no se necesitará más
    // this.notificacionesService.destruir();
  }

  /**
   * Solicita notificaciones al servidor usando WebSocket
   */
  solicitarNotificaciones(): void {
    this.notificacionesService.solicitarNotificaciones();
  }

  /**
   * Carga las notificaciones desde el servidor (método legacy)
   * @deprecated Use solicitarNotificaciones() instead
   */
  cargarNotificaciones(): void {
    this.solicitarNotificaciones();
  }

  /**
   * Marca una notificación como leída
   */
  marcarComoLeida(notificacion: Notificacion, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    this.notificacionesService.marcarComoLeida(notificacion.id);
  }

  /**
   * Marca todas las notificaciones como leídas
   */
  marcarTodasComoLeidas(): void {
    this.notificacionesService.marcarTodasComoLeidas();
  }

  /**
   * Maneja el clic en una notificación
   */
  manejarClicNotificacion(
    notificacion: Notificacion,
    event?: MouseEvent
  ): void {
    if (event) {
      event.stopPropagation();
    }

    // Marcar como leída
    this.marcarComoLeida(notificacion);

    // Navegar al enlace si existe
    if (notificacion.enlace) {
      this.router.navigateByUrl(notificacion.enlace);
    }
  }

  /**
   * Obtiene el icono según el tipo de notificación
   */
  obtenerIconoNotificacion(tipo: string): string {
    switch (tipo) {
      case 'INFO':
        return 'info';
      case 'EXITO':
        return 'check_circle';
      case 'ADVERTENCIA':
        return 'warning';
      case 'ERROR':
        return 'error';
      case 'SISTEMA':
        return 'notifications';
      default:
        return 'notifications';
    }
  }

  /**
   * Formatea la fecha para mostrarla de forma amigable
   */
  formatearFecha(fecha: string): string {
    if (!fecha) {
      return '';
    }

    const fechaObj = new Date(fecha);
    const ahora = new Date();
    const diferenciaMilisegundos = ahora.getTime() - fechaObj.getTime();
    const diferenciaMinutos = Math.floor(diferenciaMilisegundos / (1000 * 60));
    const diferenciaHoras = Math.floor(diferenciaMinutos / 60);
    const diferenciaDias = Math.floor(diferenciaHoras / 24);

    if (diferenciaMinutos < 1) {
      return 'Ahora mismo';
    } else if (diferenciaMinutos < 60) {
      return `Hace ${diferenciaMinutos} ${
        diferenciaMinutos === 1 ? 'minuto' : 'minutos'
      }`;
    } else if (diferenciaHoras < 24) {
      return `Hace ${diferenciaHoras} ${
        diferenciaHoras === 1 ? 'hora' : 'horas'
      }`;
    } else if (diferenciaDias < 7) {
      return `Hace ${diferenciaDias} ${diferenciaDias === 1 ? 'día' : 'días'}`;
    } else {
      return fechaObj.toLocaleDateString('es-ES');
    }
  }
}
