<section class="users-container" fxLayout="column" fxLayoutAlign="start stretch">
  <!-- Spinner Overlay: se muestra mientras loading sea true -->
  <div class="spinner-overlay" *ngIf="(loading$ | async) || exportLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Contenedor principal -->
  <div [ngClass]="{'dark-theme-card': isDarkTheme}" style="border-radius: 4px; background-color: white;">
    <!-- T<PERSON><PERSON><PERSON>, subtítulo y botones en la misma fila -->
    <div style="padding: 16px 16px 8px 16px;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div>
          <h2 [ngClass]="{'dark-theme-text': isDarkTheme}" style="margin: 0; font-size: 1.5rem; font-weight: 500; color: #1976d2;">Listar por Equipo</h2>
          <p [ngClass]="{'dark-theme-text': isDarkTheme}" style="margin: 4px 0 0 0; font-size: 0.9rem; color: #6c757d;">Listado de clientes asignados a asesores de tu equipo</p>
          <!-- Mensaje de acceso restringido -->
          <div *ngIf="(user$ | async)?.role === 'ADMIN' || (user$ | async)?.role === 'BACKOFFICE'" style="margin-top: 4px;">
            <p [ngClass]="{'dark-theme-text': isDarkTheme}" style="font-size: 0.8rem; color: #f44336; margin: 0;">Acceso exclusivo para Coordinadores.</p>
          </div>
        </div>
        <div class="data-table-actions">
          <button mat-raised-button color="accent" type="button" (click)="descargarExcelHoy()" class="action-button excel-button">
            <mat-icon>file_download</mat-icon>
            <span>Excel</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Filtros compactos en una sola fila -->
    <div style="padding: 0 16px 16px 16px;">
      <form [formGroup]="filterForm" (ngSubmit)="aplicarFiltros()" class="compact-form">
        <div class="search-fields-container">
          <mat-form-field appearance="outline" class="compact-field">
            <mat-label>DNI</mat-label>
            <input matInput formControlName="dni" placeholder="Ingrese DNI" id="dni" />
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="compact-field">
            <mat-label>Nombre</mat-label>
            <input matInput formControlName="nombre" placeholder="Ingrese nombre" id="nombre" />
            <mat-icon matSuffix>person</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="compact-field">
            <mat-label>Móvil</mat-label>
            <input matInput formControlName="movil" placeholder="Ingrese móvil" id="movil" />
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="compact-field">
            <mat-label>Fecha</mat-label>
            <input matInput type="date" formControlName="fecha" id="fecha" />
          </mat-form-field>
        </div>

        <div class="filter-buttons">
          <button mat-raised-button color="primary" type="submit" class="action-button filter-button">
            <mat-icon>search</mat-icon>
            <span>Filtrar</span>
          </button>
          <button mat-stroked-button color="warn" type="button" (click)="limpiarFiltros()" class="action-button clear-button">
            <mat-icon>clear</mat-icon>
            <span>Limpiar</span>
          </button>
        </div>
      </form>
    </div>

    <!-- Indicador de carga -->
    <div *ngIf="loading$ | async" style="display: flex; justify-content: center; padding: 20px;">
      <mat-spinner diameter="40"></mat-spinner>
    </div>

    <!-- Tabla personalizada con columna de acciones -->
    <div style="overflow-x: auto;" *ngIf="!(loading$ | async) && (asesoresConClientes$ | async) as data">
      <table mat-table [dataSource]="data.clientes" [ngClass]="{'dark-theme-table': isDarkTheme}" class="mat-elevation-z1" style="width: 100%;">
        <!-- Columna DNI -->
        <ng-container matColumnDef="dni">
          <th mat-header-cell *matHeaderCellDef [ngClass]="{'dark-theme-header': isDarkTheme}" style="background-color: #f5f5f5;">DNI</th>
          <td mat-cell *matCellDef="let cliente" [ngClass]="{'dark-theme-cell': isDarkTheme}">{{ cliente.dni }}</td>
        </ng-container>

        <!-- Columna Nombre -->
        <ng-container matColumnDef="nombres">
          <th mat-header-cell *matHeaderCellDef [ngClass]="{'dark-theme-header': isDarkTheme}" style="background-color: #f5f5f5;">Nombre</th>
          <td mat-cell *matCellDef="let cliente" [ngClass]="{'dark-theme-cell': isDarkTheme}">{{ cliente.asesor }}</td>
        </ng-container>

     <!-- Columna Fecha Ingreso -->
<ng-container matColumnDef="fechaIngresado">
  <th mat-header-cell *matHeaderCellDef [ngClass]="{'dark-theme-header': isDarkTheme}" style="background-color: #f5f5f5;">Fecha Ingreso</th>
  <td mat-cell *matCellDef="let cliente" [ngClass]="{'dark-theme-cell': isDarkTheme}">
    <ng-container *ngIf="isArray(cliente.fechaIngresado); else stringDate">
      {{ formatDateArrayWithTime(cliente.fechaIngresado) }}
    </ng-container>
    <ng-template #stringDate>
      {{ cliente.fechaIngresado | date:'dd/MM/yyyy HH:mm' }}
    </ng-template>
  </td>
</ng-container>

        <!-- Columna Número Móvil -->
        <ng-container matColumnDef="numeroMovil">
          <th mat-header-cell *matHeaderCellDef [ngClass]="{'dark-theme-header': isDarkTheme}" style="background-color: #f5f5f5;">Número Móvil</th>
          <td mat-cell *matCellDef="let cliente" [ngClass]="{'dark-theme-cell': isDarkTheme}">{{ cliente.numeroMovil }}</td>
        </ng-container>

        <!-- Columna Acciones -->
        <ng-container matColumnDef="accion">
          <th mat-header-cell *matHeaderCellDef [ngClass]="{'dark-theme-header': isDarkTheme}" style="background-color: #f5f5f5;">Acciones</th>
          <td mat-cell *matCellDef="let cliente" [ngClass]="{'dark-theme-cell': isDarkTheme}">
            <div style="display: flex; gap: 4px; justify-content: center;">
              <button mat-icon-button [style.color]="isDarkTheme ? 'white' : '#3f51b5'" (click)="verDetalleCliente(cliente)" matTooltip="Ver detalles">
                <mat-icon style="font-size: 18px;">visibility</mat-icon>
              </button>
              <button mat-icon-button [style.color]="isDarkTheme ? 'white' : '#2e7d32'" (click)="descargarExcelCliente(cliente.numeroMovil)" matTooltip="Descargar Excel">
                <mat-icon style="font-size: 18px;">file_download</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="['dni', 'nombres', 'fechaIngresado', 'numeroMovil', 'accion']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['dni', 'nombres', 'fechaIngresado', 'numeroMovil', 'accion'];"></tr>
      </table>

      <!-- Paginador mejorado con mat-paginator -->
      <mat-paginator
        *ngIf="clientesPage$ | async as pageData"
        [ngClass]="{'dark-theme-paginator': isDarkTheme}"
        [length]="pageData.totalItems || pageData.totalPages * size"
        [pageSize]="size"
        [pageIndex]="pageData.currentPage"
        [pageSizeOptions]="[5, 10, 20, 50]"
        [showFirstLastButtons]="true"
        (page)="onPageChange($event)">
      </mat-paginator>
    </div>
  </div>

  <div *ngIf="!(loading$ | async) && !(asesoresConClientes$ | async)?.clientes?.length" class="empty-state" [ngClass]="{'dark-theme-card': isDarkTheme}" style="text-align: center; padding: 20px; background-color: white; margin-top: 10px;">
    <span style="font-size: 48px; color: #ccc;">📁</span>
    <p [ngClass]="{'dark-theme-text': isDarkTheme}">No se encontraron clientes</p>
  </div>
</section>

<!-- Spinner Overlay para exportación -->
<div class="spinner-overlay" *ngIf="exportLoading">
  <mat-spinner diameter="40"></mat-spinner>
</div>

<!-- Modal de Detalle -->
<div class="modal-overlay" [class.show]="modalVisible" (click)="closeModal()">lta lo
  <div class="modal-content" [ngClass]="{'dark-theme-modal': isDarkTheme}" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        <ng-container *ngIf="selectedCliente$ | async as cliente">
          {{ formatDateArrayWithTime(cliente.fechaCreacion) }}
        </ng-container>
      </h2>
      <div class="header-actions">
        <!-- Botón Excel -->
        <button
          mat-icon-button
          *ngIf="selectedCliente$ | async as cliente"
          (click)="descargarExcelCliente(cliente.movilContacto)"
          matTooltip="Descargar Excel"
        >
          <mat-icon>table_chart</mat-icon>
        </button>
        <!-- Botón PDF -->
        <button
          mat-icon-button
          (click)="downloadOrPrint('clienteDetalle')"
          matTooltip="Descargar PDF"
        >
          <mat-icon>picture_as_pdf</mat-icon>
        </button>
        <button mat-icon-button (click)="closeModal()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Contenido del modal con id para la impresión -->
    <div class="modal-body">
      <div id="clienteDetalle">
      <ng-container
        *ngIf="selectedCliente$ | async as clienteDetalle; else loadingOrError"
      >
        <!-- Sección 1: DATOS DEL CLIENTE -->
        <div class="form-header">DATOS DEL CLIENTE</div>
        <div class="form-section">
          <div class="form-row" *ngIf="clienteDetalle.id">
            <div class="form-label">ID:</div>
            <div class="form-value">
              {{ clienteDetalle.id }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">NOMBRES Y APELLIDOS:</div>
            <div class="form-value">
              {{ clienteDetalle.nombresApellidos }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">NIF/NIE:</div>
            <div class="form-value">
              {{ clienteDetalle.nifNie }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">NACIONALIDAD:</div>
            <div class="form-value">
              {{ clienteDetalle.nacionalidad }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">FECHA DE NACIMIENTO:</div>
            <div class="form-value">
              {{ clienteDetalle.fechaNacimiento | date : "dd/MM/yyyy" }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">FECHA DE CREACIÓN:</div>
            <div class="form-value">
              {{ formatDateArrayWithTime(clienteDetalle.fechaCreacion) }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">CORREO ELECTRÓNICO:</div>
            <div class="form-value">
              {{ clienteDetalle.correoElectronico }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">MÓVIL DE CONTACTO:</div>
            <div class="form-value">
              {{ clienteDetalle.movilContacto }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">FIJO COMPAÑÍA:</div>
            <div class="form-value">
              {{ clienteDetalle.fijoCompania }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">DIRECCIÓN:</div>
            <div class="form-value">
              {{ clienteDetalle.direccion }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">CÓDIGO POSTAL:</div>
            <div class="form-value">
              {{ clienteDetalle.codigoPostal }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">PROVINCIA:</div>
            <div class="form-value">
              {{ clienteDetalle.provincia }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">DISTRITO:</div>
            <div class="form-value">
              {{ clienteDetalle.distrito }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">CIUDAD:</div>
            <div class="form-value">
              {{ clienteDetalle.ciudad }}
            </div>
          </div>
          <div class="form-row" *ngIf="clienteDetalle.numeroAgente">
            <div class="form-label">NÚMERO DE AGENTE:</div>
            <div class="form-value">
              {{ clienteDetalle.numeroAgente || "No especificado" }}
            </div>
          </div>
          <div class="form-row" *ngIf="clienteDetalle.usuario">
            <div class="form-label">ASESOR:</div>
            <div class="form-value">
              {{ clienteDetalle.usuario.nombre }}
              {{ clienteDetalle.usuario.apellido }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">SUPERVISOR:</div>
            <div class="form-value">
              <ng-container
                *ngIf="
                  clienteDetalle.usuario &&
                    clienteDetalle.usuario.coordinador;
                  else noCoordinador
                "
              >
                {{ clienteDetalle.usuario.coordinador.nombre }}
                {{ clienteDetalle.usuario.coordinador.apellido }}
              </ng-container>
              <ng-template #noCoordinador> No asignado </ng-template>
            </div>
          </div>
        </div>

        <!-- Sección 2: DATOS DE LA PROMOCIÓN -->
        <div class="form-header">DATOS DE LA PROMOCIÓN</div>
        <div class="form-section">
          <div class="form-row">
            <div class="form-label">CAMPAÑA:</div>
            <div class="form-value">
              {{ clienteDetalle.campania }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">NÚMEROS MÓVILES:</div>
            <div class="form-value">
              {{ clienteDetalle.numeroMoviles }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">PLAN ACTUAL:</div>
            <div class="form-value">
              {{ clienteDetalle.planActual }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">TIPO DE PLAN:</div>
            <div class="form-value">
              {{ clienteDetalle.tipoPlan }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">TECNOLOGÍA:</div>
            <div class="form-value">
              {{ clienteDetalle.tipoTecnologia }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">TIPO DE VELOCIDAD:</div>
            <div class="form-value">
              {{ clienteDetalle.velocidad }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">FÚTBOL:</div>
            <div class="form-value">
              <div class="status-inline" [ngClass]="clienteDetalle.futbol === 'SI' ? 'status-yes' : (clienteDetalle.futbol === 'NO' ? 'status-no' : 'status-undefined')">
                <mat-icon *ngIf="clienteDetalle.futbol === 'SI'">done</mat-icon>
                <mat-icon *ngIf="clienteDetalle.futbol === 'NO'">close</mat-icon>
                <mat-icon *ngIf="clienteDetalle.futbol !== 'SI' && clienteDetalle.futbol !== 'NO'">remove</mat-icon>
                <span>{{ clienteDetalle.futbol || "No especificado" }}</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">ICC:</div>
            <div class="form-value">
              {{ clienteDetalle.icc }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">CUENTA BANCARIA:</div>
            <div class="form-value">
              {{ clienteDetalle.cuentaBancaria }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">PERMANENCIA:</div>
            <div class="form-value">
              {{ clienteDetalle.permanencia }}
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">MÓVILES A PORTAR:</div>
            <div class="form-value">
              <div
                *ngIf="
                  clienteDetalle.movilesAPortar &&
                  clienteDetalle.movilesAPortar.length > 0
                "
              >
                <div *ngFor="let movil of clienteDetalle.movilesAPortar">
                  {{ movil }}
                </div>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">OBSERVACIÓN:</div>
            <div class="form-value">
              {{ clienteDetalle.observacion }}
            </div>
          </div>
        </div>

        <!-- Sección 3: AUTORIZACIONES Y CONFIRMACIONES -->
        <div class="form-header">AUTORIZACIONES Y CONFIRMACIONES</div>
        <div class="form-section">
          <div class="form-row">
            <div class="form-label">AUTORIZA SEGUROS:</div>
            <div class="form-value">
              <div class="status-inline" [ngClass]="clienteDetalle.autorizaSeguros ? 'status-yes' : 'status-no'">
                <mat-icon *ngIf="clienteDetalle.autorizaSeguros">done</mat-icon>
                <mat-icon *ngIf="!clienteDetalle.autorizaSeguros">close</mat-icon>
                <span>{{ clienteDetalle.autorizaSeguros ? "Sí" : "No" }}</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">AUTORIZA ENERGÍAS:</div>
            <div class="form-value">
              <div class="status-inline" [ngClass]="clienteDetalle.autorizaEnergias ? 'status-yes' : 'status-no'">
                <mat-icon *ngIf="clienteDetalle.autorizaEnergias">done</mat-icon>
                <mat-icon *ngIf="!clienteDetalle.autorizaEnergias">close</mat-icon>
                <span>{{ clienteDetalle.autorizaEnergias ? "Sí" : "No" }}</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">VENTA REALIZADA:</div>
            <div class="form-value">
              <div class="status-inline" [ngClass]="clienteDetalle.ventaRealizada ? 'status-yes' : 'status-no'">
                <mat-icon *ngIf="clienteDetalle.ventaRealizada">done</mat-icon>
                <mat-icon *ngIf="!clienteDetalle.ventaRealizada">close</mat-icon>
                <span>{{ clienteDetalle.ventaRealizada ? "Sí" : "No" }}</span>
              </div>
            </div>
          </div>
          <div class="form-row" *ngIf="clienteDetalle.observacionEstado">
            <div class="form-label">OBSERVACIÓN DE ESTADO:</div>
            <div class="form-value">
              {{ clienteDetalle.observacionEstado }}
            </div>
          </div>
        </div>
      </ng-container>
      </div>

      <ng-template #loadingOrError>
        <ng-container *ngIf="loading$ | async; else errorTemplate">
          <div style="display: flex; flex-direction: column; align-items: center; padding: 30px;">
            <mat-spinner diameter="40"></mat-spinner>
            <p class="loading-text" style="margin-top: 20px;">Cargando detalles del cliente...</p>
          </div>
        </ng-container>
        <ng-template #errorTemplate>
          <div class="error-container">
            <mat-icon class="error-icon">error_outline</mat-icon>
            <p class="error-text">Error al cargar los datos del cliente</p>
            <button
              mat-raised-button
              color="primary"
              (click)="closeModal()"
              class="mt-3"
            >
              Cerrar
            </button>
          </div>
        </ng-template>
      </ng-template>
    </div>

    <!-- Footer del modal -->
    <div class="modal-footer">
      <!-- Botón para cerrar el modal -->
      <button
        mat-raised-button
        color="warn"
        type="button"
        class="action-button close-button"
        (click)="closeModal()"
      >
        <mat-icon>close</mat-icon>
        <span>Cerrar</span>
      </button>
    </div>
  </div>
</div>
