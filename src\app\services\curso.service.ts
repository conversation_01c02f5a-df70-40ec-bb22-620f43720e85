import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '@src/environments/environment';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { GenericResponse } from '@app/models/backend/generic-response';
import { Curso, CursoCreateRequest, CursoUpdateRequest } from '@app/models/backend/curso/curso.model';
import { finalize, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CursoService {
  private baseUrl = environment.url + 'api/cursos';
  private cursosSubject = new BehaviorSubject<Curso[]>([]);
  public cursos$ = this.cursosSubject.asObservable();

  constructor(
    private http: HttpClient,
    private storage: AngularFireStorage
  ) {}

  /**
   * Obtiene todos los cursos
   */
  getAllCursos(): Observable<GenericResponse<Curso[]>> {
    return this.http.get<GenericResponse<Curso[]>>(this.baseUrl).pipe(
      map(response => {
        if (response.data) {
          this.cursosSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene un curso por su ID
   */
  getCursoById(id: number): Observable<GenericResponse<Curso>> {
    return this.http.get<GenericResponse<Curso>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea un nuevo curso
   * Si se proporciona un archivo de video, primero lo sube a Firebase
   */
  createCurso(curso: CursoCreateRequest, videoFile?: File): Observable<GenericResponse<Curso>> {
    // Si hay un archivo de video, primero lo subimos a Firebase
    if (videoFile) {
      return this.uploadVideoAndCreateCurso(curso, videoFile);
    }

    // Si no hay archivo, simplemente creamos el curso
    return this.http.post<GenericResponse<Curso>>(this.baseUrl, curso);
  }

  /**
   * Actualiza un curso existente
   * Si se proporciona un archivo de video, primero lo sube a Firebase
   */
  updateCurso(id: number, curso: CursoUpdateRequest, videoFile?: File): Observable<GenericResponse<Curso>> {
    // Si hay un archivo de video, primero lo subimos a Firebase
    if (videoFile) {
      return this.uploadVideoAndUpdateCurso(id, curso, videoFile);
    }

    // Si no hay archivo, simplemente actualizamos el curso
    return this.http.put<GenericResponse<Curso>>(`${this.baseUrl}/${id}`, curso);
  }

  /**
   * Elimina un curso
   */
  deleteCurso(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene cursos por IDs
   * @param ids Array de IDs de cursos
   */
  getCursosByIds(ids: number[]): Observable<GenericResponse<Curso[]>> {
    console.log('CursoService.getCursosByIds - Solicitando cursos con IDs:', ids);

    // Validar que ids sea un array válido
    if (!Array.isArray(ids) || ids.length === 0) {
      console.warn('CursoService.getCursosByIds - Array de IDs vacío o inválido');
      // Devolver una respuesta simulada para evitar errores
      return new Observable(observer => {
        observer.next({
          rpta: 1,
          msg: 'No hay cursos para mostrar',
          data: []
        });
        observer.complete();
      });
    }

    return this.http.post<GenericResponse<Curso[]>>(`${this.baseUrl}/byIds`, { ids })
      .pipe(
        map(response => {
          console.log('CursoService.getCursosByIds - Respuesta recibida:', response);
          return response;
        })
      );
  }

  /**
   * Sube un video a Firebase y luego crea el curso
   */
  private uploadVideoAndCreateCurso(curso: CursoCreateRequest, videoFile: File): Observable<GenericResponse<Curso>> {
    return new Observable<GenericResponse<Curso>>(observer => {
      // Crear un nombre único para el archivo
      const filePath = `cursos/videos/${new Date().getTime()}_${videoFile.name}`;
      const fileRef = this.storage.ref(filePath);
      const task = this.storage.upload(filePath, videoFile);

      // Esperar a que se complete la subida y obtener la URL
      task.snapshotChanges().pipe(
        finalize(() => {
          fileRef.getDownloadURL().subscribe(
            downloadURL => {
              // Asignar la URL del video al curso
              curso.videoUrl = downloadURL;

              // Crear el curso con la URL del video
              this.http.post<GenericResponse<Curso>>(this.baseUrl, curso).subscribe(
                response => {
                  observer.next(response);
                  observer.complete();
                },
                error => {
                  observer.error(error);
                }
              );
            },
            error => {
              observer.error({
                rpta: 0,
                msg: 'Error al obtener la URL del video',
                data: null,
                errors: error
              });
            }
          );
        })
      ).subscribe(
        () => {
          // Mostrar progreso si es necesario
        },
        error => {
          observer.error({
            rpta: 0,
            msg: 'Error al subir el video a Firebase',
            data: null,
            errors: error
          });
        }
      );
    });
  }

  /**
   * Sube un video a Firebase y luego actualiza el curso
   */
  private uploadVideoAndUpdateCurso(id: number, curso: CursoUpdateRequest, videoFile: File): Observable<GenericResponse<Curso>> {
    return new Observable<GenericResponse<Curso>>(observer => {
      // Crear un nombre único para el archivo
      const filePath = `cursos/videos/${new Date().getTime()}_${videoFile.name}`;
      const fileRef = this.storage.ref(filePath);
      const task = this.storage.upload(filePath, videoFile);

      // Esperar a que se complete la subida y obtener la URL
      task.snapshotChanges().pipe(
        finalize(() => {
          fileRef.getDownloadURL().subscribe(
            downloadURL => {
              // Asignar la URL del video al curso
              curso.videoUrl = downloadURL;

              // Actualizar el curso con la URL del video
              this.http.put<GenericResponse<Curso>>(`${this.baseUrl}/${id}`, curso).subscribe(
                response => {
                  observer.next(response);
                  observer.complete();
                },
                error => {
                  observer.error(error);
                }
              );
            },
            error => {
              observer.error({
                rpta: 0,
                msg: 'Error al obtener la URL del video',
                data: null,
                errors: error
              });
            }
          );
        })
      ).subscribe(
        () => {
          // Mostrar progreso si es necesario
        },
        error => {
          observer.error({
            rpta: 0,
            msg: 'Error al subir el video a Firebase',
            data: null,
            errors: error
          });
        }
      );
    });
  }
}
