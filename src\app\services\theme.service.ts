import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private darkModeSubject = new BehaviorSubject<boolean>(false);
  darkMode$: Observable<boolean> = this.darkModeSubject.asObservable();

  constructor() {
    // Check if theme preference is stored in localStorage
    const storedTheme = localStorage.getItem('darkMode');
    if (storedTheme) {
      this.darkModeSubject.next(storedTheme === 'true');
      this.applyTheme(storedTheme === 'true');
    } else {
      // Default to light theme if no preference is stored
      this.darkModeSubject.next(false);
      this.applyTheme(false);
    }
  }

  toggleDarkMode(): void {
    const newValue = !this.darkModeSubject.value;
    this.darkModeSubject.next(newValue);
    localStorage.setItem('darkMode', newValue.toString());
    this.applyTheme(newValue);
  }

  private applyTheme(isDarkMode: boolean): void {
    // Aplicar el tema directamente sin transiciones para evitar saltos
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
      // Agregar la clase 'dark' para que Tailwind detecte el modo oscuro
      document.documentElement.classList.add('dark');
    } else {
      document.body.classList.remove('dark-theme');
      // Quitar la clase 'dark' para que Tailwind detecte el modo claro
      document.documentElement.classList.remove('dark');
    }
  }
}
