<div class="p-4">
  <h2 mat-dialog-title class="text-xl font-bold mb-4 dark:text-white">
    {{ data.pregunta ? "Editar pregunta" : "Crear nueva pregunta" }}
  </h2>

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <mat-dialog-content class="mat-typography">
      <!-- Enunciado -->
      <mat-form-field appearance="outline" class="w-full mb-4">
        <mat-label>Enunciado</mat-label>
        <textarea
          matInput
          formControlName="enunciado"
          placeholder="Enunciado de la pregunta"
          rows="2"
          class="dark:bg-gray-700 dark:text-white"
        ></textarea>
        <mat-error *ngIf="form.get('enunciado')?.hasError('required')">
          El enunciado es obligatorio
        </mat-error>
      </mat-form-field>

      <!-- Descripción -->
      <mat-form-field appearance="outline" class="w-full mb-4">
        <mat-label>Descripción (opcional)</mat-label>
        <textarea
          matInput
          formControlName="descripcion"
          placeholder="Descripción o instrucciones adicionales"
          rows="2"
          class="dark:bg-gray-700 dark:text-white"
        ></textarea>
      </mat-form-field>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <!-- Tipo de pregunta -->
        <mat-form-field appearance="outline">
          <mat-label>Tipo de pregunta</mat-label>
          <mat-select
            formControlName="tipo"
            class="dark:bg-gray-700 dark:text-white"
          >
            <mat-option [value]="tipoPregunta.OPCION_MULTIPLE"
              >Opción múltiple (una respuesta)</mat-option
            >
            <mat-option [value]="tipoPregunta.SELECCION_MULTIPLE"
              >Selección múltiple (varias respuestas)</mat-option
            >
            <mat-option [value]="tipoPregunta.ESCALA_LIKERT"
              >Escala de valoración</mat-option
            >
            <mat-option [value]="tipoPregunta.TEXTO_LIBRE"
              >Texto libre</mat-option
            >
            <mat-option [value]="tipoPregunta.FECHA">Fecha</mat-option>
            <mat-option [value]="tipoPregunta.NUMERO">Número</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Orden -->
        <mat-form-field appearance="outline">
          <mat-label>Orden</mat-label>
          <input
            matInput
            type="number"
            formControlName="orden"
            class="dark:bg-gray-700 dark:text-white"
          />
        </mat-form-field>

        <!-- Es obligatoria -->
        <div class="flex items-center">
          <mat-slide-toggle
            formControlName="esObligatoria"
            color="primary"
            class="dark:text-white"
          >
            Pregunta obligatoria
          </mat-slide-toggle>
        </div>
      </div>

      <!-- Opciones de respuesta (solo para tipos que lo requieren) -->
      <div
        *ngIf="
          form.get('tipo')?.value === tipoPregunta.OPCION_MULTIPLE ||
          form.get('tipo')?.value === tipoPregunta.SELECCION_MULTIPLE ||
          form.get('tipo')?.value === tipoPregunta.ESCALA_LIKERT
        "
      >
        <div class="flex justify-between items-center mb-2">
          <h5 class="text-sm font-medium dark:text-white">
            Opciones de respuesta
          </h5>
          <button
            type="button"
            mat-mini-fab
            color="primary"
            (click)="addOpcion()"
            matTooltip="Agregar opción"
          >
            <mat-icon>add</mat-icon>
          </button>
        </div>

        <div formArrayName="opciones" class="space-y-3">
          <div
            *ngFor="let opcionGroup of opcionesForm.controls; let i = index"
            [formGroupName]="i"
            class="flex items-center gap-2 bg-gray-50 dark:bg-gray-700 p-2 rounded border border-gray-100 dark:border-gray-600"
          >
            <!-- Texto de opción -->
            <mat-form-field appearance="outline" class="flex-1 !m-0">
              <mat-label>Texto</mat-label>
              <input
                matInput
                formControlName="texto"
                placeholder="Texto de la opción"
                class="dark:bg-gray-700 dark:text-white"
              />
              <mat-error *ngIf="opcionGroup.get('texto')?.hasError('required')">
                El texto es obligatorio
              </mat-error>
            </mat-form-field>

            <!-- Valor (para escala Likert) -->
            <mat-form-field
              appearance="outline"
              *ngIf="form.get('tipo')?.value === tipoPregunta.ESCALA_LIKERT"
              class="w-24 !m-0"
            >
              <mat-label>Valor</mat-label>
              <input
                matInput
                type="number"
                formControlName="valor"
                placeholder="Valor"
                class="dark:bg-gray-700 dark:text-white"
              />
            </mat-form-field>

            <!-- Botón eliminar -->
            <button
              type="button"
              mat-icon-button
              color="warn"
              (click)="removeOpcion(i)"
              matTooltip="Eliminar opción"
              [disabled]="opcionesForm.length <= 2"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions
      align="end"
      class="flex justify-end gap-4 pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <button
        type="button"
        mat-button
        (click)="onCancel()"
        [disabled]="submitting"
        class="bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Cancelar
      </button>
      <button
        type="submit"
        mat-raised-button
        color="primary"
        [disabled]="submitting"
        class="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <mat-spinner
          *ngIf="submitting"
          diameter="20"
          class="mr-2"
        ></mat-spinner>
        <span>{{ data.pregunta ? "Actualizar" : "Crear" }}</span>
      </button>
    </mat-dialog-actions>
  </form>
</div>
