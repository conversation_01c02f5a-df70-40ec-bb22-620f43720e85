import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CursoService } from '@app/services/curso.service';
import { CursoCreateRequest } from '@app/models/backend/curso/curso.model';
import { ThemeService } from '@app/services/theme.service';
import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { Store, select } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';
import { NotificationService } from '@app/services/notification/notification.service';

@Component({
  selector: 'app-curso-create',
  templateUrl: './curso-create.component.html'
})
export class CursoCreateComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  loading: boolean = false;
  error: string | null = null;
  isDarkTheme: boolean = false;
  videoFile: File | null = null;
  videoPreviewUrl: string | null = null;
  user: User | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private cursoService: CursoService,
    private router: Router,
    private dialog: MatDialog,
    private themeService: ThemeService,
    private notification: NotificationService,
    private store: Store<fromRoot.State>
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.checkDarkTheme();
    this.getCurrentUser();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.group({
      nombre: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', [Validators.maxLength(500)]],
      fechaInicio: [null],
      fechaFin: [null],
      videoUrl: ['']
    });
  }

  private checkDarkTheme(): void {
    this.themeService.darkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isDark: boolean) => {
        this.isDarkTheme = isDark;
      });
  }

  private getCurrentUser(): void {
    this.store.pipe(
      select(fromUser.getUser),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
    });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;

    const cursoData: CursoCreateRequest = {
      nombre: this.form.value.nombre,
      descripcion: this.form.value.descripcion,
      fechaInicio: this.form.value.fechaInicio ? this.formatDate(this.form.value.fechaInicio) : null,
      fechaFin: this.form.value.fechaFin ? this.formatDate(this.form.value.fechaFin) : null,
      usuarioId: this.user?.id || null,
      videoUrl: this.form.value.videoUrl
    };

    this.cursoService.createCurso(cursoData, this.videoFile || undefined)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Curso creado exitosamente');
            this.router.navigate(['/cursos']);
          } else {
            this.error = response.msg || 'Error al crear el curso';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al crear el curso. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al crear curso:', error);
        }
      });
  }

  onCancel(): void {
    this.router.navigate(['/cursos']);
  }

  openFilesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe({
      next: (url) => {
        if (url) {
          this.form.patchValue({
            videoUrl: url
          });
        }
      }
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Verificar que sea un archivo de video
      if (!file.type.startsWith('video/')) {
        this.notification.error('Por favor, seleccione un archivo de video válido');
        return;
      }

      this.videoFile = file;

      // Crear una URL para previsualizar el video
      this.videoPreviewUrl = URL.createObjectURL(file);

      // Limpiar la URL del video existente
      this.form.patchValue({
        videoUrl: ''
      });
    }
  }

  removeSelectedVideo(): void {
    this.videoFile = null;
    this.videoPreviewUrl = null;
  }

  private formatDate(date: string | Date): string {
    try {
      // Si date ya es un string (formato YYYY-MM-DD), añadimos la parte de tiempo
      if (typeof date === 'string') {
        // Añadir la parte de tiempo para que sea un LocalDateTime válido
        return `${date}T00:00:00`;
      }

      // Si date es un objeto Date, lo convertimos a ISO string con formato completo
      if (date instanceof Date) {
        // Asegurarnos de que la fecha sea válida
        if (isNaN(date.getTime())) {
          throw new Error('Fecha inválida');
        }

        // Formatear como YYYY-MM-DDT00:00:00 (formato que espera LocalDateTime en el backend)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}T00:00:00`;
      }

      throw new Error('Tipo de fecha no soportado');
    } catch (error) {
      console.error('Error al formatear fecha:', error, date);
      return '';
    }
  }
}
