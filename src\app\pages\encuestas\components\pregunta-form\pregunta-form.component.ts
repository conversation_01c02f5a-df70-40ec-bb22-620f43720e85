import { Component, OnInit, Inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PreguntaEncuestaService } from '@app/services/pregunta-encuesta.service';
import { NotificationService } from '@app/services/notification/notification.service';
import {
  PreguntaEncuesta,
  TipoPregunta,
} from '@app/models/backend/encuesta/pregunta-encuesta.model';
import { PreguntaEncuestaCreateRequest } from '@app/models/backend/encuesta/encuesta.model';

@Component({
  selector: 'app-pregunta-form',
  templateUrl: './pregunta-form.component.html',
  styleUrls: ['./pregunta-form.component.scss'],
})
export class PreguntaFormComponent implements OnInit {
  form: FormGroup;
  opcionesForm: FormArray;

  // Enums
  tipoPregunta = TipoPregunta;

  // Estado
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private preguntaService: PreguntaEncuestaService,
    private notificationService: NotificationService,
    private dialogRef: MatDialogRef<PreguntaFormComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { encuestaId: number; pregunta?: PreguntaEncuesta }
  ) {}

  ngOnInit(): void {
    this.initForm();

    if (this.data.pregunta) {
      this.loadPreguntaData();
    }
  }

  private initForm(): void {
    this.form = this.fb.group({
      enunciado: ['', [Validators.required]],
      descripcion: [''],
      tipo: [TipoPregunta.OPCION_MULTIPLE, [Validators.required]],
      esObligatoria: [true],
      orden: [1],
      opciones: this.fb.array([]),
    });

    this.opcionesForm = this.form.get('opciones') as FormArray;

    // Añadir opciones por defecto según el tipo de pregunta
    this.form.get('tipo')?.valueChanges.subscribe((tipo) => {
      this.resetOpciones();

      if (
        tipo === TipoPregunta.OPCION_MULTIPLE ||
        tipo === TipoPregunta.SELECCION_MULTIPLE
      ) {
        this.addOpcion();
        this.addOpcion();
      } else if (tipo === TipoPregunta.ESCALA_LIKERT) {
        // Añadir opciones para escala Likert (1-5)
        for (let i = 1; i <= 5; i++) {
          this.addOpcion(`${i}`, i);
        }
      }
    });

    // Inicializar con opciones por defecto
    this.addOpcion();
    this.addOpcion();
  }

  private loadPreguntaData(): void {
    const pregunta = this.data.pregunta;

    if (!pregunta) {
      return;
    }

    this.form.patchValue({
      enunciado: pregunta.enunciado,
      descripcion: pregunta.descripcion,
      tipo: pregunta.tipo,
      esObligatoria: pregunta.esObligatoria,
      orden: pregunta.orden,
    });

    // Cargar opciones si existen
    if (pregunta.opciones && pregunta.opciones.length > 0) {
      this.resetOpciones();

      pregunta.opciones.forEach((opcion) => {
        this.opcionesForm.push(
          this.fb.group({
            id: [opcion.id],
            texto: [opcion.texto, [Validators.required]],
            orden: [opcion.orden],
            valor: [opcion.valor],
          })
        );
      });
    }
  }

  resetOpciones(): void {
    while (this.opcionesForm.length > 0) {
      this.opcionesForm.removeAt(0);
    }
  }

  addOpcion(texto: string = '', valor: number | null = null): void {
    this.opcionesForm.push(
      this.fb.group({
        id: [null],
        texto: [texto, [Validators.required]],
        orden: [this.opcionesForm.length + 1],
        valor: [valor],
      })
    );
  }

  removeOpcion(index: number): void {
    this.opcionesForm.removeAt(index);

    // Actualizar orden de las opciones restantes
    for (let i = 0; i < this.opcionesForm.length; i++) {
      const ordenControl = this.opcionesForm.at(i).get('orden');
      if (ordenControl) {
        ordenControl.setValue(i + 1);
      }
    }
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.notificationService.error(
        'Por favor, complete todos los campos requeridos'
      );
      return;
    }

    this.submitting = true;

    const formValue = this.form.value;
    const preguntaData: PreguntaEncuestaCreateRequest = {
      ...formValue,
      encuestaId: this.data.encuestaId,
    };

    if (this.data.pregunta) {
      // Actualizar pregunta existente
      this.preguntaService
        .update(this.data.pregunta.id, preguntaData)
        .subscribe({
          next: (response) => {
            this.notificationService.success(
              'Pregunta actualizada correctamente'
            );
            this.dialogRef.close(response.data);
          },
          error: (error) => {
            console.error('Error al actualizar pregunta:', error);
            this.notificationService.error('Error al actualizar pregunta');
            this.submitting = false;
          },
        });
    } else {
      // Crear nueva pregunta
      this.preguntaService.create(preguntaData).subscribe({
        next: (response) => {
          this.notificationService.success('Pregunta creada correctamente');
          this.dialogRef.close(response.data);
        },
        error: (error) => {
          console.error('Error al crear pregunta:', error);
          this.notificationService.error('Error al crear pregunta');
          this.submitting = false;
        },
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
