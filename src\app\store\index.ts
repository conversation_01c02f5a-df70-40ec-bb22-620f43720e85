import { ActionReducerMap } from '@ngrx/store';
import * as fromUser from './user';
import * as fromUserStatus from '../services/user/user.reducer';
import { WebSocketEffects } from '../services/websocket/websocket.effects';

export interface State {
  user: fromUser.UserState;
  userStatus: fromUserStatus.UserStatusState;
}

export const reducers: ActionReducerMap<State> = {
  user: fromUser.reducer,
  userStatus: fromUserStatus.userStatusReducer,
};

export const effects = [
  fromUser.UserEffects,
  WebSocketEffects,
  
]
