import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-inactivity-dialog',
  template: `
    <h2 mat-dialog-title>¿Estás ahí?</h2>
    <mat-dialog-content>
      <p>Tu sesión se cerrará en {{ counter }} segundos.</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button color="primary" (click)="stayActive()">Estoy aquí</button>
    </mat-dialog-actions>
  `
})
export class InactivityDialogComponent {
  counter = 60;
  interval: any;

  constructor(private dialogRef: MatDialogRef<InactivityDialogComponent>) {
    this.startCountdown();
  }

  startCountdown(): void {
    this.interval = setInterval(() => {
      this.counter--;
      if (this.counter <= 0) {
        clearInterval(this.interval);
        this.dialogRef.close(false); // ⛔ usuario no respondió
      }
    }, 1000);
  }

  stayActive(): void {
    clearInterval(this.interval);
    this.dialogRef.close(true); // ✅ usuario respondió
  }
}
