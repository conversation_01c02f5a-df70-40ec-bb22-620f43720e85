import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend';
import {
  RespuestaUsuario,
  RespuestaUsuarioCreateRequest,
  DetalleRespuestaUsuarioCreateRequest
} from '@app/models/backend/curso/respuesta-usuario.model';

@Injectable({
  providedIn: 'root'
})
export class RespuestaUsuarioService {
  private baseUrl = environment.url + 'api/respuestas-usuario';
  private respuestasUsuarioSubject = new BehaviorSubject<RespuestaUsuario[]>([]);
  public respuestasUsuario$ = this.respuestasUsuarioSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Inicia un nuevo intento de cuestionario
   */
  iniciarCuestionario(data: RespuestaUsuarioCreateRequest): Observable<GenericResponse<RespuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaUsuario>>(`${this.baseUrl}/iniciar`, data);
  }

  /**
   * Responde una pregunta del cuestionario
   */
  responderPregunta(respuestaUsuarioId: number, data: DetalleRespuestaUsuarioCreateRequest): Observable<GenericResponse<RespuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaUsuario>>(`${this.baseUrl}/${respuestaUsuarioId}/responder`, data);
  }

  /**
   * Finaliza un intento de cuestionario
   */
  finalizarCuestionario(respuestaUsuarioId: number): Observable<GenericResponse<RespuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaUsuario>>(`${this.baseUrl}/${respuestaUsuarioId}/finalizar`, {});
  }

  /**
   * Obtiene un intento de cuestionario por su ID
   */
  getRespuestaUsuarioById(id: number): Observable<GenericResponse<RespuestaUsuario>> {
    return this.http.get<GenericResponse<RespuestaUsuario>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene los intentos de un usuario en un cuestionario
   */
  getRespuestaUsuarioByUsuarioAndCuestionario(usuarioId: number, cuestionarioId: number): Observable<GenericResponse<RespuestaUsuario[]>> {
    return this.http.get<GenericResponse<RespuestaUsuario[]>>(`${this.baseUrl}/usuario/${usuarioId}/cuestionario/${cuestionarioId}`);
  }

  /**
   * Obtiene el mejor intento de un usuario en un cuestionario
   */
  getMejorIntentoByUsuarioAndCuestionario(usuarioId: number, cuestionarioId: number): Observable<GenericResponse<RespuestaUsuario>> {
    return this.http.get<GenericResponse<RespuestaUsuario>>(`${this.baseUrl}/usuario/${usuarioId}/cuestionario/${cuestionarioId}/mejor`);
  }

  /**
   * Obtiene un resumen de los cuestionarios de un usuario en un curso
   */
  getResumenCuestionariosByCursoAndUsuario(cursoId: number, usuarioId: number): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/curso/${cursoId}/usuario/${usuarioId}/resumen`);
  }

  /**
   * Verifica si un usuario puede intentar un cuestionario
   */
  puedeIntentarCuestionario(usuarioId: number, cuestionarioId: number): Observable<GenericResponse<boolean>> {
    return this.http.get<GenericResponse<boolean>>(`${this.baseUrl}/usuario/${usuarioId}/cuestionario/${cuestionarioId}/puede-intentar`);
  }
}
