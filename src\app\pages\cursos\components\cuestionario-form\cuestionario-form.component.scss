/* Estilos específicos para componentes Angular Material */
:host ::ng-deep {
  /* Ajustes para los campos de formulario */
  .mat-form-field-appearance-outline .mat-form-field-outline {
    @apply dark:opacity-50;
  }

  .mat-form-field-label {
    @apply dark:text-gray-300;
  }

  /* Ajustes para los botones de acción */
  .mat-mini-fab {
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
  }

  /* Ajustes para los checkboxes */
  .mat-checkbox-frame {
    @apply dark:border-gray-500;
  }

  /* Ajustes para los spinners */
  .mat-progress-spinner circle {
    @apply stroke-blue-500;
  }

  /* Ajustes para los select */
  .mat-select-panel {
    @apply dark:bg-gray-800 dark:text-white;
  }

  .mat-option {
    @apply dark:hover:bg-gray-700;
  }
}

/* Ajustes para el modo oscuro en los inputs */
input.dark\:bg-gray-700,
textarea.dark\:bg-gray-700,
.mat-select.dark\:bg-gray-700 {
  @apply dark:bg-opacity-50;
}

/* Estilos para las cards de preguntas */
.pregunta-card {
  min-height: 600px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
  }

  // Asegurar que las cards tengan la misma altura
  display: flex;
  flex-direction: column;

  // Hacer que el contenido se distribuya uniformemente
  .mat-form-field {
    margin-bottom: 1rem;
  }

  // Estilos para los mini cards de configuración
  .bg-gradient-to-br {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: scale(1.02);
    }
  }

  // Tema oscuro
  :host-context(.dark-theme) & {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid rgba(75, 85, 99, 0.3);

    &:hover {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
    }
  }
}
