import { Component, ElementRef, ViewChild } from '@angular/core';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { CoachingService } from '../../services/coaching.service';
import { CoachingPorFechaService } from '../../services/coaching.por.fecha.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-coaching',
  templateUrl: './coaching.component.html',
  styleUrls: ['./coaching.component.scss']
})
export class CoachingComponent {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  data: any[] = [];
  searchTerm = '';
  pageSize = 5;
  currentPage = 1;
  showForm = false;

  archivo: File | null = null;
  previewUrl: string | null = null;
  nombreArchivo = '';
  isDragging = false;
  esEdicion = false;

  fechaDesde: string = '';
  fechaHasta: string = '';

  form: {
    id: number | null;
    nombre: string;
    fecha: string;
    imagen: string;
    estado: string;
  } = {
      id: null,
      nombre: '',
      fecha: new Date().toISOString().split('T')[0],
      imagen: '',
      estado: 'Activo'
    };



  constructor(
    private coachingService: CoachingService,
    private coachingPorFechaService: CoachingPorFechaService
  ) {
    const ahoraUTC = new Date();

    // Calculamos la hora de Lima (UTC-5)
    const limaOffset = -5; // Horario de Lima
    const limaTimestamp = ahoraUTC.getTime() + limaOffset * 60 * 60 * 1000;

    // Creamos nueva fecha con el timestamp corregido
    const limaTime = new Date(limaTimestamp);
    const fechaHoy = limaTime.toISOString().split('T')[0];

    this.fechaDesde = fechaHoy;
    this.fechaHasta = fechaHoy;

    this.filtrarPorFechas()
    this.esEdicion = false;

  }

  imagenSeleccionada: string | null = null;

  abrirModalImagen(imagenUrl: string) {
    this.imagenSeleccionada = imagenUrl;
  }

  cerrarModalImagen() {
    this.imagenSeleccionada = null;
  }


  get filteredData() {
    return this.data
      .filter(d => d.nombre.toLowerCase().includes(this.searchTerm.toLowerCase()))
      .slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
  }

  get totalPages(): number {
    return Math.ceil(
      this.data.filter(d => d.nombre.toLowerCase().includes(this.searchTerm.toLowerCase())).length / this.pageSize
    );
  }

  changePage(delta: number) {
    const newPage = this.currentPage + delta;
    if (newPage >= 1 && newPage <= this.totalPages) {
      this.currentPage = newPage;
    }
  }

  filtrarPorFechas() {
    if (!this.fechaDesde || !this.fechaHasta) return;

    // Mostrar alerta de carga
    Swal.fire({
      title: 'Cargando ...',
      text: 'Un momento porfavor',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.coachingPorFechaService.obtenerFrasesPorRango(this.fechaDesde, this.fechaHasta).subscribe({
      next: (res: any[]) => {
        this.data = res;
        this.currentPage = 1;
        Swal.close(); // Cerrar alerta al finalizar
      },
      error: (err) => {
        console.error('Error al obtener frases por rango:', err);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudo cargar el listado. Intenta nuevamente.'
        });
      }
    });
  }

  // === UI y formulario (sin cambios) ===

  openForm() {
    this.resetForm();
    this.esEdicion = false; // Asegura que no quede en modo edición
    this.showForm = true;
  }
  closeForm() {
    this.showForm = false;
  }

  resetForm() {
    this.form = {
      id: null,
      nombre: '',
      fecha: new Date().toISOString().split('T')[0],
      imagen: '',
      estado: 'Activo'
    };
    this.previewUrl = null;
    this.nombreArchivo = '';
    this.archivo = null;
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }


  guardar() {
    if (!this.form.nombre.trim() || !this.archivo) {
      Swal.fire('Error', 'Debe ingresar un nombre y seleccionar una imagen.', 'error');
      return;
    }

    Swal.fire({
      title: 'Guardando...',
      text: 'Subiendo imagen y registrando frase',
      allowOutsideClick: false,
      didOpen: () => Swal.showLoading()
    });

    const storage = getStorage();
    const nombreUnico = `coaching/frases/${Date.now()}_${this.archivo.name}`;
    const storageRef = ref(storage, nombreUnico);

    uploadBytes(storageRef, this.archivo)
      .then(() => getDownloadURL(storageRef))
      .then((url) => {
        const datos = {
          nombre: this.form.nombre,
          fecha: this.form.fecha,
          imagen: url,
          estado: 'A'
        };

        this.coachingService.guardarFrase(datos).subscribe({
          next: () => {
            this.filtrarPorFechas();
            this.closeForm();
            Swal.fire('Éxito', 'Frase registrada correctamente.', 'success');
          },
          error: (err) => {
            console.error('Error al guardar frase en backend:', err);
            Swal.fire('Error', 'No se pudo registrar la frase. Intenta nuevamente.', 'error');
          }
        });
      })
      .catch((err) => {
        console.error('Error al subir imagen a Firebase:', err);
        Swal.fire('Error', 'No se pudo subir la imagen', 'error');
      });
  }



  openFileSelector() {
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.archivo = file;
      this.nombreArchivo = file.name;

      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result as string;
        this.form.imagen = this.previewUrl;
      };
      reader.readAsDataURL(file);
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;

    const file = event.dataTransfer?.files?.[0];
    if (file) {
      this.archivo = file;
      this.nombreArchivo = file.name;

      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result as string;
        this.form.imagen = this.previewUrl;
        if (this.fileInput?.nativeElement) {
          this.fileInput.nativeElement.value = '';
        }
      };
      reader.readAsDataURL(file);
    }
  }

  removeFile() {
    this.previewUrl = null;
    this.nombreArchivo = '';
    this.archivo = null;
    this.form.imagen = '';
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  mostrarImagen(imagenUrl: string) {
    // Pendiente: lógica para mostrar imagen en diálogo
    console.log('Mostrar imagen:', imagenUrl);
  }

  editarFrase(item: any) {
    this.form = {
      id: item.id,
      nombre: item.nombre,
      fecha: item.fecha,
      imagen: item.imagen,
      estado: item.estado
    };
    this.previewUrl = item.imagen;
    this.archivo = null;
    this.esEdicion = true;
    this.showForm = true;
  }




  eliminarFrase(item: any) {
    Swal.fire({
      title: '¿Estás seguro?',
      text: 'Esta acción no se puede deshacer.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        Swal.fire({
          title: 'Eliminando...',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        this.coachingService.eliminarFrase(item.id).subscribe({
          next: () => {
            this.data = this.data.filter(d => d.id !== item.id); // quitarlo del array
            Swal.fire('Eliminado', 'La frase fue eliminada correctamente.', 'success');
          },
          error: (err) => {
            console.error('Error al eliminar frase:', err);
            Swal.fire('Error', 'No se pudo eliminar la frase. Intenta nuevamente.', 'error');
          }
        });
      }
    });
  }


  actualizar() {
    if (!this.form.id || !this.form.nombre.trim()) {
      Swal.fire('Error', 'Faltan datos requeridos', 'error');
      return;
    }

    Swal.fire({
      title: 'Actualizando...',
      text: 'Por favor, espera',
      allowOutsideClick: false,
      didOpen: () => Swal.showLoading()
    });

    const actualizarFrase = (imagenFinalUrl: string) => {
      const datos = {

        nombre: this.form.nombre,
        fecha: this.form.fecha,
        imagen: imagenFinalUrl,
        id: this.form.id
      };

      this.coachingService.editarFrase(datos).subscribe({
        next: () => {
          Swal.close();
          Swal.fire('Éxito', 'Frase actualizada correctamente', 'success');
          this.closeForm();
          this.filtrarPorFechas();
        },
        error: (err) => {
          console.error('Error al actualizar frase:', err);
          Swal.fire('Error', 'No se pudo actualizar la frase', 'error');
        }
      });
    };

    if (this.archivo) {
      const storage = getStorage();
      const nombreUnico = `coaching/frases/${Date.now()}_${this.archivo.name}`;
      const storageRef = ref(storage, nombreUnico);

      uploadBytes(storageRef, this.archivo)
        .then(() => getDownloadURL(storageRef))
        .then((url) => actualizarFrase(url))
        .catch((err) => {
          console.error('Error al subir imagen a Firebase:', err);
          Swal.fire('Error', 'No se pudo subir la imagen', 'error');
        });
    } else {
      actualizarFrase(this.form.imagen);
    }
  }



}
