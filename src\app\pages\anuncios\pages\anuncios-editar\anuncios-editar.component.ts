import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Store, select } from '@ngrx/store';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialog } from '@angular/material/dialog';
import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import * as fromList from '../../store/save';
import { AnuncioResponse } from '../../store/save/save.models';
import { User } from '@app/models/backend/user';

@Component({
  selector: 'app-anuncios-editar',
  templateUrl: './anuncios-editar.component.html',
  styleUrls: ['./anuncios-editar.component.scss']
})
export class AnunciosEditarComponent implements OnInit, OnD<PERSON>roy {
  form!: FormGroup;
  user$!: Observable<User | null>;
  private user: User | null = null;
  private destroy$ = new Subject<void>();

  // Categorías disponibles
  categorias: string[] = ['INTERNO', 'EXTERNO'];

  // Estados disponibles
  estados: string[] = ['ACTIVO', 'INACTIVO'];

  constructor(
    private fb: FormBuilder,
    private store: Store<fromRoot.State>,
    private dialogRef: MatDialogRef<AnunciosEditarComponent>,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: { anuncio: AnuncioResponse }
  ) { }

  ngOnInit(): void {
    // Get user from store
    this.user$ = this.store.pipe(select(fromUser.getUser));
    this.user$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => this.user = user);
    // Convertir fechas de string a Date si existen
    const fechaInicio = this.data.anuncio.fechaInicio ? new Date(this.data.anuncio.fechaInicio) : new Date();
    const fechaFin = this.data.anuncio.fechaFin ? new Date(this.data.anuncio.fechaFin) : new Date(new Date().setDate(new Date().getDate() + 30));

    this.form = this.fb.group({
      titulo: [this.data.anuncio.titulo, [Validators.required]],
      descripcion: [this.data.anuncio.descripcion, [Validators.required]],
      categoria: [this.data.anuncio.categoria, [Validators.required]],
      imagenUrl: [this.data.anuncio.imagenUrl],
      fechaInicio: [fechaInicio],
      fechaFin: [fechaFin],
      orden: [this.data.anuncio.orden || 0],
      estado: [this.data.anuncio.estado || 'ACTIVO']
    });
  }

  onSubmit(): void {
    if (this.form.valid) {
      // Formatear fechas para enviar al backend
      const fechaInicio = this.form.get('fechaInicio')?.value;
      const fechaFin = this.form.get('fechaFin')?.value;

      // Create a clean object with only the necessary fields
      const anuncioActualizado = {
        titulo: this.form.get('titulo')?.value,
        descripcion: this.form.get('descripcion')?.value,
        categoria: this.form.get('categoria')?.value,
        imagenUrl: this.form.get('imagenUrl')?.value,
        fechaInicio: fechaInicio ? fechaInicio.toISOString() : null,
        fechaFin: fechaFin ? fechaFin.toISOString() : null,
        orden: this.form.get('orden')?.value,
        estado: this.form.get('estado')?.value,
        usuarioId: this.user?.id
      };

      this.store.dispatch(new fromList.Update(this.data.anuncio.id, anuncioActualizado));
      this.dialogRef.close(true);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  openFilesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe(url => {
      if (url) {
        this.form.patchValue({
          imagenUrl: url
        });
      }
    });
  }
}
