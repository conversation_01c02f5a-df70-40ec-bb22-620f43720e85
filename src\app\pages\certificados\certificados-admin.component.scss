.certificados-admin-container {
  font-family: 'Poppins', sans-serif;

  h1, h2, h3, h4 {
    font-family: 'Poppins', sans-serif;
  }

  .table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .user-card {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  }

  .status-badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .action-button {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: scale(1.1);
    }
  }

  .loading-spinner {
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .certificados-admin-container {
      padding: 1rem;
    }

    .table-container {
      font-size: 0.875rem;
    }

    .user-card {
      margin-bottom: 1rem;
    }
  }

  // Dark mode specific styles
  .dark {
    .table-container {
      background-color: #374151;
    }

    .user-card {
      background-color: #374151;
      border-color: #4B5563;
    }
  }

  // Custom scrollbar
  .overflow-x-auto::-webkit-scrollbar {
    height: 6px;
  }

  .overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .overflow-x-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  // Dark mode scrollbar
  .dark .overflow-x-auto::-webkit-scrollbar-track {
    background: #374151;
  }

  .dark .overflow-x-auto::-webkit-scrollbar-thumb {
    background: #6B7280;
  }

  .dark .overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
  }
}
