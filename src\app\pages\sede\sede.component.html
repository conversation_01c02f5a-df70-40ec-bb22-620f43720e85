<section class="p-6 w-full max-w-full">
    <div class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all">

  <!-- Cabecera -->
  <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
    <div>
      <h2 class="text-xl font-semibold text-blue-600 dark:text-blue-400">Gestión de Sedes</h2>
      <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">Sedes registradas hasta el momento</p>
    </div>
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
      <!-- Buscador -->
      <div class="relative w-full sm:w-64">
        <input
          type="text"
          [(ngModel)]="searchText"
          (keyup.enter)="onSearch()"
          placeholder="Buscar sede..."
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 pr-10"
        />
        <button
          *ngIf="searchText"
          (click)="clearSearch()"
          class="absolute right-10 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <mat-icon class="text-base">close</mat-icon>
        </button>
        <button
          (click)="onSearch()"
          class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <mat-icon class="text-base">search</mat-icon>
        </button>
      </div>

      <!-- Botón Nueva Sede -->
      <button
        (click)="openCreateDialog()"
        class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
      >
        <mat-icon class="text-base">add</mat-icon>
        <span>Nueva Sede</span>
      </button>
    </div>
  </div>

  <!-- Indicador de carga -->
  <div *ngIf="tableLoading" class="flex items-center mx-auto my-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md max-w-md">
    <mat-spinner [diameter]="24" color="primary" class="mr-3"></mat-spinner>
    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
      Cargando sedes...
    </span>
  </div>

  <!-- Tabla -->
  <div class="overflow-x-auto rounded-xl shadow">
    <table class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white">
      <thead class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs">
        <tr>
          <!-- ID Column -->
          <th class="px-6 py-3">ID</th>

          <!-- Nombre Column -->
          <th class="px-6 py-3">Nombre</th>

          <!-- Dirección Column -->
          <th class="px-6 py-3">Dirección</th>

          <!-- Ciudad Column -->
          <th class="px-6 py-3">Ciudad</th>

          <!-- Provincia Column -->
          <th class="px-6 py-3">Provincia</th>

          <!-- Teléfono Column -->
          <th class="px-6 py-3">Teléfono</th>

          <!-- Email Column -->
          <th class="px-6 py-3">Email</th>

          <!-- Estado Column -->
          <th class="px-6 py-3">Estado</th>

          <!-- Acciones Column -->
          <th class="px-6 py-3 text-center">Acciones</th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
        <tr *ngFor="let sede of tableData.data" class="bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800">
          <!-- ID Column -->
          <td class="px-6 py-4">{{ sede.id }}</td>

          <!-- Nombre Column -->
          <td class="px-6 py-4">{{ sede.nombre }}</td>

          <!-- Dirección Column -->
          <td class="px-6 py-4">{{ sede.direccion }}</td>

          <!-- Ciudad Column -->
          <td class="px-6 py-4">{{ sede.ciudad }}</td>

          <!-- Provincia Column -->
          <td class="px-6 py-4">{{ sede.provincia || '-' }}</td>

          <!-- Teléfono Column -->
          <td class="px-6 py-4">{{ sede.telefono || '-' }}</td>

          <!-- Email Column -->
          <td class="px-6 py-4">{{ sede.email || '-' }}</td>

          <!-- Estado Column -->
          <td class="px-6 py-4">
            <span
              [ngClass]="sede.activo ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'"
              class="px-2 py-1 text-xs font-semibold rounded-full"
            >
              {{ sede.activo ? 'Activo' : 'Inactivo' }}
            </span>
          </td>

          <!-- Acciones Column -->
          <td class="px-6 py-4 text-center space-x-2">
            <button
              (click)="openEditDialog(sede)"
              class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition"
              title="Editar sede"
            >
              <mat-icon>edit</mat-icon>
            </button>
            <button
              (click)="deleteSede(sede)"
              class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition"
              title="Eliminar sede"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </tr>

        <!-- Fila vacía -->
        <tr *ngIf="tableData.data.length === 0 && !tableLoading" class="bg-white dark:bg-gray-900">
          <td [attr.colspan]="displayedColumns.length" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center justify-center py-6">
              <mat-icon class="text-4xl mb-2 text-gray-400 dark:text-gray-600">location_city</mat-icon>
              <p>No se encontraron sedes{{ searchText ? ' para la búsqueda "' + searchText + '"' : '' }}</p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación -->
  <div class="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
    <div>
      <label class="text-sm text-gray-700 dark:text-gray-300 mr-2">Items por página:</label>
      <select
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()"
        class="px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
      >
        <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
      </select>
    </div>

    <!-- Controles de paginación -->
    <div class="flex gap-2">
      <button
        (click)="onFirstPage()"
        [disabled]="currentPage === 0"
        class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
      >
        <mat-icon class="text-base">first_page</mat-icon>
      </button>
      <button
        (click)="onPreviousPage()"
        [disabled]="currentPage === 0"
        class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
      >
        <mat-icon class="text-base">chevron_left</mat-icon>
      </button>

      <span class="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
        Página {{ currentPage + 1 }} de {{ getTotalPages() }}
      </span>

      <button
        (click)="onNextPage()"
        [disabled]="currentPage >= getTotalPages() - 1"
        class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
      >
        <mat-icon class="text-base">chevron_right</mat-icon>
      </button>
      <button
        (click)="onLastPage()"
        [disabled]="currentPage >= getTotalPages() - 1"
        class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
      >
        <mat-icon class="text-base">last_page</mat-icon>
      </button>
    </div>
  </div>
  </div>
</section>
