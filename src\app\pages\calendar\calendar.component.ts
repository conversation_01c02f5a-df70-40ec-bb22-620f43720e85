import {
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import {
  Calendar,
  CalendarList,
} from '@app/models/backend/calendar/calendar.model';
import {
  CalendarOptions,
  DateSelectArg,
  EventClickArg,
  EventDropArg,
  EventInput,
} from '@fullcalendar/core';
import esLocale from '@fullcalendar/core/locales/es';
import interactionPlugin, { DateClickArg } from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import { Subscription } from 'rxjs';
import moment from 'moment';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarService } from './calendar.service';
import { MatDialog } from '@angular/material/dialog';
import { CalendarModalComponent } from './calendar-modal/calendar-modal.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
})
export class CalendarComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('fullcalendar') calendarComponent!: FullCalendarComponent;

  breadCrumbItems!: Array<{}>;
  categories!: any[];
  calendarEvents: EventInput[] = [];
  calendarEventSource: any[] = [];
  eventsPromise!: Promise<EventInput>;
  newEventDate!: DateClickArg;
  // Datos iniciales del calendario
  // Variable para almacenar la vista actual
  currentView: string = 'dayGridMonth';
  // Variable para almacenar la fecha actual formateada
  currentDate: string = '';

  calendarOptions: CalendarOptions = {
    locale: esLocale,
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin],
    // Quitamos los botones del headerToolbar ya que los manejamos manualmente
    headerToolbar: {
      left: '',
      center: '',
      right: '',
    },
    views: {
      day: {
        slotDuration: '00:30:00', // Duración de cada ranura en la vista de día
        slotLabelInterval: '01:00:00', // Intervalo para mostrar etiquetas de las horas en la vista de día
      },
      week: {
        slotDuration: '00:30:00', // Duración de cada ranura en la vista de semana
        slotLabelInterval: '01:00:00', // Intervalo para mostrar etiquetas de las horas en la vista de semana
      },
    },
    initialView: 'dayGridMonth',
    slotDuration: '00:30:00',
    themeSystem: 'bootstrap',
    initialEvents: this.calendarEvents,
    height: 690,
    contentHeight: 650,
    weekends: true,
    weekNumbers: true,
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    events: [],
    // Desactivamos dateClick para evitar que se abra el modal dos veces
    // dateClick: this.handleDateClick.bind(this),
    eventClick: this.handleEventClick.bind(this),
    eventsSet: this.handleEvents.bind(this),
    datesSet: this.handleDatesSet.bind(this),
    select: this.handleSelect.bind(this),
    eventDrop: this.handleEventDrop.bind(this),
    eventTimeFormat: {
      // like '14:30:00'
      hour: '2-digit',
      minute: '2-digit',
      meridiem: false,
      hour12: true,
    },
  };

  // FECHA SELECCIONADA
  dateSelected: any = null;
  // Form
  isNewData: boolean = true;
  submitted: boolean = false;
  calendarForm!: FormGroup;
  // Table data
  lists?: CalendarList[];
  dataUserSession: any;
  private subscription: Subscription = new Subscription();
  // Control para evitar llamadas duplicadas
  private initialLoadComplete: boolean = false;

  constructor(
    private dialog: MatDialog,
    private _calendarService: CalendarService,
    private formBuilder: FormBuilder
  ) {}

  // Form getter
  get f() {
    return this.calendarForm.controls;
  }

  // Initialize form
  initForm() {
    this.calendarForm = this.formBuilder.group({
      id: [0],
      titulo: ['', [Validators.required]],
      descripcion: [''],
      color: ['bg-primary', [Validators.required]],
      fecha_inicio: ['', [Validators.required]],
      fecha_final: ['', [Validators.required]],
      hora_inicio: ['', [Validators.required]],
      hora_final: ['', [Validators.required]],
      is_active: [true],
      is_seen: [false],
    });
  }

  ngOnInit(): void {
    this.breadCrumbItems = this.getBreadcrumbItems();
    this.initForm();

    // Get user from localStorage
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        this.dataUserSession = JSON.parse(userStr);
        //console.log('Usuario cargado en ngOnInit:', this.dataUserSession);

        // Verificar que el usuario tenga un ID válido
        if (!this.dataUserSession || !this.dataUserSession.id) {
          //console.warn('El usuario cargado no tiene un ID válido:', this.dataUserSession);
          // Intentar obtener el ID de otra manera si es necesario
          // Por ejemplo, si el ID está en otra propiedad
          if (this.dataUserSession && this.dataUserSession.userId) {
            this.dataUserSession.id = this.dataUserSession.userId;
            //console.log('Se asignó el ID del usuario desde userId:', this.dataUserSession.id);
          }
        }
      } catch (error) {
        //console.error('Error al parsear el usuario en ngOnInit:', error);
      }
    } else {
      //console.warn('No se encontró información del usuario en localStorage');
    }

    // Cargar datos iniciales después de obtener el usuario
    this._fetchData();

    // Inicializar la fecha actual
    this.updateCurrentDate();
  }

  /**
   * Actualiza la variable currentDate con el mes y año actual del calendario
   */
  updateCurrentDate(): void {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      if (calendarApi) {
        // Obtener la fecha actual del calendario
        const date = calendarApi.getDate();
        // Formatear la fecha en español
        this.currentDate = new Intl.DateTimeFormat('es', {
          month: 'long',
          year: 'numeric',
        }).format(date);
        // Capitalizar la primera letra
        this.currentDate =
          this.currentDate.charAt(0).toUpperCase() + this.currentDate.slice(1);
      } else {
        // Si no hay API del calendario, usar la fecha actual
        const today = new Date();
        this.currentDate = new Intl.DateTimeFormat('es', {
          month: 'long',
          year: 'numeric',
        }).format(today);
        // Capitalizar la primera letra
        this.currentDate =
          this.currentDate.charAt(0).toUpperCase() + this.currentDate.slice(1);
      }
    } else {
      // Si no hay componente de calendario, usar la fecha actual
      const today = new Date();
      this.currentDate = new Intl.DateTimeFormat('es', {
        month: 'long',
        year: 'numeric',
      }).format(today);
      // Capitalizar la primera letra
      this.currentDate =
        this.currentDate.charAt(0).toUpperCase() + this.currentDate.slice(1);
    }
  }

  /**
   * Cambia la vista del calendario
   * @param viewName Nombre de la vista a mostrar
   */
  changeView(viewName: string): void {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      if (calendarApi) {
        calendarApi.changeView(viewName);
        this.currentView = viewName;
        this.updateCurrentDate();
      }
    }
  }

  /**
   * Navega por el calendario
   * @param action Acción de navegación: prev, next, today, prevYear, nextYear
   */
  navigateCalendar(action: string): void {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      if (calendarApi) {
        switch (action) {
          case 'prev':
            calendarApi.prev();
            break;
          case 'next':
            calendarApi.next();
            break;
          case 'today':
            calendarApi.today();
            break;
          case 'prevYear':
            calendarApi.prevYear();
            break;
          case 'nextYear':
            calendarApi.nextYear();
            break;
        }
        this.updateCurrentDate();
      }
    }
  }

  ngAfterViewInit(): void {
    // Inicializar la fecha actual después de que la vista se haya cargado
    setTimeout(() => {
      this.updateCurrentDate();
    }, 0);
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones
    this.subscription.unsubscribe();

    // Cancelar la suscripción de filtro actual si existe
    if (this.currentFilterSubscription) {
      this.currentFilterSubscription.unsubscribe();
      this.currentFilterSubscription = null;
    }

    // Limpiar cualquier timeout pendiente
    if (this.datesSetTimeout) {
      clearTimeout(this.datesSetTimeout);
      this.datesSetTimeout = null;
    }
  }

  getBreadcrumbItems() {
    return [{ label: 'Aplicación' }, { label: 'Calendario', active: true }];
  }

  /**
   * ****************************************************************
   * FULL CALENDAR EVENTS
   * ****************************************************************
   */
  showMonthEvents() {
    // Este método muestra los eventos de un mes dado
    // Get current view and refresh events
    this.refreshEvents();
  }

  /**
   * Event click modal show
   */
  handleDateClick(arg: DateClickArg) {
    this.newEventDate = arg;

    const dialogRef = this.dialog.open(CalendarModalComponent, {
      width: '500px',
      data: { date: arg.dateStr },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.action === 'create') {
          this.saveDataApi(result.data);
        }
      }
    });
  }

  /**
   * Event click modal show
   */
  handleEventClick(arg: EventClickArg) {
    const eventId = arg.event.id;
    if (!this.lists) return;

    const data = this.lists.find(
      (data: { id: any }) => data.id === parseInt(eventId)
    );
    if (data) {
      const calendar = Calendar.cast(data);

      const dialogRef = this.dialog.open(CalendarModalComponent, {
        width: '500px',
        data: { event: calendar },
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          if (result.action === 'update') {
            this.updateDataApi(result.data, result.data.id);
          } else if (result.action === 'delete') {
            this.deleteDataApi(result.data.id);
          }
        }
      });
    }
  }

  /**
   * Event select modal show
   */
  handleSelect(arg: DateSelectArg) {
    const endDate = moment(arg.endStr).subtract(1, 'day').format('YYYY-MM-DD');

    const dialogRef = this.dialog.open(CalendarModalComponent, {
      width: '500px',
      data: {
        date: arg.startStr,
        endDate: endDate,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.action === 'create') {
          this.saveDataApi(result.data);
        }
      }
    });
  }

  /**
   * Event drop modal show
   */
  handleEventDrop(arg: EventDropArg) {
    const eventId = arg.event.id;
    const startDate: string = arg.event.startStr;
    const endDate: string = arg.event.endStr;

    const request: any = {
      fechaInicio: startDate.split('T')[0],
      fechaFinal: endDate.split('T')[0],
    };

    if (!this.lists) {
      arg.revert();
      return;
    }

    const evento = this.lists.find(
      (item: CalendarList) => item.id.toString() === eventId
    );
    if (evento) {
      const confirmResult = confirm('¿Estas seguro de modificar la agenda?');
      if (confirmResult) {
        // Crear un nuevo objeto con el formato correcto para el backend
        const updatedEvent = {
          id: evento.id,
          titulo: evento.titulo,
          descripcion: evento.descripcion,
          color: evento.color,
          fechaInicio: request.fechaInicio,
          fechaFinal: request.fechaFinal,
          horaInicio: evento.hora_inicio,
          horaFinal: evento.hora_final,
          is_active: evento.is_active,
          is_seen: evento.is_seen,
        };
        this.updateDataApi(updatedEvent, evento.id);
      } else {
        arg.revert();
      }
    } else {
      arg.revert();
    }
  }

  /**
   * Events bind in calander
   * @param events events
   */
  handleEvents(_events: any) {
    // No necesitamos hacer nada con los eventos aquí
    // Esta función es requerida por FullCalendar pero no necesitamos su funcionalidad
  }

  // Variable para controlar el debounce de datesSet
  private datesSetTimeout: any = null;

  // Variable para almacenar la última solicitud de fechas
  private lastRequestKey: string = '';

  // FECHA ACTUALES
  handleDatesSet(arg: any) {
    // Aquí obtienes las fechas de inicio y fin de la vista actual
    const startDate: string = arg.startStr;
    const endDate: string = arg.endStr;

    // Actualizar la vista actual
    this.currentView = arg.view.type;

    // Actualizar la fecha actual mostrada en el encabezado
    this.updateCurrentDate();

    // Asegurarnos de que las fechas estén en el formato correcto (YYYY-MM-DD)
    const formatDate = (dateStr: string) => {
      if (!dateStr) return '';
      // Si ya tiene formato ISO, extraer solo la fecha
      if (dateStr.includes('T')) {
        return dateStr.split('T')[0];
      }
      // Si no, convertir a ISO y extraer la fecha
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    };

    const request: any = {
      fechaInicio: formatDate(startDate),
      fechaFinal: formatDate(endDate),
    };

    // Crear una clave única para esta solicitud
    const requestKey = `${request.fechaInicio}-${request.fechaFinal}`;

    // Si es la primera carga, marcar como completada
    if (!this.initialLoadComplete) {
      this.initialLoadComplete = true;

      // Si las fechas coinciden con las iniciales y ya se ha hecho una solicitud, no hacer nada
      if (
        this.dateSelected &&
        this.dateSelected.fechaInicio === request.fechaInicio &&
        this.dateSelected.fechaFinal === request.fechaFinal
      ) {
        return;
      }
    }

    // Verificar si esta solicitud es idéntica a la última
    if (requestKey === this.lastRequestKey) {
      return;
    }

    // Actualizar la clave de la última solicitud
    this.lastRequestKey = requestKey;

    // Actualizar las fechas seleccionadas
    this.dateSelected = request;

    // Limpiar cualquier timeout pendiente
    if (this.datesSetTimeout) {
      clearTimeout(this.datesSetTimeout);
    }

    // Usar debounce para evitar múltiples llamadas en rápida sucesión
    this.datesSetTimeout = setTimeout(() => {
      this.filterDates(request);
      this.datesSetTimeout = null;
    }, 200); // Aumentar a 200ms para dar más margen
  }

  // Método público para refrescar eventos
  refreshEvents() {
    // Obtener las fechas actuales del calendario
    const currentView = this.calendarComponent?.getApi()?.view;
    if (!currentView) {
      // Si no hay vista activa pero hay fechas seleccionadas
      if (this.dateSelected) {
        // Forzar una nueva solicitud añadiendo un flag de forceRefresh
        const refreshRequest = {
          ...this.dateSelected,
          forceRefresh: true,
        };
        this.filterDates(refreshRequest);
      } else {
        // Si no hay fechas seleccionadas, obtener todos los datos
        this.listDataApi(true);
      }
      return;
    }

    // Obtener fechas de la vista actual
    const start = currentView.activeStart;
    const end = currentView.activeEnd;

    // Formatear fechas para el filtro
    const fechaInicio = start.toISOString().split('T')[0];
    const fechaFinal = end.toISOString().split('T')[0];

    // Filtrar por las fechas actuales
    this.filterDates({
      fechaInicio,
      fechaFinal,
      forceRefresh: true,
    });
  }

  /**
   * Fetches the data
   */
  private _fetchData() {
    // Initialize categories for color selection
    this.categories = [
      {
        name: 'Azul',
        value: 'bg-primary',
      },
      {
        name: 'Verde',
        value: 'bg-success',
      },
      {
        name: 'Rojo',
        value: 'bg-danger',
      },
      {
        name: 'Amarillo',
        value: 'bg-warning',
      },
      {
        name: 'Celeste',
        value: 'bg-info',
      },
      {
        name: 'Morado',
        value: 'bg-purple',
      },
      {
        name: 'Gris',
        value: 'bg-secondary',
      },
      {
        name: 'Negro',
        value: 'bg-dark',
      },
    ];

    // Inicializar eventos del calendario
    this.calendarEvents = [];

    // Obtener la fecha actual para el filtro inicial
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const initialFilter = {
      fechaInicio: firstDay.toISOString().split('T')[0],
      fechaFinal: lastDay.toISOString().split('T')[0],
    };

    this.dateSelected = initialFilter;

    // Establecer la clave de solicitud inicial
    this.lastRequestKey = `${initialFilter.fechaInicio}-${initialFilter.fechaFinal}`;

    // Cargar eventos iniciales si el usuario ya está cargado
    if (this.dataUserSession && this.dataUserSession.id) {
      // Establecer initialLoadComplete a false para que handleDatesSet sepa que esta es la carga inicial
      this.initialLoadComplete = false;

      // Añadir el ID del usuario al filtro inicial
      const filterWithUser = {
        ...initialFilter,
        userCreateId: this.dataUserSession.id,
      };

      // La carga inicial se hará una sola vez aquí
      // El componente FullCalendar disparará handleDatesSet después, pero no hará una segunda llamada
      // gracias a nuestras comprobaciones de duplicación
      this.filterDates(filterWithUser);
    }
  }

  /**
   * ****************************************************************
   * OPERACIONES CON LA API
   * ****************************************************************
   */
  // Variable para almacenar la última suscripción
  private currentFilterSubscription: Subscription | null = null;

  // Método optimizado para filtrar fechas
  public filterDates(dates: any) {
    // Obtener el ID del usuario
    const userId = this.dataUserSession?.id ?? 0;

    // Función para formatear fechas
    const formatDate = (dateStr: string) => {
      if (!dateStr) return '';
      // Si ya tiene formato ISO, extraer solo la fecha
      if (typeof dateStr === 'string' && dateStr.includes('T')) {
        return dateStr.split('T')[0];
      }
      // Si no, convertir a ISO y extraer la fecha
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    };

    // Preparar datos para el filtro
    const fechaInicio = formatDate(dates.fecha_inicio || dates.fechaInicio);
    const fechaFinal = formatDate(dates.fecha_final || dates.fechaFinal);

    // Validar fechas
    if (!fechaInicio || !fechaFinal) {
      this.clearEvents();
      return;
    }

    // Crear objeto de filtro
    const filterData = {
      fechaInicio,
      fechaFinal,
      userCreateId: userId,
      forceRefresh: dates.forceRefresh || false,
    };

    // Cancelar cualquier suscripción anterior
    if (this.currentFilterSubscription) {
      this.currentFilterSubscription.unsubscribe();
      this.currentFilterSubscription = null;
    }

    // Realizar la solicitud y procesar la respuesta
    this.currentFilterSubscription = this._calendarService
      .getFilterDates(filterData)
      .subscribe({
        next: (response: any) => {
          const status = response?.rpta ?? response?.status;

          // Verificar si la respuesta es válida
          if (
            !response ||
            status !== 1 ||
            !response.data ||
            !Array.isArray(response.data) ||
            response.data.length === 0
          ) {
            this.clearEvents();
            return;
          }

          // Procesar los datos
          this.processCalendarData(response.data);

          // Limpiar la referencia a la suscripción actual
          this.currentFilterSubscription = null;
        },
        error: (err) => {
          this.clearEvents();
          // Limpiar la referencia a la suscripción actual
          this.currentFilterSubscription = null;
        },
      });
  }

  // Método para limpiar eventos
  private clearEvents(): void {
    this.lists = [];
    this.calendarOptions.events = [];
    this.eventsPromise = Promise.resolve([]);
  }

  // Método para procesar datos del calendario
  private processCalendarData(data: any[]): void {
    // Convertir los datos al formato del modelo Calendar
    this.lists = data
      .filter((item) => item && item.id)
      .map((item) => CalendarList.cast(item));

    if (this.lists.length === 0) {
      this.clearEvents();
      return;
    }

    // Convertir a formato de eventos para el calendario
    const events = this.lists.map((item) => ({
      id: item.id.toString(),
      title: item.titulo,
      start: `${item.fecha_inicio}T${item.hora_inicio}`,
      end: `${item.fecha_final}T${item.hora_final}`,
      classNames: [item.color],
      backgroundColor: '',
    }));

    // Actualizar el calendario
    this.calendarOptions.events = events;
    this.eventsPromise = Promise.resolve(events);
  }

  // Métodos para operaciones CRUD
  public saveDataApi(data: any): void {
    const userId = this.dataUserSession?.id ?? 0;

    const dataWithUser = {
      ...data,
      userCreateId: userId,
    };

    this._calendarService.register(dataWithUser).subscribe({
      next: (response: any) => {
        const status = response?.rpta ?? response?.status;
        const responseData = response?.data;

        if (status === 1 && responseData) {
          const data = CalendarList.cast(responseData);
          this._calendarService.addObjectObserver(data);
        }

        // Siempre refrescar eventos al final
        this.refreshEvents();
      },
      error: () => this.refreshEvents(),
    });
  }

  public updateDataApi(data: any, id: number): void {
    this._calendarService.update(data, id).subscribe({
      next: (response: any) => {
        const status = response?.rpta ?? response?.status;
        const responseData = response?.data;

        if (status === 1 && responseData) {
          const data = CalendarList.cast(responseData);
          this._calendarService.updateObjectObserver(data);
        }

        // Siempre refrescar eventos al final
        this.refreshEvents();
      },
      error: () => this.refreshEvents(),
    });
  }

  public deleteDataApi(id: number): void {
    this._calendarService.delete(id).subscribe({
      next: (response: any) => {
        const status = response?.rpta ?? response?.status;

        if (status === 1) {
          this._calendarService.removeObjectObserver(id);
        }

        // Siempre refrescar eventos al final
        this.refreshEvents();
      },
      error: () => this.refreshEvents(),
    });
  }

  // Método para guardar datos desde el formulario
  public saveData(): void {
    this.submitted = true;

    if (this.calendarForm.invalid) {
      return;
    }

    const formData = this.calendarForm.value;
    const calendar = new Calendar(formData);

    if (this.isNewData) {
      this.saveDataApi(calendar);
    } else {
      this.updateDataApi(calendar, formData.id);
    }
  }

  // Método para eliminar un registro
  public deleteRow(id: number): void {
    if (confirm('¿Está seguro de eliminar este registro?')) {
      this.deleteDataApi(id);
    }
  }

  // Método para obtener todos los calendarios
  public listDataApi(forceRefresh: boolean = false): void {
    this._calendarService.getAll(forceRefresh).subscribe({
      next: (response: any) => {
        const status = response?.rpta ?? response?.status;
        const responseData = response?.data;

        if (status === 1 && responseData && Array.isArray(responseData)) {
          // Procesar los datos
          this.lists = responseData.map((item) => CalendarList.cast(item));

          if (this.lists.length > 0) {
            // Convertir a formato para el calendario
            this.calendarEventSource = this.lists.map((item) => ({
              id: item.id.toString(),
              title: item.titulo,
              start: `${item.fecha_inicio}T${item.hora_inicio}`,
              end: `${item.fecha_final}T${item.hora_final}`,
              classNames: [item.color],
              backgroundColor: '',
            }));

            this.calendarOptions.events = this.calendarEventSource;
            this.eventsPromise = Promise.resolve(this.calendarEventSource);
          } else {
            this.clearEvents();
          }
        } else {
          this.clearEvents();
        }
      },
      error: () => this.clearEvents(),
    });
  }
}
