import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MapDialogComponent } from './map-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MapaTipificacionModule } from '../mapa-tipificacion-leaflet/mapa-tipificacion.module';
import { MapaTipificacionMapboxModule } from '../mapa-tipificacion-mapbox/mapa-tipificacion-mapbox.module';

@NgModule({
  declarations: [
    MapDialogComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MapaTipificacionModule,
    MapaTipificacionMapboxModule
  ],
  exports: [
    MapDialogComponent
  ]
})
export class MapDialogModule { }
