<div class="w-full px-4 sm:px-6 lg:px-10 xl:px-20 py-6">
  <!-- Men<PERSON><PERSON> de error -->
  <div
    class="bg-red-100 text-red-700 p-4 rounded-lg mb-4 flex items-center gap-2"
    *ngIf="error$ | async as error"
  >
    <mat-icon class="text-red-500">error</mat-icon>
    <span>{{ error?.message || "Ha ocurrido un error inesperado" }}</span>
  </div>

  <!-- Contenedor principal -->
  <div
    class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all"
  >
    <!-- Cabecera -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
    >
      <div>
        <h2 class="text-xl font-semibold text-blue-600">
          Lista de Supervisores
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
          Supervisores registrados en el sistema
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
      >
        <div class="relative">
          <input
            type="text"
            placeholder="Buscar supervisores"
            class="w-full sm:w-64 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
            [(ngModel)]="searchTerm"
            (input)="buscarCoordinadores()"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <button
              *ngIf="searchTerm"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white"
              (click)="limpiarBusqueda()"
            >
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon
              *ngIf="!searchTerm"
              class="text-gray-500 dark:text-gray-400"
              >search</mat-icon
            >
          </div>
        </div>
        <button
          (click)="loadCoordinadores()"
          class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
          title="Actualizar"
        >
          <!-- Ícono: Heroicon "arrow-path" (actualizar) -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M4.5 12a7.5 7.5 0 0112.772-5.303M19.5 12a7.5 7.5 0 01-12.772 5.303M15 9h3.75V5.25M9 15H5.25v3.75"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Indicador de carga -->
    <div
      *ngIf="loading$ | async"
      class="flex items-center mx-auto my-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md max-w-md"
    >
      <mat-spinner [diameter]="24" color="primary" class="mr-3"></mat-spinner>
      <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
        Cargando supervisores...
      </span>
    </div>

    <!-- Tabla -->
    <div
      class="overflow-x-auto rounded-xl shadow"
      *ngIf="!(loading$ | async) && filteredCoordinadores.length > 0"
    >
      <table
        class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
      >
        <thead
          class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
        >
          <tr>
            <th class="px-6 py-3">N°</th>
            <th class="px-6 py-3">Nombre Completo</th>
            <th class="px-6 py-3">DNI</th>
            <th class="px-6 py-3">Sede</th>
            <th class="px-6 py-3">Asesores</th>
            <th class="px-6 py-3 text-center">Acciones</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
          <tr
            *ngFor="let coordinador of filteredCoordinadores; let i = index"
            class="bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <td class="px-6 py-4">{{ currentPage * pageSize + i + 1 }}</td>
            <td class="px-6 py-4">
              {{ coordinador.nombre }} {{ coordinador.apellido }}
            </td>
            <td class="px-6 py-4">{{ coordinador.dni }}</td>
            <td class="px-6 py-4">{{ coordinador.sede }}</td>
            <td class="px-6 py-4">
              <span
                class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
              >
                {{ coordinador.asesores.length }}
              </span>
            </td>
            <td class="px-6 py-4 text-center space-x-2">
              <button
                (click)="verDetalleAsesores(coordinador)"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition"
                title="Ver detalle"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 inline"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              </button>
              <button
                (click)="openModal(coordinador.id)"
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition"
                title="Asignar asesor"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 inline"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                  />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div
      class="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4"
      *ngIf="!(loading$ | async) && filteredCoordinadores.length > 0"
    >
      <div>
        <label class="text-sm text-gray-700 dark:text-gray-300 mr-2"
          >Items por página:</label
        >
        <select
          #pageSizeSelect
          class="px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          [ngModel]="pageSize"
          (change)="onPageSizeChange(pageSizeSelect.value)"
        >
          <option value="5">5 por página</option>
          <option value="10">10 por página</option>
          <option value="20">20 por página</option>
          <option value="50">50 por página</option>
        </select>
      </div>
      <div class="flex gap-2">
        <button
          class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          [disabled]="currentPage === 0"
          (click)="
            onPageChange({
              pageIndex: currentPage - 1,
              pageSize: pageSize,
              length: totalItems
            })
          "
        >
          Anterior
        </button>
        <span class="text-sm pt-1 block text-gray-800 dark:text-white">
          Página {{ currentPage + 1 }} de {{ Math.ceil(totalItems / pageSize) }}
        </span>
        <button
          class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          [disabled]="currentPage >= Math.ceil(totalItems / pageSize) - 1"
          (click)="
            onPageChange({
              pageIndex: currentPage + 1,
              pageSize: pageSize,
              length: totalItems
            })
          "
        >
          Siguiente
        </button>
      </div>
    </div>
  </div>

  <!-- Estado vacío -->
  <div
    class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all mt-4"
    *ngIf="filteredCoordinadores.length === 0 && !(loading$ | async)"
  >
    <!-- Cabecera -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
    >
      <div>
        <h2 class="text-xl font-semibold text-blue-600">
          Lista de Supervisores
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
          Supervisores registrados en el sistema
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
      >
        <div class="relative">
          <input
            type="text"
            placeholder="Buscar supervisores"
            class="w-full sm:w-64 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
            [(ngModel)]="searchTerm"
            (input)="buscarCoordinadores()"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <button
              *ngIf="searchTerm"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white"
              (click)="limpiarBusqueda()"
            >
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon
              *ngIf="!searchTerm"
              class="text-gray-500 dark:text-gray-400"
              >search</mat-icon
            >
          </div>
        </div>
        <button
          (click)="loadCoordinadores()"
          class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
          title="Actualizar"
        >
          <!-- Ícono: Heroicon "arrow-path" (actualizar) -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M4.5 12a7.5 7.5 0 0112.772-5.303M19.5 12a7.5 7.5 0 01-12.772 5.303M15 9h3.75V5.25M9 15H5.25v3.75"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Tabla vacía -->
    <div class="overflow-x-auto rounded-xl shadow">
      <table
        class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
      >
        <thead
          class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
        >
          <tr>
            <th class="px-6 py-3">N°</th>
            <th class="px-6 py-3">Nombre Completo</th>
            <th class="px-6 py-3">DNI</th>
            <th class="px-6 py-3">Sede</th>
            <th class="px-6 py-3">Asesores</th>
            <th class="px-6 py-3 text-center">Acciones</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900">
          <tr>
            <td
              colspan="6"
              class="px-6 py-12 text-center text-gray-500 dark:text-gray-400"
            >
              <div class="flex flex-col items-center">
                <mat-icon class="text-5xl mb-4 text-gray-400 dark:text-gray-500"
                  >search_off</mat-icon
                >
                <p class="text-lg mb-2">
                  <span *ngIf="searchTerm"
                    >No se encontraron supervisores con el criterio de búsqueda
                    "{{ searchTerm }}"</span
                  >
                  <span *ngIf="!searchTerm"
                    >No hay supervisores registrados</span
                  >
                </p>
                <div *ngIf="searchTerm" class="mt-4">
                  <button
                    (click)="limpiarBusqueda()"
                    class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
                  >
                    <!-- Ícono: Heroicon "arrow-path" (actualizar) -->
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M4.5 12a7.5 7.5 0 0112.772-5.303M19.5 12a7.5 7.5 0 01-12.772 5.303M15 9h3.75V5.25M9 15H5.25v3.75"
                      />
                    </svg>
                    <span>Limpiar búsqueda</span>
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<!-- Modal para detalle de asesores con Tailwind CSS -->
<div
  class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 transition-all duration-300 ease-in-out"
  [class.hidden]="!isDetalleAsesoresModalOpen"
  (click)="closeDetalleAsesoresModal()"
>
  <div
    class="w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-auto animate-[zoomIn_0.3s_ease-in-out]"
    (click)="$event.stopPropagation()"
    role="dialog"
    aria-modal="true"
  >
    <!-- Cabecera del modal -->
    <div
      class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700"
    >
      <h2
        class="flex items-center gap-2 text-xl font-semibold text-gray-900 dark:text-white"
      >
        <mat-icon class="text-blue-600 dark:text-blue-400">group</mat-icon>
        <span
          >Asesores asignados a {{ selectedCoordinador?.nombre }}
          {{ selectedCoordinador?.apellido }}</span
        >
      </h2>
      <button
        class="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white transition-colors"
        (click)="closeDetalleAsesoresModal()"
      >
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Información del coordinador -->
    <div
      class="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
    >
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300"
      >
        <mat-icon class="text-gray-600 dark:text-gray-400 text-base"
          >badge</mat-icon
        >
        <span>DNI: {{ selectedCoordinador?.dni }}</span>
      </div>
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300"
      >
        <mat-icon class="text-gray-600 dark:text-gray-400 text-base"
          >location_on</mat-icon
        >
        <span>Sede: {{ selectedCoordinador?.sede }}</span>
      </div>
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-blue-100 dark:bg-blue-900 rounded-full text-sm text-blue-800 dark:text-blue-300"
      >
        <mat-icon class="text-blue-700 dark:text-blue-400 text-base"
          >people</mat-icon
        >
        <span
          >Total Asesores:
          {{ selectedCoordinador?.asesores?.length || 0 }}</span
        >
      </div>
    </div>

    <!-- Cuerpo del modal -->
    <div class="p-6">
      <!-- Buscador -->
      <div class="mb-6">
        <div class="relative">
          <input
            type="text"
            class="w-full px-4 py-3 pr-10 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            [(ngModel)]="searchAsesor"
            (keyup)="filterAsesoresDetalle()"
            placeholder="Buscar por DNI, nombre, apellido o email"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <mat-icon
              *ngIf="!searchAsesor"
              class="text-gray-400 dark:text-gray-500"
              >search</mat-icon
            >
            <button
              *ngIf="searchAsesor"
              class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
              (click)="resetFilterAsesoresDetalle()"
            >
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Grid de asesores asignados -->
      <div
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"
        *ngIf="paginatedAsesoresDetalle.length > 0"
      >
        <div
          *ngFor="let asesor of paginatedAsesoresDetalle"
          class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200"
        >
          <!-- Cabecera de la tarjeta -->
          <div
            class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            <div
              class="flex items-center justify-center w-10 h-10 rounded-full text-blue-800 dark:text-blue-300 font-semibold"
              [ngStyle]="{ 'background-color': getAvatarColor(asesor.dni) }"
            >
              {{ getInitials(asesor.nombre, asesor.apellido) }}
            </div>
            <div class="overflow-hidden">
              <div class="font-semibold text-gray-900 dark:text-white truncate">
                {{ asesor.nombre }} {{ asesor.apellido }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ asesor.dni }}
              </div>
            </div>
          </div>

          <!-- Cuerpo de la tarjeta -->
          <div class="p-4">
            <div class="flex items-center gap-2 mb-2">
              <mat-icon class="text-gray-500 dark:text-gray-400"
                >email</mat-icon
              >
              <span
                class="text-sm text-gray-800 dark:text-gray-200 truncate"
                [matTooltip]="asesor.email"
                >{{ asesor.email }}</span
              >
            </div>
            <div class="flex items-center gap-2" *ngIf="asesor.telefono">
              <mat-icon class="text-gray-500 dark:text-gray-400"
                >phone</mat-icon
              >
              <span class="text-sm text-gray-800 dark:text-gray-200">{{
                asesor.telefono
              }}</span>
            </div>
          </div>

          <!-- Pie de la tarjeta -->
          <div
            class="flex justify-end p-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700"
          >
            <button
              class="flex items-center gap-1.5 px-3 py-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white rounded-md text-sm font-medium transition-colors"
              (click)="removerAsesor(asesor.id)"
            >
              <mat-icon>person_remove</mat-icon>
              <span>Remover</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Paginador personalizado -->
      <div
        class="flex flex-wrap justify-between items-center gap-4 mt-4 mb-2"
        *ngIf="totalAsesoresDetalle > 0"
      >
        <div class="text-sm text-gray-600 dark:text-gray-400">
          Mostrando {{ currentPageDetalle * pageSize + 1 }} -
          {{
            currentPageDetalle * pageSize + pageSize > totalAsesoresDetalle
              ? totalAsesoresDetalle
              : currentPageDetalle * pageSize + pageSize
          }}
          de {{ totalAsesoresDetalle }}
        </div>
        <div class="flex items-center gap-1">
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="currentPageDetalle === 0"
            (click)="
              onPageChangeDetalle({
                pageIndex: 0,
                pageSize: pageSize,
                length: totalAsesoresDetalle
              })
            "
          >
            <mat-icon>first_page</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="currentPageDetalle === 0"
            (click)="
              onPageChangeDetalle({
                pageIndex: currentPageDetalle - 1,
                pageSize: pageSize,
                length: totalAsesoresDetalle
              })
            "
          >
            <mat-icon>chevron_left</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="
              currentPageDetalle >=
              Math.ceil(totalAsesoresDetalle / pageSize) - 1
            "
            (click)="
              onPageChangeDetalle({
                pageIndex: currentPageDetalle + 1,
                pageSize: pageSize,
                length: totalAsesoresDetalle
              })
            "
          >
            <mat-icon>chevron_right</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="
              currentPageDetalle >=
              Math.ceil(totalAsesoresDetalle / pageSize) - 1
            "
            (click)="
              onPageChangeDetalle({
                pageIndex: Math.ceil(totalAsesoresDetalle / pageSize) - 1,
                pageSize: pageSize,
                length: totalAsesoresDetalle
              })
            "
          >
            <mat-icon>last_page</mat-icon>
          </button>

          <select
            #pageSizeSelectDetalle
            class="ml-2 px-3 py-1.5 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            [ngModel]="pageSize"
            (change)="onPageSizeChange(pageSizeSelectDetalle.value)"
          >
            <option value="6">6 por página</option>
            <option value="12">12 por página</option>
            <option value="24">24 por página</option>
            <option value="48">48 por página</option>
          </select>
        </div>
      </div>

      <!-- Mensaje cuando no hay resultados de búsqueda -->
      <div
        class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
        *ngIf="totalAsesoresDetalle === 0 && searchAsesor"
      >
        <mat-icon class="text-5xl text-gray-400 dark:text-gray-600 mb-4"
          >search_off</mat-icon
        >
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          No se encontraron asesores con los criterios de búsqueda especificados
        </p>
        <button
          class="flex items-center gap-1.5 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          (click)="resetFilterAsesoresDetalle()"
        >
          <mat-icon>refresh</mat-icon>
          <span>Limpiar filtros</span>
        </button>
      </div>

      <!-- Mensaje cuando no hay asesores asignados -->
      <div
        class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
        *ngIf="!selectedCoordinador?.asesores?.length"
      >
        <mat-icon class="text-5xl text-gray-400 dark:text-gray-600 mb-4"
          >group_off</mat-icon
        >
        <p class="text-gray-600 dark:text-gray-400">
          No hay asesores asignados a este supervisor
        </p>
      </div>
    </div>

    <!-- Pie del modal -->
    <div
      class="flex justify-end gap-3 p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
    >
      <button
        class="px-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 text-sm font-medium transition-colors"
        (click)="closeDetalleAsesoresModal()"
      >
        Cerrar
      </button>
      <button
        class="flex items-center gap-1.5 px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded text-sm font-medium transition-colors"
        (click)="actualizarListaCoordinadores()"
      >
        <mat-icon>refresh</mat-icon>
        <span>Actualizar Lista</span>
      </button>
    </div>
  </div>
</div>

<!-- Modal para asignar asesores con Tailwind CSS -->
<div
  class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 transition-all duration-300 ease-in-out"
  [class.hidden]="!isAsignarAsesorModalOpen"
  (click)="closeAsignarAsesorModal()"
>
  <div
    class="w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-auto animate-[zoomIn_0.3s_ease-in-out]"
    (click)="$event.stopPropagation()"
    role="dialog"
    aria-modal="true"
  >
    <!-- Cabecera del modal -->
    <div
      class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700"
    >
      <h2
        class="flex items-center gap-2 text-xl font-semibold text-gray-900 dark:text-white"
      >
        <mat-icon class="text-blue-600 dark:text-blue-400">person_add</mat-icon>
        <span
          >Asignar Asesores al Supervisor: {{ selectedCoordinador?.nombre }}
          {{ selectedCoordinador?.apellido }}</span
        >
      </h2>
      <button
        class="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white transition-colors"
        (click)="closeAsignarAsesorModal()"
      >
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Información del coordinador -->
    <div
      class="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
    >
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300"
      >
        <mat-icon class="text-gray-600 dark:text-gray-400 text-base"
          >badge</mat-icon
        >
        <span>DNI: {{ selectedCoordinador?.dni }}</span>
      </div>
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300"
      >
        <mat-icon class="text-gray-600 dark:text-gray-400 text-base"
          >location_on</mat-icon
        >
        <span>Sede: {{ selectedCoordinador?.sede }}</span>
      </div>
      <div
        class="flex items-center gap-1.5 px-3 py-1.5 bg-blue-100 dark:bg-blue-900 rounded-full text-sm text-blue-800 dark:text-blue-300"
      >
        <mat-icon class="text-blue-700 dark:text-blue-400 text-base"
          >people</mat-icon
        >
        <span
          >Total Asesores:
          {{ selectedCoordinador?.asesores?.length || 0 }}</span
        >
      </div>
    </div>

    <!-- Cuerpo del modal -->
    <div class="p-6">
      <!-- Buscador -->
      <div class="mb-6">
        <div class="relative">
          <input
            type="text"
            class="w-full px-4 py-3 pr-10 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            [(ngModel)]="searchAsesorDisponible"
            (keyup)="filterAsesoresDisponibles()"
            placeholder="Buscar por DNI, nombre, apellido o email"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <mat-icon
              *ngIf="!searchAsesorDisponible"
              class="text-gray-400 dark:text-gray-500"
              >search</mat-icon
            >
            <button
              *ngIf="searchAsesorDisponible"
              class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
              (click)="resetFilterAsesoresDisponibles()"
            >
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Indicador de carga -->
      <div
        *ngIf="loadingAsesoresDisponibles"
        class="flex items-center mx-auto my-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md"
      >
        <mat-spinner [diameter]="24" color="primary" class="mr-3"></mat-spinner>
        <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
          Cargando asesores disponibles...
        </span>
      </div>

      <!-- Grid de asesores disponibles -->
      <div
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"
        *ngIf="
          !loadingAsesoresDisponibles && paginatedAsesoresDisponibles.length > 0
        "
      >
        <div
          *ngFor="let asesor of paginatedAsesoresDisponibles"
          class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200"
        >
          <!-- Cabecera de la tarjeta -->
          <div
            class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            <div
              class="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full text-blue-800 dark:text-blue-300 font-semibold"
            >
              {{ getInitials(asesor.nombre, asesor.apellido) }}
            </div>
            <div class="overflow-hidden">
              <div class="font-semibold text-gray-900 dark:text-white truncate">
                {{ asesor.nombre }} {{ asesor.apellido }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ asesor.dni }}
              </div>
            </div>
          </div>

          <!-- Cuerpo de la tarjeta -->
          <div class="p-4">
            <div class="flex items-center gap-2">
              <mat-icon class="text-gray-500 dark:text-gray-400"
                >email</mat-icon
              >
              <span
                class="text-sm text-gray-800 dark:text-gray-200 truncate"
                [matTooltip]="asesor.email"
                >{{ asesor.email }}</span
              >
            </div>
          </div>

          <!-- Pie de la tarjeta -->
          <div
            class="flex justify-end p-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700"
          >
            <button
              class="flex items-center gap-1.5 px-3 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded-md text-sm font-medium transition-colors"
              (click)="asignarAsesor(asesor.id)"
            >
              <mat-icon>person_add</mat-icon>
              <span>Asignar</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Paginador personalizado -->
      <div
        class="flex flex-wrap justify-between items-center gap-4 mt-4 mb-2"
        *ngIf="totalAsesoresDisponibles > 0"
      >
        <div class="text-sm text-gray-600 dark:text-gray-400">
          Mostrando {{ currentPageDisponibles * pageSize + 1 }} -
          {{
            currentPageDisponibles * pageSize + pageSize >
            totalAsesoresDisponibles
              ? totalAsesoresDisponibles
              : currentPageDisponibles * pageSize + pageSize
          }}
          de {{ totalAsesoresDisponibles }}
        </div>
        <div class="flex items-center gap-1">
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="currentPageDisponibles === 0"
            (click)="
              onPageChangeDisponibles({
                pageIndex: 0,
                pageSize: pageSize,
                length: totalAsesoresDisponibles
              })
            "
          >
            <mat-icon>first_page</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="currentPageDisponibles === 0"
            (click)="
              onPageChangeDisponibles({
                pageIndex: currentPageDisponibles - 1,
                pageSize: pageSize,
                length: totalAsesoresDisponibles
              })
            "
          >
            <mat-icon>chevron_left</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="
              currentPageDisponibles >=
              Math.ceil(totalAsesoresDisponibles / pageSize) - 1
            "
            (click)="
              onPageChangeDisponibles({
                pageIndex: currentPageDisponibles + 1,
                pageSize: pageSize,
                length: totalAsesoresDisponibles
              })
            "
          >
            <mat-icon>chevron_right</mat-icon>
          </button>
          <button
            class="flex items-center justify-center w-8 h-8 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            [disabled]="
              currentPageDisponibles >=
              Math.ceil(totalAsesoresDisponibles / pageSize) - 1
            "
            (click)="
              onPageChangeDisponibles({
                pageIndex: Math.ceil(totalAsesoresDisponibles / pageSize) - 1,
                pageSize: pageSize,
                length: totalAsesoresDisponibles
              })
            "
          >
            <mat-icon>last_page</mat-icon>
          </button>

          <select
            #pageSizeSelectDisponibles
            class="ml-2 px-3 py-1.5 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            [ngModel]="pageSize"
            (change)="onPageSizeChange(pageSizeSelectDisponibles.value)"
          >
            <option value="5">5 por página</option>
            <option value="10">10 por página</option>
            <option value="20">20 por página</option>
            <option value="40">40 por página</option>
          </select>
        </div>
      </div>

      <!-- Mensaje cuando no hay resultados de búsqueda -->
      <div
        class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
        *ngIf="totalAsesoresDisponibles === 0 && searchAsesorDisponible"
      >
        <mat-icon class="text-5xl text-gray-400 dark:text-gray-600 mb-4"
          >search_off</mat-icon
        >
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          No se encontraron asesores con los criterios de búsqueda especificados
        </p>
        <button
          class="flex items-center gap-1.5 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          (click)="resetFilterAsesoresDisponibles()"
        >
          <mat-icon>refresh</mat-icon>
          <span>Limpiar filtros</span>
        </button>
      </div>

      <!-- Mensaje cuando no hay asesores disponibles -->
      <div
        class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
        *ngIf="totalAsesoresDisponibles === 0 && !searchAsesorDisponible"
      >
        <mat-icon class="text-5xl text-gray-400 dark:text-gray-600 mb-4"
          >group_off</mat-icon
        >
        <p class="text-gray-600 dark:text-gray-400">
          No hay asesores disponibles para asignar
        </p>
      </div>

      <!-- Información de selección -->
      <div
        class="mt-4 text-sm text-gray-600 dark:text-gray-400"
        *ngIf="totalAsesoresDisponibles > 0"
      >
        Total de asesores disponibles: {{ totalAsesoresDisponibles }}
      </div>
    </div>

    <!-- Pie del modal -->
    <div
      class="flex justify-end gap-3 p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
    >
      <button
        class="px-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 text-sm font-medium transition-colors"
        (click)="closeAsignarAsesorModal()"
      >
        Cancelar
      </button>
      <button
        class="flex items-center gap-1.5 px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded text-sm font-medium transition-colors"
        (click)="finalizarAsignacion()"
      >
        <mat-icon>check</mat-icon>
        <span>Finalizar y Actualizar</span>
      </button>
    </div>
  </div>
</div>
