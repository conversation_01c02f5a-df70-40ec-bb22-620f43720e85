<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
  <!-- Header con título y acciones -->
  <div
    class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4"
  >
    <h1 class="text-2xl font-bold text-gray-800 dark:text-blue-100">
      <span class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-7 w-7 mr-2 text-indigo-600 dark:text-blue-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>
        Gestión de Notificaciones
      </span>
    </h1>

    <div class="flex flex-wrap gap-2">
      <button
        [disabled]="contadorNoLeidas === 0"
        (click)="marcarTodasComoLeidas()"
        class="inline-flex items-center px-3 py-2 sm:px-4 border border-transparent rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-indigo-600 dark:bg-blue-700 hover:bg-indigo-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span class="whitespace-nowrap">Marcar leídas</span>
      </button>

      <button
        (click)="abrirDialogoEnviarNotificacion()"
        class="inline-flex items-center px-3 py-2 sm:px-4 border border-transparent rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-600 dark:hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
          />
        </svg>
        <span class="whitespace-nowrap">Enviar</span>
      </button>

      <button
        (click)="cargarNotificaciones()"
        class="inline-flex items-center px-3 py-2 sm:px-4 border border-gray-300 dark:border-blue-900/20 rounded-md shadow-sm text-xs sm:text-sm font-medium text-gray-700 dark:text-blue-100 bg-white dark:bg-[#0a1628] hover:bg-gray-50 dark:hover:bg-blue-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-blue-400 transition-colors duration-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-gray-500 dark:text-blue-300"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        <span class="whitespace-nowrap">Actualizar</span>
      </button>
    </div>
  </div>

  <!-- Filtros -->
  <div class="mb-6 overflow-x-auto pb-2">
    <div class="inline-flex rounded-md shadow-sm">
      <button
        (click)="aplicarFiltro('todas')"
        class="px-4 py-2 text-sm font-medium border border-gray-300 dark:border-blue-500/20 rounded-l-md focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'todas'
            ? 'bg-indigo-50 dark:bg-blue-900/20 text-indigo-700 dark:text-blue-300 border-indigo-500 dark:border-blue-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        Todas
      </button>
      <button
        (click)="aplicarFiltro('no-leidas')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 focus:z-10 focus:outline-none transition-colors duration-200 relative"
        [ngClass]="
          filtroActual === 'no-leidas'
            ? 'bg-indigo-50 dark:bg-blue-900/20 text-indigo-700 dark:text-blue-300 border-indigo-500 dark:border-blue-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        No leídas
        <span
          *ngIf="contadorNoLeidas > 0"
          class="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 dark:bg-red-600 rounded-full"
          >{{ contadorNoLeidas }}</span
        >
      </button>
      <button
        (click)="aplicarFiltro('leidas')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'leidas'
            ? 'bg-indigo-50 dark:bg-blue-900/20 text-indigo-700 dark:text-blue-300 border-indigo-500 dark:border-blue-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        Leídas
      </button>
      <button
        (click)="aplicarFiltro('info')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'info'
            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-500 dark:border-blue-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        <span class="flex items-center">
          <span
            class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2"
          ></span>
          Info
        </span>
      </button>
      <button
        (click)="aplicarFiltro('exito')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'exito'
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-500 dark:border-green-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        <span class="flex items-center">
          <span
            class="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 mr-2"
          ></span>
          Éxito
        </span>
      </button>
      <button
        (click)="aplicarFiltro('advertencia')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'advertencia'
            ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border-yellow-500 dark:border-yellow-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        <span class="flex items-center">
          <span
            class="w-2 h-2 rounded-full bg-yellow-500 dark:bg-yellow-400 mr-2"
          ></span>
          Advertencia
        </span>
      </button>
      <button
        (click)="aplicarFiltro('error')"
        class="px-4 py-2 text-sm font-medium border-t border-b border-r border-gray-300 dark:border-blue-500/20 rounded-r-md focus:z-10 focus:outline-none transition-colors duration-200"
        [ngClass]="
          filtroActual === 'error'
            ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-500 dark:border-red-400'
            : 'bg-white dark:bg-[#0a1628] text-gray-700 dark:text-blue-100 hover:bg-gray-50 dark:hover:bg-blue-900/10'
        "
      >
        <span class="flex items-center">
          <span
            class="w-2 h-2 rounded-full bg-red-500 dark:bg-red-400 mr-2"
          ></span>
          Error
        </span>
      </button>
    </div>
  </div>

  <!-- Contenido -->
  <div class="space-y-4">
    <!-- Estado de carga -->
    <div
      *ngIf="cargando"
      class="flex flex-col items-center justify-center py-12"
    >
      <svg
        class="animate-spin h-10 w-10 text-indigo-600 dark:text-indigo-400 mb-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <p class="text-gray-600 dark:text-gray-300">Cargando notificaciones...</p>
    </div>

    <!-- Estado vacío -->
    <div
      *ngIf="!cargando && notificacionesFiltradas.length === 0"
      class="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-16 w-16 text-gray-400 dark:text-gray-500 mb-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
          d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <p class="text-lg font-medium">
        No hay notificaciones que coincidan con el filtro seleccionado
      </p>
      <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">
        Prueba con otro filtro o vuelve más tarde
      </p>
    </div>

    <!-- Lista de notificaciones -->
    <div
      *ngFor="let notificacion of notificacionesFiltradas"
      class="bg-white dark:bg-[#0a1628] rounded-lg shadow-sm border dark:border-blue-500/20 overflow-hidden transition-all duration-200 hover:shadow-md dark:hover:shadow-lg dark:hover:shadow-blue-900/20 transform hover:-translate-y-1"
      [ngClass]="{
        'border-l-4': !notificacion.leida,
        'border-indigo-500 dark:border-blue-400':
          !notificacion.leida && notificacion.tipo === 'INFO',
        'border-green-500 dark:border-green-400':
          !notificacion.leida && notificacion.tipo === 'EXITO',
        'border-yellow-500 dark:border-yellow-400':
          !notificacion.leida && notificacion.tipo === 'ADVERTENCIA',
        'border-red-500 dark:border-red-400':
          !notificacion.leida && notificacion.tipo === 'ERROR',
        'border-purple-500 dark:border-purple-400':
          !notificacion.leida && notificacion.tipo === 'SISTEMA'
      }"
      (click)="manejarClicNotificacion(notificacion)"
    >
      <div class="p-4">
        <div class="flex items-start">
          <!-- Icono según tipo -->
          <div class="flex-shrink-0 mr-4">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center text-white"
              [ngClass]="{
                'bg-blue-500 dark:bg-blue-600': notificacion.tipo === 'INFO',
                'bg-green-500 dark:bg-green-600': notificacion.tipo === 'EXITO',
                'bg-yellow-500 dark:bg-yellow-600':
                  notificacion.tipo === 'ADVERTENCIA',
                'bg-red-500 dark:bg-red-600': notificacion.tipo === 'ERROR',
                'bg-purple-500 dark:bg-purple-600':
                  notificacion.tipo === 'SISTEMA'
              }"
            >
              <svg
                *ngIf="notificacion.tipo === 'INFO'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                *ngIf="notificacion.tipo === 'EXITO'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                *ngIf="notificacion.tipo === 'ADVERTENCIA'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <svg
                *ngIf="notificacion.tipo === 'ERROR'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                *ngIf="notificacion.tipo === 'SISTEMA'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>

          <!-- Contenido -->
          <div class="flex-1 min-w-0">
            <div class="flex justify-between">
              <h3
                class="text-lg font-medium text-gray-900 dark:text-blue-100"
                [ngClass]="{ 'font-semibold': !notificacion.leida }"
              >
                {{ notificacion.titulo }}
              </h3>
              <button
                *ngIf="!notificacion.leida"
                (click)="marcarComoLeida(notificacion, $event)"
                class="ml-2 p-1 rounded-full text-gray-400 dark:text-blue-300/60 hover:text-indigo-600 dark:hover:text-blue-300 hover:bg-indigo-50 dark:hover:bg-blue-900/20 focus:outline-none"
                title="Marcar como leída"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </button>
            </div>
            <p
              class="text-sm text-gray-500 dark:text-blue-300/60 mt-1 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              {{ formatearFecha(notificacion.fechaCreacion) }}
            </p>

            <!-- Remitente de la notificación -->
            <div
              class="mt-1 text-xs text-gray-500 dark:text-blue-300/70 flex items-center"
            >
              <span class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-3 w-3 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                {{ notificacion.datos?.senderName || "Sistema" }}
              </span>
            </div>

            <p class="mt-2 text-gray-700 dark:text-blue-200/80">
              {{ notificacion.mensaje }}
            </p>

            <!-- Usuarios que han leído la notificación -->
            <div
              class="mt-3 pt-2 border-t border-gray-100 dark:border-blue-500/10"
            >
              <app-notification-readers
                [notificationId]="notificacion.id"
                [notificationTitle]="notificacion.titulo"
                [showCount]="true"
                [maxAvatars]="5"
              >
              </app-notification-readers>
            </div>
          </div>
        </div>

        <!-- Botón de acción si hay enlace -->
        <div *ngIf="notificacion.enlace" class="mt-4 flex justify-end">
          <button
            (click)="manejarClicNotificacion(notificacion)"
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 dark:text-blue-300 bg-indigo-100 dark:bg-blue-900/20 hover:bg-indigo-200 dark:hover:bg-blue-800/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-blue-400 transition-colors duration-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
            Ver detalles
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
