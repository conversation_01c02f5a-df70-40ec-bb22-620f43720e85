/**
 * Modelo que representa la respuesta de un usuario a una pregunta específica
 */
export interface DetalleRespuestaUsuario {
  id: number;
  respuestaUsuarioId: number;
  preguntaId: number;
  preguntaEnunciado: string;
  respuestaId: number;
  respuestaTexto: string;
  textoRespuesta: string;
  esCorrecta: boolean;
  puntajeObtenido: number;
  fechaCreacion: string;
  fechaActualizacion: string;
}
