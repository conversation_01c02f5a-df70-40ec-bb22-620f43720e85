<app-spinner *ngIf="loading$ | async"></app-spinner>

<mat-toolbar color="secondary">
  <span>Crear Nuevo Anuncio</span>
  <span class="flex-1"></span>
  <button mat-icon-button (click)="onClose()">
    <mat-icon>close</mat-icon>
  </button>
</mat-toolbar>

<section class="flex justify-center">
  <mat-card class="w-full max-w-[550px] mt-5 p-5 mx-auto md:mx-auto sm:mx-2.5">
      <form
        class="flex flex-col items-center w-full"
        #f = "ngForm"
        (ngSubmit) = "registrarAnuncio(f)"
      >

        <div class="flex justify-center">
              <div class="inline-flex flex-col items-center pb-5 w-[150px] h-[150px]">
                  <app-entity-photo class="mb-4" [photoURL]="photoLoaded"></app-entity-photo>
                  <button mat-button appFilesUpload (changed)="onFilesChanged($event)">
                      <mat-icon>upload</mat-icon>
                      Subir Imagen
                  </button>
              </div>
        </div>

        <mat-form-field class="w-full max-w-[500px] mb-2.5">
          <mat-label>Título del Anuncio</mat-label>
          <input
          type="text"
          ngModel
          name="titulo"
          required
          placeholder="Ingrese el título"
          matInput
          />
          <mat-error>Debe ingresar un título</mat-error>
        </mat-form-field>

        <mat-form-field class="w-full max-w-[500px]">
          <mat-label>Descripción</mat-label>
          <textarea
          ngModel
          name="descripcion"
          required
          placeholder="Ingrese la descripción"
          matInput
          cdkTextareaAutosize
          cdkAutosizeMinRows="4"
          cdkAutosizeMaxRows="10"
          class="resize-y overflow-auto leading-normal min-h-[80px] break-words whitespace-pre-wrap"
          ></textarea>
          <mat-error>Debe ingresar una descripción</mat-error>
        </mat-form-field>


        <mat-form-field class="w-full max-w-[500px] mb-2.5">
          <mat-label>Categoría</mat-label>
          <mat-select
          ngModel="INTERNO"
          name="categoria"
          required>
            <mat-option *ngFor="let cat of categorias" [value]="cat">{{cat}}</mat-option>
          </mat-select>
          <mat-error>Debe seleccionar una categoría</mat-error>
        </mat-form-field>

        <!-- Fechas de inicio y fin -->
        <div class="flex flex-col md:flex-row gap-4 w-full max-w-[500px]">
          <mat-form-field class="flex-1">
            <mat-label>Fecha de inicio</mat-label>
            <input matInput [matDatepicker]="pickerInicio" [ngModel]="fechaInicio" name="fechaInicio">
            <mat-datepicker-toggle matSuffix [for]="pickerInicio"></mat-datepicker-toggle>
            <mat-datepicker #pickerInicio></mat-datepicker>
          </mat-form-field>

          <mat-form-field class="flex-1">
            <mat-label>Fecha de fin</mat-label>
            <input matInput [matDatepicker]="pickerFin" [ngModel]="fechaFin" name="fechaFin">
            <mat-datepicker-toggle matSuffix [for]="pickerFin"></mat-datepicker-toggle>
            <mat-datepicker #pickerFin></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- Orden, Estado y Sede -->
        <div class="flex flex-col md:flex-row gap-4 w-full max-w-[500px]">
          <mat-form-field class="flex-1">
            <mat-label>Orden</mat-label>
            <input matInput type="number" ngModel="0" name="orden" min="0">
            <mat-hint>Orden de visualización (menor número = mayor prioridad)</mat-hint>
          </mat-form-field>

          <mat-form-field class="flex-1">
            <mat-label>Estado</mat-label>
            <mat-select ngModel="ACTIVO" name="estado">
              <mat-option *ngFor="let estado of estados" [value]="estado">{{estado}}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="flex-1">
            <mat-label>Sede</mat-label>
            <mat-select [ngModel]="selectedSedeId" name="sedeId">
              <mat-option [value]="null">Todas las sedes</mat-option>
              <mat-option *ngFor="let sede of sedes" [value]="sede.id">{{sede.nombre}}</mat-option>
            </mat-select>
            <mat-hint>Seleccione la sede a la que va dirigido el anuncio</mat-hint>
          </mat-form-field>
        </div>

        <div class="flex gap-4 justify-end mt-4 w-full">
          <button type="button" mat-button (click)="onClose()">Cancelar</button>
          <button type="submit" mat-raised-button color="primary" [disabled]="f.invalid">Publicar Anuncio</button>
        </div>

      </form>
  </mat-card>
</section>
