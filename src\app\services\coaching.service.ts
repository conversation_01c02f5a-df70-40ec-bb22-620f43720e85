import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class CoachingService {
  private baseUrl = 'https://apisozarusac.com/Coaching/api';

  constructor(private http: HttpClient) {}

  guardarFrase(data: any) {
    return this.http.post(`${this.baseUrl}/insertar-frase/`, data);
  }

  editarFrase(data: any) {
    return this.http.post(`${this.baseUrl}/editar-frase/`, data);
  }

  eliminarFrase(id: number) {
  return this.http.post(`${this.baseUrl}/eliminar-frase/`, { id });
}
}
