import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'arrayToDate'
})
export class ArrayToDatePipe implements PipeTransform {
  transform(value: any): Date | null {
    if (!value) {
      return null;
    }

    // Si ya es un objeto Date, devolverlo directamente
    if (value instanceof Date) {
      return value;
    }

    // Si es un string ISO, convertirlo a Date
    if (typeof value === 'string') {
      return new Date(value);
    }

    // Si es un array [año, mes, día, hora, minuto, segundo, nanosegundo]
    if (Array.isArray(value)) {
      try {
        // Ajustar el mes (en JavaScript los meses van de 0 a 11)
        const year = value[0];
        const month = value[1] - 1; // Restar 1 al mes
        const day = value[2];
        const hour = value.length > 3 ? value[3] : 0;
        const minute = value.length > 4 ? value[4] : 0;
        const second = value.length > 5 ? value[5] : 0;
        
        return new Date(year, month, day, hour, minute, second);
      } catch (error) {
        console.error('Error al convertir array a fecha:', error, value);
        return null;
      }
    }

    // Si no se puede convertir, devolver null
    return null;
  }
}
