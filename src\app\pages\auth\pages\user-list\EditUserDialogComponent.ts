import { Component, Inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { User } from '@app/models/backend/user';
import { FormControl, Validators } from '@angular/forms';
import { SedeService } from '@app/services/sede.service';
import { Sede } from '@app/models/backend/sede/sede.model';
import { Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';

@Component({
  selector: 'app-edit-user-dialog',
  templateUrl: './edit-user-dialog.component.html',
})
export class EditUserDialogComponent implements OnInit, OnDestroy {
  editingUser: User;
  hidePassword = true;
  password = new FormControl('', [Validators.minLength(7)]);
  changePassword = false;

  // Lista de sedes disponibles
  sedes: Sede[] = [];
  // Lista de roles disponibles
  roles: string[] = [];
  loading = false;

  // Suscripciones para limpiar al destruir el componente
  private subscriptions: Subscription[] = [];

  constructor(
    public dialogRef: MatDialogRef<EditUserDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: User,
    private sedeService: SedeService,
    private http: HttpClient
  ) {
    this.editingUser = { ...data };
    // Roles predeterminados en caso de que falle la carga desde el backend
    this.roles = [
      'ADMIN',
      'ASESOR',
      'BACKOFFICE',
      'BACKOFFICETRAMITADOR',
      'BACKOFFICESEGUIMIENTO',
      'COORDINADOR',
      'AUDITOR',
      'PROGRAMADOR'
    ];
  }

  ngOnInit(): void {
    // Cargar las sedes disponibles
    this.loadSedes();

    // Cargar los roles disponibles
    this.loadRoles();

    // Inicializar campos de sede si es necesario
    this.initializeSede();
  }

  // Cargar los roles disponibles desde el backend
  loadRoles(): void {
    this.http.get<string[]>(`${environment.url}api/user/roles`)
      .subscribe({
        next: (roles) => {
          if (roles && roles.length > 0) {
            this.roles = roles;
          }
        },
        error: (error) => {
          console.error('Error al cargar roles:', error);
          // Ya tenemos roles predeterminados definidos en el constructor
        }
      });
  }

  // Inicializar campos de sede
  initializeSede(): void {
    // Si el usuario viene con sedeId del backend, asignarlo a sede_id para el frontend
    if (this.editingUser.sedeId && !this.editingUser.sede_id) {
      this.editingUser.sede_id = this.editingUser.sedeId;
    } else if (this.editingUser.sede && this.editingUser.sede.id && !this.editingUser.sede_id && !this.editingUser.sedeId) {
      // Si el usuario tiene un objeto sede con id pero no tiene sede_id ni sedeId
      this.editingUser.sede_id = this.editingUser.sede.id;
    }

    // Eliminar campos redundantes
    delete this.editingUser.sedeNombre;
    delete this.editingUser.sede;
    delete this.editingUser.sedeId; // Solo mantenemos sede_id para el frontend
  }

  // Cargar todas las sedes
  loadSedes(): void {
    this.loading = true;

    const sedesSubscription = this.sedeService.getAllSedes().subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.sedes = response.data;
          // Ordenar alfabéticamente por nombre
          this.sedes.sort((a, b) => a.nombre.localeCompare(b.nombre));

          // No necesitamos buscar el nombre de la sede, solo asegurarnos de que sede_id sea correcto
          if (this.editingUser.sede_id) {
            // Verificar que la sede exista
            const sedeActual = this.sedes.find(sede => sede.id === this.editingUser.sede_id);
            if (!sedeActual) {
              // Si la sede no existe, limpiar el sede_id
              delete this.editingUser.sede_id;
            }
            // Eliminar campos redundantes
            delete this.editingUser.sedeNombre;
            delete this.editingUser.sede;
          }
        } else {
          this.sedes = [];
          console.error('Error al cargar sedes:', response.msg);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar sedes:', error);
        this.loading = false;
      }
    });

    this.subscriptions.push(sedesSubscription);
  }

  onSave(): void {
    // Si se ha marcado la opción de cambiar contraseña y la contraseña es válida
    if (this.changePassword && this.password.valid && this.password.value) {
      this.editingUser.password = this.password.value;
    } else if (!this.changePassword) {
      // Si no se quiere cambiar la contraseña, asegurarse de que no se envíe
      delete this.editingUser.password;
    }

    // Preparar el objeto para enviar al backend
    const userToSave: any = { ...this.editingUser };

    // Asegurarse de que se envíen los campos de sede correctamente
    if (this.editingUser.sede_id) {
      // Mantener solo el sede_id para la relación en la BD
      userToSave.sede_id = this.editingUser.sede_id;

      // Eliminar campos redundantes
      delete userToSave.sedeNombre;
      delete userToSave.sede;
      delete userToSave.sedeId; // Asegurarse de que no se envíe sedeId
    } else {
      // Si no hay sede seleccionada, asegurarse de que no se envíe
      delete userToSave.sede_id;
      delete userToSave.sedeNombre;
      delete userToSave.sede;
      delete userToSave.sedeId;
    }

    console.log('Enviando datos de usuario al backend:', JSON.stringify(userToSave, null, 2));
    console.log('Sede seleccionada ID:', this.editingUser.sede_id);

    this.dialogRef.close(userToSave);
  }

  // Método para actualizar la sede seleccionada
  onSedeChange(sedeId: number): void {
    // Guardar el ID de la sede
    this.editingUser.sede_id = sedeId;

    // Eliminar campos redundantes
    delete this.editingUser.sedeNombre;
    delete this.editingUser.sede;
    delete this.editingUser.sedeId; // Asegurarse de que no se use sedeId

    console.log('Sede seleccionada:', sedeId);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  validateNumberInput(event: KeyboardEvent): void {
    const charCode = event.key.charCodeAt(0);
    if (charCode < 48 || charCode > 57) {
      event.preventDefault(); // Bloquea cualquier carácter que no sea número
    }
  }

  ngOnDestroy(): void {
    // Limpiar todas las suscripciones al destruir el componente
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

}