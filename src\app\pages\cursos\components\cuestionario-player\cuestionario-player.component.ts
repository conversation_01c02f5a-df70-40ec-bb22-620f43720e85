import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CuestionarioService } from '@app/services/cuestionario.service';
import { RespuestaUsuarioService } from '@app/services/respuesta-usuario.service';
import { NotificationService } from '@app/services/notification/notification.service';
import { Cuestionario } from '@app/models/backend/curso/cuestionario.model';
import { Pregunta, TipoPregunta } from '@app/models/backend/curso/pregunta.model';
import { RespuestaUsuario, DetalleRespuestaUsuarioCreateRequest } from '@app/models/backend/curso/respuesta-usuario.model';
import { User } from '@app/models/backend/user';

@Component({
  selector: 'app-cuestionario-player',
  templateUrl: './cuestionario-player.component.html',
  styleUrls: ['./cuestionario-player.component.scss']
})
export class CuestionarioPlayerComponent implements OnInit, OnDestroy {
  @Input() leccionId!: number;
  @Input() user!: User | null;
  @Output() completed = new EventEmitter<boolean>();

  cuestionario: Cuestionario | null = null;
  respuestaUsuario: RespuestaUsuario | null = null;
  preguntaActual: Pregunta | null = null;
  preguntaIndex: number = 0;
  preguntas: Pregunta[] = [];

  // Para controlar el estado del cuestionario
  loading: boolean = true;
  error: string | null = null;
  iniciado: boolean = false;
  finalizado: boolean = false;
  puedeIntentar: boolean = true;

  // Para el temporizador
  tiempoRestante: number = 0;
  tiempoTotal: number = 0;
  tiempoInterval: any;

  // Para el formulario de respuestas
  respuestaForm!: FormGroup;

  // Enum para acceder desde el template
  TipoPregunta = TipoPregunta;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private cuestionarioService: CuestionarioService,
    private respuestaUsuarioService: RespuestaUsuarioService,
    private notification: NotificationService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.cargarCuestionario();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.detenerTemporizador();
  }

  private initForm(): void {
    this.respuestaForm = this.fb.group({
      respuestaId: [null],
      respuestasIds: [[]],
      textoRespuesta: [''],
      verdaderoFalso: [null]
    });
  }

  private cargarCuestionario(): void {
    this.loading = true;
    this.error = null;

    this.cuestionarioService.getCuestionarioByLeccionId(this.leccionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.cuestionario = response.data;
            this.verificarSiPuedeIntentar();
          } else {
            this.error = response.msg || 'Error al cargar el cuestionario';
            this.notification.error(this.error);
            this.loading = false;
          }
        },
        error: (error) => {
          this.error = 'Error al cargar el cuestionario. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al cargar cuestionario:', error);
        }
      });
  }

  private verificarSiPuedeIntentar(): void {
    if (!this.cuestionario || !this.user) {
      this.loading = false;
      return;
    }

    this.respuestaUsuarioService.puedeIntentarCuestionario(this.user.id, this.cuestionario.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.puedeIntentar = response.data;

            // Si no puede intentar, cargar el mejor intento
            if (!this.puedeIntentar) {
              this.cargarMejorIntento();
            }
          } else {
            this.error = response.msg || 'Error al verificar si puede intentar el cuestionario';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al verificar si puede intentar el cuestionario';
          this.notification.error(this.error);
          console.error('Error al verificar si puede intentar:', error);
        }
      });
  }

  private cargarMejorIntento(): void {
    if (!this.cuestionario || !this.user) {
      return;
    }

    this.loading = true;

    this.respuestaUsuarioService.getMejorIntentoByUsuarioAndCuestionario(this.user.id, this.cuestionario.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            // Hay un intento previo
            this.respuestaUsuario = response.data;
            this.finalizado = true;
          } else if (response.msg?.includes('No hay intentos previos')) {
            // No hay intentos previos, no mostrar error
            console.log('No hay intentos previos para este cuestionario');
          } else {
            // Otro tipo de error
            this.error = response.msg || 'Error al cargar el mejor intento';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;

          // Verificar si el error es porque no hay intentos previos
          if (error?.error?.msg?.includes('No hay intentos previos') ||
              error?.error?.msg?.includes('RESPUESTA_USUARIO_NOT_FOUND') ||
              error?.status === 500) {
            // No hay intentos previos, no mostrar error
            console.log('No hay intentos previos para este cuestionario');
          } else {
            // Otro tipo de error
            this.error = 'Error al cargar el mejor intento';
            this.notification.error(this.error);
            console.error('Error al cargar mejor intento:', error);
          }
        }
      });
  }

  iniciarCuestionario(): void {
    if (!this.cuestionario || !this.user) {
      return;
    }

    this.loading = true;

    const data = {
      usuarioId: this.user.id,
      cuestionarioId: this.cuestionario.id
    };

    this.respuestaUsuarioService.iniciarCuestionario(data)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.respuestaUsuario = response.data;
            this.iniciado = true;

            // Preparar las preguntas
            if (this.cuestionario?.preguntas) {
              this.preguntas = [...this.cuestionario.preguntas];

              // Aleatorizar preguntas si está configurado
              if (this.cuestionario.aleatorizarPreguntas) {
                this.preguntas = this.preguntas.sort(() => Math.random() - 0.5);
              } else {
                // Ordenar por el campo orden
                this.preguntas = this.preguntas.sort((a, b) => a.orden - b.orden);
              }

              // Mostrar la primera pregunta
              if (this.preguntas.length > 0) {
                this.preguntaActual = this.preguntas[0];
                this.preguntaIndex = 0;
              }
            }

            // Iniciar temporizador si hay tiempo límite
            if (this.cuestionario?.tiempoLimite && this.cuestionario.tiempoLimite > 0) {
              this.tiempoTotal = this.cuestionario.tiempoLimite * 60; // Convertir a segundos
              this.tiempoRestante = this.tiempoTotal;
              this.iniciarTemporizador();
            }

            this.loading = false;
          } else {
            this.error = response.msg || 'Error al iniciar el cuestionario';
            this.notification.error(this.error);
            this.loading = false;
          }
        },
        error: (error) => {
          this.error = 'Error al iniciar el cuestionario';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al iniciar cuestionario:', error);
        }
      });
  }

  responderPregunta(): void {
    if (!this.preguntaActual || !this.respuestaUsuario) {
      return;
    }

    // Validar que se haya seleccionado una respuesta
    if (this.preguntaActual.tipo === TipoPregunta.OPCION_MULTIPLE && !this.respuestaForm.value.respuestaId) {
      this.notification.error('Debe seleccionar una respuesta');
      return;
    }

    if (this.preguntaActual.tipo === TipoPregunta.SELECCION_MULTIPLE &&
        (!this.respuestaForm.value.respuestasIds || this.respuestaForm.value.respuestasIds.length === 0)) {
      this.notification.error('Debe seleccionar al menos una respuesta');
      return;
    }

    if (this.preguntaActual.tipo === TipoPregunta.VERDADERO_FALSO && this.respuestaForm.value.verdaderoFalso === null) {
      this.notification.error('Debe seleccionar Verdadero o Falso');
      return;
    }

    if (this.preguntaActual.tipo === TipoPregunta.TEXTO_LIBRE &&
        (!this.respuestaForm.value.textoRespuesta || this.respuestaForm.value.textoRespuesta.trim() === '')) {
      this.notification.error('Debe ingresar una respuesta');
      return;
    }

    this.loading = true;

    // Preparar los datos según el tipo de pregunta
    let data: DetalleRespuestaUsuarioCreateRequest;

    switch (this.preguntaActual.tipo) {
      case TipoPregunta.OPCION_MULTIPLE:
        data = {
          respuestaUsuarioId: this.respuestaUsuario.id,
          preguntaId: this.preguntaActual.id,
          respuestaId: this.respuestaForm.value.respuestaId
        };
        break;
      case TipoPregunta.SELECCION_MULTIPLE:
        // Para selección múltiple, enviamos la primera respuesta y luego las demás
        if (this.respuestaForm.value.respuestasIds.length > 0) {
          data = {
            respuestaUsuarioId: this.respuestaUsuario.id,
            preguntaId: this.preguntaActual.id,
            respuestaId: this.respuestaForm.value.respuestasIds[0]
          };
        } else {
          this.notification.error('Debe seleccionar al menos una respuesta');
          this.loading = false;
          return;
        }
        break;
      case TipoPregunta.VERDADERO_FALSO:
        // Buscar la respuesta correspondiente (Verdadero o Falso)
        const respuestaVF = this.preguntaActual.respuestas.find(r =>
          (r.esCorrecta && this.respuestaForm.value.verdaderoFalso === true) ||
          (!r.esCorrecta && this.respuestaForm.value.verdaderoFalso === false)
        );

        if (respuestaVF) {
          data = {
            respuestaUsuarioId: this.respuestaUsuario.id,
            preguntaId: this.preguntaActual.id,
            respuestaId: respuestaVF.id
          };
        } else {
          this.notification.error('Error al procesar la respuesta');
          this.loading = false;
          return;
        }
        break;
      case TipoPregunta.TEXTO_LIBRE:
        data = {
          respuestaUsuarioId: this.respuestaUsuario.id,
          preguntaId: this.preguntaActual.id,
          textoRespuesta: this.respuestaForm.value.textoRespuesta
        };
        break;
      default:
        this.notification.error('Tipo de pregunta no soportado');
        this.loading = false;
        return;
    }

    // Enviar la respuesta
    this.respuestaUsuarioService.responderPregunta(this.respuestaUsuario.id, data)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.respuestaUsuario = response.data;

            // Si es selección múltiple y hay más respuestas, enviarlas
            if (this.preguntaActual?.tipo === TipoPregunta.SELECCION_MULTIPLE &&
                this.respuestaForm.value.respuestasIds.length > 1) {

              // Enviar las demás respuestas
              this.enviarRespuestasMultiples(1);
            } else {
              // Pasar a la siguiente pregunta o finalizar
              this.siguientePregunta();
            }
          } else {
            this.error = response.msg || 'Error al responder la pregunta';
            this.notification.error(this.error);
            this.loading = false;
          }
        },
        error: (error) => {
          this.error = 'Error al responder la pregunta';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al responder pregunta:', error);
        }
      });
  }

  private enviarRespuestasMultiples(index: number): void {
    if (!this.preguntaActual || !this.respuestaUsuario ||
        index >= this.respuestaForm.value.respuestasIds.length) {
      // Pasar a la siguiente pregunta cuando se han enviado todas las respuestas
      this.siguientePregunta();
      return;
    }

    const data: DetalleRespuestaUsuarioCreateRequest = {
      respuestaUsuarioId: this.respuestaUsuario.id,
      preguntaId: this.preguntaActual.id,
      respuestaId: this.respuestaForm.value.respuestasIds[index]
    };

    this.respuestaUsuarioService.responderPregunta(this.respuestaUsuario.id, data)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.respuestaUsuario = response.data;

            // Enviar la siguiente respuesta
            this.enviarRespuestasMultiples(index + 1);
          } else {
            this.error = response.msg || 'Error al responder la pregunta';
            this.notification.error(this.error);
            this.loading = false;
          }
        },
        error: (error) => {
          this.error = 'Error al responder la pregunta';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al responder pregunta:', error);
        }
      });
  }

  siguientePregunta(): void {
    // Limpiar el formulario
    this.respuestaForm.reset();

    // Verificar si hay más preguntas
    if (this.preguntaIndex < this.preguntas.length - 1) {
      this.preguntaIndex++;
      this.preguntaActual = this.preguntas[this.preguntaIndex];
      this.loading = false;
    } else {
      // Si no hay más preguntas, finalizar el cuestionario
      this.finalizarCuestionario();
    }
  }

  finalizarCuestionario(): void {
    if (!this.respuestaUsuario) {
      return;
    }

    this.loading = true;

    this.respuestaUsuarioService.finalizarCuestionario(this.respuestaUsuario.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.respuestaUsuario = response.data;
            this.finalizado = true;
            this.detenerTemporizador();
            this.loading = false;

            // Emitir evento de completado
            this.completed.emit(this.respuestaUsuario.aprobado);

            // Mostrar mensaje según resultado
            if (this.respuestaUsuario.aprobado) {
              this.notification.success('¡Felicidades! Has aprobado el cuestionario');
            } else {
              this.notification.error('No has aprobado el cuestionario. Puedes intentarlo nuevamente si tienes intentos disponibles.');
            }
          } else {
            this.error = response.msg || 'Error al finalizar el cuestionario';
            this.notification.error(this.error);
            this.loading = false;
          }
        },
        error: (error) => {
          this.error = 'Error al finalizar el cuestionario';
          this.notification.error(this.error);
          this.loading = false;
          console.error('Error al finalizar cuestionario:', error);
        }
      });
  }

  private iniciarTemporizador(): void {
    this.tiempoInterval = setInterval(() => {
      this.tiempoRestante--;

      if (this.tiempoRestante <= 0) {
        this.detenerTemporizador();
        this.finalizarCuestionario();
      }
    }, 1000);
  }

  private detenerTemporizador(): void {
    if (this.tiempoInterval) {
      clearInterval(this.tiempoInterval);
    }
  }

  // Métodos para el template

  formatTiempo(segundos: number): string {
    const minutos = Math.floor(segundos / 60);
    const segs = segundos % 60;
    return `${minutos.toString().padStart(2, '0')}:${segs.toString().padStart(2, '0')}`;
  }

  getPorcentajeProgreso(): number {
    return (this.preguntaIndex / this.preguntas.length) * 100;
  }

  getPorcentajeTiempo(): number {
    return (this.tiempoRestante / this.tiempoTotal) * 100;
  }

  getColorTiempo(): string {
    const porcentaje = this.getPorcentajeTiempo();
    if (porcentaje > 50) {
      return 'primary';
    } else if (porcentaje > 25) {
      return 'accent';
    } else {
      return 'warn';
    }
  }

  reiniciarCuestionario(): void {
    this.iniciado = false;
    this.finalizado = false;
    this.preguntaIndex = 0;
    this.preguntaActual = null;
    this.respuestaUsuario = null;
    this.respuestaForm.reset();
    this.detenerTemporizador();
    this.verificarSiPuedeIntentar();
  }
}
