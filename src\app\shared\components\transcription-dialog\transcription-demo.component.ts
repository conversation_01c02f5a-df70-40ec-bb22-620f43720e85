import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './transcription-dialog.component';

@Component({
  selector: 'app-transcription-demo',
  template: `
    <div class="p-8 max-w-4xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
          <mat-icon class="text-4xl mr-2 align-middle">record_voice_over</mat-icon>
          Transcripción de Audio con IA
        </h1>
        <p class="text-gray-600 text-lg">
          Convierte tus archivos de audio en texto usando inteligencia artificial
        </p>
      </div>

      <!-- Botones de ejemplo -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        
        <!-- Botón principal -->
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
          <div class="text-center mb-4">
            <mat-icon class="text-5xl text-indigo-500 mb-2">audiotrack</mat-icon>
            <h3 class="text-xl font-semibold text-gray-800">Transcripción Básica</h3>
            <p class="text-gray-600 text-sm">Sube un archivo y obtén la transcripción</p>
          </div>
          <button 
            type="button"
            mat-raised-button 
            color="primary" 
            (click)="openBasicTranscription()"
            class="w-full py-3 text-lg">
            <mat-icon class="mr-2">upload</mat-icon>
            Subir Audio
          </button>
        </div>

        <!-- Botón con cliente -->
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
          <div class="text-center mb-4">
            <mat-icon class="text-5xl text-green-500 mb-2">person</mat-icon>
            <h3 class="text-xl font-semibold text-gray-800">Con Datos de Cliente</h3>
            <p class="text-gray-600 text-sm">Transcripción asociada a un cliente</p>
          </div>
          <button 
            type="button"
            mat-raised-button 
            color="accent" 
            (click)="openClientTranscription()"
            class="w-full py-3 text-lg">
            <mat-icon class="mr-2">business</mat-icon>
            Transcribir para Cliente
          </button>
        </div>
      </div>

      <!-- Información -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
          <mat-icon class="mr-2">info</mat-icon>
          Características
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div class="flex items-center">
            <mat-icon class="text-sm mr-2">check_circle</mat-icon>
            Múltiples formatos de audio
          </div>
          <div class="flex items-center">
            <mat-icon class="text-sm mr-2">check_circle</mat-icon>
            Análisis de estado de ánimo
          </div>
          <div class="flex items-center">
            <mat-icon class="text-sm mr-2">check_circle</mat-icon>
            Detección automática de idioma
          </div>
          <div class="flex items-center">
            <mat-icon class="text-sm mr-2">check_circle</mat-icon>
            Guardado automático en cliente
          </div>
        </div>
      </div>

      <!-- Formatos soportados -->
      <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-green-800 mb-3 flex items-center">
          <mat-icon class="mr-2">audio_file</mat-icon>
          Formatos Soportados
        </h3>
        <div class="flex flex-wrap gap-2 mb-3">
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">MP3</span>
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">WAV</span>
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">M4A</span>
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">OGG</span>
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">FLAC</span>
        </div>
        <p class="text-green-700 text-sm">
          <mat-icon class="text-sm mr-1">storage</mat-icon>
          Tamaño máximo: 100MB por archivo
        </p>
      </div>
    </div>
  `,
  styles: [`
    .grid {
      display: grid;
    }
    .grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    @media (min-width: 768px) {
      .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
    }
    .gap-6 {
      gap: 1.5rem;
    }
    .gap-4 {
      gap: 1rem;
    }
    .gap-2 {
      gap: 0.5rem;
    }
  `]
})
export class TranscriptionDemoComponent {

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  /**
   * Abre el modal de transcripción básico
   */
  openBasicTranscription(): void {
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true
    };

    this.openTranscriptionDialog(dialogData, 'Transcripción básica iniciada');
  }

  /**
   * Abre el modal de transcripción con datos de cliente
   */
  openClientTranscription(): void {
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true,
      cliente: {
        nombres: 'Juan Carlos',
        apellidos: 'Pérez García'
      },
      numeroMovil: '987654321'
    };

    this.openTranscriptionDialog(dialogData, 'Transcripción para cliente iniciada');
  }

  /**
   * Método común para abrir el modal de transcripción
   */
  private openTranscriptionDialog(data: TranscriptionDialogData, successMessage: string): void {
    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '95vw',
      maxWidth: '900px',
      height: '95vh',
      maxHeight: '800px',
      disableClose: false,
      data: data,
      panelClass: 'transcription-dialog-panel'
    });

    // Mostrar mensaje de inicio
    this.snackBar.open(successMessage, 'Cerrar', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });

    // Manejar el resultado
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Transcripción completada exitosamente:', result);
        
        this.snackBar.open('¡Transcripción completada exitosamente!', 'Ver', {
          duration: 5000,
          panelClass: ['success-snackbar']
        }).onAction().subscribe(() => {
          // Aquí puedes abrir una vista para mostrar los resultados
          console.log('Usuario quiere ver los resultados:', result);
        });
        
      } else if (result === false) {
        console.log('❌ Transcripción cancelada por el usuario');
        
        this.snackBar.open('Transcripción cancelada', 'Cerrar', {
          duration: 3000,
          panelClass: ['warning-snackbar']
        });
      }
    });
  }
}
