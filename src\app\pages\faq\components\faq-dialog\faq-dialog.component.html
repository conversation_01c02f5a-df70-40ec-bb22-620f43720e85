<div class="flex justify-between items-center p-4 border-b border-gray-200 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <h2 class="text-lg font-semibold text-blue-600 dark:text-blue-400">{{ data.title }}</h2>
  <button class="w-8 h-8 flex items-center justify-center rounded-full text-gray-500 hover:bg-gray-200 hover:text-red-500 transition-colors dark:text-gray-300 dark:hover:bg-gray-700" (click)="onCancel()">
    <i class="fa fa-times"></i>
  </button>
</div>

<div class="p-5 max-h-[65vh] overflow-y-auto">
  <form [formGroup]="faqForm" autocomplete="off">
    <!-- Campos solo visibles en modo normal (no respuesta) -->
    <ng-container *ngIf="!data.isResponseMode">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <!-- Tipo de usuario -->
        <div class="mb-4 md:mb-0">
          <label class="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">Tipo de usuario</label>
          <select
            formControlName="tipoUsuario"
            class="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="">Seleccione un tipo...</option>
            <option *ngFor="let tipo of tiposUsuario" [value]="tipo.value">
              {{ tipo.viewValue }}
            </option>
          </select>
          <div
            class="mt-1 text-sm text-red-600 dark:text-red-400"
            *ngIf="
              faqForm.get('tipoUsuario')?.hasError('required') &&
              faqForm.get('tipoUsuario')?.touched
            "
          >
            Debe seleccionar un tipo de usuario
          </div>
        </div>

        <!-- Categoría -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">Categoría</label>
          <select
            formControlName="categoria"
            class="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="">Seleccione una categoría...</option>
            <option value="Acceso">Acceso</option>
            <option value="Perfil">Perfil</option>
            <option value="Ventas">Ventas</option>
            <option value="Clientes">Clientes</option>
            <option value="Sistema">Sistema</option>
            <option value="Otros">Otros</option>
          </select>
        </div>
      </div>

      <!-- Pregunta -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">Pregunta</label>
        <textarea
          class="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          formControlName="pregunta"
          rows="3"
          placeholder="Ingrese su pregunta..."
        ></textarea>
        <div
          class="mt-1 text-sm text-red-600 dark:text-red-400"
          *ngIf="
            faqForm.get('pregunta')?.hasError('required') &&
            faqForm.get('pregunta')?.touched
          "
        >
          La pregunta es requerida
        </div>
      </div>
    </ng-container>

    <!-- En modo respuesta, mostrar la pregunta como texto de solo lectura -->
    <div *ngIf="data.isResponseMode" class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
      <h4 class="text-base font-semibold text-blue-600 mb-2 dark:text-blue-400">Pregunta:</h4>
      <p class="text-gray-700 mb-3 leading-relaxed dark:text-gray-300">{{ data.faq?.pregunta }}</p>
      <div class="flex flex-wrap gap-2 text-sm">
        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium dark:bg-blue-900/30 dark:text-blue-300" *ngIf="data.faq?.categoria">
          {{ data.faq?.categoria }}
        </span>
        <span class="flex items-center text-gray-600 dark:text-gray-400" *ngIf="data.faq?.usuarioPreguntaNombre">
          <i class="fa fa-user mr-1 text-xs"></i> {{ data.faq?.usuarioPreguntaNombre }}
        </span>
        <span class="flex items-center text-gray-600 dark:text-gray-400" *ngIf="data.faq?.createdAt">
          <i class="fa fa-calendar mr-1 text-xs"></i> {{ data.faq?.createdAt || "" | dateArray | date : "dd/MM/yyyy HH:mm" }}
        </span>
      </div>
    </div>

    <!-- Respuesta -->
    <div class="mb-4 border border-gray-200 rounded-lg overflow-hidden dark:border-gray-700">
      <div class="bg-gray-50 py-2 px-3 text-sm font-medium text-blue-600 border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700 dark:text-blue-400">
        Respuesta
      </div>

      <!-- Barra de herramientas del editor -->
      <div class="flex flex-wrap items-center gap-1 p-2 bg-gray-100 border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-bold"></i>
        </button>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-italic"></i>
        </button>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-underline"></i>
        </button>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-strikethrough"></i>
        </button>
        <div class="h-4 w-px bg-gray-300 mx-1 dark:bg-gray-600"></div>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-code"></i>
        </button>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-quote-right"></i>
        </button>
        <div class="h-4 w-px bg-gray-300 mx-1 dark:bg-gray-600"></div>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-list-ul"></i>
        </button>
        <button type="button" class="p-1.5 text-gray-600 hover:bg-gray-200 rounded dark:text-gray-400 dark:hover:bg-gray-700">
          <i class="fa fa-list-ol"></i>
        </button>
      </div>

      <!-- Campo de texto para la respuesta -->
      <div class="p-2">
        <textarea
          id="respuesta"
          class="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          formControlName="respuesta"
          rows="6"
          placeholder="Escriba aquí..."
        ></textarea>
      </div>
    </div>

    <!-- Opciones adicionales (solo en modo normal) -->
    <div class="flex flex-wrap gap-6 mb-4" *ngIf="!data.isResponseMode">
      <div class="flex items-center">
        <input
          type="checkbox"
          class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          id="esPublica"
          formControlName="esPublica"
        />
        <label class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300" for="esPublica">Pública</label>
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          id="respondida"
          formControlName="respondida"
        />
        <label class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300" for="respondida">Respondida</label>
      </div>
    </div>

    <!-- Archivos -->
    <div class="mb-4 border border-gray-200 rounded-lg overflow-hidden dark:border-gray-700">
      <div class="flex justify-between items-center bg-gray-50 py-2 px-3 border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Archivos Seleccionados ({{ uploadFiles.length }})</span>
        <button
          type="button"
          class="p-1 text-gray-500 hover:bg-gray-200 rounded-full dark:text-gray-400 dark:hover:bg-gray-700"
          (click)="toggleArchivosCollapsed()"
        >
          <i
            class="fa"
            [ngClass]="{
              'fa-chevron-down': archivosCollapsed,
              'fa-chevron-up': !archivosCollapsed
            }"
          ></i>
        </button>
      </div>

      <div [class.hidden]="archivosCollapsed">
        <div
          class="p-6 bg-gray-50 border-b border-gray-200 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
          (click)="openFilesUpload()"
          (dragover)="$event.preventDefault()"
          (drop)="handleFileDrop($event)"
        >
          <div class="text-blue-500 text-3xl mb-2 dark:text-blue-400">
            <i class="fa fa-cloud-upload"></i>
          </div>
          <div class="text-gray-600 text-sm dark:text-gray-400">
            Suelte los archivos aquí o haga clic para cargarlos.
          </div>
        </div>

        <!-- Lista de archivos -->
        <div class="divide-y divide-gray-200 dark:divide-gray-700" *ngIf="uploadFiles.length > 0">
          <div class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700" *ngFor="let file of uploadFiles">
            <div class="mr-3 text-xl"
              [ngClass]="{
                'text-green-500 dark:text-green-400': file.fileType === 'image',
                'text-blue-500 dark:text-blue-400': file.fileType === 'video',
                'text-yellow-500 dark:text-yellow-400': file.fileType === 'audio',
                'text-red-500 dark:text-red-400': file.fileType === 'document',
                'text-gray-500 dark:text-gray-400': file.fileType === 'other'
              }"
            >
              <i
                class="fa"
                [ngClass]="{
                  'fa-file-image-o': file.fileType === 'image',
                  'fa-file-video-o': file.fileType === 'video',
                  'fa-file-audio-o': file.fileType === 'audio',
                  'fa-file-text-o': file.fileType === 'document',
                  'fa-file-o': file.fileType === 'other'
                }"
              ></i>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-800 truncate dark:text-gray-200">{{ file.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400" *ngIf="file.size">
                {{ formatFileSize(file.size) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div class="flex justify-end items-center gap-3 p-4 border-t border-gray-200 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <button
    class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
    (click)="onCancel()"
  >
    Cerrar
  </button>
  <button
    class="px-4 py-2 text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-600"
    (click)="saveData()"
  >
    <ng-container *ngIf="data.isResponseMode; else normalButton">Responder</ng-container>
    <ng-template #normalButton>{{ isNewData ? "Guardar" : "Actualizar" }}</ng-template>
  </button>
</div>
