/**
 * Modelo que representa una respuesta a una pregunta
 */
export interface Respuesta {
  id: number;
  texto: string;
  esCorrecta: boolean;
  orden: number;
  preguntaId: number;
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
}

/**
 * Modelo para crear una nueva respuesta
 */
export interface RespuestaCreateRequest {
  texto: string;
  esCorrecta: boolean;
  orden: number;
  preguntaId: number;
}

/**
 * Modelo para actualizar una respuesta existente
 */
export interface RespuestaUpdateRequest {
  texto?: string;
  esCorrecta?: boolean;
  orden?: number;
  estado?: string;
}
