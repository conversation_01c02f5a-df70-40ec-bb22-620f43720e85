import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SidenavStateService {
  private sidenavOpenSubject = new BehaviorSubject<boolean>(true);
  public sidenavOpen$: Observable<boolean> = this.sidenavOpenSubject.asObservable();

  constructor() {
    // Inicializar el estado basado en el tamaño de la pantalla
    const isMobile = window.innerWidth <= 768;
    this.sidenavOpenSubject.next(!isMobile);
    
    // Escuchar cambios en el tamaño de la ventana
    window.addEventListener('resize', () => {
      const isMobile = window.innerWidth <= 768;
      if (isMobile) {
        this.sidenavOpenSubject.next(false);
      }
    });
  }

  toggleSidenav(): void {
    this.sidenavOpenSubject.next(!this.sidenavOpenSubject.value);
  }

  setSidenavState(isOpen: boolean): void {
    this.sidenavOpenSubject.next(isOpen);
  }

  getSidenavState(): boolean {
    return this.sidenavOpenSubject.value;
  }
}
