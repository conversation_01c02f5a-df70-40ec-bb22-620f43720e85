import { Component, OnInit, Inject, ElementRef, ViewChild, ChangeDetectorRef, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Faq } from '@app/models/backend/faq/faq.model';
import { FileFaq } from '@app/models/backend/faq/file-faq.model';
import { Store } from '@ngrx/store';
import { FaqState } from '../../store/faq.reducer';
import * as FaqActions from '../../store/faq.actions';
import { MatDialog } from '@angular/material/dialog';
import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { FaqService } from '@app/services/faq/faq.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-faq-dialog',
  templateUrl: './faq-dialog.component.html'
})
export class FaqDialogComponent implements OnInit, OnDestroy {
  faqForm: FormGroup;
  isNewData: boolean = true;
  submitted: boolean = false;
  uploadFiles: FileFaq[] = [];
  archivosCollapsed: boolean = false;
  isDarkTheme: boolean = false;
  private observer: MutationObserver;

  // Tipos de usuario disponibles
  tiposUsuario = [
    { value: 'ADMIN', viewValue: 'ADMIN' },
    { value: 'AUDITOR', viewValue: 'AUDITOR' },
    { value: 'BACKOFFICE', viewValue: 'BACKOFFICE' },
    { value: 'COORDINADOR', viewValue: 'COORDINADOR' },
    { value: 'ASESOR', viewValue: 'ASESOR' },
    { value: 'Invitado', viewValue: 'Invitado' },
    { value: 'DESARROLLADOR', viewValue: 'DESARROLLADOR' }
  ];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<FaqDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string; faq?: Faq; isResponseMode?: boolean },
    private store: Store<{ faq: FaqState }>,
    private dialog: MatDialog,
    private faqService: FaqService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.faqForm = this.fb.group({
      tipoUsuario: ['', Validators.required],
      categoria: [''],
      pregunta: ['', Validators.required],
      respuesta: [''],
      esPublica: [true],
      respondida: [false],
      archivos: [[]]
    });
  }

  ngOnInit(): void {
    console.log('FaqDialogComponent - ngOnInit - data:', this.data);

    // Verificar si el tema oscuro está activo
    this.checkDarkTheme();

    // Observar cambios en el tema
    this.observeThemeChanges();

    // Inicializar el formulario con valores por defecto
    this.faqForm.patchValue({
      tipoUsuario: '',
      categoria: '',
      pregunta: '',
      respuesta: '',
      esPublica: true,
      respondida: false,
      archivos: []
    });

    // Si estamos editando o respondiendo, cargar los datos existentes
    if (this.data && this.data.faq) {
      this.isNewData = false;
      console.log('FaqDialogComponent - ngOnInit - Editando FAQ:', this.data.faq);

      // Crear un objeto con solo las propiedades que necesitamos para el formulario
      const formData = {
        tipoUsuario: this.data.faq.tipoUsuario || '',
        categoria: this.data.faq.categoria || '',
        pregunta: this.data.faq.pregunta || '',
        respuesta: this.data.faq.respuesta || '',
        esPublica: this.data.faq.esPublica !== undefined ? this.data.faq.esPublica : true,
        respondida: this.data.faq.respondida !== undefined ? this.data.faq.respondida : false
      };

      this.faqForm.patchValue(formData);

      if (this.data.faq.archivos && Array.isArray(this.data.faq.archivos)) {
        this.uploadFiles = [...this.data.faq.archivos];
      }

      // Si estamos en modo respuesta, configurar el formulario adecuadamente
      if (this.data.isResponseMode) {
        console.log('Modo respuesta activado');
        // Deshabilitar campos que no deben modificarse en modo respuesta
        this.faqForm.get('pregunta')?.disable();
        this.faqForm.get('tipoUsuario')?.disable();
        this.faqForm.get('categoria')?.disable();
        this.faqForm.get('esPublica')?.disable();

        // Enfocar en el campo de respuesta
        setTimeout(() => {
          const respuestaElement = document.getElementById('respuesta');
          if (respuestaElement) {
            respuestaElement.focus();
          }
        }, 300);

        // Marcar como respondida
        this.faqForm.patchValue({ respondida: true });
      }
    }

    // Suscribirse a errores del store
    this.store.select(state => state.faq.error)
      .subscribe(error => {
        if (error) {
          console.error('Error en el store:', error);
          const errorMessage = typeof error === 'object' && error !== null && 'message' in error
            ? error.message
            : 'Ocurrió un error al procesar la solicitud';

          this.snackBar.open(
            'Error: ' + errorMessage,
            'Cerrar',
            { duration: 5000, panelClass: ['error-snackbar'] }
          );
        }
      });
  }

  openFilesUpload(): void {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: ['files-upload-dialog', isDarkTheme ? 'dark-theme' : ''],
      data: {
        multiple: true,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe(urls => {
      if (urls && urls.length) {
        // Convertir las URLs en objetos FileFaq
        const newFiles: FileFaq[] = urls.map((url: string) => {
          const fileName = url.split('/').pop() || 'archivo';
          const fileType = this.detectFileType(fileName);
          return {
            name: fileName,
            type: fileType,
            size: 0, // No podemos saber el tamaño real desde la URL
            url: url,
            fileType: this.detectType(fileType)
          };
        });

        this.uploadFiles = [...this.uploadFiles, ...newFiles];
        this.faqForm.patchValue({ archivos: this.uploadFiles });
      }
    });
  }

  private detectFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image/' + extension;
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      default:
        return 'application/octet-stream';
    }
  }

  private detectType(mime: string): FileFaq['fileType'] {
    if (mime.startsWith('image/')) { return 'image'; }
    if (mime.startsWith('video/')) { return 'video'; }
    if (mime.startsWith('audio/')) { return 'audio'; }
    if (mime === 'application/pdf' ||
        mime.startsWith('application/')) { return 'document'; }
    return 'other';
  }

  saveData(): void {
    this.submitted = true;
    if (this.faqForm.invalid) {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.faqForm.controls).forEach(key => {
        const control = this.faqForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    try {
      // Obtener los valores del formulario como un objeto plano
      const formValues = this.faqForm.getRawValue();

      // Crear un nuevo objeto FAQ con los valores del formulario
      let faqData: Faq;

      if (this.data.isResponseMode) {
        // En modo respuesta, solo actualizamos los campos necesarios
        // Creamos un objeto con todas las propiedades requeridas para evitar errores de tipo
        const userId = this.getUserId();
        const userName = this.getUserName();

        console.log('FaqDialog - saveData - Usuario que responde:', { userId, userName });

        // Verificar que los datos del usuario que responde estén presentes
        if (!userId || !userName) {
          console.error('FaqDialog - saveData - Error: No se pudo obtener la información del usuario que responde');
          this.snackBar.open('Error: No se pudo obtener la información del usuario', 'Cerrar', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          return; // Detener la ejecución si no hay datos de usuario
        }

        // Crear el objeto FAQ con los datos necesarios
        faqData = {
          id: this.data.faq?.id,
          pregunta: this.data.faq?.pregunta || '',
          respuesta: formValues.respuesta || '',
          categoria: this.data.faq?.categoria,
          tipoUsuario: this.data.faq?.tipoUsuario,
          views: this.data.faq?.views || 0,
          usuarioPreguntaId: this.data.faq?.usuarioPreguntaId,
          usuarioPreguntaNombre: this.data.faq?.usuarioPreguntaNombre,
          usuarioRespuestaId: userId, // ID del usuario que responde
          usuarioRespuestaNombre: userName, // Nombre del usuario que responde
          esPublica: this.data.faq?.esPublica !== undefined ? this.data.faq.esPublica : true,
          respondida: true, // Marcar explícitamente como respondida
          archivos: this.uploadFiles || [],
          createdAt: this.data.faq?.createdAt,
          updatedAt: this.data.faq?.updatedAt
        };

        console.log('FaqDialog - saveData - Datos de FAQ en modo respuesta:', {
          id: faqData.id,
          respuesta: faqData.respuesta,
          usuarioRespuestaId: faqData.usuarioRespuestaId,
          usuarioRespuestaNombre: faqData.usuarioRespuestaNombre,
          respondida: faqData.respondida
        });
      } else {
        // En modo normal, actualizamos todos los campos
        faqData = {
          pregunta: formValues.pregunta || '',
          respuesta: formValues.respuesta || '',
          categoria: formValues.categoria || null,
          tipoUsuario: formValues.tipoUsuario || null,
          esPublica: formValues.esPublica || false,
          respondida: formValues.respondida || false,
          archivos: this.uploadFiles || []
        };
      }

      // Si estamos editando, mantener el ID
      if (this.data?.faq?.id) {
        faqData.id = this.data.faq.id;
      }

      console.log('FaqDialog - saveData - Datos de FAQ a guardar:', faqData);

      // Verificar que los campos necesarios estén presentes
      if (this.data.isResponseMode) {
        if (!faqData.usuarioRespuestaId) {
          console.error('FaqDialog - saveData - Error: Falta el ID del usuario que responde');
          this.snackBar.open('Error: No se pudo obtener el ID del usuario que responde', 'Cerrar', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          return;
        }

        if (!faqData.respondida) {
          console.error('FaqDialog - saveData - Error: El campo respondida no está establecido como true');
          // Asegurarse de que esté marcado como respondida
          faqData.respondida = true;
        }
      }

      // Dispatch de la acción correspondiente
      if (this.data?.faq?.id) {
        console.log('FaqDialog - saveData - Actualizando FAQ con ID:', this.data.faq.id);
        this.store.dispatch(FaqActions.updateFaq({ id: this.data.faq.id, faq: faqData }));
      } else {
        console.log('FaqDialog - saveData - Creando nueva FAQ');
        this.store.dispatch(FaqActions.createFaq({ faq: faqData }));
      }

      // Cerrar el diálogo
      this.dialogRef.close(true);
    } catch (err) {
      console.error('Error al guardar FAQ:', err);
      const error = err as Error;
      this.snackBar.open('Error al guardar: ' + (error.message || 'Error desconocido'), 'Cerrar', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    }
  }

  // Métodos para manejar la sección de archivos
  toggleArchivosCollapsed() {
    this.archivosCollapsed = !this.archivosCollapsed;
  }

  /**
   * Obtiene el ID del usuario actual desde localStorage
   */
  getUserId(): number | undefined {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        if (user && user.id) {
          console.log('FaqDialog - getUserId - ID del usuario obtenido:', user.id);
          return user.id;
        } else {
          console.warn('FaqDialog - getUserId - No se encontró ID de usuario en el objeto user:', user);
        }
      } else {
        console.warn('FaqDialog - getUserId - No se encontró información del usuario en localStorage');
      }
      return undefined;
    } catch (error) {
      console.error('FaqDialog - getUserId - Error al obtener el ID del usuario:', error);
      return undefined;
    }
  }

  /**
   * Obtiene el nombre completo del usuario actual desde localStorage
   */
  getUserName(): string | undefined {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        let userName: string | undefined;

        // Combinar nombre y apellido si ambos existen
        if (user.nombre && user.apellido) {
          userName = `${user.nombre} ${user.apellido}`;
        } else {
          // Si solo hay nombre o solo apellido o username
          userName = user.nombre || user.apellido || user.username || undefined;
        }

        if (userName) {
          console.log('FaqDialog - getUserName - Nombre del usuario obtenido:', userName);
          return userName;
        } else {
          console.warn('FaqDialog - getUserName - No se encontró nombre de usuario en el objeto user:', user);
        }
      } else {
        console.warn('FaqDialog - getUserName - No se encontró información del usuario en localStorage');
      }
      return undefined;
    } catch (error) {
      console.error('FaqDialog - getUserName - Error al obtener el nombre del usuario:', error);
      return undefined;
    }
  }

  handleFileDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer?.files) {
      // Aquí se manejaría la subida de archivos por arrastrar y soltar
      // Por ahora, solo abrimos el selector de archivos normal
      this.openFilesUpload();
    }
  }

  formatFileSize(size: number): string {
    if (size < 1024) {
      return `${size} bytes`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(1)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    // Verificar si el body tiene la clase dark-theme
    this.isDarkTheme = document.body.classList.contains('dark-theme');
    this.cdr.detectChanges();
  }

  /**
   * Observa cambios en el tema
   */
  observeThemeChanges(): void {
    // Crear un observer para detectar cambios en el body
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          // Ejecutar fuera del ciclo de detección de cambios de Angular
          setTimeout(() => {
            this.checkDarkTheme();
          }, 0);
        }
      });
    });

    // Iniciar la observación del body
    this.observer.observe(document.body, { attributes: true });
  }

  /**
   * Limpieza al destruir el componente
   */
  ngOnDestroy(): void {
    // Desconectar el observer si existe
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
