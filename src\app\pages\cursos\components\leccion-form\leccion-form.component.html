<div class="p-5 bg-white dark:bg-gray-800 rounded-lg shadow-sm" [ngClass]="{'dark-theme': isDarkTheme}">
  <h3 class="text-xl font-medium text-indigo-600 dark:text-indigo-400 mb-4">{{ isEditMode ? 'Editar Lección' : 'Nueva Lección' }}</h3>

  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-4">
    <!-- Títu<PERSON> de la Lección -->
    <div>
      <label for="nombre" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Tí<PERSON><PERSON> de la Lección <span class="text-red-500">*</span>
      </label>
      <input type="text" id="nombre" formControlName="nombre"
             class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
             placeholder="Ingrese el título de la lección">
      <div *ngIf="form.get('nombre')?.hasError('required') && form.get('nombre')?.touched"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título de la lección es obligatorio
      </div>
      <div *ngIf="form.get('nombre')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título no debe exceder los 100 caracteres
      </div>
    </div>

    <!-- Descripción -->
    <div>
      <label for="descripcion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Descripción
      </label>
      <textarea id="descripcion" formControlName="descripcion" rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                placeholder="Ingrese una descripción breve de la lección"></textarea>
      <div *ngIf="form.get('descripcion')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        La descripción no debe exceder los 500 caracteres
      </div>
    </div>

    <!-- Campos de configuración -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Tipo de Lección -->
      <div>
        <label for="tipoLeccion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Tipo de Lección
        </label>
        <select id="tipoLeccion" formControlName="tipoLeccion"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
          <option [value]="TipoLeccion.VIDEO">Video</option>
          <option [value]="TipoLeccion.CUESTIONARIO">Cuestionario</option>
          <option [value]="TipoLeccion.PDF">Documento PDF</option>
        </select>
      </div>

      <!-- Duración -->
      <div>
        <label for="duracion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Duración (minutos)
        </label>
        <input type="number" id="duracion" formControlName="duracion" min="0"
               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
        <div *ngIf="form.get('duracion')?.hasError('min')"
             class="mt-1 text-sm text-red-600 dark:text-red-400">
          La duración debe ser un número positivo
        </div>
      </div>

      <!-- Orden -->
      <div>
        <label for="orden" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Orden
        </label>
        <input type="number" id="orden" formControlName="orden" min="0"
               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
        <div *ngIf="form.get('orden')?.hasError('min')"
             class="mt-1 text-sm text-red-600 dark:text-red-400">
          El orden debe ser un número positivo
        </div>
      </div>

      <!-- Estado -->
      <div>
        <label for="estado" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Estado
        </label>
        <select id="estado" formControlName="estado"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
          <option value="A">Activo</option>
          <option value="I">Inactivo</option>
        </select>
      </div>
    </div>

    <!-- Video de la lección (solo visible si el tipo de lección es VIDEO) -->
    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg" *ngIf="form.get('tipoLeccion')?.value === 'VIDEO'">
      <h4 class="text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-2">Video de la Lección</h4>
      <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Cada lección de tipo video debe tener su propio video. Este video se mostrará cuando el usuario seleccione esta lección.
      </p>

      <div class="space-y-4">
        <div class="flex flex-wrap items-center gap-4">
          <button type="button" (click)="openFilesUpload()"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            Subir desde Firebase
          </button>
        </div>

        <!-- Información del video seleccionado -->
        <div *ngIf="videoPreviewUrl" class="space-y-3">
          <div class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-md border border-green-200 dark:border-green-800">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>Video cargado correctamente</span>
          </div>

          <button type="button" (click)="removeSelectedVideo()"
                  class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            Eliminar video
          </button>
        </div>
      </div>
    </div>

    <!-- Subtítulos de la lección (solo visible si el tipo de lección es VIDEO) -->
    <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg" *ngIf="form.get('tipoLeccion')?.value === 'VIDEO'">
      <h4 class="text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-2">Subtítulos de la Lección</h4>
      <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Añade subtítulos para el video de esta lección. Los subtítulos se mostrarán cuando el usuario reproduzca el video.
      </p>

      <div class="space-y-4">
        <div class="flex flex-wrap items-center gap-4">
          <button type="button" (click)="openSubtitlesUpload()"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            Subir subtítulos desde Firebase
          </button>

          <div class="flex items-center text-blue-600 dark:text-blue-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
            <span class="text-sm">Formatos soportados: .vtt (recomendado), .srt, .txt</span>
          </div>
        </div>

        <!-- Información de subtítulos cargados -->
        <div *ngIf="subtitlesUrl" class="space-y-3">
          <div class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-md border border-green-200 dark:border-green-800">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>Subtítulos cargados correctamente</span>
          </div>

          <button type="button" (click)="removeSelectedSubtitles()"
                  class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            Eliminar subtítulos
          </button>
        </div>
      </div>

      <div class="mt-4 flex items-start p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-md border border-blue-200 dark:border-blue-800 text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        <span>Los subtítulos se subirán a Firebase Storage y se asociarán automáticamente con el video de esta lección.</span>
      </div>
    </div>

    <!-- Sección de cuestionario (solo visible si el tipo de lección es CUESTIONARIO) -->
    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg" *ngIf="form.get('tipoLeccion')?.value === 'CUESTIONARIO'">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
        <h4 class="text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-2 sm:mb-0">Cuestionario de la Lección</h4>
        <button *ngIf="isEditMode" type="button" (click)="gestionarCuestionario()"
                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
          Gestionar Cuestionario
        </button>
      </div>

      <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {{ isEditMode ? 'Haga clic en el botón "Gestionar Cuestionario" para configurar las preguntas y respuestas.' : 'Después de guardar la lección, podrás crear y configurar el cuestionario asociado a esta lección.' }}
      </p>

      <div class="flex items-start p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-md border border-blue-200 dark:border-blue-800 text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        <p class="m-0">
          Los cuestionarios permiten evaluar el conocimiento de los usuarios sobre el contenido del curso.
          Podrás configurar preguntas de diferentes tipos, establecer puntajes, tiempo límite y más.
        </p>
      </div>
    </div>

    <!-- Sección de PDF (solo visible si el tipo de lección es PDF) -->
    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg" *ngIf="form.get('tipoLeccion')?.value === 'PDF'">
      <h4 class="text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-2">Documento PDF de la Lección</h4>
      <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Cada lección de tipo PDF debe tener su propio documento. Este documento se mostrará cuando el usuario seleccione esta lección.
      </p>

      <div class="space-y-4">
        <div class="flex flex-wrap items-center gap-4">
          <button type="button" (click)="openPdfUpload()"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            Subir PDFs desde Firebase
          </button>
        </div>

        <!-- Información de los PDFs seleccionados -->
        <div *ngIf="pdfUrls.length > 0" class="space-y-3">
          <div class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-md border border-green-200 dark:border-green-800">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
            </svg>
            <span>{{ pdfUrls.length }} PDF(s) cargados</span>
          </div>

          <div class="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
            <div class="divide-y divide-gray-200 dark:divide-gray-600">
              <div class="flex items-center justify-between p-3 hover:bg-gray-100 dark:hover:bg-gray-600" *ngFor="let url of pdfUrls; let i = index">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 dark:text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-sm text-gray-700 dark:text-gray-300 truncate max-w-xs">{{ url | slice:-30 }}</span>
                </div>
                <button type="button" (click)="removeSelectedPdf(i)" title="Eliminar este PDF"
                        class="p-1 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <button *ngIf="pdfUrls.length > 1" type="button" (click)="removeAllPdfs()"
                  class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            Eliminar todos los PDFs
          </button>
        </div>
      </div>

      <div class="mt-4 flex items-start p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-md border border-blue-200 dark:border-blue-800 text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        <span>Los documentos PDF se subirán a Firebase Storage y estarán disponibles para los usuarios del curso. Puedes subir múltiples PDFs para una sola lección.</span>
      </div>
    </div>

    <!-- Mensaje de error -->
    <div *ngIf="error" class="mt-4 flex items-center p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md border border-red-200 dark:border-red-800">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>{{ error }}</span>
    </div>

    <!-- Botones de acción -->
    <div class="flex justify-end pt-4 mt-6 border-t border-gray-200 dark:border-gray-700">
      <button type="button" (click)="onCancel()"
              class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm">
        Cancelar
      </button>
      <button type="submit" [disabled]="form.invalid || loading"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed">
        <svg *ngIf="!loading" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
        <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>{{ isEditMode ? 'Actualizar' : 'Guardar' }}</span>
      </button>
    </div>
  </form>
</div>
