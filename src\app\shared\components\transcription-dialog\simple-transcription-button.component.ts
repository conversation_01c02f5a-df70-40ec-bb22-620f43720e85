import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './transcription-dialog.component';

@Component({
  selector: 'app-simple-transcription-button',
  template: `
    <!-- Botón simple para abrir el modal de transcripción -->
    <button 
      type="button"
      mat-raised-button 
      color="primary" 
      (click)="openTranscriptionModal()"
      class="transcription-btn">
      <mat-icon>record_voice_over</mat-icon>
      Transcribir Audio
    </button>
  `,
  styles: [`
    .transcription-btn {
      min-width: 180px;
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }
    
    .transcription-btn mat-icon {
      margin-right: 8px;
    }
  `]
})
export class SimpleTranscriptionButtonComponent {

  constructor(private dialog: MatDialog) {}

  /**
   * Abre el modal de transcripción con subida de archivos
   */
  openTranscriptionModal(): void {
    // Configurar los datos para el modal
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true, // Esto habilita la subida de archivos
      // Opcional: puedes agregar datos de cliente si los tienes
      cliente: {
        nombres: 'Usuario',
        apellidos: 'Ejemplo'
      },
      numeroMovil: '123456789'
    };

    // Abrir el modal
    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '95vw',
      maxWidth: '1000px',
      height: '95vh',
      maxHeight: '900px',
      disableClose: false,
      data: dialogData,
      panelClass: 'transcription-modal'
    });

    // Manejar el resultado cuando se cierre el modal
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Transcripción completada:', result);
        // Aquí puedes hacer algo con el resultado
        alert('¡Transcripción completada exitosamente!');
      } else if (result === false) {
        console.log('❌ Usuario canceló la transcripción');
      }
    });
  }
}
