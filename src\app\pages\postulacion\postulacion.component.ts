import { Component, OnInit } from '@angular/core';
import { PostulacionService } from 'app/services/postulacion.service';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-postulacion',
  templateUrl: './postulacion.component.html',
  styleUrls: ['./postulacion.component.scss']

  // styleUrls eliminado porque usamos Tailwind directamente en HTML
})
export class PostulacionComponent implements OnInit {
  postulaciones: any[] = [];
  postulacionesFiltradas: any[] = [];

  filtroNombre: string = '';
  filtroFecha: string = ''; // Será inicializada en ngOnInit
  filtroSede: string = '';
  filtroCampania: string = '';

  filtroFechaDesde: string = '';
  filtroFechaHasta: string = '';


  sedesUnicas: string[] = [];
  campaniasUnicas: string[] = [];

  displayedColumns: string[] = ['documento', 'apellidos', 'celular', 'campania', 'sede', 'fecha', 'hora'];

  constructor(private postulacionService: PostulacionService) {}

  ngOnInit(): void {
    // ✅ Fecha actual de Perú en formato YYYY-MM-DD
     const peruDate = new Date(new Date().toLocaleString("en-US", { timeZone: "America/Lima" }));
      const hoy = peruDate.toISOString().split('T')[0];
      this.filtroFechaDesde = hoy;
      this.filtroFechaHasta = hoy;

  this.obtenerPostulaciones();
  }

  obtenerPostulaciones(): void {
    this.postulacionService.getPostulaciones().subscribe(response => {
      if (response.success) {
        this.postulaciones = response.data.map((p: any) => ({
          ...p,
          campania: p['campaña'] // Renombrar campo con ñ
        }));

        this.sedesUnicas = [...new Set(this.postulaciones.map(p => p.sede))];
        this.campaniasUnicas = [...new Set(this.postulaciones.map(p => p.campania))];

        this.filtrarPostulaciones(); // Aplicar filtros iniciales
      }
    });
  }

filtrarPostulaciones(): void {
  const desde = this.filtroFechaDesde ? new Date(this.filtroFechaDesde) : null;
  const hasta = this.filtroFechaHasta ? new Date(this.filtroFechaHasta) : null;

  this.postulacionesFiltradas = this.postulaciones.filter(p => {
    const coincideNombre = p.apellidos.toLowerCase().includes(this.filtroNombre.toLowerCase());
    const coincideSede = this.filtroSede ? p.sede === this.filtroSede : true;
    const coincideCampania = this.filtroCampania ? p.campania === this.filtroCampania : true;

    const fechaPostulacion = new Date(p.fecha_postulacion);
    const coincideFecha =
      (!desde || fechaPostulacion >= desde) &&
      (!hasta || fechaPostulacion <= hasta);

    return coincideNombre && coincideFecha && coincideSede && coincideCampania;
  });
}


  descargarExcel(): void {
    // Crear un nuevo arreglo solo con las columnas que deseas exportar
    const datosParaExportar = this.postulacionesFiltradas.map(p => ({
      Documento: p.documento,
      Apellidos: p.apellidos,
      Celular: p.celular,
      'Campaña': p['campaña'], // usar la propiedad original con ñ
      Sede: p.sede,
      Fecha: new Date(p.fecha_postulacion).toISOString().split('T')[0],
      Hora: p.hora_postulacion
    }));
  
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(datosParaExportar);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Postulaciones');
    XLSX.writeFile(wb, 'postulaciones.xlsx');
  }
  
}
