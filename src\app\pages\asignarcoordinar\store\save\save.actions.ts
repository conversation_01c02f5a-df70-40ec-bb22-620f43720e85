// coordinador.actions.ts
import { createAction, props } from '@ngrx/store';
import { CoordinadorDTO } from '@app/models/backend/dto/coordinador.dto';
import { AsignacionAsesorDTO } from '@app/models/backend/dto/asignacion-asesor.dto';
import { AsesorDTO } from '@app/models/backend/dto/asesor.dto';
import { ClienteResidencial } from '@app/models/backend/clienteresidencial';

// Coordinador paginado
export const loadCoordinadoresPaginados = createAction(
  '[Coordinador List] Load Coordinadores Paginados',
  props<{ page: number; size: number; searchTerm?: string }>()
);

export const loadCoordinadoresPaginadosSuccess = createAction(
  '[Coordinador API] Load Coordinadores Paginados Success',
  props<{
    coordinadores: CoordinadorDTO[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    size: number;
    hasNext: boolean;
    hasPrevious: boolean;
  }>()
);

export const loadCoordinadoresPaginadosFailure = createAction(
  '[Coordinador API] Load Coordinadores Paginados Failure',
  props<{ error: any }>()
);


export const asignarAsesores = createAction(
  '[Coordinador Detail] Asignar Asesores',
  props<{ asignacion: AsignacionAsesorDTO }>()
);

export const asignarAsesoresSuccess = createAction(
  '[Coordinador API] Asignar Asesores Success',
  props<{ coordinador: CoordinadorDTO }>()
);

export const asignarAsesoresFailure = createAction(
  '[Coordinador API] Asignar Asesores Failure',
  props<{ error: any }>()
);

// --- Nuevas acciones para el modal ---

// Cargar asesores disponibles
export const loadAsesoresDisponibles = createAction(
  '[Coordinador Modal] Load Asesores Disponibles'
);

export const loadAsesoresDisponiblesSuccess = createAction(
  '[Coordinador API] Load Asesores Disponibles Success',
  props<{ asesores: AsesorDTO[] }>()
);

export const loadAsesoresDisponiblesFailure = createAction(
  '[Coordinador API] Load Asesores Disponibles Failure',
  props<{ error: any }>()
);

// Asignar un asesor individual a un coordinador
export const asignarAsesorIndividual = createAction(
  '[Coordinador Modal] Asignar Asesor Individual',
  props<{ coordinadorId: number; asesorId: number }>()
);

export const asignarAsesorIndividualSuccess = createAction(
  '[Coordinador API] Asignar Asesor Individual Success',
  props<{ coordinadorId: number; asesor: AsesorDTO }>()
);

export const asignarAsesorIndividualFailure = createAction(
  '[Coordinador API] Asignar Asesor Individual Failure',
  props<{ error: any }>()
);

// Acciones para obtener clientes de un asesor
export const loadClientesDeAsesor = createAction(
  '[Asesor Clientes] Load Clientes De Asesor',
  props<{ asesorId: number }>()
);

export const loadClientesDeAsesorSuccess = createAction(
  '[Asesor API] Load Clientes De Asesor Success',
  props<{ clientes: any[] }>()
);

export const loadClientesDeAsesorFailure = createAction(
  '[Asesor API] Load Clientes De Asesor Failure',
  props<{ error: any }>()
);

// Acción para cargar asesores con sus clientes
export const loadAsesoresConClientes = createAction(
  '[Coordinador] Load Asesores Con Clientes',
  props<{
    coordinadorId: number,
    dni?: string,
    nombre?: string,
    numeroMovil?: string,
    fecha?: string,
    page: number,
    size: number
  }>()
);

export const loadAsesoresConClientesSuccess = createAction(
  '[Coordinador] Load Asesores Con Clientes Success',
  props<{ data: any }>()
);

export const loadAsesoresConClientesFailure = createAction(
  '[Coordinador] Load Asesores Con Clientes Failure',
  props<{ error: any }>()
);

// Acciones para remover asesor
export const removerAsesor = createAction(
  '[Coordinador] Remover Asesor',
  props<{ coordinadorId: number; asesorId: number }>()
);

export const removerAsesorSuccess = createAction(
  '[Coordinador] Remover Asesor Success',
  props<{ coordinadorId: number; asesorId: number }>()
);

export const removerAsesorFailure = createAction(
  '[Coordinador] Remover Asesor Failure',
  props<{ error: any }>()
);

export const obtenerClientePorMovil = createAction(
  '[Cliente] Obtener por Móvil',
  props<{ numeroMovil: string }>()
);

export const obtenerClientePorMovilSuccess = createAction(
  '[Cliente] Obtener por Móvil Success',
  props<{ cliente: ClienteResidencial }>()
);

export const obtenerClientePorMovilFailure = createAction(
  '[Cliente] Obtener por Móvil Failure',
  props<{ error: any }>()
);
export const descargarExcelCliente = createAction(
  '[Coordinador] Descargar Excel Individual Cliente',
  props<{ numeroMovil: string }>()
);

export const descargarExcelClienteSuccess = createAction(
  '[Coordinador] Descargar Excel Individual Cliente Success'
);

export const descargarExcelClienteFailure = createAction(
  '[Coordinador] Descargar Excel Individual Cliente Failure',
  props<{ error: any }>()
);
export const loadClienteByDniAndMobile = createAction(
  '[Cliente] Load Cliente by DNI and Mobile',
  props<{ dni: string; mobile: string; fechaCreacion: string }>()
);
export const loadClienteByDniAndMobileSuccess = createAction(
  '[Cliente] Load Cliente by DNI and Mobile Success',
  props<{ cliente: ClienteResidencial }>()
);
export const loadClienteByDniAndMobileFailure = createAction(
  '[Cliente] Load Cliente by DNI and Mobile Failure',
  props<{ error: any }>()
);
export const exportarClientesHoyCoordinador = createAction(
  '[Asesores/API] Exportar Clientes Hoy Coordinador',
  props<{ coordinadorId: number }>()
);

export const exportarClientesHoyCoordinadorSuccess = createAction(
  '[Asesores/API] Exportar Clientes Hoy Coordinador Success',
  props<{ excelBlob: Blob | null }>() // <- permite null
);


export const exportarClientesHoyCoordinadorFailure = createAction(
  '[Asesores/API] Exportar Clientes Hoy Coordinador Failure',
  props<{ error: any }>()
);
export const clearExcelBlob = createAction('[Asesores/API] Clear Excel Blob');
