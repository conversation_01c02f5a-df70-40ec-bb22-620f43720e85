/**
 * Modelo para la respuesta optimizada de asignación de cursos a usuarios
 */
export interface CursoUsuarioDTO {
  id: number;
  cursoId: number;
  usuarioId: number;
  usuarioNombre?: string;
  usuarioApellido?: string;
  usuarioUsername?: string;
  usuarioEmail?: string;
  usuarioDni?: string;
  usuarioRole?: string;
  fechaAsignacion: string | any[];
  estado: string; // A: Activo, I: Inactivo
  completado: boolean;
  fechaCompletado?: string | any[];
  porcentajeCompletado: number;
  ultimaVisualizacion?: string | any[];
}
