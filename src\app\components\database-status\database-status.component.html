<div class="database-status-container">
  <div class="status-indicator" [ngClass]="status">
    <mat-icon *ngIf="status === 'online'">check_circle</mat-icon>
    <mat-icon *ngIf="status === 'offline'">error</mat-icon>
    <mat-icon *ngIf="status === 'checking'" class="rotating">sync</mat-icon>
    <span class="status-text">
      Base de datos: {{ status === 'online' ? 'Conectada' : status === 'offline' ? 'Desconectada' : 'Verificando...' }}
    </span>
  </div>
  <div class="last-checked" *ngIf="lastChecked">
    Última verificación: {{ lastChecked | date:'dd/MM/yyyy HH:mm:ss' }}
  </div>
  <button mat-icon-button (click)="forceCheck()" matTooltip="Verificar conexión">
    <mat-icon>refresh</mat-icon>
  </button>
</div>
