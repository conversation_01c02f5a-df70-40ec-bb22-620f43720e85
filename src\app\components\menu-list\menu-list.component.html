<aside
  class="flex flex-col h-screen w-full bg-white dark:bg-[#0a1628] shadow-sm overflow-hidden"
>
  <!-- Logo - Altura correcta del header -->
  <div
    class="flex items-center justify-center h-16 px-4 border-b dark:border-b-blue-500/10"
  >
    <img
      [src]="
        isDarkTheme ? 'assets/Logotipo-sb.svg' : 'assets/logovector-MIDAS.svg'
      "
      alt="Midas Solutions"
      routerLink="/home"
      class="w-32 max-w-[85%] cursor-pointer transition-transform hover:scale-105"
    />
  </div>

  <!-- Navegación -->
  <nav class="flex-1 overflow-y-auto select-none">
    <ul class="px-4 py-2 space-y-0.5">
      <ng-container *ngFor="let section of visibleSections">
        <li
          *ngIf="section.label"
          class="mt-1 mb-1 mx-2 px-2 text-xs font-semibold uppercase tracking-wider text-blue-600 dark:text-blue-400"
        >
          {{ section.label }}
        </li>

        <ng-container *ngFor="let item of section.items">
          <li>
            <!-- Enlace simple -->
            <a
              *ngIf="!item.children; else hasChildren"
              [routerLink]="item.route"
              routerLinkActive="!text-blue-600 dark:!text-blue-400 bg-blue-50 dark:bg-blue-500/10"
              class="flex items-center gap-3 px-3 py-1.5 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 transition-colors"
              (click)="closeMenu()"
            >
              <mat-icon class="text-lg">{{ item.icon }}</mat-icon>
              <span class="text-sm font-medium">{{ item.label }}</span>
            </a>

            <!-- Con submenú -->
            <ng-template #hasChildren>
              <button
                (click)="toggle(item)"
                class="w-full flex items-center gap-3 px-3 py-1.5 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 transition-colors"
              >
                <mat-icon class="text-lg">{{ item.icon }}</mat-icon>
                <span class="flex-1 text-left text-sm font-medium">{{
                  item.label
                }}</span>
                <mat-icon class="text-lg">{{
                  isOpen(item) ? "expand_less" : "expand_more"
                }}</mat-icon>
              </button>
              <ul *ngIf="isOpen(item)" class="pl-8 space-y-0.5 mt-0.5">
                <li *ngFor="let child of item.children">
                  <a
                    [routerLink]="child.route"
                    routerLinkActive="!text-blue-600 dark:!text-blue-400 bg-blue-50 dark:bg-blue-500/10"
                    class="flex items-center gap-3 px-3 py-1 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 transition-colors"
                    (click)="closeMenu()"
                  >
                    <mat-icon class="text-base">{{ child.icon }}</mat-icon>
                    <span class="text-sm font-medium">{{ child.label }}</span>
                  </a>
                </li>
              </ul>
            </ng-template>
          </li>
        </ng-container>
      </ng-container>
    </ul>
  </nav>

  <!-- Botón para cerrar sesión, solo si está autorizado -->
  <div
    class="flex justify-center p-2 border-t border-blue-900/10 w-full m-0 relative bottom-0 dark:border-blue-500/10"
    *ngIf="isAuthorized"
  >
    <button
      mat-raised-button
      color="warn"
      (click)="onSignOut()"
      class="w-full rounded text-white font-medium tracking-wide text-xs uppercase transition-colors bg-red-600 hover:bg-red-700 dark:bg-red-600/80 dark:hover:bg-red-600 py-1"
    >
      Cerrar sesión
    </button>
  </div>
</aside>
