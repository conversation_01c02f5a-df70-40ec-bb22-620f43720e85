.leccion-list-container {
  padding: 20px;

  &.dark-theme {
    background-color: #1e1e1e;
    color: #ffffff;
  }
}

.leccion-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0 20px;
    flex-grow: 1;
    color: #3f51b5;

    .dark-theme & {
      color: #7986cb;
    }
  }
}

.leccion-list-content {
  position: relative;
  min-height: 100px;
}

.leccion-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.leccion-item {
  position: relative;
  cursor: move;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .dark-theme & {
    background-color: #2d2d2d;
    color: #ffffff;
  }
}

.leccion-drag-handle {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: move;
  color: #757575;

  .dark-theme & {
    color: #bdbdbd;
  }
}

.leccion-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3f51b5;
  color: white;

  .dark-theme & {
    background-color: #7986cb;
  }
}

.leccion-orden {
  margin-right: 15px;
}

.leccion-duracion {
  display: inline-flex;
  align-items: center;

  mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    margin-right: 4px;
  }
}

.leccion-video-indicator,
.leccion-cuestionario-indicator,
.leccion-pdf-indicator {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 6px 10px;
  border-radius: 4px;
  width: fit-content;

  mat-icon {
    margin-right: 8px;
    font-size: 18px;
    height: 18px;
    width: 18px;
  }

  span {
    font-size: 13px;
    color: #555;
  }

  .dark-theme & {
    span {
      color: #bbb;
    }
  }
}

.leccion-video-indicator {
  background-color: rgba(33, 150, 243, 0.1);

  mat-icon {
    color: #2196f3;
  }

  .dark-theme & {
    background-color: rgba(33, 150, 243, 0.05);

    mat-icon {
      color: #64b5f6;
    }
  }
}

.leccion-cuestionario-indicator {
  background-color: rgba(156, 39, 176, 0.1);

  mat-icon {
    color: #9c27b0;
  }

  .dark-theme & {
    background-color: rgba(156, 39, 176, 0.05);

    mat-icon {
      color: #ce93d8;
    }
  }
}

.leccion-pdf-indicator {
  background-color: rgba(244, 67, 54, 0.1);

  mat-icon {
    color: #f44336;
  }

  .dark-theme & {
    background-color: rgba(244, 67, 54, 0.05);

    mat-icon {
      color: #ef9a9a;
    }
  }
}

.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

mat-card-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 8px;
  gap: 8px;
}

.leccion-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin: 8px;

  &.active {
    background-color: #4caf50;
    color: white;
  }

  &.inactive {
    background-color: #f44336;
    color: white;
  }
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin: 15px 0;

  mat-icon {
    margin-right: 8px;
  }
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #3f51b5;

    .dark-theme & {
      color: #7986cb;
    }
  }

  p {
    margin-bottom: 20px;
    color: #757575;

    .dark-theme & {
      color: #bdbdbd;
    }
  }
}
