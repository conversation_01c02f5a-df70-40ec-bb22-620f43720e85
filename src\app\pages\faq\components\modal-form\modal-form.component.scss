/* Estilos para el modal de FAQ */
:host ::ng-deep {
  /* Asegurar que el modal esté centrado */
  .modal-dialog {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
  }

  .modal-dialog-centered {
    justify-content: center;
  }
  .modal-content {
    border-radius: 0;
    background-color: #f0f2f5;
  }

  .modal-header {
    background-color: #f0f2f5;
    border-bottom: none;
    padding: 1.5rem 1.5rem 0.5rem;
  }

  .modal-title {
    font-size: 1.25rem;
    font-weight: 500;
  }

  .modal-body {
    padding: 1rem 1.5rem;
  }

  .modal-footer {
    background-color: #f0f2f5;
    border-top: none;
    padding: 0.5rem 1.5rem 1.5rem;
  }

  /* Estilos para el formulario */
  .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .form-control, .form-select {
    border-radius: 0;
    border: 1px solid #ced4da;
  }

  textarea.form-control {
    resize: none;
  }

  /* Estilos para la barra de herramientas del editor */
  .editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-bottom: none;
  }

  .editor-toolbar .btn {
    border-radius: 0;
    padding: 0.25rem 0.5rem;
  }

  /* Estilos para el área de carga de archivos */
  .file-upload-area {
    background-color: #fff;
    border: 1px dashed #ced4da;
    transition: all 0.2s ease;
  }

  .card {
    border-radius: 0;
  }

  .card-header {
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
  }

  /* Botones */
  .btn {
    border-radius: 0;
    padding: 0.5rem 1.5rem;
  }

  .btn-success {
    background-color: #28a745;
  }

  .btn-secondary {
    background-color: #6c757d;
  }
}
