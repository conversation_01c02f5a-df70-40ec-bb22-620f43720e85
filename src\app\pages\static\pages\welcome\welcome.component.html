<section class="form-section">
  <!-- Formulario template-driven -->
  <form #clienteForm="ngForm" (ngSubmit)="onSubmit()">
    <!-- TARJETA 1: DATOS DEL CLIENTE -->
    <mat-card class="main-card datos-cliente">
      <div class="icon-container">
        <mat-icon class="icono-flotante">account_circle</mat-icon>
      </div>
      <mat-card-content>
        <section class="datos-cliente-content">
          <div class="flex justify-between items-center w-full box-border p-2">
            <h2 class="section-title">Datos del cliente</h2>

            <div class="numero-agente-wrapper">
              <!-- Campo: Estado de la llamada -->
              <!---
              <mat-form-field appearance="outline" class="numero-agente-field">
                <mat-label>Estado de la llamada</mat-label>
                <mat-select
                  name="estadoLlamada"
                  [(ngModel)]="formData.clienteResidencial.estadoLlamada"
                >
                  <mat-option value="RED 4G">RED 4G</mat-option>
                  <mat-option value="RED 5G">RED 5G</mat-option>
                  <mat-option value="No contrata por Futbol"
                    >No contrata por Futbol</mat-option
                  >
                  <mat-option value="Agendado">Agendado</mat-option>
                  <mat-option value="Cliente Vodafone"
                    >Cliente Vodafone</mat-option
                  >
                  <mat-option value="Sin Cobertura">Sin Cobertura</mat-option>
                  <mat-option value="No Interesado">No Interesado</mat-option>
                  <mat-option value="No Es Titular">No Es Titular</mat-option>
                  <mat-option value="Permanencia">Permanencia</mat-option>
                  <mat-option value="Precio Menor">Precio Menor</mat-option>
                  <mat-option value="Prepago">Prepago</mat-option>
                  <mat-option value="Persona Mayor">Persona Mayor</mat-option>
                </mat-select>
                <mat-error>Este campo es obligatorio</mat-error>
              </div>
              -->
              <!-- Campo: Número de Agente -->
              <div
                class="flex flex-col w-full gap-2"
                class="numero-agente-field"
              >
                <label
                  class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >Número de Agente</label
                >
                <input
                  required
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  name="numeroAgente"
                  [(ngModel)]="formData.clienteResidencial.numeroAgente"
                  #numeroAgente="ngModel"
                />
                <mat-error
                  *ngIf="
                    numeroAgente.invalid &&
                    (numeroAgente.dirty || numeroAgente.touched)
                  "
                  class="!text-xs text-red-600"
                  >El número de agente es obligatorio</mat-error
                >
              </div>
            </div>
          </div>
          <hr class="title-separator" />

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            <!-- Móvil de contacto -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Móvil de contacto</label
              >
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                type="tel"
                name="movilContacto"
                required
                maxlength="9"
                minlength="9"
                pattern="^[0-9]*$"
                [(ngModel)]="formData.clienteResidencial.movilContacto"
                #movilContacto="ngModel"
                (keypress)="validateNumberInput($event)"
              />
              <div *ngIf="movilContacto.touched" class="!text-xs text-red-600">
                <mat-error
                  *ngIf="movilContacto.errors?.['minlength'] || movilContacto.errors?.['maxlength'] || (movilContacto.value && movilContacto.value.length !== 9)"
                >
                  El número debe tener 9 dígitos
                </mat-error>
                <mat-error *ngIf="movilContacto.errors?.['pattern']">
                  Solo se permiten números
                </mat-error>
                <mat-error *ngIf="!movilContacto.value">
                  El número de móvil es obligatorio
                </mat-error>
              </div>
            </div>

            <!-- Operador con búsqueda -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Operador</label
              >
              <div class="relative operador-dropdown">
                <input
                  class="w-full px-4 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 transition-colors"
                  [class.border-gray-300]="operadorValido"
                  [class.dark:border-gray-700]="operadorValido"
                  [class.focus:ring-blue-500]="operadorValido"
                  [class.border-red-300]="!operadorValido"
                  [class.dark:border-red-700]="!operadorValido"
                  [class.focus:ring-red-500]="!operadorValido"
                  name="operadorFilter"
                  [formControl]="operadorFilterControl"
                  placeholder="Buscar operador..."
                  autocomplete="new-password"
                  autocorrect="off"
                  autocapitalize="off"
                  spellcheck="false"
                  (click)="toggleOperadoresDropdown($event)"
                  (input)="onOperadorInput($event)"
                  (blur)="onOperadorBlur()"
                />

                <!-- Icono de validación -->
                <div
                  class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
                >
                  <mat-icon
                    *ngIf="!operadorValido && operadorFilterControl.value"
                    class="text-red-500 text-sm"
                  >
                    error
                  </mat-icon>
                  <mat-icon
                    *ngIf="
                      operadorValido &&
                      operadorFilterControl.value &&
                      formData.clienteResidencial.campania
                    "
                    class="text-green-500 text-sm"
                  >
                    check_circle
                  </mat-icon>
                </div>

                <!-- Dropdown de operadores -->
                <div
                  *ngIf="mostrarOperadores && operadoresFiltrados.length > 0"
                  class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto"
                  (mousedown)="$event.preventDefault()"
                >
                  <div
                    *ngFor="let operador of operadoresFiltrados"
                    class="px-4 py-2 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-800 dark:text-white text-sm transition-colors"
                    (mousedown)="$event.preventDefault()"
                    (click)="seleccionarOperador(operador)"
                  >
                    {{ operador }}
                  </div>
                </div>

                <!-- Mensaje cuando no hay resultados -->
                <div
                  *ngIf="mostrarOperadores && operadoresFiltrados.length === 0"
                  class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg"
                >
                  <div
                    class="px-4 py-2 text-gray-500 dark:text-gray-400 text-sm"
                  >
                    No se encontraron operadores
                  </div>
                </div>
              </div>

              <!-- Mensaje de ayuda -->
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span
                  *ngIf="!operadorValido && operadorFilterControl.value"
                  class="text-red-500"
                >
                  ⚠️ Debe seleccionar un operador de la lista
                </span>
                <span
                  *ngIf="operadorValido || !operadorFilterControl.value"
                  class="text-gray-500"
                >
                  💡 Escriba para buscar y seleccione un operador de la lista
                </span>
              </div>
            </div>

            <!-- Tipo de plan -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Tipo de plan</label
              >
              <select
                name="tipoPlan"
                [(ngModel)]="formData.clienteResidencial.tipoPlan"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              >
                <option value="prepago">Prepago</option>
                <option value="contrato">Contrato</option>
              </select>
            </div>

            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Titular de Servicio</label
              >
              <select
                name="titularDelServicio"
                [(ngModel)]="formData.clienteResidencial.titularDelServicio"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              >
                <option value="SI">SI ES TITULAR</option>
                <option value="NO">NO ES TITULAR</option>
              </select>
            </div>

            <!-- NIF/NIE -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >NIF / NIE</label
              >
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                name="nifNie"
                [(ngModel)]="formData.clienteResidencial.nifNie"
              />
            </div>

            <!-- Nombre y Apellido -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Nombre y Apellido</label
              >
              <input
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                name="nombresApellidos"
                [(ngModel)]="formData.clienteResidencial.nombresApellidos"
              />
            </div>

            <!-- Fecha de nacimiento -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Fecha de nacimiento</label
              >
              <div class="relative w-full">
                <input
                  class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  [matDatepicker]="pickerNacimiento"
                  name="fechaNacimiento"
                  [(ngModel)]="formData.clienteResidencial.fechaNacimiento"
                  placeholder="Selecciona fecha"
                />
                <mat-datepicker-toggle
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400 mt-2"
                  [for]="pickerNacimiento"
                ></mat-datepicker-toggle>
                <mat-datepicker #pickerNacimiento startView="year">
                </mat-datepicker>
              </div>
            </div>
          </div>
        </section>
      </mat-card-content>
    </mat-card>

    <!-- TARJETA 2: DATOS DEL SERVICIO -->
    <mat-card class="main-card datos-servicio">
      <div class="icon-container">
        <mat-icon class="icono-flotante">wifi_tethering</mat-icon>
      </div>
      <mat-card-content>
        <section class="datos-servicio-content">
          <h2 class="section-title">Datos del servicio</h2>
          <hr class="title-separator" />
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4"
          >
            <!-- Tipo de Tecnologia -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Tipo de Tecnología</label
              >
              <select
                name="tipoTecnologia"
                [(ngModel)]="formData.clienteResidencial.tipoTecnologia"
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
              >
                <option value="Sin Cobertura">SIN COBERTURA</option>
                <option value="Fibra">FTTH</option>
                <option value="NEBA">NEBA</option>
                <option value="Cable">CABLE</option>
                <option value="Red 4G">Red 4G</option>
                <option value="Red 5G">Red 5G</option>
              </select>
            </div>
            <!-- Velocidad -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Velocidad</label
              >
              <select
                name="velocidad"
                [(ngModel)]="formData.clienteResidencial.velocidad"
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
              >
                <option value="No aplica">NO APLICA</option>
                <option value="300">300</option>
                <option value="600">600</option>
                <option value="1gb">1GB</option>
              </select>
            </div>
            <!-- Fútbol -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Fútbol</label
              >
              <select
                name="futbol"
                [(ngModel)]="formData.clienteResidencial.futbol"
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
              >
                <option value="SI">SI</option>
                <option value="NO">NO</option>
              </select>
            </div>
          </div>

          <!-- Segunda fila de campos -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4"
          >
            <!-- Permanencia -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Permanencia</label
              >
              <select
                [(ngModel)]="selectedPermanencia"
                name="selectedPermanencia"
                (ngModelChange)="onPermanenciaChange($event)"
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
              >
                <option value="No tiene permanencia">
                  No tiene permanencia
                </option>
                <option value="si">Sí tengo permanencia</option>
                <option
                  [value]="selectedPermanencia"
                  *ngIf="isDateFormat(selectedPermanencia)"
                >
                  {{ selectedPermanencia }}
                </option>
              </select>
            </div>

            <!-- Datepicker condicional -->
            <div *ngIf="showDatePicker" class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Último Mes/Año de Permanencia</label
              >
              <div class="relative w-full">
                <input
                  class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  [matDatepicker]="pickerPermanencia"
                  placeholder="Selecciona mes y año"
                  readonly
                  [value]="formData.clienteResidencial.permanencia"
                />
                <mat-datepicker-toggle
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400"
                  [for]="pickerPermanencia"
                ></mat-datepicker-toggle>
                <mat-datepicker
                  #pickerPermanencia
                  startView="multi-year"
                  (monthSelected)="
                    chosenMonthHandler($event, pickerPermanencia)
                  "
                  panelClass="month-picker"
                >
                </mat-datepicker>
              </div>
            </div>

            <!-- Número de móviles -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Números móviles</label
              >
              <input
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                type="number"
                min="0"
                name="numeroMoviles"
                [(ngModel)]="formData.clienteResidencial.numeroMoviles"
              />
            </div>

            <!-- Plan actual -->
            <div class="flex flex-col w-full gap-2">
              <label
                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Costo de servicio</label
              >
              <input
                class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                name="planActual"
                [(ngModel)]="formData.clienteResidencial.planActual"
              />
            </div>

            <!-- Ciudad: si ya no necesitas un listado, puedes cambiarlo a un input o mantenerlo -->
            <!--  <div class="flex flex-col w-full gap-2">
              <mat-label>Ciudad</mat-label>
              <input
                matInput
                name="ciudad"
                [(ngModel)]="formData.clienteResidencial.ciudad"
              />
            </div>
 -->
          </div>

          <!-- SECCIÓN DE DIRECCIÓN - ANCHO COMPLETO -->
          <div class="w-full mb-4">
            <!-- Dirección - Layout que ocupa todo el ancho disponible -->
            <div class="w-full">
              <!-- Primera fila: Provincia y Municipio -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <!-- Provincia (Catastro) -->
                <div class="w-full">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Provincia</label
                  >
                  <div class="relative provincia-dropdown">
                    <input
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                      name="provinciaFilter_nofill"
                      [formControl]="provinciaFilterControl"
                      placeholder="Seleccione"
                      autocomplete="new-password"
                      autocorrect="off"
                      autocapitalize="off"
                      spellcheck="false"
                      (click)="toggleProvinciasDropdown($event)"
                      (input)="filtrarProvincias($event)"
                    />
                    <div
                      *ngIf="cargandoProvincias"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      <mat-spinner diameter="16"></mat-spinner>
                    </div>

                    <!-- Dropdown de provincias -->
                    <div
                      *ngIf="
                        provinciasCatastroFiltradas.length > 0 &&
                        mostrarProvincias === true
                      "
                      class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-xl max-h-48 overflow-y-auto provincia-dropdown"
                      (click)="$event.stopPropagation()"
                    >
                      <div
                        *ngFor="let provincia of provinciasCatastroFiltradas"
                        class="px-4 py-2.5 text-sm cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-150"
                        (click)="
                          seleccionarProvinciaCatastro(provincia);
                          $event.stopPropagation()
                        "
                      >
                        {{ provincia.Denominacion }}
                      </div>
                      <div
                        *ngIf="provinciasCatastroFiltradas.length === 0"
                        class="px-4 py-2.5 text-sm text-gray-500 dark:text-gray-400"
                      >
                        No se encontraron resultados
                      </div>
                    </div>
                  </div>
                  <input
                    class="hidden"
                    name="provincia"
                    [(ngModel)]="formData.clienteResidencial.provincia"
                  />
                </div>

                <!-- Municipio (Catastro) -->
                <div class="w-full">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Municipio</label
                  >
                  <div class="relative municipio-dropdown">
                    <input
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                      name="municipioFilter_nofill"
                      [formControl]="municipioFilterControl"
                      placeholder="Seleccione un municipio"
                      autocomplete="new-password"
                      autocorrect="off"
                      autocapitalize="off"
                      spellcheck="false"
                      [disabled]="!selectedProvinciaCatastro"
                      (click)="toggleMunicipiosDropdown($event)"
                      (input)="filtrarMunicipios($event)"
                    />
                    <div
                      *ngIf="cargandoMunicipios"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      <mat-spinner diameter="16"></mat-spinner>
                    </div>

                    <!-- Dropdown de municipios -->
                    <div
                      *ngIf="
                        municipiosCatastroFiltrados.length > 0 &&
                        mostrarMunicipios
                      "
                      class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-xl max-h-48 overflow-y-auto municipio-dropdown"
                      (click)="$event.stopPropagation()"
                    >
                      <div
                        *ngFor="let municipio of municipiosCatastroFiltrados"
                        class="px-4 py-2.5 text-sm cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-150"
                        (click)="
                          seleccionarMunicipioCatastro(municipio);
                          $event.stopPropagation()
                        "
                      >
                        {{ municipio.Denominacion }}
                      </div>
                      <div
                        *ngIf="municipiosCatastroFiltrados.length === 0"
                        class="px-4 py-2.5 text-sm text-gray-500 dark:text-gray-400"
                      >
                        No se encontraron resultados
                      </div>
                    </div>
                  </div>
                  <input
                    class="hidden"
                    name="municipio"
                    [(ngModel)]="formData.clienteResidencial.distrito"
                  />
                </div>
              </div>

              <!-- Segunda fila: Vía (ancho completo) -->
              <div class="w-full mb-4">
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >Vía</label
                >
                <div class="relative via-dropdown">
                  <input
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                    name="viaFilter_nofill"
                    [formControl]="viaFilterControl"
                    placeholder="Escriba o seleccione una vía"
                    autocomplete="new-password"
                    autocorrect="off"
                    autocapitalize="off"
                    spellcheck="false"
                    [disabled]="!selectedMunicipioCatastro"
                    (click)="toggleViasDropdown($event)"
                    (input)="filtrarVias($event); actualizarViaManual($event)"
                    (blur)="actualizarDireccionCompleta()"
                  />
                  <div
                    *ngIf="cargandoVias"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2"
                  >
                    <mat-spinner diameter="16"></mat-spinner>
                  </div>

                  <!-- Dropdown de vías -->
                  <div
                    *ngIf="viasCatastroFiltradas.length > 0 && mostrarVias"
                    class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-40 overflow-y-auto via-dropdown"
                    (click)="$event.stopPropagation()"
                  >
                    <div
                      *ngFor="let via of viasCatastroFiltradas"
                      class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                      (click)="
                        seleccionarViaCatastro(via); $event.stopPropagation()
                      "
                    >
                      {{ via.DenominacionCompleta }}
                    </div>
                    <div
                      *ngIf="viasCatastroFiltradas.length === 0"
                      class="px-3 py-1.5 text-sm text-gray-500 dark:text-gray-400"
                    >
                      No se encontraron resultados
                    </div>
                  </div>
                </div>
              </div>

              <!-- Detalles de dirección - Layout compacto en tres filas con 2 campos por fila -->
              <!-- Primera fila de detalles de dirección -->
              <div class="grid grid-cols-12 gap-3 mb-3">
                <!-- Número -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Número</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="numero"
                    [(ngModel)]="formData.clienteResidencial.numero"
                    (input)="actualizarDireccionCompleta()"
                    placeholder="Nº"
                  />
                </div>

                <!-- Bloque -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Bloque</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="bloque"
                    [(ngModel)]="formData.clienteResidencial.bloque"
                    (input)="actualizarDireccionCompleta()"
                    placeholder="Bloque"
                  />
                </div>
              </div>

              <!-- Segunda fila de detalles de dirección -->
              <div class="grid grid-cols-12 gap-3 mb-3">
                <!-- Escalera -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Escalera</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="escalera"
                    [(ngModel)]="formData.clienteResidencial.escalera"
                    (input)="actualizarDireccionCompleta()"
                    placeholder="Esc."
                  />
                </div>

                <!-- Planta -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Planta</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="planta"
                    [(ngModel)]="formData.clienteResidencial.planta"
                    (input)="actualizarDireccionCompleta()"
                    placeholder="Planta"
                  />
                </div>
              </div>

              <!-- Tercera fila de detalles de dirección -->
              <div class="grid grid-cols-12 gap-3 mb-3">
                <!-- Puerta -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Puerta</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="puerta"
                    [(ngModel)]="formData.clienteResidencial.puerta"
                    (input)="actualizarDireccionCompleta()"
                    placeholder="Puerta"
                  />
                </div>

                <!-- Código postal -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Codigo Postal</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="text"
                    name="codigoPostal2"
                    [ngModel]="formData.clienteResidencial.codigoPostal"
                    (ngModelChange)="
                      formData.clienteResidencial.codigoPostal = $event
                    "
                    maxlength="5"
                    pattern="^[0-9]{5}$"
                    (keypress)="validateNumberInput($event)"
                    (input)="onCodigoPostalInput($event)"
                    (paste)="validatePaste($event)"
                    placeholder="00000"
                  />
                </div>
              </div>

              <!-- Dirección completa en una fila completa -->
              <div class="grid grid-cols-12 gap-3 mb-3">
                <!-- Dirección completa -->
                <div class="col-span-12">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Dirección completa</label
                  >
                  <div class="relative">
                    <textarea
                      class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                      name="direccion"
                      [(ngModel)]="formData.clienteResidencial.direccion"
                      rows="2"
                      placeholder="La dirección completa se generará automáticamente"
                    ></textarea>

                    <!-- Botón para generar dirección automáticamente -->
                    <button
                      type="button"
                      class="absolute bottom-3 right-14 w-auto h-10 px-3 flex items-center justify-center rounded-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white shadow-lg transform transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:from-gray-400 disabled:to-gray-500 dark:disabled:from-gray-700 dark:disabled:to-gray-800"
                      title="Generar dirección completa automáticamente"
                      (click)="actualizarDireccionCompleta()"
                    >
                      <span class="text-xs font-medium mr-1"
                        >Generar dirección</span
                      >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </button>

                    <!-- Botón para ver en mapa -->
                    <button
                      type="button"
                      class="absolute bottom-3 right-2 w-10 h-10 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transform transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:from-gray-400 disabled:to-gray-500 dark:disabled:from-gray-700 dark:disabled:to-gray-800"
                      title="Ver ubicación en mapa (requiere provincia, municipio y vía)"
                      (click)="verEnMapa()"
                      [disabled]="
                        !formData.clienteResidencial.provincia ||
                        !formData.clienteResidencial.distrito ||
                        !(selectedViaCatastro || viaFilterControl.value)
                      "
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Fijo y Móviles a portar -->
              <div class="grid grid-cols-12 gap-3 mb-3">
                <!-- Fijo -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Fijo</label
                  >
                  <input
                    class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    type="tel"
                    name="fijoCompania"
                    [(ngModel)]="formData.clienteResidencial.fijoCompania"
                    maxlength="9"
                    pattern="^[0-9]*$"
                    (keypress)="validateNumberInput($event)"
                    placeholder="Fijo"
                  />
                </div>

                <!-- Móviles a portar -->
                <div class="col-span-6 sm:col-span-6 md:col-span-6">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >Móviles a portar</label
                  >
                  <div class="flex flex-wrap gap-x-2 gap-y-2">
                    <ng-container
                      *ngFor="
                        let movil of formData.clienteResidencial.movilesAPortar;
                        let i = index;
                        trackBy: trackByIndex
                      "
                    >
                      <div class="flex">
                        <input
                          class="w-24 px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-l-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                          type="tel"
                          name="movilAPortar{{ i }}"
                          [(ngModel)]="
                            formData.clienteResidencial.movilesAPortar[i]
                          "
                          (input)="onMovilInput($event, i)"
                          pattern="^[0-9]*$"
                          maxlength="9"
                          placeholder="Móvil {{ i + 1 }}"
                        />
                        <button
                          type="button"
                          class="px-1 py-1.5 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-r-md flex items-center justify-center shadow-sm transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-red-500"
                          (click)="removeMovilAPortar(i)"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </ng-container>

                    <!-- botón agregar móvil -->
                    <button
                      type="button"
                      (click)="addMovilAPortar()"
                      class="px-2 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 border border-blue-500 dark:border-blue-400 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      + Agregar móvil a portar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Fin de la sección de dirección -->
        </section>
      </mat-card-content>
    </mat-card>

    <!-- Panel hover para volver al mapa con temporizador -->
    <div
      *ngIf="showMapHoverPanel"
      class="fixed bottom-6 right-6 z-[1000] flex items-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer animate-fadeIn"
      (click)="abrirMapaDirecto()"
      (mouseenter)="resetMapHoverTimer()"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 animate-bounce"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
          clip-rule="evenodd"
        />
      </svg>
      <div class="flex flex-col">
        <span class="font-medium"
          >Volver al mapa
          {{ lastMapType === "leaflet" ? "Leaflet" : "Mapbox" }}</span
        >
        <span class="text-xs text-white/80"
          >Se cerrará en {{ mapHoverTimeRemaining }} segundos</span
        >
      </div>
      <!-- Indicador visual del tiempo restante -->
      <div
        class="absolute bottom-0 left-0 h-1 bg-white/50 rounded-b-lg transition-all duration-1000 pulse"
        [style.width]="(mapHoverTimeRemaining / 5) * 100 + '%'"
      ></div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
          clip-rule="evenodd"
        />
      </svg>
    </div>

    <!-- TARJETA 3: INFORMACIÓN -->
    <mat-card class="main-card datos-informacion">
      <div class="icon-container">
        <mat-icon class="icono-flotante">info</mat-icon>
      </div>
      <mat-card-content>
        <section class="datos-informacion-content">
          <h2 class="section-title">Información</h2>
          <hr class="title-separator" />

          <!-- Sección de datos bancarios y contacto -->
          <div class="info-section banking-contact">
            <div
              class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-4 p-4"
            >
              <!-- Cuenta Bancaria -->
              <div class="flex flex-col w-full gap-2" class="iban-field">
                <label
                  class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >Cuenta Bancaria (IBAN)</label
                >
                <div class="relative w-full">
                  <input
                    class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                    name="cuentaBancaria"
                    [(ngModel)]="formData.clienteResidencial.cuentaBancaria"
                    placeholder="ES91 2100 0418 4502 0005 1332"
                  />
                  <mat-icon
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400"
                    >account_balance</mat-icon
                  >
                </div>
              </div>

              <!-- Correo electrónico -->
              <div class="flex flex-col w-full gap-2">
                <label
                  class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >Correo electrónico</label
                >
                <div class="relative w-full">
                  <input
                    class="w-full h-10 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                    type="email"
                    name="correoElectronico"
                    [(ngModel)]="formData.clienteResidencial.correoElectronico"
                    placeholder="<EMAIL>"
                  />
                  <mat-icon
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400"
                    >email</mat-icon
                  >
                </div>
                <mat-error class="!text-xs text-red-600"
                  >Correo electrónico inválido</mat-error
                >
              </div>

              <!-- Observación -->
              <div class="flex flex-col w-full gap-2" class="observation-field">
                <label
                  class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >Observación</label
                >
                <textarea
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  name="observacion"
                  [(ngModel)]="formData.clienteResidencial.observacion"
                  rows="3"
                  placeholder="Añada aquí cualquier información adicional relevante..."
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Sección de autorizaciones -->
          <div class="info-section authorizations">
            <h3 class="subsection-title">Autorizaciones y confirmaciones</h3>
            <div class="checkbox-container">
              <div class="checkbox-row">
                <mat-checkbox
                  [(ngModel)]="formData.clienteResidencial.autorizaSeguros"
                  name="autorizaSeguros"
                  class="custom-checkbox"
                  color="primary"
                >
                  <span class="checkbox-label"
                    >Acepta recibir promociones sobre seguros</span
                  >
                </mat-checkbox>
              </div>

              <div class="checkbox-row">
                <mat-checkbox
                  [(ngModel)]="formData.clienteResidencial.autorizaEnergias"
                  name="autorizaEnergias"
                  class="custom-checkbox"
                  color="primary"
                >
                  <span class="checkbox-label"
                    >Acepta recibir promociones sobre energía</span
                  >
                </mat-checkbox>
              </div>

              <div class="checkbox-row">
                <mat-checkbox
                  [(ngModel)]="formData.clienteResidencial.ventaRealizada"
                  name="ventaRealizada"
                  class="custom-checkbox"
                  color="primary"
                >
                  <span class="checkbox-label">Venta realizada</span>
                </mat-checkbox>
              </div>

              <div class="checkbox-row">
                <mat-checkbox
                  [(ngModel)]="formData.clienteResidencial.deseaPromocionesLowi"
                  name="deseaPromocionesLowi"
                  class="custom-checkbox"
                  color="primary"
                >
                  <span class="checkbox-label">
                    Desea promociones LOWI o
                    <strong style="color: black">costos más bajos</strong>
                  </span>
                </mat-checkbox>
              </div>
            </div>
          </div>

          <!-- Botón de registro
          !clienteForm.form.valid ||
                !formData.clienteResidencial.numeroAgente ||
                !formData.clienteResidencial.movilContacto
          -->
          <div class="button-container flex justify-center mt-6">
            <button
              type="submit"
              class="px-6 py-3 flex items-center gap-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-lg shadow-lg transform transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:from-gray-400 disabled:to-gray-500 dark:disabled:from-gray-700 dark:disabled:to-gray-800"
              [disabled]="
                !formData.clienteResidencial.movilContacto ||
                formData.clienteResidencial.movilContacto.length !== 9 ||
                !formData.clienteResidencial.numeroAgente ||
                isSubmitting
              "
            >
              <svg
                *ngIf="!isSubmitting"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
              <mat-spinner
                *ngIf="isSubmitting"
                diameter="20"
                color="accent"
              ></mat-spinner>
              <span *ngIf="!isSubmitting" class="text-base"
                >Registrar datos</span
              >
              <span *ngIf="isSubmitting" class="text-base">Enviando...</span>
            </button>
          </div>
        </section>
      </mat-card-content>
    </mat-card>
  </form>
</section>
