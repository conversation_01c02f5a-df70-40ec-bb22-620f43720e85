import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CoachingListadoService {
  private apiUrl = 'https://apisozarusac.com/Coaching/api/frases-por-fecha/';

  constructor(private http: HttpClient) {}

  obtenerFrasesPorFecha(fecha: string): Observable<any[]> {
    const body = { fecha };
    return this.http.post<any[]>(this.apiUrl, body);
  }
}
