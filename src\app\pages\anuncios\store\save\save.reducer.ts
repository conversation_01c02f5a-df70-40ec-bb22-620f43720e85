import { AnuncioResponse } from './save.models';
import * as fromActions from './save.actions';

export interface ListState {
  anuncios: AnuncioResponse[] | null;
  anuncio: AnuncioResponse | null;
  loading: boolean | null;
  error: string | null;
  totalElements: number;
  totalPages: number;
  currentPage: number;
}

export const initialState: ListState = {
  anuncios: null,
  anuncio: null,
  loading: null,
  error: null,
  totalElements: 0,
  totalPages: 0,
  currentPage: 0,
};

export function reducer(
  state: ListState = initialState,
  action: fromActions.All | any
) {
  switch (action.type) {
    case fromActions.Types.CREATE: {
      return { ...state, loading: true, error: null };
    }

    case fromActions.Types.CREATE_SUCCESS: {
      // No modificamos la lista de anuncios aquí para evitar duplicación
      // La lista se actualizará cuando se reciba la notificación WebSocket
      // o cuando se haga una nueva petición HTTP
      return {
        ...state,
        loading: false,
        error: null,
        anuncio: action.anuncio,
        // No modificamos anuncios: state.anuncios
      };
    }

    case fromActions.Types.CREATE_ERROR: {
      return { ...state, loading: false, error: action.error };
    }

    case fromActions.Types.READ:
    case fromActions.Types.READ_RECENT: {
      return { ...state, loading: true, error: null };
    }

    case fromActions.Types.READ_SUCCESS: {
      return {
        ...state,
        loading: false,
        anuncios: action.anuncios,
        totalElements: action.totalElements,
        totalPages: action.totalPages,
        currentPage: action.currentPage,
      };
    }

    case fromActions.Types.READ_RECENT_SUCCESS: {
      // Para anuncios recientes, NO actualizar el estado si estamos en el componente de listado
      // Solo mostrar un mensaje de depuración
      console.log('[DEBUG] Reducer - READ_RECENT_SUCCESS - Recibidos anuncios recientes:',
                  action.anuncios.length, 'anuncios con IDs:',
                  action.anuncios.map((a: AnuncioResponse) => a.id).join(', '));

      // Verificar si la URL actual contiene 'anuncios/list'
      const isAnunciosList = window.location.href.includes('anuncios/list');

      // Si estamos en la página de listado de anuncios, NO actualizar el estado
      if (isAnunciosList) {
        console.log('[DEBUG] Reducer - READ_RECENT_SUCCESS - Ignorando actualización en listado de anuncios');
        return state;
      }

      // En otros componentes (como Home), sí actualizar el estado
      return {
        ...state,
        loading: false,
        anuncios: [...action.anuncios], // Crear una nueva copia del array para evitar referencias
        totalElements: action.totalElements,
        totalPages: action.totalPages,
        currentPage: action.currentPage,
      };
    }

    case fromActions.Types.READ_ERROR:
    case fromActions.Types.READ_RECENT_ERROR: {
      return { ...state, loading: false, error: action.error };
    }

    case fromActions.Types.DELETE: {
      return { ...state, loading: true, error: null };
    }

    case fromActions.Types.DELETE_SUCCESS: {
      return { ...state, loading: false, error: null };
    }

    case fromActions.Types.DELETE_ERROR: {
      return { ...state, loading: false, error: action.error };
    }

    case fromActions.Types.UPDATE: {
      return { ...state, loading: true, error: null };
    }

    case fromActions.Types.UPDATE_SUCCESS: {
      // Update the anuncio in the list if it exists
      const updatedAnuncios = state.anuncios
        ? state.anuncios.map(anuncio =>
            anuncio.id === action.anuncio.id ? action.anuncio : anuncio
          )
        : state.anuncios;

      return {
        ...state,
        loading: false,
        error: null,
        anuncio: action.anuncio,
        anuncios: updatedAnuncios
      };
    }

    case fromActions.Types.UPDATE_ERROR: {
      return { ...state, loading: false, error: action.error };
    }

    default: {
      return state;
    }
  }
}
