import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { IpPermitidasComponent } from './ip-permitidas.component';
import { AuthGuard } from '@app/guards/auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: IpPermitidasComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ADMIN'] }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class IpPermitidasRoutingModule { }

