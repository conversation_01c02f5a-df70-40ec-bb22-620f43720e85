import { Component, OnInit, OnDestroy, OnChanges, SimpleChanges, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SeccionService } from '@app/services/seccion.service';
import { LeccionService } from '@app/services/leccion.service';
import { Seccion } from '@app/models/backend/curso/seccion.model';
import { Modulo } from '@app/models/backend/curso/modulo.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-seccion-list',
  templateUrl: './seccion-list.component.html',
  styleUrls: ['./seccion-list.component.scss']
})
export class SeccionListComponent implements OnInit, OnDestroy, OnChanges {
  @Input() modulo: Modulo | null = null;
  @Input() editable: boolean = false;
  @Output() seccionSelected = new EventEmitter<Seccion>();
  @Output() seccionCreated = new EventEmitter<Seccion>();
  @Output() seccionUpdated = new EventEmitter<Seccion>();
  @Output() seccionDeleted = new EventEmitter<number>();
  @Output() editSeccion = new EventEmitter<Seccion | null>();
  @Output() back = new EventEmitter<void>();

  secciones: Seccion[] = [];
  loading: boolean = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private seccionService: SeccionService,
    private leccionService: LeccionService,
    private notification: NotificationService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadSecciones();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Detectar cambios en el módulo
    if (changes['modulo']) {
      // Limpiar secciones anteriores inmediatamente
      this.secciones = [];
      this.error = null;

      // Cargar nuevas secciones
      this.loadSecciones();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadSecciones(): void {
    if (!this.modulo) {
      return;
    }

    this.loading = true;
    this.error = null;

    this.seccionService.getSeccionesByModuloId(this.modulo.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.secciones = response.data;
            // Ordenar las secciones por orden
            this.secciones.sort((a, b) => a.orden - b.orden);
          } else {
            this.error = response.msg || 'Error al cargar las secciones';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar las secciones. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar secciones:', error);
        }
      });
  }

  onSeccionClick(seccion: Seccion): void {
    this.seccionSelected.emit(seccion);
  }

  onCreateSeccion(): void {
    this.editSeccion.emit(null);
  }

  onEditSeccion(seccion: Seccion, event: Event): void {
    event.stopPropagation();
    this.editSeccion.emit(seccion);
  }

  onDeleteSeccion(seccionId: number, event: Event): void {
    event.stopPropagation();
    // Implementar lógica para confirmar y eliminar una sección
    // Emitir seccionDeleted cuando se elimine la sección
  }

  onDrop(event: CdkDragDrop<Seccion[]>): void {
    if (!this.editable) return;

    // Actualizar el orden localmente
    moveItemInArray(this.secciones, event.previousIndex, event.currentIndex);

    // Actualizar el orden en el backend
    const seccionIds = this.secciones.map(seccion => seccion.id);

    // Implementar lógica para reordenar secciones en el backend
  }

  getLeccionesCount(seccion: Seccion): number {
    return seccion.lecciones?.length || 0;
  }

  isSeccionCompleted(seccion: Seccion): boolean {
    return seccion.completado || false;
  }
}
