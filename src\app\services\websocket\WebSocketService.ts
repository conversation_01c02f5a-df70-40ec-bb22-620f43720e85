import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  Subject,
  timer,
  Subscription,
  EMPTY,
  of,
} from 'rxjs';
import {
  filter,
  map,
  takeUntil,
  switchMap,
  shareReplay,
  catchError,
  debounceTime,
  timeout,
} from 'rxjs/operators';
import SockJS from 'sockjs-client';
import { Client, IMessage } from '@stomp/stompjs';
import { environment } from '@src/environments/environment';
import { GeneralService } from '../general.service';
import { HttpClient } from '@angular/common/http';
import { GenericResponse } from '@app/models/backend/generic-response';

export interface WebSocketMessage {
  type: string;
  payload: any;
  destination?: string;
}

@Injectable({ providedIn: 'root' })
export class WebSocketService {
  private stomp: Client;
  private messages$ = new Subject<WebSocketMessage>();
  private connected$ = new BehaviorSubject<boolean>(false);
  private activeSubscriptions = new Set<string>();
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 15; // Aumentar intentos para reinicio de servidor
  private pollingSub?: Subscription;
  private readonly pollingIntervalMs = 30000;
  private stopPolling$ = new Subject<void>();
  private static instance: WebSocketService;
  private connectionInProgress = false;
  private messageQueue: WebSocketMessage[] = [];
  private readonly MESSAGE_QUEUE_THROTTLE = 100; // 100ms entre mensajes
  private lastMessageTime = 0;
  private messageQueueTimeout: any = null;
  private lastUserStatusRequest = 0;
  private serverRestartDetected = false; // Detectar reinicio de servidor
  private lastConnectionError: string | null = null;
  private healthCheckInProgress = false;
  private readonly USER_STATUS_REQUEST_THROTTLE = 10000; // 10 segundos entre solicitudes de estado
  private connectionEstablishedEmitted = false;
  private visibilityChangeListener: any = null;
  private wasHidden = false;

  constructor(private auth: GeneralService, private http: HttpClient) {
    if (WebSocketService.instance) {
      return WebSocketService.instance;
    }
    WebSocketService.instance = this;
    this.stomp = new Client();

    this.connected$
      .pipe(filter((connected) => !connected))
      .subscribe(() => this.startHttpPolling());
    this.connected$
      .pipe(filter((connected) => connected))
      .subscribe(() => this.stopHttpPolling());

    // Configurar detector de visibilidad para reconectar cuando se reanuda el sistema
    this.setupVisibilityChangeDetection();

    // Configurar detector de beforeunload para manejar refresh
    this.setupBeforeUnloadDetection();
  }

  /**
   * Configura la detección de eventos beforeunload para manejar refresh
   */
  private setupBeforeUnloadDetection(): void {
    window.addEventListener('beforeunload', () => {
      // Marcar que estamos en proceso de refresh
      if (this.isConnected()) {
        console.log(
          'WebSocketService: Detectado evento beforeunload, marcando para refresh'
        );
        sessionStorage.setItem('wsRefreshing', 'true');

        // No desconectar explícitamente, dejar que el navegador cierre la conexión
        // Esto evita enviar mensajes de desconexión innecesarios al backend
      }
    });
  }

  /**
   * Configura la detección de cambios de visibilidad para reconectar cuando se reanuda el sistema
   */
  private setupVisibilityChangeDetection(): void {
    // Limpiar listener anterior si existe
    if (this.visibilityChangeListener) {
      document.removeEventListener(
        'visibilitychange',
        this.visibilityChangeListener
      );
    }

    // Crear nuevo listener
    this.visibilityChangeListener = () => {
      // Cuando la página se oculta (suspensión del sistema, cambio de pestaña, etc.)
      if (document.hidden) {
        this.wasHidden = true;
        // Reducir logs en consola
        if (environment.production === false) {
         /* console.log(
            'WebSocketService: Página oculta, marcando para reconexión cuando se reanude'
          );*/
        }
      }
      // Cuando la página vuelve a ser visible
      else if (this.wasHidden) {
        this.wasHidden = false;

        // Reducir logs en consola
        if (environment.production === false) {
          console.log(
            'WebSocketService: Página visible nuevamente, verificando conexión WebSocket'
          );
        }

        // Verificar si estamos en la página de login o si no hay token
        if (location.href.includes('/auth/login') || !this.auth.getToken()) {
          return;
        }

        // Verificar si el WebSocket está desconectado y reconectar
        if (!this.isConnected()) {
          // Reducir logs en consola
          if (environment.production === false) {
            console.log(
              'WebSocketService: Reconectando WebSocket después de reanudar el sistema'
            );
          }

          // Reiniciar los intentos de reconexión
          this.reconnectAttempts = 0;
          window.wsConnectionInitiated = false;
          this.connectionInProgress = false;

          // Intentar reconectar
          setTimeout(() => {
            // Verificar nuevamente si hay token y no estamos en login
            if (
              this.auth.getToken() &&
              !location.href.includes('/auth/login')
            ) {
              this.connect();

              // Emitir mensaje de reconexión para notificar a los componentes
              this.messages$.next({
                type: 'CONNECTION_RESUMED',
                payload: { timestamp: new Date().toISOString() },
              });
            }
          }, 1000);
        }
      }
    };

    // Registrar el listener
    document.addEventListener(
      'visibilitychange',
      this.visibilityChangeListener
    );
  }

  /**
   * Conecta al WebSocket con manejo mejorado de errores y reintentos
   * @returns void
   */
  connect(): void {
    // Verificar si ya estamos conectados o en proceso de conexión
    if (
      this.stomp.connected ||
      !this.auth.getToken() ||
      this.connectionInProgress ||
      location.href.includes('/auth/login')
    ) {
      return;
    }

    // Verificar si ya hay una conexión global iniciada
    if (window.wsConnectionInitiated) {
     

      // Esperar un poco y verificar si la conexión se estableció
      setTimeout(() => {
        if (this.stomp.connected) {
          return;
        } else if (window.wsConnectionInitiated) {
          

          // Esperar un poco más
          setTimeout(() => {
            if (!this.stomp.connected && window.wsConnectionInitiated) {
              
              window.wsConnectionInitiated = false;
              this.connect();
            }
          }, 3000);
        } else {
          // La bandera se restableció, intentar conectar
          this.connect();
        }
      }, 2000);

      return;
    }

    // Marcar como en proceso de conexión
    window.wsConnectionInitiated = true;
    this.connectionInProgress = true;

    console.log('WebSocketService: Iniciando nueva conexión WebSocket...');

    // Obtener las URLs para la conexión
    const wsUrl = environment.wsUrl;
    const protocol = wsUrl.startsWith('wss') ? 'https://' : 'http://';
    const without = wsUrl.replace(/^(ws|wss):\/\//, '').replace(/\/ws\/?$/, '');
    const sockJsUrl = `${protocol}${without}/ws`;

    try {
      // Configurar el cliente STOMP con manejo mejorado de errores
      this.stomp.configure({
        brokerURL: wsUrl,
        connectHeaders: {
          Authorization: `Bearer ${this.auth.getToken()}`,
          // Agregar un timestamp para evitar problemas de caché
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
          'X-Timestamp': Date.now().toString(),
        },
        // Desactivar logs de depuración
        debug: () => {},
        // Desactivar reconexión automática para manejarla manualmente
        reconnectDelay: 0,
        // Usar SockJS como factory para WebSocket
        webSocketFactory: () => {
          const sockjs = new SockJS(sockJsUrl, null, {
            timeout: 15000, // Reducir timeout para detectar problemas más rápido
            transports: ['websocket', 'xhr-streaming', 'xhr-polling'], // Priorizar WebSocket
          });

          // Agregar listeners para detectar errores específicos
          sockjs.addEventListener('close', (event: any) => {
            if (event.code === 1006 || event.code === 1002) {
              // Códigos que indican problemas de conexión/servidor
              this.serverRestartDetected = true;
              this.lastConnectionError = `Connection closed with code: ${event.code}`;

              // Emitir evento de reinicio de servidor detectado
              this.messages$.next({
                type: 'SERVER_RESTART_DETECTED',
                payload: {
                  error: `Connection closed with code: ${event.code}`,
                  timestamp: new Date().toISOString(),
                },
              });
            }
          });

          return sockjs;
        },
        // Callbacks para eventos de conexión
        onConnect: () => this.handleConnect(),
        onDisconnect: () => this.handleDisconnect(),
        onWebSocketClose: () => this.handleDisconnect(),
        onStompError: (frame) => {
          console.error('Error STOMP:', frame);
          // Detectar errores específicos de servidor no disponible
          if (frame && frame.headers && frame.headers['message']) {
            this.lastConnectionError = frame.headers['message'];
            if (
              frame.headers['message'].includes('502') ||
              frame.headers['message'].includes('Bad Gateway') ||
              frame.headers['message'].includes('Connection refused')
            ) {
              this.serverRestartDetected = true;
              // Emitir evento de reinicio de servidor detectado
              this.messages$.next({
                type: 'SERVER_RESTART_DETECTED',
                payload: {
                  error: frame.headers['message'],
                  timestamp: new Date().toISOString(),
                },
              });
            }
          }
          this.handleDisconnect();
        },
      });

      // Activar la conexión
      this.stomp.activate();

      // Establecer un timeout para la conexión (más corto para reinicio de servidor)
      const connectionTimeout = this.serverRestartDetected ? 10000 : 20000;
      setTimeout(() => {
        // Si después del timeout no se ha conectado, reiniciar
        if (this.connectionInProgress && !this.stomp.connected) {
          console.warn(
            `Timeout de conexión WebSocket después de ${
              connectionTimeout / 1000
            } segundos`
          );
          if (!this.serverRestartDetected) {
            this.serverRestartDetected = true; // Marcar como posible reinicio de servidor
          }
          this.handleDisconnect();
        }
      }, connectionTimeout);
    } catch (error) {
      console.error('Error al configurar WebSocket:', error);
      this.handleDisconnect();
    }
  }

  private handleConnect(): void {
    console.log('WebSocketService: Conexión WebSocket establecida');

    this.connected$.next(true);
    this.reconnectAttempts = 0;
    this.connectionInProgress = false;
    this.serverRestartDetected = false; // Resetear detección de reinicio
    this.lastConnectionError = null; // Limpiar último error

    // Suscribirse a los tópicos
    this.subscribeToTopics();

    const userId = this.auth.getUserId();
    if (!userId) {
      return;
    }

    // Verificar si es una reconexión después de un refresh
    const isRefresh = sessionStorage.getItem('wsRefreshing') === 'true';
    if (isRefresh) {
      //console.log('WebSocketService: Detectada reconexión después de refresh');
      sessionStorage.removeItem('wsRefreshing');
    }

    // Si se había detectado un reinicio de servidor y ahora estamos conectados, notificar
    if (this.serverRestartDetected) {
      //console.log(
        //'WebSocketService: Conexión restaurada después de reinicio de servidor'
      //);
      this.messages$.next({
        type: 'CONNECTION_RESUMED',
        payload: {
          reason: 'server_restart',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Enviar mensajes en secuencia con throttling
    this.sendWithThrottle('/app/user.connect', { userId, isRefresh });

    // Solicitar notificaciones y contador de no leídas
    this.sendWithThrottle('/app/notifications/all', { userId });
    this.sendWithThrottle('/app/notifications.unreadCount', userId);

    // Solicitar estado de usuario y actividad
    this.sendWithThrottle('/app/user.status', { userId });
    this.sendWithThrottle('/app/user.activity', { userId });

    // Solicitar explícitamente el estado de todos los usuarios
    // Este es el endpoint que devuelve la lista completa de usuarios conectados
    this.lastUserStatusRequest = Date.now();
    this.sendWithThrottle('/app/users.status.all');

    // Solicitar explícitamente la lista de usuarios
    this.sendWithThrottle('/app/users.list', { page: 0, size: 10 });

    // Asegurarse de que estamos suscritos a los tópicos importantes
    this.subscribeToDynamicTopic('/topic/users/list', 'USERS_LIST');
    this.subscribeToDynamicTopic('/topic/notifications', 'TOPIC');

    // Suscribirse a tópicos específicos del usuario
    this.subscribeToDynamicTopic(
      `/user/${userId}/queue/notifications`,
      'USER_NOTIFICATIONS'
    );
    this.subscribeToDynamicTopic(
      `/user/${userId}/queue/notifications.count`,
      'NOTIFICATIONS_COUNT'
    );
    this.subscribeToDynamicTopic(
      `/user/${userId}/queue/notifications.read`,
      'NOTIFICATIONS_READ'
    );

    // Emitir evento de conexión establecida solo una vez por sesión
    if (!this.connectionEstablishedEmitted) {
      this.connectionEstablishedEmitted = true;
      this.messages$.next({
        type: 'CONNECTION_ESTABLISHED',
        payload: { userId, timestamp: new Date().toISOString(), isRefresh },
      });
    }

    // Procesar la cola de mensajes pendientes
    this.processMessageQueue();
  }

  private sendWithThrottle(dest: string, body: any = {}): void {
    const message: WebSocketMessage = {
      type: 'SEND',
      payload: body,
      destination: dest,
    };
    this.messageQueue.push(message);
    this.processMessageQueue();
  }

  private processMessageQueue(): void {
    if (this.messageQueueTimeout) return;

    this.messageQueueTimeout = setTimeout(() => {
      const now = Date.now();
      if (now - this.lastMessageTime >= this.MESSAGE_QUEUE_THROTTLE) {
        // Procesar hasta 3 mensajes a la vez para mejorar el rendimiento
        const batchSize = Math.min(3, this.messageQueue.length);
        for (let i = 0; i < batchSize; i++) {
          const message = this.messageQueue.shift();
          if (message) {
            // Verificar el tipo de mensaje
            if (message.type === 'SEND') {
              // Enviar mensaje normal
              this.send(message.destination!, message.payload);
            } else if (
              message.type === 'SUBSCRIBE' &&
              message.payload &&
              message.payload.topic
            ) {
              // Procesar suscripción pendiente
              try {
                this.subscribe(
                  message.payload.topic,
                  message.payload.type || 'TOPIC'
                );
              } catch (error) {
                // Error silencioso - se volverá a intentar en el próximo ciclo
                // Volver a poner en la cola si falló
                this.messageQueue.push(message);
              }
            }
          }
        }
        this.lastMessageTime = now;
      }
      this.messageQueueTimeout = null;
      if (this.messageQueue.length > 0) {
        this.processMessageQueue();
      }
    }, this.MESSAGE_QUEUE_THROTTLE);
  }

  private handleDisconnect(): void {
    if (!this.connected$.value) return;

    console.log('WebSocketService: Manejando desconexión WebSocket...');

    this.connected$.next(false);
    this.activeSubscriptions.clear();
    window.wsConnectionInitiated = false;
    this.connectionInProgress = false;
    this.connectionEstablishedEmitted = false;

    // Limpiar cualquier timeout pendiente
    if (this.messageQueueTimeout) {
      clearTimeout(this.messageQueueTimeout);
      this.messageQueueTimeout = null;
    }

    // Limpiar la cola de mensajes para evitar envíos duplicados
    this.messageQueue = [];

    // Verificar si estamos en la página de login
    const isLoginPage = window.location.href.includes('/auth/login');

    // No intentar reconectar si estamos en la página de login
    if (isLoginPage) {
      this.reconnectAttempts = 0;
      return;
    }

    // Verificar si hay un token válido
    const token = this.auth.getToken();
    if (!token) {
      this.reconnectAttempts = 0;
      return;
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      // Calcular delay basado en si se detectó reinicio de servidor
      let delay;
      if (this.serverRestartDetected && this.reconnectAttempts < 5) {
        // Para reinicio de servidor, usar delays más cortos al principio
        delay = Math.min(5000, 1000 + this.reconnectAttempts * 1000);
      } else {
        // Usar backoff exponencial normal
        delay = Math.min(
          15000, // Aumentar límite máximo a 15 segundos
          1000 * Math.pow(1.5, this.reconnectAttempts)
        );
      }

      this.reconnectAttempts++;

      // No mostrar mensajes de error en la consola para evitar ruido
      // Solo mostrar en modo desarrollo
      if (environment.production === false) {
        const restartMsg = this.serverRestartDetected
          ? ' (reinicio de servidor detectado)'
          : '';
        console.log(
          `Intentando reconectar WebSocket en ${
            delay / 1000
          } segundos (intento ${this.reconnectAttempts}/${
            this.maxReconnectAttempts
          })${restartMsg}...`
        );
      }

      setTimeout(() => {
        if (
          !this.stomp.connected &&
          !this.connectionInProgress &&
          !window.wsConnectionInitiated
        ) {
          // Verificar nuevamente si hay un token válido antes de intentar reconectar
          if (this.auth.getToken()) {
            // Si se detectó reinicio de servidor, hacer health check primero
            if (this.serverRestartDetected && this.reconnectAttempts <= 8) {
              this.checkServerHealthAndReconnect();
            } else {
              this.connect();
            }
          }
        }
      }, delay);
    } else {
      // No mostrar mensajes de error en la consola para evitar ruido
      // Solo mostrar en modo desarrollo
      if (environment.production === false) {
        console.log(
          `Se alcanzó el número máximo de intentos de reconexión (${this.maxReconnectAttempts}). Cambiando a HTTP polling.`
        );
      }

      this.startHttpPolling();

      // Reiniciar los intentos de reconexión después de un tiempo más corto (30 segundos)
      setTimeout(() => {
        this.reconnectAttempts = 0;
        window.wsConnectionInitiated = false;

        // Intentar conectar nuevamente después de reiniciar los intentos
        if (this.auth.getToken() && !isLoginPage) {
          this.connect();
        }
      }, 30000);
    }
  }

  /**
   * Verifica la salud del servidor antes de intentar reconectar WebSocket
   */
  private checkServerHealthAndReconnect(): void {
    if (this.healthCheckInProgress) {
      return;
    }

    this.healthCheckInProgress = true;

    // Hacer una petición HTTP simple para verificar si el servidor está disponible
    const healthCheckUrl = `${environment.url}api/health`;

    this.http
      .get(healthCheckUrl, {
        headers: {
          Authorization: `Bearer ${this.auth.getToken()}`,
        },
      })
      .pipe(
        timeout(5000), // Usar timeout como operador RxJS
        catchError((error) => {
          // Si el endpoint de health no existe, intentar con otro endpoint conocido
          if (error.status === 404) {
            return this.http
              .get(`${environment.url}api/notifications/count`, {
                headers: {
                  Authorization: `Bearer ${this.auth.getToken()}`,
                },
              })
              .pipe(
                timeout(5000), // Usar timeout como operador RxJS
                catchError(() => of(null))
              );
          }
          return of(null);
        })
      )
      .subscribe({
        next: (response) => {
          this.healthCheckInProgress = false;
          if (response !== null) {
            // Servidor está disponible, intentar reconectar
            if (environment.production === false) {
              console.log(
                'WebSocketService: Servidor disponible, intentando reconectar...'
              );
            }
            this.serverRestartDetected = false; // Servidor ya está disponible
            this.connect();
          } else {
            // Servidor aún no disponible, esperar más tiempo
            if (environment.production === false) {
              console.log(
                'WebSocketService: Servidor aún no disponible, esperando...'
              );
            }
            // Continuar con la lógica normal de reconexión
            setTimeout(() => {
              if (!this.stomp.connected && this.auth.getToken()) {
                this.connect();
              }
            }, 2000);
          }
        },
        error: () => {
          this.healthCheckInProgress = false;
          // En caso de error, continuar con reconexión normal
          setTimeout(() => {
            if (!this.stomp.connected && this.auth.getToken()) {
              this.connect();
            }
          }, 2000);
        },
      });
  }

  private subscribeToTopics(): void {
    const userId = this.auth.getUserId();
    if (!userId) {
      return;
    }

    // Tópicos específicos del usuario
    const userTopics = [
      `/user/${userId}/queue/notifications`,
      `/user/${userId}/queue/messages`,
      `/user/${userId}/queue/notifications.count`,
      `/user/${userId}/queue/notifications.read`,
    ];

    // Tópicos globales (compartidos entre todos los usuarios)
    const globalTopics = [
      '/topic/users.status',
      '/topic/user.disconnected',
      '/topic/users/status', // Tópico para actualizaciones individuales
      '/topic/users/status/all', // Tópico para la lista completa de usuarios
      '/topic/users/list', // Tópico para la lista de usuarios
      '/topic/anuncios', // Tópico para la lista completa de anuncios
      '/topic/anuncios/recientes', // Tópico para anuncios recientes
      '/topic/anuncios/new', // Tópico para nuevos anuncios
      '/topic/anuncios/update', // Tópico para actualizaciones de anuncios
      '/topic/anuncios/delete', // Tópico para eliminación de anuncios
      '/topic/notifications', // Tópico para notificaciones broadcast
    ];

    // Suscribirse a tópicos específicos del usuario
    userTopics.forEach((topic) => {
      if (!this.activeSubscriptions.has(topic)) {
        this.subscribe(topic, 'TOPIC');
      }
    });

    // Suscribirse a tópicos globales
    globalTopics.forEach((topic) => {
      if (!this.activeSubscriptions.has(topic)) {
        // Para el tópico de lista de usuarios, usar un tipo específico
        if (topic === '/topic/users/list') {
          this.subscribe(topic, 'USERS_LIST');
        } else {
          this.subscribe(topic, 'TOPIC');
        }
      }
    });
  }

  private subscribe(topic: string, type: string): void {
    // Si ya estamos suscritos a este tópico, no hacer nada
    if (this.activeSubscriptions.has(topic)) {
      return;
    }

    // Verificar si hay una conexión STOMP activa
    if (!this.isConnected()) {
      // Agregar a una cola de suscripciones pendientes para procesar cuando se conecte
      this.messageQueue.push({
        type: 'SUBSCRIBE',
        payload: { topic, type },
        destination: topic,
      });
      return;
    }

    try {
      // Intentar suscribirse al tópico
      this.stomp.subscribe(topic, (message: IMessage) => {
        try {
          const payload = JSON.parse(message.body);
          const messageType =
            type === 'TOPIC' ? topic.split('/').pop()!.toUpperCase() : type;
          this.messages$.next({
            type: messageType,
            payload,
            destination: topic,
          });
        } catch (error) {
          // Error silencioso
        }
      });

      // Marcar como suscrito
      this.activeSubscriptions.add(topic);
    } catch (error) {
      // Intentar nuevamente cuando la conexión esté disponible
      this.messageQueue.push({
        type: 'SUBSCRIBE',
        payload: { topic, type },
        destination: topic,
      });
    }
  }

  private send(dest: string, body: any = {}): void {
    if (!this.stomp.connected) {
      this.messageQueue.push({
        type: 'SEND',
        payload: body,
        destination: dest,
      });
      return;
    }

    try {
      this.stomp.publish({
        destination: dest,
        body: JSON.stringify(body),
      });
    } catch (error) {
      // Agregar a la cola para reintentar
      this.messageQueue.push({
        type: 'SEND',
        payload: body,
        destination: dest,
      });
    }
  }

  sendMessage(dest: string, body: any = {}): void {
    this.sendWithThrottle(dest, body);
  }

  getConnectionStatus(): Observable<boolean> {
    return this.connected$.asObservable();
  }

  getMessages(): Observable<WebSocketMessage> {
    return this.messages$.asObservable();
  }

  private messagesByTypeCache: Map<string, Observable<any>> = new Map();

  getMessagesByType(type: string): Observable<any> {
    if (!this.messagesByTypeCache.has(type)) {
      // Usar debounceTime para reducir la frecuencia de emisiones
      this.messagesByTypeCache.set(
        type,
        this.messages$.pipe(
          filter((msg) => msg.type === type),
          map((msg) => msg.payload),
          debounceTime(100), // Agrupar mensajes que llegan en rápida sucesión
          shareReplay(1)
        )
      );
    }
    return this.messagesByTypeCache.get(type)!;
  }

  private messagesByDestinationCache: Map<string, Observable<any>> = new Map();

  getMessagesByDestination(
    destination: string,
    type?: string
  ): Observable<any> {
    const cacheKey = `${destination}-${type || 'any'}`;
    if (!this.messagesByDestinationCache.has(cacheKey)) {
      this.messagesByDestinationCache.set(
        cacheKey,
        this.messages$.pipe(
          filter(
            (msg) =>
              msg.destination === destination && (!type || msg.type === type)
          ),
          map((msg) => msg.payload),
          debounceTime(100), // Agrupar mensajes que llegan en rápida sucesión
          shareReplay(1)
        )
      );
    }
    return this.messagesByDestinationCache.get(cacheKey)!;
  }

  subscribeToDynamicTopic(topic: string, type: string): void {
    // Verificar si hay una conexión STOMP activa antes de intentar suscribirse
    if (!this.isConnected()) {
      // Agregar a una cola de suscripciones pendientes para procesar cuando se conecte
      this.messageQueue.push({
        type: 'SUBSCRIBE',
        payload: { topic, type },
        destination: topic,
      });
      return;
    }

    try {
      this.subscribe(topic, type);
    } catch (error) {
      // Error silencioso - se intentará nuevamente cuando la conexión esté disponible
    }
  }

  isConnected(): boolean {
    return !!this.stomp.connected;
  }

  requestUsersStatus(): void {
    // Aplicar throttling a las solicitudes de estado de usuarios
    const now = Date.now();
    if (now - this.lastUserStatusRequest > this.USER_STATUS_REQUEST_THROTTLE) {
      this.lastUserStatusRequest = now;
      this.sendWithThrottle('/app/users.status');
    }
  }

  updateUserActivity(): void {
    const userId = this.auth.getUserId();
    if (userId) {
      this.sendWithThrottle('/app/user.activity', { userId });
    }
  }

  private startHttpPolling(): void {
    // Si ya hay una suscripción activa o estamos en la página de login, no iniciar polling
    if (this.pollingSub || location.href.includes('/auth/login')) return;

    // Verificar si hay un token válido
    const token = this.auth.getToken();
    if (!token) return;

    // Aumentar el intervalo de polling para reducir la carga en el servidor
    const pollingInterval = this.pollingIntervalMs * 2;

    this.pollingSub = timer(0, pollingInterval)
      .pipe(
        takeUntil(this.stopPolling$),
        switchMap(() => {
          // Verificar nuevamente si hay token y no estamos en login en cada ciclo
          if (!this.auth.getToken() || location.href.includes('/auth/login')) {
            this.stopHttpPolling(); // Detener el polling si no hay token o estamos en login
            return EMPTY;
          }

          const userId = this.auth.getUserId();
          if (!userId) return EMPTY;

          // Verificar si ya estamos conectados por WebSocket
          if (this.stomp.connected) {
            return EMPTY; // No hacer polling si ya estamos conectados
          }

          return this.http
            .get<GenericResponse<any>>(
              `${environment.url}api/notifications/polling/${userId}`
            )
            .pipe(
              catchError((error) => {
                // Si obtenemos un error 401 o 403, detener el polling
                if (error.status === 401 || error.status === 403) {
                  console.error(
                    'Error de autorización en polling HTTP:',
                    error.status,
                    error.message
                  );
                  this.stopHttpPolling();
                }
                return of(null);
              })
            );
        })
      )
      .subscribe((response) => {
        if (response?.rpta === 1) {
          this.messages$.next({
            type: 'NOTIFICATIONS_UPDATED',
            payload: response.data,
          });
        }
      });
  }

  private stopHttpPolling(): void {
    if (this.pollingSub) {
      this.pollingSub.unsubscribe();
      this.pollingSub = undefined;
    }
  }

  // Variable para evitar desconexiones múltiples
  private disconnecting = false;

  disconnect(): void {
    // Evitar múltiples desconexiones simultáneas
    if (
      this.disconnecting ||
      (!this.stomp.connected && !this.connectionInProgress)
    )
      return;

    // Marcar como en proceso de desconexión
    this.disconnecting = true;

    try {
      const userId = this.auth.getUserId();
      if (userId) {
        // Enviar una sola solicitud de desconexión
        this.send('/app/user.disconnect', { userId });
      }

      // Desactivar el cliente STOMP
      this.stomp.deactivate();

      // Actualizar el estado
      this.connected$.next(false);
      this.activeSubscriptions.clear();
      window.wsConnectionInitiated = false;
      this.connectionInProgress = false;
      this.connectionEstablishedEmitted = false;

      // Limpiar la cola de mensajes
      this.messageQueue = [];
      if (this.messageQueueTimeout) {
        clearTimeout(this.messageQueueTimeout);
        this.messageQueueTimeout = null;
      }
    } catch (error) {
      // Error silencioso durante la desconexión
    } finally {
      // Restablecer el flag de desconexión después de un breve retraso
      setTimeout(() => {
        this.disconnecting = false;
      }, 2000);
    }
  }

  /**
   * Limpia los recursos cuando se destruye el servicio
   */
  cleanup(): void {
    // Limpiar el listener de visibilitychange
    if (this.visibilityChangeListener) {
      document.removeEventListener(
        'visibilitychange',
        this.visibilityChangeListener
      );
      this.visibilityChangeListener = null;
    }

    // Limpiar el listener de beforeunload
    window.removeEventListener('beforeunload', () => {});

    // Desconectar si está conectado
    if (this.isConnected()) {
      this.disconnect();
    }

    // Detener el polling HTTP
    this.stopHttpPolling();

    // Limpiar variables de estado
    this.connectionInProgress = false;
    window.wsConnectionInitiated = false;
    this.connectionEstablishedEmitted = false;
    this.reconnectAttempts = 0;

    // Limpiar colas y timeouts
    this.messageQueue = [];
    if (this.messageQueueTimeout) {
      clearTimeout(this.messageQueueTimeout);
      this.messageQueueTimeout = null;
    }
  }
}
