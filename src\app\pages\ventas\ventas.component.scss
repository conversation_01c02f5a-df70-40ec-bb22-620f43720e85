.ventas-container {
  padding: 1.5rem;
  max-width: 100%;
  margin: 0 auto;
}

  /***************************************************
   * BORDE Y LABEL DE LOS CAMPOS (#006a66)
   ***************************************************/
  /* 1) El contorno outline normal, SIEMPRE #006a66 */
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #26AFE5 !important;
    border-radius: 0.5rem;
    box-shadow: 0.5rem 0.5rem 0px 0px #F5F7FB;
    border-radius: 0.75rem;

  }
  /* 2) El "trazo grueso" (cuando enfoca) también #26AFE5 */
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #26AFE5 !important;
    box-shadow: 0.5rem 0.5rem 0px 0px #F5F7FB;
    border-radius: 0.75rem;

  }

  /* Estilos para el tema oscuro */
  :host-context(body.dark-theme) {
    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
      color: rgba(255, 255, 255, 0.3) !important;
      box-shadow: none !important;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
      color: #1e4976 !important;
      box-shadow: none !important;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
      color: #1e4976 !important;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-select-arrow {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    ::ng-deep .mat-form-field-appearance-outline .mat-select-value-text {
      color: #ffffff !important;
    }
  }
  /* 3) Etiquetas (label) en negro */
  ::ng-deep .mat-form-field-label {
    color: #000 !important;
    font-weight: 500;
    transform: translateY(0) scale(1) !important;
    transition: transform 0.2s ease-in-out !important;
  }

  ::ng-deep .mat-form-field.mat-focused .mat-form-field-label,
  ::ng-deep .mat-form-field.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.28125em) scale(0.75) !important;
    color: #26AFE5 !important;
  }

  /* 4) Quitar cualquier hover/focus que cambie color a otro */
  ::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-hover:hover
    .mat-form-field-outline-thick {
    color: #26AFE5 !important;
  }
  .form-section mat-card{
    background-color:  #FFF !important;
  }

  ::ng-deep .mat-input-element {
    caret-color: #000; /* Asegúrate de que el cursor sea visible */
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #26AFE5; /* Color del borde cuando no está enfocado */
  }

  ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
    color: #26AFE5; /* Color del borde cuando está enfocado */
  }
  /***************************************************
   * TEXTO INTERNO DE LOS INPUTS (placeholder/typed)
   ***************************************************/
  /* Normalmente Angular Material deja el texto en negro,
     pero si necesitas forzar:
  */
  ::ng-deep .mat-input-element,
  ::ng-deep .mat-select-value-text {
    color: #000 !important; /* Texto real que escribe el usuario */
  }

.header-card {
  margin-bottom: 1.5rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-buttons {
      display: flex;
      gap: 16px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  width: 100%;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    user-select: none;
    background-color: #f5f5f5;
    border-radius: 4px;

    &:hover {
      background-color: #eeeeee;
    }

    span {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .filter-form {
    padding: 16px;
    width: 100%;

    .filter-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      mat-form-field {
        flex: 1;
        min-width: 200px;
      }
    }

    .filter-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 16px;

      button {
        min-width: 120px;

        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

/* Estilos para el tema oscuro en el filtro */
:host-context(body.dark-theme) {
  .filter-card {
    background-color: var(--sidenav-dark) !important;

    .filter-header {
      background-color: #1e4976 !important;
      color: #ffffff !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      span {
        color: #ffffff !important;
      }

      mat-icon {
        color: #ffffff !important;
      }
    }

    .filter-form {
      background-color: var(--sidenav-dark) !important;

      mat-form-field {
        .mat-form-field-flex {
          background-color: transparent !important;
        }

        .mat-form-field-outline {
          color: rgba(255, 255, 255, 0.3) !important;
        }

        .mat-form-field-outline-thick {
          color: #1e4976 !important;
        }

        .mat-form-field-label {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        .mat-input-element {
          color: #ffffff !important;
        }

        .mat-select-value-text {
          color: #ffffff !important;
        }

        .mat-select-arrow {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        .mat-datepicker-toggle {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        .mat-icon {
          color: rgba(255, 255, 255, 0.7) !important;
        }
      }

      .filter-actions {
        button {
          &.mat-primary {
            background-color: #1e4976 !important;
            color: #ffffff !important;
          }

          &.mat-warn {
            background-color: #c62828 !important;
            color: #ffffff !important;
          }

          mat-icon {
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

// Estilos para pantallas pequeñas
@media (max-width: 768px) {
  .filter-form {
    .filter-row {
      flex-direction: column;

      mat-form-field {
        width: 100%;
      }
    }
  }
}

.table-card {
  .table-container {
    position: relative;
    min-height: 200px;
    overflow: auto;
  }

  table {
    width: 100%;
  }

  .mat-column-codigo {
    width: 100px;
    padding-right: 2rem !important;
  }

  .mat-column-nombres_apellidos {
    min-width: 200px;
  }

  .mat-column-movilContacto {
    width: 120px;
  }

  .mat-column-email {
    min-width: 200px;
  }

  .mat-column-tipoFibra {
    width: 120px;
  }

  .mat-column-acciones {
    width: 100px;
    text-align: center;
  }

  .mat-column-documento {
    padding-right: 1rem !important;
  }
  .mat-column-agente , .mat-column-coordinador {
    max-width: 20% !important;
    width: 100% !important;
  }
}

.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.no-data-message {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: rgba(0, 0, 0, 0.54);
}
.boton-pendiente-validar {
  background-color: #18a724 !important;
  color: white !important;
  border: none !important;

}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .ventas-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch !important;

    button {
      width: 100%;
    }
  }

  .table-container {
    overflow-x: auto;
  }

  .mat-column-email,
  .mat-column-tipoFibra {
    display: none;
  }
}

.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;

  &.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 20px;
}
.modal-content-editar {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 70%;
  max-height: 90vh;
  overflow-y: auto;


  &.azulino-modal {
    &.dark-theme {
      background-color: #1a2035 !important;
      color: white !important;

      .modal-header {
        background-color: #1e2746 !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;

        h2 {
          color: white !important;
        }

        button {
          color: rgba(255, 255, 255, 0.7) !important;

          &:hover {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
          }

          mat-icon {
            color: rgba(255, 255, 255, 0.7) !important;
          }
        }
      }

      .modal-body {
        background-color: #1a2035 !important;

        mat-card {
          background-color: #1e2746 !important;
          color: white !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;

          mat-card-header {
            background-color: rgba(25, 118, 210, 0.2) !important;
            border-bottom-color: rgba(255, 255, 255, 0.1) !important;

            mat-card-title {
              color: #4dabf5 !important;
            }
          }

          mat-card-content {
            color: rgba(255, 255, 255, 0.9) !important;
          }
        }

        .mat-form-field {
          .mat-form-field-label {
            color: rgba(255, 255, 255, 0.7) !important;
          }

          .mat-form-field-outline {
            color: rgba(255, 255, 255, 0.3) !important;
            box-shadow: none !important;
          }

          input, textarea, select, mat-select {
            color: white !important;
          }

          .mat-select-arrow {
            color: rgba(255, 255, 255, 0.7) !important;
          }
        }

        button {
          &.mat-primary {
            background-color: rgba(25, 118, 210, 0.3) !important;
            color: white !important;

            &:hover {
              background-color: rgba(25, 118, 210, 0.5) !important;
            }
          }

          &.mat-warn {
            background-color: rgba(244, 67, 54, 0.3) !important;
            color: white !important;

            &:hover {
              background-color: rgba(244, 67, 54, 0.5) !important;
            }
          }

          mat-icon {
            color: white !important;
          }
        }

        .grid-two-columns, .grid-three-columns {
          column-gap: 1rem !important;
        }
      }

      .modal-footer {
        background-color: #1e2746 !important;
        border-top-color: rgba(255, 255, 255, 0.1) !important;

        button {
          &.mat-button {
            color: rgba(255, 255, 255, 0.7) !important;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
              color: white !important;
            }
          }

          &.mat-raised-button.mat-primary {
            background-color: rgba(25, 118, 210, 0.3) !important;
            color: white !important;

            &:hover {
              background-color: rgba(25, 118, 210, 0.5) !important;
            }
          }
        }
      }
    }
  }
}
  /***************************************************
   * GRID 3x3 Y GRID 2x2
   ***************************************************/
   /* Estilos para los formularios en el modal */
  .form-section {
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    mat-card-header {
      padding: 1rem;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;

      mat-card-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1976d2;
      }
    }

    mat-card-content {
      padding: 1.25rem;
    }
  }

   .grid-three-columns {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    column-gap: 1rem;
    margin-bottom: 1rem;
    width: 100%;
  }
  .grid-two-columns {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
    width: 100%;
  }
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eeeeee;

  h2 {
    margin: 0;
  }

  button[mat-icon-button] {
    height: 36px;
    width: 36px;
    line-height: 36px;

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      line-height: 18px;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    button {
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
}

.modal-body {
  font-size: 0.9rem;

  .form-header {
    font-size: 1rem;
    font-weight: 500;
  }

  .form-section {
    .form-row {
      .form-label {
        font-size: 0.85rem;
      }

      .form-value {
        font-size: 0.85rem;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .modal-body {
    font-size: 0.8rem;

    .form-header {
      font-size: 0.9rem;
    }

    .form-section {
      .form-row {

        .form-label,
        .form-value {
          font-size: 0.8rem;
        }
      }
    }
  }
}

.form-header {
  background-color: #4CAF50;
  padding: 5px;
  margin: 5px ;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.form-section {
  margin-bottom: 20px;
  border: 1px solid #eee;
}

.form-row {
  display: flex;
  margin-bottom: 5px;
  padding: 2px 5px;
  border-bottom: 1px solid #eee;
}

.form-label {
  width: 40%;
  font-weight: 500;
  color: #666;

}

.form-value {
  width: 60%;
  color: #333;
  border-left: 1px solid #eee;
  text-indent: 10px;
  display: flex;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 0.125rem;

  button {
    min-width: auto;
    padding: 0 8px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #eee;
}

@media print {
  .modal-overlay {
    position: static;
    background: none;
  }

  .modal-content {
    box-shadow: none;
    padding: 0;
  }
  .modal-content-editar {
    box-shadow: none;
    padding: 0;
  }
  .modal-header button,
  .modal-footer {
    display: none;
  }
}

.observacion-modal {
  max-width: 500px;
  width: 90%;

  .modal-body {
    padding: 20px;
  }

  .full-width {
    width: 100%;
  }

  textarea {
    min-height: 100px;
  }
}

.estado-modal {
  max-width: 400px;
  width: 90%;

  .modal-body {
    padding: 20px;
  }

  .full-width {
    width: 100%;
  }
}

.action-buttons {
  button {
    margin-right: 8px;
   }

}
.estado-button{
  min-width: 12.5rem !important;
  border:none !important;
  color: black ;
  text-align: start !important;
  font-size: 0.8rem !important;
  border: 1px solid #000 !important;
 }
 .boton-venta-fallida {
  background-color: #ff4726 !important; // Color naranja
  color: white !important;
  border: none !important;
}
// Estilos para los estados
.estado-valido {
  background-color: #4CAF50 !important;
  color: white !important;
}

.estado-proceso {
  background-color: #FFC107 !important;
  color: black !important;
}

.estado-no-valido {
  background-color: #F44336 !important;
  color: white !important;
}

// Estilos para el tema oscuro
:host-context(body.dark-theme) {
  .ventas-container {
    background-color: var(--background-dark);

    .header-card, .table-card, .filter-card {
      background-color: var(--sidenav-dark);
      color: #ffffff;
    }

    .filter-header {
      color: #ffffff;
    }

    .filter-form {
      background-color: var(--sidenav-dark);
    }

    .mat-table {
      background-color: transparent !important;

      .mat-header-cell {
        color: #ffffff !important;
        background-color: #1e4976 !important;
        font-weight: bold !important;
      }

      .mat-cell {
        color: #ffffff !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
      }

      .mat-row:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }

    .mat-paginator {
      background-color: transparent !important;
      color: #ffffff !important;
    }

    .mat-paginator-page-size-label,
    .mat-paginator-range-label {
      color: #ffffff !important;
    }

    .mat-paginator-navigation-previous,
    .mat-paginator-navigation-next,
    .mat-paginator-navigation-first,
    .mat-paginator-navigation-last {
      color: #ffffff !important;
    }

    .mat-paginator-icon {
      fill: #ffffff !important;
    }

    .estado-proceso {
      background-color: #f57c00 !important;
      color: white !important;
    }

    // Estilos para los botones de acción
    .action-buttons button {
      color: #ffffff !important;
      border-color: rgba(255, 255, 255, 0.3) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      &.mat-primary {
        background-color: var(--primary-dark) !important;
        color: #ffffff !important;
      }

      &.mat-accent {
        background-color: #1976d2 !important;
        color: #ffffff !important;
      }

      &.mat-warn {
        background-color: #c62828 !important;
        color: #ffffff !important;
      }

      mat-icon {
        color: #ffffff !important;
      }
    }
  }

  // Estilos para los modales
  .modal-overlay {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  .modal-content {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important;
  }

  .venta-detalle-modal, .observacion-modal, .estado-modal, .modal-content {
    .modal-header {
      background-color: #1e4976 !important;
      border-bottom-color: rgba(255, 255, 255, 0.1) !important;
      color: #ffffff !important;

      h2, h3 {
        color: #ffffff !important;
      }

      button mat-icon {
        color: #ffffff !important;
      }
    }

    .modal-body {
      background-color: var(--sidenav-dark) !important;
      color: #ffffff !important;

      .form-header {
        color: #ffffff !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
        background-color: #1e4976 !important;
      }

      .form-section {
        background-color: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
      }

      .form-label {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: bold !important;
      }

      .form-value {
        color: #ffffff !important;
        border-left-color: rgba(255, 255, 255, 0.1) !important;
      }

      .form-row {
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
      }

      .mat-form-field-appearance-outline .mat-form-field-outline {
        color: rgba(255, 255, 255, 0.3) !important;
      }

      .mat-form-field-appearance-outline .mat-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      .mat-input-element {
        color: #ffffff !important;
      }

      textarea {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
      }

      .observacion-item {
        background-color: rgba(255, 255, 255, 0.05) !important;
        color: #ffffff !important;

        small {
          color: rgba(255, 255, 255, 0.7) !important;
        }
      }
    }

    .modal-footer {
      border-top-color: rgba(255, 255, 255, 0.1) !important;
      background-color: var(--sidenav-dark) !important;
    }
  }
}

.observaciones-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.observacion-item {
  margin-bottom: 10px;
  background-color: #f5f5f5;

  small {
    display: block;
    color: #666;
    margin-top: 5px;
  }
}

.nueva-observacion-form {
  margin-top: 20px;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

.full-width {
  width: 100%;
}

.filter-form {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  padding: 16px;

  mat-form-field {
    flex: 1;
    min-width: 200px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;

    mat-form-field {
      width: 100%;
    }
  }
}

.direcciones-section {
  margin: 20px 0;

  .direccion-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    mat-form-field {
      flex: 1;
    }
  }
}

.full-width {
  width: 100%;
}

.Documento-column {
  margin-left: 2rem;
}

.selected {
  font-weight: 500;
  color: var(--primary-color);
}

.mat-form-field {
  margin-bottom: 0rem;
}
.mat-form-field-wrapper{
  padding-bottom: 0rem !important;
}
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.pending-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background-color: #18a724;
  border-radius: 50%;
  position: relative;
  animation: pulse-ring 2s infinite;

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #18a724;
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.8);
  }
}

@keyframes pulse-dot {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 167, 36, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(24, 167, 36, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 167, 36, 0);
  }
}

.pending-count {
  color: #ff4726;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  background-color: rgba(255, 71, 38, 0.1);
}

// Para el tema oscuro
:host-context(body.dark-theme) {
  .pending-count {
    background-color: rgba(255, 71, 38, 0.2);
  }

  .pulse-dot {
    background-color: #18a724;
    &::after {
      background-color: #18a724;
    }
  }
}
.faq-add-button {
  padding: 0.375rem 0.75rem;
  background-color: #4e73df;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.faq-add-button:hover {
  background-color: #2e59d9;
}

// ===== ESTILOS PARA MODAL DE GUÍA =====
.guia-modal {
  max-width: 1000px !important;
  width: 95% !important;
  max-height: 95vh !important;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f8fafc;

    h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e40af;
    }

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 0.375rem;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f3f4f6;
      }

      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    .video-container {
      position: relative;
      width: 100%;
      margin-bottom: 1.5rem;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      background-color: #000;

      .video-player {
        width: 100%;
        height: auto;
        min-height: 400px;
        border: none;
        outline: none;
      }

      .video-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        background-color: #f3f4f6;
        color: #6b7280;

        .placeholder-icon {
          font-size: 4rem;
          width: 4rem;
          height: 4rem;
          margin-bottom: 1rem;
          color: #9ca3af;
        }

        p {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 500;
        }
      }
    }

    .info-section {
      margin-top: 1rem;
      padding: 1rem;
      background-color: #eff6ff;
      border-radius: 0.5rem;
      border-left: 4px solid #3b82f6;

      h3 {
        display: flex;
        align-items: center;
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 500;
        color: #1e40af;

        mat-icon {
          margin-right: 0.5rem;
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }
      }

      p {
        margin: 0 0 1rem 0;
        font-size: 0.875rem;
        color: #1e40af;
        line-height: 1.5;
      }

      .download-section {
        margin-top: 1rem;

        .download-link {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #3b82f6;
          color: white;
          text-decoration: none;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          transition: background-color 0.2s;

          &:hover {
            background-color: #2563eb;
          }

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f8fafc;

    button {
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      font-weight: 500;
      transition: all 0.2s;

      &.mat-button {
        color: #1e40af;

        &:hover {
          background-color: #dbeafe;
        }
      }
    }
  }
}

// Estilos para tema oscuro del modal de guía
:host-context(body.dark-theme) {
  .guia-modal {
    background-color: #1f2937 !important;
    color: #f9fafb !important;

    .modal-header {
      background-color: #374151 !important;
      border-bottom-color: #4b5563 !important;

      h2 {
        color: #60a5fa !important;
      }

      button {
        color: #d1d5db !important;

        &:hover {
          background-color: #4b5563 !important;
        }

        mat-icon {
          color: #d1d5db !important;
        }
      }
    }

    .modal-body {
      background-color: #1f2937 !important;

      .video-container {
        .video-placeholder {
          background-color: #374151 !important;
          color: #d1d5db !important;

          .placeholder-icon {
            color: #6b7280 !important;
          }
        }
      }

      .info-section {
        background-color: #1e3a8a !important;
        border-left-color: #3b82f6 !important;

        h3 {
          color: #93c5fd !important;

          mat-icon {
            color: #93c5fd !important;
          }
        }

        p {
          color: #bfdbfe !important;
        }

        .download-section {
          .download-link {
            background-color: #3b82f6 !important;

            &:hover {
              background-color: #2563eb !important;
            }
          }
        }
      }
    }

    .modal-footer {
      background-color: #374151 !important;
      border-top-color: #4b5563 !important;

      button {
        &.mat-button {
          color: #60a5fa !important;

          &:hover {
            background-color: #1e3a8a !important;
          }
        }
      }
    }
  }
}

// Responsive para el modal de guía
@media (max-width: 768px) {
  .guia-modal {
    width: 98% !important;
    max-height: 98vh !important;

    .modal-header {
      padding: 0.75rem 1rem;

      h2 {
        font-size: 1.125rem;
      }
    }

    .modal-body {
      padding: 1rem;

      .video-container {
        .video-player {
          min-height: 250px;
        }

        .video-placeholder {
          min-height: 250px;

          .placeholder-icon {
            font-size: 3rem;
            width: 3rem;
            height: 3rem;
          }
        }
      }

      .info-section {
        padding: 0.75rem;

        h3 {
          font-size: 1rem;
        }

        p {
          font-size: 0.8rem;
        }
      }
    }

    .modal-footer {
      padding: 0.75rem 1rem;
    }
  }
}

