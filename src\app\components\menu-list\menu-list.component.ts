import {
  Component,
  EventEmitter,
  Input,
  Output,
  HostListener,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { Store, select } from '@ngrx/store';

import { environment } from '@src/environments/environment';
import { ThemeService, NotificationService } from '@app/services';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';

/**
 * Menú lateral responsive generado dinámicamente con Tailwind.
 * Incluye TODOS los roles del sistema.
 * Sin archivos SCSS: solo utilidades Tailwind.
 */
@Component({
  selector: 'app-menu-list',
  templateUrl: './menu-list.component.html',
})
export class MenuListComponent implements OnInit, OnD<PERSON>roy {
  // ============ Inputs / Outputs ============
  @Input() isAuthorized: boolean | null = null;
  private _user: User | null = null;
  @Input() set user(val: User | null) {
    this._user = val;
    this.visibleSections = this.buildMenu();
  }
  get user() {
    return this._user;
  }

  @Output() signOut = new EventEmitter<void>();
  @Output() closeMenuEvent = new EventEmitter<void>();

  // ============ State ============
  isDarkTheme = false;
  visibleSections: Section[] = [];
  private expanded = new Set<string>();
  private sub = new Subscription();
  private signingOut = false;

  constructor(
    private router: Router,
    private theme: ThemeService,
    private notify: NotificationService,
    private store: Store,
    private ws: WebSocketService,
    private http: HttpClient
  ) {}

  // ============ Lifecycle ============
  ngOnInit(): void {
    this.sub.add(this.theme.darkMode$.subscribe((d) => (this.isDarkTheme = d)));
    this.visibleSections = this.buildMenu();
  }
  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }

  // ============ UI helpers ============
  toggle(item: MenuItem) {
    this.expanded.has(item.label)
      ? this.expanded.delete(item.label)
      : this.expanded.add(item.label);
  }
  isOpen(item: MenuItem) {
    return this.expanded.has(item.label);
  }
  closeMenu() {
    this.closeMenuEvent.emit();
  }

  // ============ Logout ============
  onSignOut(): void {
    if (this.signingOut) return;
    this.signingOut = true;
    const token = localStorage.getItem('token');
    const userId = this.user?.id;
    if (token && userId) {
      const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
      this.http
        .post(
          `${environment.url}api/authentication/sign-out`,
          { userId },
          { headers }
        )
        .subscribe({
          next: () => this.finishLogout(),
          error: () => this.finishLogout(),
        });
    } else {
      this.finishLogout();
    }
  }
  private finishLogout() {
    localStorage.clear();
    this.ws.disconnect();
    this.signOut.emit();
    this.signingOut = false;
  }

  // ============ Generador de menú ============
  private buildMenu(): Section[] {
    const role = this.user?.role || '';
    const sections: Section[] = [];
    const add = (label: string, items: MenuItem[]) =>
      sections.push({ label, items });

    // Panel base
    if (role !== 'COACHING') {
      add('Panel principal', [
        { label: 'Inicio', icon: 'home', route: '/home' },
      ]);
    }
    const educationCommon: MenuItem[] = [
      { label: 'Mis Cursos', icon: 'school', route: '/cursos/mis-cursos' },
    ];

    switch (role) {
      case 'ADMIN':
      case 'PROGRAMADOR': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          {
            label: 'Leads',
            icon: 'assignment',
            route: '/leads/listar',
          },
          { label: 'Anuncios', icon: 'campaign', route: '/anuncios/list' },
          { label: 'Ventas', icon: 'shopping_cart', route: '/ventas' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
        ]);

        add('Administrativo', [
          {
            label: 'Supervisión',
            icon: 'person_add',
            route: '/coordinador/listar',
          },
          { label: 'Usuarios', icon: 'group', route: '/auth/listar' },
          {
            label: 'IPs Permitidas',
            icon: 'security',
            route: '/ipsPermitidas',
          },
          {
            label: 'Notificaciones',
            icon: 'notifications_active',
            route: '/notificaciones',
          },
          { label: 'Sede', icon: 'location_on', route: '/sede' },
          {
            label: 'Datos Landing',
            icon: 'folder_open',
            children: [
              {
                label: 'Contacto',
                icon: 'contact_mail',
                route: '/landing-contact',
              },
              {
                label: 'Postulación',
                icon: 'how_to_reg',
                route: '/postulacion',
              },
            ],
          },
        ]);

        add('Educación', [
          { label: 'Administrar Cursos', icon: 'school', route: '/cursos' },
          {
            label: 'Gestión de Certificados',
            icon: 'workspace_premium',
            route: '/certificados',
          },
          ...educationCommon,
        ]);

        add('Herramientas', [
          { label: 'Mapa', icon: 'map', route: '/map-example' },
          {
            label: 'Coaching',
            icon: 'folder_open',
            children: [
              {
                label: 'Panel Coaching',
                icon: 'psychology',
                route: '/landing-contact',
              },
              {
                label: 'Tareas',
                icon: 'task_alt',
                route: '/landing-contact',
              },
              {
                label: 'Encuestas',
                icon: 'assignment',
                route: '/encuestas',
              },
              {
                label: 'Coaching',
                icon: 'emoji_people',
                route: '/coaching',
              },
            ],
          },
        ]);
        break;
      }

      case 'AUDITOR': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          {
            label: 'Listar Clientes',
            icon: 'how_to_reg',
            route: '/leads/listar',
          },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      case 'COACHING': {
        add('Aplicaciones', [
          {
            label: 'Coaching',
            icon: 'folder_open',
            children: [
              {
                label: 'Panel Coaching',
                icon: 'psychology',
                route: '/landing-contact',
              },
              {
                label: 'Tareas',
                icon: 'task_alt',
                route: '/landing-contact',
              },
              {
                label: 'Encuentas',
                icon: 'assignment',
                route: '/encuestas',
              },
              {
                label: 'Coaching',
                icon: 'emoji_people',
                route: '/coaching',
              },
            ],
          },
        ]);

        break;
      }

      case 'BACKOFFICE': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          {
            label: 'Listar Clientes',
            icon: 'group',
            route: '/leads/listar',
          },
          { label: 'Ventas', icon: 'shopping_cart', route: '/ventas' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        add('Herramientas', [
          { label: 'Mapa', icon: 'map', route: '/map-example' },
        ]);
        break;
      }

      case 'BACKOFFICETRAMITADOR':
      case 'BACKOFFICESEGUIMIENTO': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          {
            label: 'Listar Clientes',
            icon: 'group',
            route: '/leads/listar',
          },
          { label: 'Ventas', icon: 'shopping_cart', route: '/ventas' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      case 'COORDINADOR': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          {
            label: 'Leads',
            icon: 'assignment',
            route: '/coordinador/obtenerclientesdeasesor',
          },
          { label: 'Ventas', icon: 'shopping_cart', route: '/ventas' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      case 'ASESOR': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          { label: 'Ventas', icon: 'shopping_cart', route: '/ventas' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      case 'PSICOLOGO': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Calendario', icon: 'calendar_today', route: '/calendar' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      case 'GERENCIA': {
        add('Aplicaciones', [
          { label: 'Manuales', icon: 'menu_book', route: '/manual' },
          { label: 'Agenda', icon: 'calendar_today', route: '/calendar' },
          { label: 'Anuncios', icon: 'campaign', route: '/anuncios/list' },
          { label: 'Foro', icon: 'question_answer', route: '/faq' },
          { label: 'Encuestas', icon: 'quiz', route: '/encuestas' },
        ]);
        add('Educación', educationCommon);
        break;
      }

      default: {
        // Roles desconocidos -> solo panel principal y cursos
        add('Educación', educationCommon);
        break;
      }
    }

    return sections;
  }
}

// ===== Interfaces helper =====
interface Section {
  label: string;
  items: MenuItem[];
}

interface MenuItem {
  label: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
}
