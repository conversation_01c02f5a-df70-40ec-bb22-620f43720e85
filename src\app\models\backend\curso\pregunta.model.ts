import { Respuesta, RespuestaCreateRequest } from './respuesta.model';

/**
 * Enum que define los tipos de preguntas disponibles
 */
export enum TipoPregunta {
  OPCION_MULTIPLE = 'OPCION_MULTIPLE',    // Una sola respuesta correcta
  SELECCION_MULTIPLE = 'SELECCION_MULTIPLE', // Múltiples respuestas correctas
  VERDADERO_FALSO = 'VERDADERO_FALSO',    // Pregunta de verdadero o falso
  TEXTO_LIBRE = 'TEXTO_LIBRE'         // Respuesta de texto libre
}

/**
 * Modelo que representa una pregunta de un cuestionario
 */
export interface Pregunta {
  id: number;
  enunciado: string;
  explicacion: string; // Explicación que se muestra después de responder
  puntaje: number; // Valor de la pregunta en puntos
  orden: number;
  tipo: TipoPregunta;
  cuestionarioId: number;
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
  respuestas: Respuesta[];
}

/**
 * Modelo para crear una nueva pregunta
 */
export interface PreguntaCreateRequest {
  enunciado: string;
  explicacion: string;
  puntaje: number;
  orden: number;
  tipo: TipoPregunta;
  cuestionarioId: number;
  respuestas?: RespuestaCreateRequest[];
}

/**
 * Modelo para actualizar una pregunta existente
 */
export interface PreguntaUpdateRequest {
  enunciado?: string;
  explicacion?: string;
  puntaje?: number;
  orden?: number;
  tipo?: TipoPregunta;
  estado?: string;
}
