// Estilos para el título del diálogo
h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  margin: 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  font-size: 20px;

  mat-icon {
    color: #1976d2;
  }
}

// Contenido del diálogo
mat-dialog-content {
  padding: 0 !important;
  max-height: 75vh;
  overflow-y: auto;

  // Diseño de dos columnas
  .form-layout {
    display: flex;
    flex-direction: column;

    @media (min-width: 768px) {
      flex-direction: row;
    }

    // Columna izquierda
    .left-column {
      padding: 20px;

      @media (min-width: 768px) {
        width: 40%;
        border-right: 1px solid #e0e0e0;
      }
    }

    // Columna derecha
    .right-column {
      padding: 20px;

      @media (min-width: 768px) {
        width: 60%;
      }
    }

    // Títulos de sección
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1976d2;
      margin-top: 0;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e0e0e0;
    }

    // Secciones
    .image-section,
    .metadata-section,
    .content-section,
    .dates-section {
      margin-bottom: 24px;
    }

    // Campos de formulario
    mat-form-field {
      width: 100%;
      margin-bottom: 16px;
    }

    // Campo de descripción
    .description-field {
      textarea {
        min-height: 120px;
      }
    }

    // Campos de fecha
    .date-fields {
      display: flex;
      flex-direction: column;
      gap: 16px;

      @media (min-width: 600px) {
        flex-direction: row;

        mat-form-field {
          flex: 1;
        }
      }
    }

    // Campo de imagen
    .image-field {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .image-preview {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        border: 2px dashed #ddd;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 200px;

        &:hover {
          border-color: #2196f3;
          .overlay {
            opacity: 1;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(33, 150, 243, 0.8);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          color: white;

          mat-icon {
            font-size: 32px;
            height: 32px;
            width: 32px;
            margin-bottom: 8px;
          }

          span {
            font-size: 14px;
          }
        }
      }

      button {
        width: 100%;
        height: 200px;
        border: 2px dashed #ddd;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #666;
        transition: all 0.3s ease;

        &:hover {
          border-color: #2196f3;
          color: #2196f3;
        }

        mat-icon {
          font-size: 32px;
          height: 32px;
          width: 32px;
        }
      }
    }
  }
}

// Acciones del diálogo
mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
  border-top: 1px solid #e0e0e0;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
    }
  }
}

// Estilos para tema oscuro
:host-context(.dark-theme) {
  h2[mat-dialog-title] {
    background-color: #0a1628;
    color: #ffffff;
    border-bottom-color: rgba(255, 255, 255, 0.1);

    mat-icon {
      color: #64b5f6;
    }
  }

  mat-dialog-content {
    .form-layout {
      .left-column {
        @media (min-width: 768px) {
          border-right-color: rgba(255, 255, 255, 0.1);
        }
      }

      .section-title {
        color: #64b5f6;
        border-bottom-color: rgba(255, 255, 255, 0.1);
      }

      .image-field {
        .image-preview {
          border-color: rgba(255, 255, 255, 0.3);

          &:hover {
            border-color: #64b5f6;
          }

          .overlay {
            background: rgba(33, 150, 243, 0.7);
          }
        }

        button {
          border-color: rgba(255, 255, 255, 0.3);
          color: rgba(255, 255, 255, 0.7);

          &:hover {
            border-color: #64b5f6;
            color: #64b5f6;
          }
        }
      }
    }
  }

  mat-dialog-actions {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
