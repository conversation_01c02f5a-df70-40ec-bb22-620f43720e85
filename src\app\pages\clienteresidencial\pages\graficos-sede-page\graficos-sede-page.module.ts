import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

// Routing
import { GraficosSedePageRoutingModule } from './graficos-sede-page-routing.module';

// Módulo compartido
import { SharedComponentsModule } from '../../components/shared/shared-components.module';

// Components
import { GraficosSedePageComponent } from './graficos-sede-page.component';

@NgModule({
  declarations: [GraficosSedePageComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    SharedComponentsModule,
    GraficosSedePageRoutingModule,
  ],
})
export class GraficosSedePageModule {}
