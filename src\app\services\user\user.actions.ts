import { createAction, props } from '@ngrx/store';
import { UserStatus } from '@app/models/backend/user/user-status.model';

// Acciones para WebSocket relacionadas con usuarios
export const requestUsersStatusWs = createAction(
  '[User WebSocket] Request Users Status'
);

export const usersStatusUpdatedWs = createAction(
  '[User WebSocket] Users Status Updated',
  props<{ usersStatus: UserStatus[] }>()
);

export const updateUserActivity = createAction(
  '[User WebSocket] Update User Activity'
);
