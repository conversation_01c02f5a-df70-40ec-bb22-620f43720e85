import {
  Component,
  OnInit,
  Inject,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Manual, ManualList } from '@app/services/manual.service';
import { User } from '@app/models/backend/user';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { SedeService } from '@app/services/sede.service';
import { Sede } from '@app/models/backend/sede/sede.model';

@Component({
  selector: 'app-manual-dialog',
  templateUrl: './manual-dialog.component.html',
  styleUrls: ['./manual-dialog.component.scss'],
})
export class ManualDialogComponent implements OnInit {
  manualForm: FormGroup;
  isNewData: boolean = true;
  submitted: boolean = false;
  uploadFiles: File[] = [];
  URL_FILES: string = '';
  dataUserSession: User | null = null;
  isDragging: boolean = false;
  sedes: Sede[] = [];
  loadingSedes: boolean = false;

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ManualDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private storage: AngularFireStorage,
    private sedeService: SedeService
  ) {
    this.manualForm = this.fb.group({
      id: [0],
      nombre: ['', [Validators.required, Validators.maxLength(50)]],
      tipo: ['', [Validators.required]],
      file: [null, []],
      archivo: ['', []], // Agregamos el campo archivo explícitamente
      isActive: [true],
      sedeId: [null], // Campo para la sede (opcional)
    });

    if (data) {
      this.URL_FILES = data.URL_FILES || '';
      this.dataUserSession = data.dataUserSession || null;

      if (data.manual) {
        this.isNewData = false;
        console.log(
          'Inicializando formulario con manual existente:',
          data.manual
        );
        console.log('URL base para archivos:', this.URL_FILES);
        this.patchFormValues(data.manual);
      }
    }
  }

  ngOnInit(): void {
    // Cargar sedes disponibles
    this.loadSedes();

    // Obtener datos del usuario desde localStorage si no se proporcionó
    if (!this.dataUserSession) {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          this.dataUserSession = JSON.parse(userStr);
          //console.log('Usuario cargado en ngOnInit del dialog:', this.dataUserSession);

          // Verificar que el usuario tenga un ID válido
          if (!this.dataUserSession || !this.dataUserSession.id) {
            //console.warn('El usuario cargado no tiene un ID válido:', this.dataUserSession);
            // Intentar obtener el ID de otra manera si es necesario
            if (this.dataUserSession && (this.dataUserSession as any).userId) {
              this.dataUserSession.id = (this.dataUserSession as any).userId;
              //console.log('Se asignó el ID del usuario desde userId:', this.dataUserSession.id);
            }
          }
        } catch (error) {
          //console.error('Error al parsear el usuario en ngOnInit:', error);
        }
      } else {
        //console.warn('No se encontró información del usuario en localStorage');
      }
    } else {
      //console.log('Usuario proporcionado al dialog:', this.dataUserSession);
    }

    // Verificar si tenemos un archivo en el formulario
    const archivoValue = this.manualForm.get('archivo')?.value;
    if (archivoValue) {
      console.log('Archivo en el formulario:', archivoValue);

      // Si el archivo no es una URL completa, verificar si tenemos la URL base
      if (archivoValue && !archivoValue.startsWith('http') && this.URL_FILES) {
        console.log('URL completa del archivo:', this.URL_FILES + archivoValue);
      }
    }
  }

  /**
   * Carga las sedes disponibles
   */
  private loadSedes(): void {
    this.loadingSedes = true;
    this.sedeService.getAllSedes().subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.sedes = response.data;
        }
        this.loadingSedes = false;
      },
      error: (error) => {
        console.error('Error al cargar sedes:', error);
        this.loadingSedes = false;
      },
    });
  }

  /**
   * Actualiza el formulario con los valores del manual
   */
  private patchFormValues(manual: Manual): void {
    console.log('Actualizando formulario con manual:', manual);

    // Verificar si el manual tiene un archivo
    if (manual.archivo) {
      if (manual.archivo.startsWith(',')) {
        console.warn('⚠️ El archivo tiene una coma al inicio:', manual.archivo);
      }
    } else {
      console.log('El manual no tiene un archivo asociado');
    }

    this.manualForm.patchValue({
      id: manual.id,
      nombre: manual.nombre,
      tipo: manual.tipo,
      archivo: manual.archivo || '',
      isActive: manual.isActive,
      sedeId: manual.sedeId || null,
    });

    // Verificar que el campo archivo se haya actualizado correctamente
    console.log(
      'Valor del campo archivo después de patchValue:',
      this.manualForm.get('archivo')?.value
    );
  }

  /**
   * Obtiene los controles del formulario
   */
  get form() {
    return this.manualForm.controls;
  }

  /**
   * Abre el selector de archivos
   */
  openFileSelector() {
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  /**
   * Elimina el archivo seleccionado
   */
  removeSelectedFile() {
    this.uploadFiles = [];
    this.form['file'].setValue(null);
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  /**
   * Maneja el evento dragover
   */
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  /**
   * Maneja el evento dragleave
   */
  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  /**
   * Maneja el evento drop
   */
  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      // Usar solo el primer archivo
      const file = files[0];

      // Validar extensión
      const allowedExtensions = [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'txt',
      ];
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      if (!allowedExtensions.includes(extension)) {
        //console.error(`El archivo ${file.name} tiene una extensión no permitida. Extensiones permitidas: ${allowedExtensions.join(', ')}`);
        return;
      }

      // Validar tamaño
      const maxSizeMB = 5;
      const maxSizeBytes = maxSizeMB * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        //console.error(`El archivo ${file.name} excede el tamaño máximo permitido de ${maxSizeMB} MB.`);
        return;
      }

      // Actualizar el formulario
      this.form['file'].setValue('file_upload');
      this.uploadFiles = [file];
    }
  }

  /**
   * Maneja la selección de archivos
   * @param fileInput elemento input
   */
  async onFileSelected(fileInput: HTMLInputElement) {
    const files = Array.from(fileInput.files || []);
    let error = '';

    if (files.length === 0) {
      return;
    }

    // Validar extensiones permitidas
    const allowedExtensions = [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
    ];
    for (const file of files) {
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      if (!allowedExtensions.includes(extension)) {
        error = `El archivo ${
          file.name
        } tiene una extensión no permitida. Extensiones permitidas: ${allowedExtensions.join(
          ', '
        )}`;
        //console.error(error);
        return;
      }
    }

    // Validar tamaño máximo (5MB)
    const maxSizeMB = 5;
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    for (const file of files) {
      if (file.size > maxSizeBytes) {
        error = `El archivo ${file.name} excede el tamaño máximo permitido de ${maxSizeMB} MB.`;
        //console.error(error);
        return;
      }
    }

    // Si todo está bien, actualizar el formulario
    this.form['file'].setValue('file_upload');
    this.uploadFiles = files;
  }

  /**
   * Guarda los datos del formulario
   */
  saveData() {
    this.submitted = true;

    if (!this.manualForm.valid) {
      return;
    }

    const values: Manual = this.manualForm.value;
    const formData = new FormData();
    const manualId = values.id; // Guardar el ID para usarlo después

    // Iterar a través de las propiedades de 'values' y Crearlas al FormData
    for (const key of Object.keys(values)) {
      // No incluir el ID, file, ni archivo en el FormData
      // El campo 'archivo' será manejado por el servicio después de subir a Firebase
      if (key !== 'id' && key !== 'file' && key !== 'archivo') {
        if (key === 'isActive') {
          // Asegurarse de que los valores booleanos se conviertan correctamente a string
          formData.append(key, values[key] ? 'true' : 'false');
          // También agregar el campo is_active para compatibilidad
          formData.append('is_active', values[key] ? 'true' : 'false');
        } else {
          formData.append(key, (values as any)[key]);
        }
      }
    }

    // Si hay archivos seleccionados, agregarlos al FormData
    if (this.uploadFiles && this.uploadFiles.length > 0) {
      // Agregar el archivo al FormData
      formData.append('file', this.uploadFiles[0]);
    }

    // Agregar el ID del usuario que crea/actualiza el manual
    if (this.dataUserSession && this.dataUserSession.id) {
      formData.append('userAuthId', this.dataUserSession.id.toString());

      // Mantener los campos anteriores por compatibilidad
      formData.append('user_create_id', this.dataUserSession.id.toString());
      if (!this.isNewData) {
        formData.append('user_update_id', this.dataUserSession.id.toString());
      }
    }

    // Cerrar el diálogo y enviar los datos
    this.dialogRef.close({
      action: this.isNewData ? 'create' : 'update',
      data: formData,
      id: manualId, // Usar el ID guardado
    });
  }

  /**
   * Cierra el diálogo sin guardar
   */
  onCancel(): void {
    this.dialogRef.close();
  }

  /**
   * Extrae el nombre del archivo de una URL
   * @param url URL del archivo
   * @returns Nombre del archivo o la URL completa si no se puede extraer
   */
  getFileName(url: string): string {
    if (!url) return 'Sin archivo';

    try {
      // Intentar extraer el nombre del archivo de la URL
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];

      // Si el nombre del archivo tiene parámetros, eliminarlos
      if (fileName.includes('?')) {
        return fileName.split('?')[0];
      }

      return fileName || url;
    } catch (error) {
      console.error('Error al extraer nombre de archivo:', error);
      return url;
    }
  }

  /**
   * Obtiene la URL correcta para el archivo
   * @param url URL del archivo (puede ser relativa o absoluta)
   * @returns URL completa para acceder al archivo
   */
  getFileUrl(url: string): string {
    if (!url) return '';

    // Si la URL ya es absoluta (comienza con http o https), usarla directamente
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Si no, concatenar con la URL base
    return this.URL_FILES + url;
  }
}
