import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { GenericResponse } from '@app/models/backend/generic-response';
import { CodeResponse } from '@app/models/backend/code-response.interface';
import { environment } from '@src/environments/environment';
import { tap } from 'rxjs/operators';

export interface VentaRequest {
  idVenta?: number;
  codigoVenta?: string;
  tipoFibra?: string;
  horaInstalacion?: string;
  promocion?: string;
  tvDeco?: string;
  grabacionOCM?: string;
  envioSMS?: string;
  movilContacto?: string;
  telefono_compania?: string;
  movilAPortar1?: string;
  movilAPortar2?: string;
  movilAPortar3?: string;
  movilAPortar4?: string;
  movilAPortar5?: string;
  precioPromocionTiempo?: string;
  precioReal?: string;
  Comercial?: string;
  Coordinador?: string;
  presentacionCCliente?: string;
  operacion?: string;
  operador?: string;
  segmento?: string;
  nombres_apellidos?: string;
  nif_nie?: string;
  genero?: string;
  direccion?: string;
  cuenta_bancaria?: string;
  email?: string;
  permanencia?: string;
  nacimiento?: string | null;
  nacionalidad?: string;
  campania?: string;
}

export interface VentaPageResponse {
  ventas: VentaRequest[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
}

export interface Observacion {
  idObservacion?: number;
  codigoVenta: string;
  codi_usuario: string | number;
  observacion: string;
  fecha?: string;
}

export interface BackofficeAsignado {
  idBackEncargados: number;
  idBackoffice: number;
  idVenta: number;
  tipoBack: 'TRAMITADOR' | 'SEGUIMIENTO';
}

@Injectable({
  providedIn: 'root'
})
export class VentaService {
  constructor(private http: HttpClient) {}

  generateCode(): Observable<CodeResponse> {
    return this.http.get<CodeResponse>(`${environment.urlVentas}generate-code/`);
  }

  crearVenta(venta: VentaRequest): Observable<any> {
    return this.http.post<any>(`${environment.urlVentas}ventas/`, venta);
  }

  getVentas(): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${environment.urlVentas}ventas/`);
  }

  searchVentas(query: string): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(
      `${environment.urlVentas}ventas/buscar?query=${query}`
    );
  }

  eliminarVenta(codigo: string, idVenta: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${environment.urlVentas}ventas/eliminar/${codigo}/${idVenta}/`);
  }

  editarVenta(codigo: string, venta: VentaRequest): Observable<any> {
    return this.http.put<any>(`${environment.urlVentas}ventas/${codigo}/`, venta);
  }

  obtenerVentaPorId(codigo: string): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${environment.urlVentas}ventas/${codigo}/`);
  }

  actualizarObservacion(codigo: string, observacion: string): Observable<any> {
    return this.http.patch<any>(`${environment.urlVentas}ventas/${codigo}/observacion/`, {
      observacionEstado: observacion
    });
  }

  actualizarEstado(codigo: string, estado: string): Observable<any> {
    return this.http.patch<any>(`${environment.urlVentas}ventas/${codigo}/estado/`, {
      estado: estado
    });
  }

  crearObservacion(observacion: Observacion): Observable<any> {
    return this.http.post<any>(`${environment.urlVentas}observaciones/`, observacion);
  }

  obtenerObservaciones(codigoVenta: string): Observable<Observacion[]> {
    return this.http.get<Observacion[]>(`${environment.urlVentas}observaciones/${codigoVenta}/`);
  }

  validarNifNie(nifNie: string): Observable<any> {
    return this.http.get<any>(`${environment.urlVentas}ventas/validar/${nifNie}/`);
  }

  obtenerBackoffices(rol: string): Observable<any[]> {
    return this.http.get<any[]>(`${environment.urlVentas}back-encargados/lista/backoffices/${rol}/`);
  }
  eliminarBackoffice(idVenta: string): Observable<any[]> {
    return this.http.delete<any[]>(`${environment.urlVentas}back-encargados/eliminar/backoffices/${idVenta}/`);
  }
  asignarBackoffice(data: {
    idBackoffice: number,
    idVenta: number,
    tipoBack: 'TRAMITADOR' | 'SEGUIMIENTO'
  }): Observable<any> {
    return this.http.post<any>(`${environment.urlVentas}back-encargados/`, data);
  }

  obtenerBackofficesAsignados(idVenta: number): Observable<BackofficeAsignado[]> {
    return this.http.get<BackofficeAsignado[]>(`${environment.urlVentas}back-encargados/venta/${idVenta}/`);
  }
}




























