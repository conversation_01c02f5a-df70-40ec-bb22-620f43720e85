<div class="dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all">
  <div
    class="mb-3"
    *ngIf="
      fechaFinControl.value &&
      fechaFinControl.value !== fechaInicioControl.value
    "
  >
    <div
      class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
    >
      <div class="flex items-center">
        <svg
          class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clip-rule="evenodd"
          ></path>
        </svg>
        <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
          Mostrando estadísticas acumuladas por coordinador del
          {{ fechaInicioControl.value }} al
          {{ fechaFinControl.value }}
        </span>
      </div>
    </div>
  </div>

  <!-- Cabecera -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
  >
    <div class="flex flex-col">
      <p class="text-gray-600 dark:text-gray-400">
        Resumen de estadísticas agrupadas por supervisor
      </p>
    </div>

    <!-- Controles de filtro -->
    <div
      class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 w-full sm:w-auto"
    >
      <!-- Selector de Sede -->
      <div class="w-full sm:w-40">
        <label
          class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Sede</label
        >
        <select
          [formControl]="sedeControl"
          class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        >
          <option value="todas">Todas las sedes</option>
          <option *ngFor="let sede of sedes" [value]="sede.id">
            {{ sede.nombre }}
          </option>
        </select>
      </div>

      <!-- Campo Fecha Inicio -->
      <div class="w-full sm:w-32">
        <label
          class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Fecha Inicio</label
        >
        <input
          type="date"
          [formControl]="fechaInicioControl"
          class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <!-- Campo Fecha Fin -->
      <div class="w-full sm:w-32">
        <label
          class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Fecha Fin</label
        >
        <input
          type="date"
          [formControl]="fechaFinControl"
          class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <!-- Botón Exportar Tabla de Estadísticas -->
      <div class="w-full sm:w-auto">
        <label class="block text-xs font-medium text-transparent mb-1"
          >&nbsp;</label
        >
        <button
          (click)="exportarTablaEstadisticas()"
          [disabled]="
            loading || exportTablaLoading || estadisticas.length === 0
          "
          class="w-full sm:w-auto px-3 py-1.5 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white text-xs font-medium rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center gap-1"
          title="Exportar tabla de estadísticas de coordinador a Excel"
        >
          <svg
            *ngIf="!exportTablaLoading"
            class="w-3 h-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            ></path>
          </svg>
          <div
            *ngIf="exportTablaLoading"
            class="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent"
          ></div>
          <span class="hidden sm:inline">{{
            exportTablaLoading ? "Exportando..." : "Exportar Excel"
          }}</span>
          <span class="sm:hidden">{{
            exportTablaLoading ? "..." : "Excel"
          }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Indicador de carga mejorado -->
  <div
    *ngIf="loading"
    class="flex flex-col items-center justify-center p-12 bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
  >
    <div class="relative">
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"
      ></div>
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
      ></div>
    </div>
    <div class="mt-4 text-center">
      <p class="text-gray-600 dark:text-gray-400 font-medium">
        Cargando estadísticas de supervisores...
      </p>
      <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
        Agrupando datos por supervisor...
      </p>
    </div>
  </div>

  <!-- Tabla de estadísticas -->
  <div *ngIf="!loading" class="overflow-x-auto rounded-xl shadow">
    <table
      class="min-w-full border border-gray-200 dark:border-gray-700 text-xs text-left text-gray-700 dark:text-white"
    >
      <thead>
        <tr>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold"
          >
            Sede
          </th>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold"
          >
            Supervisor
          </th>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            <span class="hidden sm:inline">Total Toma de Datos</span>
            <span class="sm:hidden">Datos</span>
          </th>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            <span class="hidden sm:inline">Total Seguros</span>
            <span class="sm:hidden">Seguros</span>
          </th>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            <span class="hidden sm:inline">Total Energía</span>
            <span class="sm:hidden">Energía</span>
          </th>
          <th
            class="px-2 sm:px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            <span class="hidden sm:inline">Total Lowi</span>
            <span class="sm:hidden">Lowi</span>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let estadistica of estadisticas"
          class="hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium text-xs"
          >
            <div
              class="truncate max-w-20 sm:max-w-none"
              [title]="estadistica.sede"
            >
              {{ estadistica.sede }}
            </div>
          </td>
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium text-purple-600 dark:text-purple-400 text-xs"
          >
            {{ estadistica.coordinador }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-blue-600 text-xs"
          >
            {{ estadistica.totalTomaDatos }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-green-600 text-xs"
          >
            {{ estadistica.totalSeguros }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-orange-600 text-xs"
          >
            {{ estadistica.totalEnergia }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-purple-600 text-xs"
          >
            {{ estadistica.totalLowi }}
          </td>
        </tr>
      </tbody>

      <!-- Subtotales por sede -->
      <tbody *ngIf="estadisticas.length > 0">
        <tr
          *ngFor="let sede of getSedesUnicas()"
          class="bg-gray-100 dark:bg-gray-800 border-t-2 border-gray-300 dark:border-gray-600"
        >
          <td
            class="px-2 sm:px-4 py-2 font-semibold text-gray-700 dark:text-gray-300 text-xs"
            colspan="2"
          >
            TOTAL SUMATORIA DE SUPERVISORES DE {{ sede }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 text-center font-semibold text-blue-600 dark:text-blue-400 text-xs"
          >
            {{ getSubtotalPorSede(sede, "totalTomaDatos") }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 text-center font-semibold text-green-600 dark:text-green-400 text-xs"
          >
            {{ getSubtotalPorSede(sede, "totalSeguros") }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 text-center font-semibold text-orange-600 dark:text-orange-400 text-xs"
          >
            {{ getSubtotalPorSede(sede, "totalEnergia") }}
          </td>
          <td
            class="px-2 sm:px-4 py-2 text-center font-semibold text-purple-600 dark:text-purple-400 text-xs"
          >
            {{ getSubtotalPorSede(sede, "totalLowi") }}
          </td>
        </tr>
      </tbody>

      <!-- Fila de totales generales -->
      <tbody *ngIf="estadisticas.length > 0">
        <tr
          class="bg-purple-50 dark:bg-purple-900/20 border-t-2 border-purple-500"
        >
          <td
            class="px-2 sm:px-4 py-3 font-bold text-purple-800 dark:text-purple-200 text-xs"
            colspan="2"
          >
            TOTAL SUMATORIA DE SUPERVISORES
          </td>
          <td
            class="px-2 sm:px-4 py-3 text-center font-bold text-blue-700 dark:text-blue-300 text-xs"
          >
            {{ getTotalTomaDatos() }}
          </td>
          <td
            class="px-2 sm:px-4 py-3 text-center font-bold text-green-700 dark:text-green-300 text-xs"
          >
            {{ getTotalSeguros() }}
          </td>
          <td
            class="px-2 sm:px-4 py-3 text-center font-bold text-orange-700 dark:text-orange-300 text-xs"
          >
            {{ getTotalEnergia() }}
          </td>
          <td
            class="px-2 sm:px-4 py-3 text-center font-bold text-purple-700 dark:text-purple-300 text-xs"
          >
            {{ getTotalLowi() }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación inferior -->
  <mat-paginator
    #coordinadorPaginator
    *ngIf="!loading && estadisticas && estadisticas.length > 0"
    class="bg-white dark:bg-gray-900 border-t-0 border border-gray-200 dark:border-gray-700"
    [length]="totalElements"
    [pageIndex]="currentPage"
    [pageSize]="pageSize"
    [pageSizeOptions]="pageSizeOptions"
    [showFirstLastButtons]="true"
    (page)="handlePageEvent($event)"
  >
  </mat-paginator>

  <!-- Estado vacío -->
  <div
    *ngIf="!loading && estadisticas?.length === 0"
    class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg mt-6"
  >
    <div class="text-5xl text-gray-400 dark:text-gray-600 mb-4">👥</div>
    <p class="text-gray-600 dark:text-gray-400">
      No hay estadísticas de coordinador disponibles para los filtros
      seleccionados
    </p>
  </div>
</div>
