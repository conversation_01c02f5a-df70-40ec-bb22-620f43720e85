import {Anuncio} from '@app/models/backend/anuncios/anuncio';

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}
export interface AnuncioResponse {
  id: number;
  titulo: string;
  descripcion: string;
  imagenUrl: string;
  categoria: string;
  fechaPublicacion: string;
  fechaInicio?: string;
  fechaFin?: string;
  orden?: number;
  estado?: string;
  nombreUsuario: string;
  sedeId?: number;
  nombreSede?: string;
}

export interface AnuncioCreateRequest {
  titulo: string;
  descripcion: string;
  imagenUrl: string;
  categoria: string;
  fechaInicio?: string;
  fechaFin?: string;
  orden?: number;
  estado?: string;
  usuarioId?: number;
  sedeId?: number;
}
