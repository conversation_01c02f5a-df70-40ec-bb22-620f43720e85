// src/app/services/contacto.service.ts
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ContactoService {
  //private baseUrl = 'https://back-landing-midas.onrender.com/api';
  private baseUrl = 'https://apisozarusac.com/LandingPage/'

  constructor(private http: HttpClient) {}

  getContactos() {
    return this.http.get<any>(`${this.baseUrl}/listar_contactos/`);
  }
}
