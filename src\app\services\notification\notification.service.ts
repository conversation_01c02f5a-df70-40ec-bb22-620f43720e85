import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NotificationComponent } from './components';


@Injectable()
export class NotificationService {

  constructor(private snackBar: MatSnackBar) { }

  error(message: string, options?: any): void {
    // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      // Configuración por defecto
      const config = {
        duration: 3000,
        data: { message },
        panelClass: ['mat-snackbar_error']
      };

      // Fusionar con las opciones proporcionadas
      if (options) {
        // Si se proporcionan panelClass, añadirlos a los existentes
        if (options.panelClass) {
          if (Array.isArray(options.panelClass)) {
            config.panelClass = [...config.panelClass, ...options.panelClass];
          } else {
            config.panelClass = [...config.panelClass, options.panelClass];
          }
          delete options.panelClass;
        }

        // Fusionar el resto de opciones
        Object.assign(config, options);
      }

      this.snackBar.openFromComponent(NotificationComponent, config);
    }, 0);
  }

  success(message: string, options?: any): void {
    // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      // Configuración por defecto
      const config = {
        duration: 3000,
        data: { message },
        panelClass: ['mat-snackbar_success']
      };

      // Fusionar con las opciones proporcionadas
      if (options) {
        // Si se proporcionan panelClass, añadirlos a los existentes
        if (options.panelClass) {
          if (Array.isArray(options.panelClass)) {
            config.panelClass = [...config.panelClass, ...options.panelClass];
          } else {
            config.panelClass = [...config.panelClass, options.panelClass];
          }
          delete options.panelClass;
        }

        // Fusionar el resto de opciones
        Object.assign(config, options);
      }

      this.snackBar.openFromComponent(NotificationComponent, config);
    }, 0);
  }

  info(message: string, options?: any): void {
    // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      // Configuración por defecto
      const config = {
        duration: 5000,
        data: { message },
        panelClass: ['mat-snackbar_info']
      };

      // Fusionar con las opciones proporcionadas
      if (options) {
        // Si se proporcionan panelClass, añadirlos a los existentes
        if (options.panelClass) {
          if (Array.isArray(options.panelClass)) {
            config.panelClass = [...config.panelClass, ...options.panelClass];
          } else {
            config.panelClass = [...config.panelClass, options.panelClass];
          }
          delete options.panelClass;
        }

        // Fusionar el resto de opciones
        Object.assign(config, options);
      }

      this.snackBar.openFromComponent(NotificationComponent, config);
    }, 0);
  }



}


