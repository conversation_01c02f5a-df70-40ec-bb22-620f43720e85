import { Component, OnInit, OnDestroy } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { Observable, Subject, of } from 'rxjs';
import { filter, skip, take, takeUntil, tap } from 'rxjs/operators';
import * as fromCoordinadorSelectors from '../../store/save/save.selectors';
import * as CoordinadorActions from '../../store/save/save.actions';
import { User } from '@app/models/backend/user/index';
import { FormBuilder, FormGroup } from '@angular/forms';
import { GeneralService } from '@app/services/general.service';
import { ClienteConUsuarioDTO, ClienteResidencial } from '@app/models/backend/clienteresidencial';
import { ofType } from '@ngrx/effects';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import Swal from 'sweetalert2';

// Define la interfaz con la estructura esperada de la respuesta
interface AsesoresConClientesResponse {
  clientes: any[]; // Puedes reemplazar 'any' por el tipo real de cliente
  currentPage: number;
  totalPages: number;
  totalItems?: number; // Agregamos totalItems opcional para la paginación
}

@Component({
  selector: 'app-obtener-clientes-de-asesor',
  templateUrl: './obtenerclientesdeasesor.component.html',
  styleUrls: ['./obtenerclientesdeasesor.component.scss']
})
export class ObtenerclientesdeasesorComponent implements OnInit, OnDestroy {
  userId: number = 0;
  coordinadorId!: number;
  filterForm: FormGroup;
  modalVisible = false;
  selectedCliente$!: Observable<ClienteResidencial | null>;
  isDarkTheme = false;
  exportLoading = false;

  private destroy$ = new Subject<void>();
  selectedAdvisorName: string | null = null;
  // Propiedad que contiene la respuesta con clientes y datos de paginación
  asesoresConClientes$: Observable<AsesoresConClientesResponse>;

  // Alias para la paginación en el template
  clientesPage$: Observable<AsesoresConClientesResponse>;

  loading$: Observable<boolean>;

  // user$ se define como Observable<User> para usarlo con el async pipe en el template.
  user$: Observable<User>;

  // Parámetros de paginación
  page: number = 0;
  size: number = 10;
  totalPages: number = 0;

  constructor(
    private store: Store<any>,
    private fb: FormBuilder,
    public GeneralService: GeneralService,
  ) {
    this.loading$ = this.store.pipe(
      select(fromCoordinadorSelectors.selectCoordinadorLoading)
    );

    // Se asigna la respuesta del store a la propiedad correspondiente
    this.asesoresConClientes$ = this.store.pipe(
      select(fromCoordinadorSelectors.selectAsesoresConClientes),
      takeUntil(this.destroy$)
    ) as Observable<AsesoresConClientesResponse>;

    // Alias para usar en la paginación del template
    this.clientesPage$ = this.asesoresConClientes$;

    // Inicializar el formulario de filtros
    this.filterForm = this.fb.group({
      dni: [''],
      nombre: [''],
      movil: [''],
      fecha: ['']
    });

    // Si GeneralService.usuario$ es de tipo User (no Observable), se envuelve con 'of'
    this.user$ = of(this.GeneralService.usuario$);
  }

  ngOnInit(): void {
    // Asumimos que GeneralService.usuario$ es un objeto User y se obtiene su id
    this.userId = this.GeneralService.usuario$?.id;
    this.coordinadorId = this.userId;

    // Cargar datos iniciales sin filtros
    this.cargarDatosIniciales();
    this.selectedCliente$ = this.store.select(fromCoordinadorSelectors.selectClienteSeleccionado);

    // Actualizar el total de páginas a partir de la respuesta
    this.asesoresConClientes$.pipe(takeUntil(this.destroy$)).subscribe(response => {
      if (response) {
        this.totalPages = response.totalPages;
      }
    });

    // Detectar si el tema oscuro está activo
    this.checkDarkTheme();
    // Observar cambios en el tema
    this.observeThemeChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  cargarDatosIniciales(): void {
    this.store.dispatch(CoordinadorActions.loadAsesoresConClientes({
      coordinadorId: this.coordinadorId,
      page: this.page,
      size: this.size
    }));
  }

  cargarAsesoresConClientes(): void {
    const filters = this.filterForm.value;
    this.store.dispatch(CoordinadorActions.loadAsesoresConClientes({
      coordinadorId: this.coordinadorId,
      dni: filters.dni || undefined,
      nombre: filters.nombre || undefined,
      numeroMovil: filters.movil || undefined,
      fecha: filters.fecha || undefined,
      page: this.page,
      size: this.size
    }));
  }

  aplicarFiltros(): void {
    this.page = 0; // Reiniciar la página al aplicar filtros
    this.cargarAsesoresConClientes();
  }

  limpiarFiltros(): void {
    this.filterForm.reset({
      dni: '',
      nombre: '',
      movil: '',
      fecha: ''
    });
    this.page = 0;
    this.cargarDatosIniciales();
  }

  private isRequestInProgress = false;

  verDetalleCliente(cliente: ClienteConUsuarioDTO): void {
    console.log('Iniciando verDetalleCliente', cliente);

    // Evitar múltiples peticiones si ya hay una en progreso o si hay una exportación en curso
    if (this.isRequestInProgress || this.exportLoading) {
      console.log('Petición en progreso o exportación en curso, no se puede abrir el modal');
      return;
    }

    if (!cliente.dni || !cliente.numeroMovil) {
      console.error('Datos del cliente incompletos');
      Swal.fire('Error', 'Datos del cliente incompletos', 'error');
      return;
    }

    // Marcar que hay una petición en progreso
    this.isRequestInProgress = true;

    // Formatear la fecha según el tipo de dato
    let fechaCreacion = '';

    // Si es un array, convertir a fecha ISO estándar
    if (Array.isArray(cliente.fechaIngresado) && cliente.fechaIngresado.length >= 3) {
      const year = cliente.fechaIngresado[0];
      const month = String(cliente.fechaIngresado[1]).padStart(2, '0');
      const day = String(cliente.fechaIngresado[2]).padStart(2, '0');
      const hours = cliente.fechaIngresado.length >= 4 ? String(cliente.fechaIngresado[3]).padStart(2, '0') : '00';
      const minutes = cliente.fechaIngresado.length >= 5 ? String(cliente.fechaIngresado[4]).padStart(2, '0') : '00';
      const seconds = cliente.fechaIngresado.length >= 6 ? String(cliente.fechaIngresado[5]).padStart(2, '0') : '00';
      const millis = cliente.fechaIngresado.length >= 7
        ? String(Math.floor(cliente.fechaIngresado[6] / 1000000)).padStart(3, '0')
        : '000';

      fechaCreacion = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${millis}`;
    }
    // Si es un string, usar directamente
    else if (typeof cliente.fechaIngresado === 'string') {
      fechaCreacion = cliente.fechaIngresado;
    }

    // Verificar el tema actual
    const isDark = document.body.classList.contains('dark-theme');
    this.isDarkTheme = isDark;

    console.log('Despachando acción loadClienteByDniAndMobile');

    // Despachar la acción para cargar los detalles del cliente
    this.store.dispatch(
      CoordinadorActions.loadClienteByDniAndMobile({
        dni: cliente.dni,
        mobile: cliente.numeroMovil,
        fechaCreacion: fechaCreacion || ''
      })
    );

    // Mostrar el modal inmediatamente
    this.modalVisible = true;

    // Suscribirse al estado de carga para detectar cuando se completa la petición
    this.loading$.pipe(skip(1), take(1)).subscribe(loading => {
      console.log('Estado de carga cambiado:', loading);
      if (!loading) {
        this.isRequestInProgress = false;
      }
    });

    // Verificar si se recibió el cliente
    this.selectedCliente$.pipe(
      filter(cliente => !!cliente),
      take(1)
    ).subscribe(cliente => {
      console.log('Cliente recibido:', cliente);
    });

    // Manejar errores o timeout
    setTimeout(() => {
      if (this.isRequestInProgress) {
        this.isRequestInProgress = false;
        console.warn('La petición está tardando demasiado tiempo');
      }
    }, 10000);
  }



  closeModal(): void {
    // Si hay una exportación en curso, no cerrar el modal
    if (this.exportLoading) {
      return;
    }

    this.modalVisible = false;
    this.isRequestInProgress = false;
  }

  nextPage(): void {
    if (this.page < this.totalPages - 1) {
      this.page++;
      this.cargarAsesoresConClientes();
    }
  }

  prevPage(): void {
    if (this.page > 0) {
      this.page--;
      this.cargarAsesoresConClientes();
    }
  }

  goToFirstPage(): void {
    this.page = 0;
    this.cargarAsesoresConClientes();
  }

  goToLastPage(): void {
    this.page = this.totalPages - 1;
    this.cargarAsesoresConClientes();
  }

  /**
   * Maneja los cambios de página desde el mat-paginator
   * @param event Evento del paginador
   */
  onPageChange(event: any): void {
    this.page = event.pageIndex;
    this.size = event.pageSize;
    this.cargarAsesoresConClientes();
  }

  descargarExcelCliente(numeroMovil: string): void {
    this.store.dispatch(CoordinadorActions.descargarExcelCliente({ numeroMovil }));
  }
  descargarExcelHoy(): void {
    this.user$.pipe(take(1)).subscribe(user => {
      if (!user || !user.id) {
        alert("Usuario no identificado.");
        return;
      }

      this.store.dispatch(CoordinadorActions.exportarClientesHoyCoordinador({ coordinadorId: user.id }));

      this.store.pipe(
        select(fromCoordinadorSelectors.selectExcelBlob),
        filter((blob): blob is Blob => !!blob),
        take(1)
      ).subscribe((blob) => {
        const fecha = new Date().toISOString().split('T')[0];
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `clientes_asesores_${fecha}.xlsx`;
        a.click();
        window.URL.revokeObjectURL(url);
      });

    });
  }

  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    // Verificar si el body tiene la clase dark-theme
    this.isDarkTheme = document.body.classList.contains('dark-theme');
  }

  /**
   * Observa cambios en el tema
   */
  observeThemeChanges(): void {
    // Crear un observador para detectar cambios en las clases del body
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          this.checkDarkTheme();
        }
      });
    });

    // Iniciar la observación
    observer.observe(document.body, { attributes: true });
  }

  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  downloadOrPrint(elementId: string): void {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Mostrar indicador de carga
    this.exportLoading = true;

    // Cargar el logo primero
    const logoImg = new Image();
    logoImg.src = 'assets/logovector-MIDAS.svg';

    logoImg.onload = () => {
      // Convertir el SVG a PNG usando canvas
      const canvas = document.createElement('canvas');
      const logoWidth = 300; // Ancho del logo en el canvas (mayor resolución)
      const logoHeight = 100; // Altura del logo en el canvas
      canvas.width = logoWidth;
      canvas.height = logoHeight;

      const ctx = canvas.getContext('2d');
      if (ctx) {
        // Fondo transparente
        ctx.clearRect(0, 0, logoWidth, logoHeight);
        // Dibujar el logo en el canvas
        ctx.drawImage(logoImg, 0, 0, logoWidth, logoHeight);
        // Convertir a formato de imagen
        const logoDataUrl = canvas.toDataURL('image/png');

        // Ahora proceder con la generación del PDF
        this.generatePDFWithLogo(element, logoDataUrl);
      } else {
        // Si no se puede obtener el contexto del canvas, generar PDF sin logo
        this.generatePDFWithoutLogo(element);
      }
    };

    logoImg.onerror = () => {
      console.error('Error al cargar el logo');
      // Continuar sin el logo en caso de error
      this.generatePDFWithoutLogo(element);
    };
  }

  // Método para generar PDF con logo
  private generatePDFWithLogo(element: HTMLElement, logoDataUrl: string): void {
    // Configuración para html2canvas
    const options = {
      scale: 2, // Mejor calidad
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null
    };

    html2canvas(element, options).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // Añadir el logo en la parte superior
      const logoWidth = 50; // Ancho del logo en mm en el PDF
      const logoHeight = 15; // Altura del logo en mm en el PDF
      const logoX = (pdfWidth - logoWidth) / 2; // Centrar horizontalmente
      const logoY = 10; // Margen superior para el logo

      // Añadir el logo al PDF como PNG
      pdf.addImage(logoDataUrl, 'PNG', logoX, logoY, logoWidth, logoHeight);

      // Calcular la altura proporcional manteniendo la relación de aspecto
      const contentHeight = (canvas.height * pdfWidth) / canvas.width;

      // Margen superior después del logo
      const marginTop = logoY + logoHeight + 5; // 5mm de espacio adicional después del logo

      // Si el contenido es más alto que una página, dividirlo en múltiples páginas
      if (contentHeight > pageHeight - marginTop) {
        let remainingHeight = contentHeight;
        let position = 0;

        // Primera página (con logo)
        pdf.addImage(imgData, 'PNG', 0, marginTop, pdfWidth, contentHeight, '', 'FAST');
        remainingHeight -= (pageHeight - marginTop);
        position += (pageHeight - marginTop);

        // Páginas adicionales si es necesario
        while (remainingHeight > 0) {
          pdf.addPage();

          // Añadir el logo en cada página nueva
          pdf.addImage(logoDataUrl, 'PNG', logoX, logoY, logoWidth, logoHeight);

          pdf.addImage(
            imgData,
            'PNG',
            0,
            -(position - marginTop),
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );

          remainingHeight -= pageHeight;
          position += pageHeight;
        }
      } else {
        // Si cabe en una sola página
        pdf.addImage(imgData, 'PNG', 0, marginTop, pdfWidth, contentHeight, '', 'FAST');
      }

      this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
        pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
        // Ocultar indicador de carga
        this.exportLoading = false;
      });
    }).catch(error => {
      console.error('Error al generar PDF:', error);
      Swal.fire('Error', 'No se pudo generar el PDF. Intente nuevamente.', 'error');
      this.exportLoading = false;
    });
  }

  // Método auxiliar para generar PDF sin logo en caso de error al cargar el logo
  private generatePDFWithoutLogo(element: HTMLElement): void {
    const options = {
      scale: 2,
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null
    };

    html2canvas(element, options).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const contentHeight = (canvas.height * pdfWidth) / canvas.width;
      const pageHeight = pdf.internal.pageSize.getHeight();
      const marginTop = 10; // Margen superior sin logo

      if (contentHeight > pageHeight - marginTop) {
        let remainingHeight = contentHeight;
        let position = 0;

        pdf.addImage(imgData, 'PNG', 0, marginTop, pdfWidth, contentHeight, '', 'FAST');
        remainingHeight -= (pageHeight - marginTop);
        position += (pageHeight - marginTop);

        while (remainingHeight > 0) {
          pdf.addPage();
          pdf.addImage(
            imgData,
            'PNG',
            0,
            -(position - marginTop),
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );

          remainingHeight -= pageHeight;
          position += pageHeight;
        }
      } else {
        pdf.addImage(imgData, 'PNG', 0, marginTop, pdfWidth, contentHeight, '', 'FAST');
      }

      this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
        pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
        this.exportLoading = false;
      });
    }).catch(error => {
      console.error('Error al generar PDF:', error);
      Swal.fire('Error', 'No se pudo generar el PDF. Intente nuevamente.', 'error');
      this.exportLoading = false;
    });
  }


  formatDateArrayWithTime(dateValue: any[] | string | undefined): string {
    // Si es undefined o null, devolver un guión
    if (!dateValue) {
      return '-';
    }

    // Si es un string, intentar formatear como fecha
    if (typeof dateValue === 'string') {
      try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
          return dateValue; // Si no es una fecha válida, devolver el string original
        }
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        return `${day}/${month}/${year} ${hour}:${minute}`;
      } catch (e) {
        return dateValue; // En caso de error, devolver el string original
      }
    }

    // Si es un array pero no tiene suficientes elementos, devolver un guión
    if (Array.isArray(dateValue) && dateValue.length < 3) {
      return '-';
    }

    // Si es un array con al menos 3 elementos (año, mes, día)
    if (Array.isArray(dateValue)) {
      const year = dateValue[0];
      const month = String(dateValue[1]).padStart(2, '0');
      const day = String(dateValue[2]).padStart(2, '0');

      let timeStr = '';
      if (dateValue.length >= 5) {
        const hour = String(dateValue[3]).padStart(2, '0');
        const minute = String(dateValue[4]).padStart(2, '0');
        timeStr = ` ${hour}:${minute}`;
      }

      return `${day}/${month}/${year}${timeStr}`;
    }

    // Para cualquier otro tipo de dato, convertir a string
    return String(dateValue);
  }
}
