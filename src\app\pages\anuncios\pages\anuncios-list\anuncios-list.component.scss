/* Estilos mínimos para compatibilidad con Material Design y funcionalidades específicas */

/* Tema oscuro - mantenemos solo los estilos necesarios para el tema oscuro */
:host-context(.dark-theme) {
  .bg-gray-50 {
    background-color: #0e1c33 !important;
  }

  .bg-white {
    background-color: #0a1628 !important;
  }

  .text-gray-800 {
    color: #ffffff !important;
  }

  .text-gray-600 {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .text-blue-600 {
    color: #64b5f6 !important;
  }

  .bg-gray-50 {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .border-blue-600 {
    border-color: #64b5f6 !important;
  }

  .border-gray-100 {
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  }

  .hover\:shadow-xl:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3) !important;
  }

  input {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    caret-color: #ffffff !important;

    &:focus {
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2) !important;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }

  .text-green-500 {
    color: #66bb6a !important;
  }

  .text-red-500 {
    color: #ef5350 !important;
  }

  .text-orange-500 {
    color: #ffb74d !important;
  }

  .text-gray-500 {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .bg-green-500 {
    background-color: #43a047 !important;
  }

  .bg-red-500 {
    background-color: #d32f2f !important;
  }

  .text-blue-600 {
    color: #64b5f6 !important;
  }

  .hover\:bg-blue-50:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .hover\:bg-red-50:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}
