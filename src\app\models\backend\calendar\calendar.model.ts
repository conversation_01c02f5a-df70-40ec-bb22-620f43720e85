import { Model } from "./Model";
import { DateUtils } from "@app/utils/date-utils";

export class Calendar extends Model{
  public id: number = 0;
  public titulo: string = '';
  public descripcion: string = '';
  public color: string = '';
  public fecha_inicio: string = '';
  public fecha_final: string = '';
  public hora_inicio: string = '';
  public hora_final: string = '';
  public is_active: boolean = true;
  public is_seen: boolean = false;

  constructor(data?: any){
    super(data);
    // Override default values if data is provided
    if (data) {
      // Procesar fechas y horas si vienen en formato de array
      if (Array.isArray(data.fechaInicio)) {
        data.fecha_inicio = DateUtils.formatDateArrayToString(data.fechaInicio);
      } else if (data.fechaInicio) {
        data.fecha_inicio = data.fechaInicio;
      }

      if (Array.isArray(data.fechaFinal)) {
        data.fecha_final = DateUtils.formatDateArrayToString(data.fechaFinal);
      } else if (data.fechaFinal) {
        data.fecha_final = data.fechaFinal;
      }

      if (Array.isArray(data.horaInicio)) {
        data.hora_inicio = DateUtils.formatTimeArrayToString(data.horaInicio);
      } else if (data.horaInicio) {
        data.hora_inicio = data.horaInicio;
      }

      if (Array.isArray(data.horaFinal)) {
        data.hora_final = DateUtils.formatTimeArrayToString(data.horaFinal);
      } else if (data.horaFinal) {
        data.hora_final = data.horaFinal;
      }

      Object.assign(this, data);
    }
  }

  public static cast(data: any): Calendar{
    return new Calendar(data);
  }

  public static casts(dataArray: any[]): Calendar[]{
    return dataArray.map((data) => Calendar.cast(data));
  }
}

export class CalendarList extends Model{
  public id: number = 0;
  public titulo: string = '';
  public descripcion: string = '';
  public color: string = '';
  public fecha_inicio: string = '';
  public fecha_final: string = '';
  public hora_inicio: string = '';
  public hora_final: string = '';
  public is_active: boolean = true;
  public is_seen: boolean = false;
  public userCreateId: number = 0;

  constructor(data?: any){
    super(data);
    // Override default values if data is provided
    if (data) {
      // Procesar fechas y horas si vienen en formato de array
      if (Array.isArray(data.fechaInicio)) {
        data.fecha_inicio = DateUtils.formatDateArrayToString(data.fechaInicio);
      } else if (data.fechaInicio) {
        data.fecha_inicio = data.fechaInicio;
      }

      if (Array.isArray(data.fechaFinal)) {
        data.fecha_final = DateUtils.formatDateArrayToString(data.fechaFinal);
      } else if (data.fechaFinal) {
        data.fecha_final = data.fechaFinal;
      }

      if (Array.isArray(data.horaInicio)) {
        data.hora_inicio = DateUtils.formatTimeArrayToString(data.horaInicio);
      } else if (data.horaInicio) {
        data.hora_inicio = data.horaInicio;
      }

      if (Array.isArray(data.horaFinal)) {
        data.hora_final = DateUtils.formatTimeArrayToString(data.horaFinal);
      } else if (data.horaFinal) {
        data.hora_final = data.horaFinal;
      }

      Object.assign(this, data);
    }
  }

  public static cast(data: any): CalendarList{
    return new CalendarList(data);
  }

  public static casts(dataArray: any[]): CalendarList[]{
    return dataArray.map((data) => CalendarList.cast(data));
  }
}