import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { WebSocketService } from '../websocket/WebSocketService';
import { map, shareReplay, debounceTime } from 'rxjs/operators';

export interface UserStatus {
  userId: number;
  username: string;
  nombre: string;
  apellido: string;
  status?: string; // "ONLINE" o "OFFLINE"
  online: boolean;
  lastActivity: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserStatusService {
  private userStatusSubject = new BehaviorSubject<UserStatus[]>([]);
  private currentUserStatus = new BehaviorSubject<boolean>(false);
  private initialized = false;
  private lastRequestTime = 0;
  private readonly REQUEST_THROTTLE = 10000; // 10 segundos entre solicitudes

  constructor(
    private webSocketService: WebSocketService
  ) {
    this.initialize();
  }

  /**
   * Inicializa el servicio y configura las suscripciones WebSocket
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    // Suscribirse a las actualizaciones de estado de usuarios
    this.webSocketService.getMessagesByType('USERS_STATUS_UPDATED')
      .subscribe(statusList => {


        if (Array.isArray(statusList)) {
          // Actualizar la lista completa de estados
          this.userStatusSubject.next(statusList);

          // Actualizar el estado del usuario actual
          const authService = this.webSocketService['auth'];
          if (authService) {
            const userId = authService.getUserId();
            if (userId) {
              const currentUser = statusList.find(u => Number(u.userId) === Number(userId));
              if (currentUser) {
                this.currentUserStatus.next(currentUser.online);
              }
            }
          }
        } else if (statusList && typeof statusList === 'object') {
          // Si recibimos un solo estado, lo agregamos a la lista existente
          this.updateSingleUserStatus(statusList);
        }
      });

    // Suscribirse también al tópico específico de estado de usuarios
    this.webSocketService.getMessagesByDestination('/topic/users/status')
      .subscribe(statusList => {
        if (Array.isArray(statusList)) {

          // Actualizar la lista completa de estados
          this.userStatusSubject.next(statusList);
        }
      });

    // Suscribirse al tópico que contiene la lista completa de usuarios
    this.webSocketService.getMessagesByDestination('/topic/users/status/all')
      .pipe(
        // Usar debounceTime para reducir la frecuencia de actualizaciones
        debounceTime(500)
      )
      .subscribe(statusList => {
        if (Array.isArray(statusList)) {

          // Actualizar la lista completa de estados
          this.userStatusSubject.next(statusList);
        }
      });

    // Suscribirse al evento de conexión establecida
    this.webSocketService.getMessagesByType('CONNECTION_ESTABLISHED')
      .subscribe(() => {


        // Actualizar el estado del usuario actual como conectado
        this.updateCurrentUserStatus(true);

        // No solicitar el estado de usuarios aquí, ya se hace en el login
        // y el backend enviará actualizaciones automáticamente
      });

    // Verificar el estado de conexión actual
    this.checkConnectionStatus();

    // No solicitar el estado de usuarios aquí, ya se hace en el login
    // y el backend enviará actualizaciones automáticamente

    this.initialized = true;
  }

  /**
   * Actualiza un solo estado de usuario en la lista
   * Simplificado ya que el backend envía la lista completa
   */
  private updateSingleUserStatus(status: UserStatus): void {
    // Verificar si el status es válido
    if (!status || typeof status.userId === 'undefined') {
      return;
    }

    // En lugar de actualizar manualmente, solicitamos la lista completa al backend
    if (this.webSocketService.isConnected()) {
      this.webSocketService.sendMessage('/app/users.status.all');
    } else {
      // Si no hay conexión, actualizamos solo el estado local para UI inmediata
      const currentList = this.userStatusSubject.value;
      const index = currentList.findIndex(s => Number(s.userId) === Number(status.userId));

      // Crear una copia de la lista actual
      const updatedList = [...currentList];

      if (index !== -1) {
        // Actualizar estado existente
        updatedList[index] = {
          ...updatedList[index],  // Mantener propiedades existentes
          ...status,              // Sobrescribir con nuevas propiedades
          // Asegurar que online sea booleano
          online: status.online === true || (status.status === 'ONLINE')
        };
      } else {
        // Agregar nuevo estado
        updatedList.push({
          ...status,
          // Asegurar que online sea booleano
          online: status.online === true || (status.status === 'ONLINE')
        });
      }

      // Actualizar la lista completa
      this.userStatusSubject.next(updatedList);
    }

    // Si es el usuario actual, actualizar su estado
    const authService = this.webSocketService['auth'];
    if (authService) {
      const userId = authService.getUserId();
      if (userId && Number(userId) === Number(status.userId)) {
        this.currentUserStatus.next(status.online === true || (status.status === 'ONLINE'));
      }
    }
  }

  /**
   * Verifica el estado de conexión actual
   * Ya no usamos HTTP, confiamos en el WebSocket
   */
  checkConnectionStatus(): void {
    const authService = this.webSocketService['auth'];
    if (!authService) {
      return;
    }

    const userId = authService.getUserId();
    if (!userId) {
      return;
    }

    // Simplemente actualizamos el estado local basado en la conexión WebSocket
    this.currentUserStatus.next(this.webSocketService.isConnected());
  }

  /**
   * Solicita el estado actual de los usuarios
   * Simplificado para confiar en las actualizaciones automáticas del backend
   */
  requestUsersStatus(): void {
    // Aplicar throttling a las solicitudes
    const now = Date.now();
    if (now - this.lastRequestTime < this.REQUEST_THROTTLE) {
      return;
    }

    this.lastRequestTime = now;

    if (this.webSocketService.isConnected()) {
      // Solicitar estado de usuarios
      this.webSocketService.requestUsersStatus();

      // Registrar el usuario actual como conectado
      this.updateCurrentUserStatus(true);
    } else {
      // Intentar conectar
      this.webSocketService.connect();
    }
  }

  // Variable para evitar actualizaciones de estado redundantes
  private lastStatusUpdate: { userId: number, online: boolean, timestamp: number } | null = null;
  private readonly STATUS_UPDATE_THROTTLE = 2000; // 2 segundos entre actualizaciones

  /**
   * Actualiza el estado del usuario actual
   * @param online Estado de conexión (true = conectado, false = desconectado)
   */
  updateCurrentUserStatus(online: boolean): void {
    // Obtener el ID del usuario actual
    const authService = this.webSocketService['auth'];
    if (!authService) {
      return;
    }

    const userId = authService.getUserId();
    if (!userId) {
      return;
    }

    // Verificar si es una actualización redundante
    const now = Date.now();
    if (this.lastStatusUpdate &&
        this.lastStatusUpdate.userId === userId &&
        this.lastStatusUpdate.online === online &&
        now - this.lastStatusUpdate.timestamp < this.STATUS_UPDATE_THROTTLE) {
      // Ignorar actualizaciones redundantes en un corto período de tiempo
      return;
    }

    // Actualizar el estado local
    this.currentUserStatus.next(online);

    // Registrar esta actualización
    this.lastStatusUpdate = {
      userId: Number(userId),
      online,
      timestamp: now
    };

    // Enviar mensaje WebSocket al servidor para actualizar el estado
    if (online) {
      // Si está online, enviar mensaje de conexión
      this.webSocketService.sendMessage('/app/user.connect', { userId });

      // Solicitar la lista completa actualizada al backend
      if (this.webSocketService.isConnected()) {
        this.webSocketService.sendMessage('/app/users.status.all');
      }
    } else {
      // Si está offline, NO enviar mensaje de desconexión desde aquí
      // La desconexión se maneja exclusivamente en WebSocketService.disconnect()
    }
  }

  /**
   * Obtiene la lista de estados de usuarios
   */
  getUsersStatus(): Observable<UserStatus[]> {
    return this.userStatusSubject.asObservable();
  }

  /**
   * Obtiene el estado de conexión del usuario actual
   */
  getCurrentUserStatus(): Observable<boolean> {
    return this.currentUserStatus.asObservable();
  }

  /**
   * Obtiene la lista de usuarios conectados
   */
  getOnlineUsers(): Observable<UserStatus[]> {
    return this.getUsersStatus().pipe(
      map(users => users.filter(user => user.online)),
      shareReplay(1)
    );
  }

  /**
   * Obtiene la lista de usuarios desconectados
   */
  getOfflineUsers(): Observable<UserStatus[]> {
    return this.getUsersStatus().pipe(
      map(users => users.filter(user => !user.online)),
      shareReplay(1)
    );
  }

  /**
   * Verifica si un usuario está conectado
   * @param userId ID del usuario a verificar
   */
  isUserOnline(userId: number): Observable<boolean> {
    return this.getUsersStatus().pipe(
      map(users => {
        const user = users.find(u => Number(u.userId) === Number(userId));
        return user ? user.online : false;
      }),
      shareReplay(1)
    );
  }
}
