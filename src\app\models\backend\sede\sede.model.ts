import { Model } from "../calendar/Model";

export class Sede extends Model {
  public id?: number;
  public nombre: string = '';
  public direccion: string = '';
  public ciudad: string = '';
  public provincia: string = '';
  public codigoPostal: string = '';
  public telefono: string = '';
  public email: string = '';
  public activo: boolean = true;
  public fechaCreacion: string = '';
  public fechaActualizacion: string = '';

  constructor(data?: object) {
    super(data);
    // Override default values if data is provided
    if (data) {
      Object.assign(this, data);
    }
  }

  public static cast(data: object): Sede {
    return new Sede(data);
  }

  public static casts(dataArray: object[]): Sede[] {
    return dataArray.map((data) => Sede.cast(data));
  }
}

export interface SedePaginadoResponse {
  sedes: Sede[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
}
