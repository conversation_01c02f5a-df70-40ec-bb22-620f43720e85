import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Store, select } from '@ngrx/store';

import { EncuestaService } from '@app/services/encuesta.service';
import { NotificationService } from '@app/services/notification/notification.service';
import {
  Encuesta,
  TipoAsignacion,
} from '@app/models/backend/encuesta/encuesta.model';
import { EncuestaFormComponent } from '../../components/encuesta-form/encuesta-form.component';
import { UserService } from '@app/services/user.service';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';

@Component({
  selector: 'app-encuestas-page',
  templateUrl: './encuestas-page.component.html',
  styleUrls: ['./encuestas-page.component.scss'],
})
export class EncuestasPageComponent implements OnInit, OnDestroy {
  // Encuestas
  encuestas: Encuesta[] = [];
  encuestasDisponibles: Encuesta[] = [];
  encuestasCreadas: Encuesta[] = [];

  // Paginación
  totalItems = 0;
  pageSize = 8;
  currentPage = 0;
  pageSizeOptions: number[] = [4, 8, 12, 24];

  // Filtros
  searchTerm = '';

  // Estado
  loading = false;
  error = false;

  // Roles
  isAdmin = false;
  isPsicologo = false;

  // Destructor del componente
  private destroy$ = new Subject<void>();

  constructor(
    private encuestaService: EncuestaService,
    private userService: UserService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private store: Store<fromRoot.State>
  ) {}

  ngOnInit(): void {
    // Primero verificamos los roles y luego cargamos las encuestas
    this.checkUserRoles();

    // También podemos obtener el rol directamente del localStorage como alternativa
    const userStr = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    console.log('Token en localStorage:', token ? 'Existe' : 'No existe');

    // Intentar obtener información del token JWT
    if (token) {
      try {
        const tokenData = this.parseJwt(token);
        console.log('Información del token JWT:', tokenData);

        if (tokenData && tokenData.role) {
          const tokenRole = tokenData.role.toString().toUpperCase();
          this.isAdmin = tokenRole === 'ADMIN';
          this.isPsicologo = tokenRole === 'PSICOLOGO';

          // Garantizamos que los administradores tengan las mismas capacidades que los psicólogos
          if (this.isAdmin) {
            this.isPsicologo = true;
          }

          console.log('Roles detectados desde token JWT:', {
            isAdmin: this.isAdmin,
            isPsicologo: this.isPsicologo,
            role: tokenRole,
          });

          // Cargamos las encuestas con los roles detectados
          this.loadEncuestas();
        }
      } catch (e) {
        console.error('Error al parsear token JWT:', e);
      }
    }

    // Intentar obtener información del usuario en localStorage
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.role) {
          const userRole = user.role.toString().toUpperCase();
          this.isAdmin = userRole === 'ADMIN';
          this.isPsicologo = userRole === 'PSICOLOGO';

          // Garantizamos que los administradores tengan las mismas capacidades que los psicólogos
          if (this.isAdmin) {
            this.isPsicologo = true;
          }

          console.log('Roles detectados desde localStorage:', {
            isAdmin: this.isAdmin,
            isPsicologo: this.isPsicologo,
            role: userRole,
            user: user,
          });

          // No cargamos las encuestas aquí porque ya se cargarán en checkUserRoles()
        }
      } catch (e) {
        console.error('Error al parsear usuario desde localStorage:', e);
      }
    }
  }

  /**
   * Parsea un token JWT para obtener su contenido
   */
  private parseJwt(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

      // Decodificar base64 usando atob
      const binaryString = window.atob(base64);

      // Convertir a string UTF-8
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const jsonPayload = new TextDecoder().decode(bytes);

      return JSON.parse(jsonPayload);
    } catch (e) {
      console.error('Error al parsear token JWT:', e);
      return null;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Verifica los roles del usuario
   */
  checkUserRoles(): void {
    // Primero intentamos obtener el usuario desde el store
    this.store
      .pipe(select(fromUser.getUser), takeUntil(this.destroy$))
      .subscribe((user) => {
        if (user && user.role) {
          // Si tenemos el usuario en el store, verificamos su rol
          const userRole = user.role.toString().toUpperCase();
          this.isAdmin = userRole === 'ADMIN';
          this.isPsicologo = userRole === 'PSICOLOGO';

          // Garantizamos que los administradores tengan las mismas capacidades que los psicólogos
          if (this.isAdmin) {
            this.isPsicologo = true;
          }

          console.log('Roles detectados desde store:', {
            isAdmin: this.isAdmin,
            isPsicologo: this.isPsicologo,
            role: userRole,
            user: user,
          });

          // Cargamos las encuestas después de verificar los roles
          this.loadEncuestas();
        } else {
          // Si no tenemos el usuario en el store, intentamos obtenerlo del servicio
          this.userService.getUsers().subscribe({
            next: (response) => {
              // Intentamos obtener el usuario actual de diferentes formas posibles
              const currentUser =
                response.data?.currentUser || response.data?.user;

              if (currentUser) {
                // Verificamos si el rol está en formato de array o como string único
                if (Array.isArray(currentUser.roles)) {
                  // Si es un array de roles
                  this.isAdmin = currentUser.roles.includes('ADMIN');
                  this.isPsicologo = currentUser.roles.includes('PSICOLOGO');
                } else {
                  // Si es un string único en el campo 'role'
                  const userRole =
                    currentUser.role?.toString().toUpperCase() || '';
                  this.isAdmin = userRole === 'ADMIN';
                  this.isPsicologo = userRole === 'PSICOLOGO';
                }

                // Garantizamos que los administradores tengan las mismas capacidades que los psicólogos
                if (this.isAdmin) {
                  this.isPsicologo = true;
                }

                console.log('Roles detectados desde API:', {
                  isAdmin: this.isAdmin,
                  isPsicologo: this.isPsicologo,
                  role: currentUser.role || currentUser.roles,
                });

                // Cargamos las encuestas después de verificar los roles
                this.loadEncuestas();
              } else {
                console.warn(
                  'No se pudo obtener información del usuario actual'
                );
                this.loadEncuestas();
              }
            },
            error: (error) => {
              console.error('Error al obtener usuario:', error);
              // Incluso en caso de error, intentamos cargar las encuestas
              this.loadEncuestas();
            },
          });
        }
      });
  }

  /**
   * Carga las encuestas según el rol del usuario
   */
  loadEncuestas(): void {
    this.loading = true;

    // Si es admin o psicólogo, cargar todas las encuestas con paginación
    if (this.isAdmin || this.isPsicologo) {
      this.encuestaService
        .getPageable(this.currentPage, this.pageSize, this.searchTerm)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            this.encuestas = response.data.content;
            this.totalItems = response.data.totalElements;
            this.loading = false;
          },
          error: (error) => {
            console.error('Error al cargar encuestas:', error);
            this.notificationService.error('Error al cargar encuestas');
            this.loading = false;
            this.error = true;
          },
        });

      // Cargar encuestas creadas por el usuario
      this.encuestaService
        .getCreadas()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            this.encuestasCreadas = response.data;
          },
          error: (error) => {
            console.error('Error al cargar encuestas creadas:', error);
          },
        });
    }

    // Cargar encuestas disponibles para el usuario
    this.encuestaService
      .getDisponibles()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.encuestasDisponibles = response.data;

          // Si no es admin ni psicólogo, mostrar solo las encuestas disponibles
          if (!this.isAdmin && !this.isPsicologo) {
            this.encuestas = this.encuestasDisponibles;
            this.loading = false;
          }
        },
        error: (error) => {
          console.error('Error al cargar encuestas disponibles:', error);
          if (!this.isAdmin && !this.isPsicologo) {
            this.notificationService.error('Error al cargar encuestas');
            this.loading = false;
            this.error = true;
          }
        },
      });
  }

  /**
   * Maneja el cambio de página
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadEncuestas();
  }

  /**
   * Filtra las encuestas por término de búsqueda
   */
  applyFilter(): void {
    this.currentPage = 0;
    this.loadEncuestas();
  }

  /**
   * Abre el formulario para crear una nueva encuesta
   */
  openCreateForm(): void {
    const dialogRef = this.dialog.open(EncuestaFormComponent, {
      width: '800px',
      disableClose: true,
      data: { isNew: true },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadEncuestas();
      }
    });
  }

  /**
   * Abre el formulario para editar una encuesta existente
   */
  openEditForm(encuesta: Encuesta): void {
    const dialogRef = this.dialog.open(EncuestaFormComponent, {
      width: '800px',
      disableClose: true,
      data: { isNew: false, encuesta },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadEncuestas();
      }
    });
  }

  /**
   * Elimina una encuesta
   */
  deleteEncuesta(id: number): void {
    if (confirm('¿Está seguro de eliminar esta encuesta?')) {
      this.encuestaService
        .delete(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.notificationService.success(
              'Encuesta eliminada correctamente'
            );
            this.loadEncuestas();
          },
          error: (error) => {
            console.error('Error al eliminar encuesta:', error);
            this.notificationService.error('Error al eliminar encuesta');
          },
        });
    }
  }

  /**
   * Obtiene el texto del tipo de asignación
   */
  getTipoAsignacionText(tipo: TipoAsignacion): string {
    switch (tipo) {
      case TipoAsignacion.TODOS:
        return 'Todos los usuarios';
      case TipoAsignacion.SEDE:
        return 'Usuarios de sede específica';
      case TipoAsignacion.COORDINACION:
        return 'Usuarios de coordinador específico';
      case TipoAsignacion.PERSONAL:
        return 'Usuario específico';
      default:
        return 'Desconocido';
    }
  }
}
