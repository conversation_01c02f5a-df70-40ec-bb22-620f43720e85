:host {
  display: block;
  width: 100%;
  box-sizing: border-box;
}

mat-dialog-title {
  margin: 0 !important;
  padding: 16px 24px !important;
  background-color: #f5f5f5;
  color: #333;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
}

mat-dialog-content {
  min-height: 200px;
  max-height: calc(80vh - 130px);
  overflow-y: auto;
  padding: 20px 24px !important;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

mat-dialog-actions {
  padding: 16px 24px !important;
  border-top: 1px solid #e0e0e0;
  margin: 0 !important;
  gap: 12px;
}

.w-100 {
  width: 100%;
}

.manual-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mt-3 {
  margin-top: 1rem;
}

.file-upload-container {
  margin: 1rem 0;
  padding: 1rem;
  border: 1px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;

  &:hover {
    border-color: #3f51b5;
    background-color: #f5f5f5;
  }
}

.file-upload-header {
  margin-bottom: 1rem;

  span {
    font-weight: 500;
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.87);
  }
}

.current-file-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.current-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #e3f2fd;
  border-radius: 4px;

  mat-icon {
    color: #1976d2;
  }

  .file-name {
    flex: 1;
    font-weight: 500;
  }
}

.replace-btn {
  align-self: flex-end;
}

.file-selector {
  position: relative;
}

.dropzone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border: 2px dashed #ccc;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;

  &:hover {
    border-color: #3f51b5;
    background-color: #f0f0f0;
  }

  &.dropzone-active {
    border-color: #3f51b5;
    background-color: #e8eaf6;
    box-shadow: 0 0 10px rgba(63, 81, 181, 0.3);
  }

  mat-icon {
    font-size: 2rem;
    height: 2rem;
    width: 2rem;
    margin-bottom: 0.75rem;
    color: #3f51b5;
  }

  p {
    margin-bottom: 0.75rem;
    color: #666;
    font-size: 0.9rem;
    max-width: 100%;
    word-wrap: break-word;
    text-align: center;
  }

  button.select-file-btn {
    margin-top: 0.5rem;
    max-width: 200px;
  }
}

.file-input {
  display: none;
}

.selected-file {
  margin-top: 1rem;
}

.file-info-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #e3f2fd;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;

  mat-icon {
    color: #1976d2;
    flex-shrink: 0;
  }

  .file-name {
    flex: 1;
    font-weight: 500;
    word-break: break-all;
    font-size: 0.9rem;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.file-info {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 100%;
  box-sizing: border-box;

  small {
    color: #666;
    font-size: 0.8rem;
    line-height: 1.2;
  }
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  mat-dialog-title {
    background-color: #1e2746 !important;
    color: white !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  mat-dialog-content {
    color: white;
  }

  mat-dialog-actions {
    border-top-color: rgba(255, 255, 255, 0.1) !important;
  }
  .file-upload-container {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);

    &:hover {
      border-color: #1e4976;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .dropzone {
    background-color: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.2);

    &:hover {
      border-color: #1e4976;
      background-color: rgba(255, 255, 255, 0.08);
    }

    &.dropzone-active {
      border-color: #1e4976;
      background-color: rgba(30, 73, 118, 0.2);
      box-shadow: 0 0 10px rgba(30, 73, 118, 0.5);
    }

    mat-icon {
      color: #90caf9;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .current-file,
  .file-info-container {
    background-color: rgba(30, 73, 118, 0.3);

    mat-icon {
      color: #90caf9;
    }
  }

  .file-info small {
    color: rgba(255, 255, 255, 0.7);
  }
}
