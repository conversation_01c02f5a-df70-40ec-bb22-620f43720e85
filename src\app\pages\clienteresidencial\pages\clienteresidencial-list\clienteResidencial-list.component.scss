/* Estilos específicos para el componente clienteResidencial-list */

/* Estilos para tema oscuro */
:host-context(.dark-theme) {
  /* Estilos para la tabla Angular Material */
  .mat-table {
    background-color: transparent !important;
    color: #ffffff !important;
  }

  .mat-header-cell {
    color: #ffffff !important;
    background-color: #374151 !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    font-weight: 600 !important;
  }

  .mat-cell {
    color: #ffffff !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .mat-row {
    background-color: transparent !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
    }
  }

  .mat-header-row {
    background-color: #374151 !important;
  }

  /* Estilos para el paginador Angular Material */
  .mat-paginator {
    background-color: #1f2937 !important;
    color: #ffffff !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .mat-paginator-container {
    color: #ffffff !important;
  }

  .mat-paginator-page-size-label,
  .mat-paginator-range-label {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .mat-paginator-navigation-previous,
  .mat-paginator-navigation-next,
  .mat-paginator-navigation-first,
  .mat-paginator-navigation-last {
    color: #ffffff !important;

    &[disabled] {
      color: rgba(255, 255, 255, 0.3) !important;
    }
  }

  .mat-select-value,
  .mat-select-arrow {
    color: #ffffff !important;
  }

  .mat-paginator-icon {
    fill: #ffffff !important;
  }

  /* Estilos para los iconos de Material */
  .mat-icon {
    color: inherit !important;
  }

  /* Estilos para los botones de acción */
  .mat-icon-button {
    color: inherit !important;
  }

  /* Estilos para los tooltips */
  .mat-tooltip {
    background-color: #374151 !important;
    color: #ffffff !important;
  }

  /* Estilos para los spinners */
  .mat-progress-spinner circle {
    stroke: #60a5fa !important;
  }

  /* Estilos específicos para elementos con clases de Tailwind que no funcionan correctamente */
  .text-gray-700 {
    color: #ffffff !important;
  }

  .text-gray-800 {
    color: #ffffff !important;
  }

  .text-gray-900 {
    color: #ffffff !important;
  }

  /* Asegurar que los elementos de la tabla tengan el color correcto */
  table.mat-table {
    .mat-header-cell,
    .mat-cell {
      color: #ffffff !important;
    }
  }

  /* Estilos para los select de Angular Material */
  .mat-select-panel {
    background-color: #374151 !important;
    color: #ffffff !important;
  }

  .mat-option {
    color: #ffffff !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  /* Estilos para los campos de formulario */
  .mat-form-field {
    .mat-form-field-label {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .mat-input-element {
      color: #ffffff !important;
      caret-color: #ffffff !important;
    }

    .mat-form-field-outline {
      color: rgba(255, 255, 255, 0.3) !important;
    }

    &.mat-focused {
      .mat-form-field-outline-thick {
        color: #60a5fa !important;
      }

      .mat-form-field-label {
        color: #60a5fa !important;
      }
    }
  }
}

/* Estilos adicionales para asegurar que Tailwind funcione correctamente */
:host-context(.dark) {
  /* Duplicar algunos estilos críticos para la clase 'dark' de Tailwind */
  .mat-table {
    .mat-header-cell,
    .mat-cell {
      color: #ffffff !important;
    }
  }

  .mat-paginator {
    background-color: #1f2937 !important;
    color: #ffffff !important;
  }
}

/* Estilos para mejorar la legibilidad general */
:host {
  /* Asegurar que los elementos tengan suficiente contraste */
  .mat-table {
    .mat-header-cell {
      font-weight: 600;
    }
  }
}

/* Media queries para dispositivos móviles */
@media (max-width: 768px) {
  :host-context(.dark-theme) {
    .mat-table {
      .mat-header-cell,
      .mat-cell {
        padding: 8px 4px !important;
        font-size: 0.875rem !important;
      }
    }
  }
}

/* Estilos para elementos específicos que pueden necesitar ajustes adicionales */
:host-context(.dark-theme) {
  /* Asegurar que los elementos de texto en la tabla sean visibles */
  td, th {
    color: #ffffff !important;
  }

  /* Estilos para los elementos de acción en la tabla */
  .mat-icon-button {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  /* Estilos para los elementos de estado vacío */
  .mat-icon {
    &.text-gray-400 {
      color: rgba(255, 255, 255, 0.6) !important;
    }
  }
}
