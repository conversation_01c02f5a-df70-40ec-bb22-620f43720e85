// export-by-date-range-dialog.component.ts
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-export-by-date-range-dialog',
  template: `
    <h2 mat-dialog-title>Exportar por Rango de Fechas</h2>
    <mat-dialog-content>
      <form [formGroup]="rangeForm" class="date-range-form">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Fecha de inicio</mat-label>
          <input matInput [matDatepicker]="pickerStart" formControlName="fechaInicio" placeholder="Seleccione fecha de inicio">
          <mat-datepicker-toggle matSuffix [for]="pickerStart"></mat-datepicker-toggle>
          <mat-datepicker #pickerStart></mat-datepicker>
          <mat-error *ngIf="rangeForm.get('fechaInicio')?.hasError('required')">
            La fecha de inicio es obligatoria
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Fecha de fin</mat-label>
          <input matInput [matDatepicker]="pickerEnd" formControlName="fechaFin" placeholder="Seleccione fecha de fin">
          <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
          <mat-datepicker #pickerEnd></mat-datepicker>
          <mat-error *ngIf="rangeForm.get('fechaFin')?.hasError('required')">
            La fecha de fin es obligatoria
          </mat-error>
        </mat-form-field>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancelar</button>
      <button 
        mat-raised-button 
        color="primary" 
        (click)="onExport()" 
        [disabled]="rangeForm.invalid || isInvalidDateRange()">
        Exportar
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .date-range-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .w-full {
      width: 100%;
    }
  `]
})
export class ExportByDateRangeDialogComponent {
  rangeForm: FormGroup;

  constructor(
    private dialogRef: MatDialogRef<ExportByDateRangeDialogComponent>,
    private fb: FormBuilder
  ) {
    this.rangeForm = this.fb.group({
      fechaInicio: [null, Validators.required],
      fechaFin: [null, Validators.required]
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onExport(): void {
    if (this.rangeForm.valid && !this.isInvalidDateRange()) {
      const { fechaInicio, fechaFin } = this.rangeForm.value;
      this.dialogRef.close({
        fechaInicio,
        fechaFin
      });
    }
  }

  isInvalidDateRange(): boolean {
    const fechaInicio = this.rangeForm.get('fechaInicio')?.value;
    const fechaFin = this.rangeForm.get('fechaFin')?.value;
    
    if (fechaInicio && fechaFin) {
      // Verificar que la fecha de inicio no sea posterior a la fecha de fin
      return new Date(fechaInicio) > new Date(fechaFin);
    }
    
    return false;
  }
}
