<div class="p-6 max-w-8xl mx-auto">
    <div class="bg-white rounded-xl shadow p-6 space-y-4">
        <!-- Título -->
        <h1 class="text-2xl font-semibold text-gray-800">Gestión de Coaching</h1>

        <!-- Filtros y acciones -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <!-- Botón para agregar -->
            <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 flex items-center gap-1"
                (click)="openForm()">
                <mat-icon>add</mat-icon>
                Nueva frase
            </button>

            <!-- Filtro por fechas -->
            <div class="flex flex-wrap items-center gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Desde</label>
                    <input type="date" [(ngModel)]="fechaDesde" class="border px-3 py-2 rounded text-sm" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Hasta</label>
                    <input type="date" [(ngModel)]="fechaHasta" class="border px-3 py-2 rounded text-sm" />
                </div>
                <div class="self-end">
                    <button (click)="filtrarPorFechas()"
                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        Filtrar
                    </button>
                </div>
            </div>

            <!-- Filtro por nombre -->
            <input type="text" [(ngModel)]="searchTerm" placeholder="Buscar por nombre..."
                class="border px-3 py-1 rounded-md text-sm" />
        </div>

        <!-- Tabla -->
        <div class="overflow-auto rounded-lg border mt-4">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100 text-gray-700 text-sm">
                    <tr>
                        <th class="text-left p-3">ID</th>
                        <th class="text-left p-3">Nombre</th>
                        <th class="text-left p-3">Fecha de Publicación</th>
                        <th class="text-left p-3">Imagen</th>
                        <th class="text-left p-3">Estado</th>
                        <th class="text-left p-3">Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="filteredData.length === 0" class="text-sm text-gray-500 text-center">
                        <td colspan="5" class="p-4">Sin datos disponibles</td>
                    </tr>
                    <tr *ngFor="let item of filteredData" class="text-sm text-gray-800 border-t">
                        <td class="p-3">{{ item.id }}</td>
                        <td class="p-3">{{ item.nombre }}</td>
                        <td class="p-3">{{ item.fecha }}</td>
                        <td class="text-center">
                            <img *ngIf="item.imagen" [src]="item.imagen" alt="Imagen"
                                class="w-16 h-16 object-cover border rounded cursor-pointer hover:scale-105 transition-transform"
                                (click)="abrirModalImagen(item.imagen)" />
                        </td>

                        <td class="p-3">{{ item.estado === 'A' ? 'ACTIVO' : 'INACTIVO' }}</td>
                        <td class="p-3 flex gap-2 items-center">
                            <button (click)="editarFrase(item)" mat-icon-button color="primary" matTooltip="Editar">
                                <mat-icon>edit</mat-icon>
                            </button>
                            <button (click)="eliminarFrase(item)" mat-icon-button color="warn" matTooltip="Eliminar">
                                <mat-icon>delete</mat-icon>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Paginación -->
        <div class="flex justify-between items-center text-sm mt-2">
            <div>
                Página {{ currentPage }} de {{ totalPages }}
            </div>
            <div class="space-x-2">
                <button mat-stroked-button color="primary" [disabled]="currentPage === 1" (click)="changePage(-1)">
                    Anterior
                </button>
                <button mat-stroked-button color="primary" [disabled]="currentPage === totalPages"
                    (click)="changePage(1)">
                    Siguiente
                </button>
            </div>
        </div>
    </div>

    <!-- Modal flotante para ver la imagen -->
    <div *ngIf="imagenSeleccionada"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm">
        <div class="bg-white rounded-2xl shadow-2xl max-w-3xl w-full relative p-6 flex flex-col items-center">
            <!-- Botón X en la esquina superior derecha -->
            <button (click)="cerrarModalImagen()"
                class="absolute top-2 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">
                &times;
            </button>

            <!-- Imagen centrada -->
            <img [src]="imagenSeleccionada" alt="Vista previa"
                class="w-full max-h-[70vh] object-contain rounded mb-6" />

            <!-- Botón Cerrar -->
            <button (click)="cerrarModalImagen()"
                class="px-6 py-2 bg-blue-600 hover:bg-blue-800 text-white font-semibold rounded-md shadow">
                Cerrar
            </button>
        </div>
    </div>



    <!-- Formulario flotante -->
    <div *ngIf="showForm" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md rounded-lg shadow-lg overflow-hidden">
            <div class="bg-red-600 text-white px-6 py-4">
                <h2 class="text-lg font-semibold"> {{ esEdicion ? 'Editar Frase' : 'Agregar Frase' }}</h2>
            </div>

            <div class="p-6 space-y-4">
                <div *ngIf="esEdicion">
                    <label class="block text-sm font-medium text-gray-700 mb-1">ID</label>
                    <input type="text" [(ngModel)]="form.id" placeholder="ID" class="w-full border px-3 py-2 rounded"
                        disabled />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Título</label>
                    <input type="text" [(ngModel)]="form.nombre" placeholder="Escribe un título"
                        class="w-full border px-3 py-2 rounded" />
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Fecha de publicación</label>
                    <input type="date" [(ngModel)]="form.fecha" class="w-full border px-3 py-2 rounded" />
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Imagen</label>
                    <div *ngIf="!previewUrl"
                        class="p-6 border border-dashed border-gray-400 rounded-lg text-center bg-white cursor-pointer hover:bg-blue-50"
                        (click)="openFileSelector()" (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)"
                        (drop)="onDrop($event)" [class.bg-blue-100]="isDragging">
                        <i class="fa-solid fa-cloud-arrow-up text-3xl text-gray-400 mb-2"></i>
                        <p class="text-sm text-gray-600 mb-3">Haz clic o arrastra el archivo aquí</p>
                        <button type="button"
                            class="text-sm px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            Seleccionar archivo
                        </button>
                    </div>

                    <div *ngIf="previewUrl" class="text-center space-y-2 mt-3">
                        <img [src]="previewUrl" alt="preview"
                            class="mx-auto max-w-[128px] max-h-[128px] object-contain rounded border" />
                        <div class="text-sm text-gray-700">{{ nombreArchivo }}</div>
                        <button type="button" (click)="removeFile()"
                            class="text-red-600 text-xs hover:underline">Quitar</button>
                    </div>

                    <input type="file" class="hidden" #fileInput (change)="onFileSelected($event)" accept="image/*" />
                </div>
            </div>

            <div class="flex justify-end gap-2 px-6 py-4 border-t">
                <button mat-button class="text-red-600 hover:bg-red-100" (click)="closeForm()">Cancelar</button>
                <button mat-raised-button class="bg-green-600 text-white hover:bg-green-700" (click)="esEdicion ? actualizar() : guardar()">{{ esEdicion ? 'Actualizar' : 'Guardar' }}</button>
            </div>
        </div>
    </div>
</div>