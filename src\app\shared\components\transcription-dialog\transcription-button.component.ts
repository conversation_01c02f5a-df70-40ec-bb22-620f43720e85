import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './transcription-dialog.component';

@Component({
  selector: 'app-transcription-button',
  template: `
    <button 
      type="button"
      mat-raised-button 
      color="primary" 
      (click)="openTranscriptionModal()"
      class="flex items-center gap-2">
      <mat-icon>record_voice_over</mat-icon>
      Transcribir Audio
    </button>
  `,
  styles: [`
    button {
      min-width: 160px;
    }
  `]
})
export class TranscriptionButtonComponent {

  constructor(private dialog: MatDialog) {}

  /**
   * Abre el modal de transcripción directamente para subir archivos
   */
  openTranscriptionModal(): void {
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true,
      // Opcional: agregar datos de cliente si están disponibles
      cliente: {
        nombres: 'Usuario',
        apellidos: 'Ejemplo'
      },
      numeroMovil: '123456789'
    };

    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '90vw',
      maxWidth: '900px',
      height: '90vh',
      maxHeight: '800px',
      disableClose: false,
      data: dialogData,
      panelClass: 'transcription-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Transcripción completada exitosamente:', result);
        // Aquí puedes manejar el resultado de la transcripción
        // Por ejemplo, mostrar un mensaje de éxito, actualizar datos, etc.
      } else if (result === false) {
        console.log('❌ Transcripción cancelada por el usuario');
      }
    });
  }
}
