import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import * as fromList from '../../store/save';
import { select, Store } from '@ngrx/store';
import { User } from '@app/models/backend/user/index';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { MatDialog } from '@angular/material/dialog';
import { SedeService } from '@app/services/sede.service';
import { Sede } from '@app/models/backend/sede/sede.model';
import { SedeUserService } from '@app/services/sede-user.service';

@Component({
  selector: 'app-anuncio-form-dialog',
  templateUrl: './anuncio-form-dialog.component.html',
  styleUrls: ['./anuncio-form-dialog.component.scss']
})
export class AnuncioFormDialogComponent implements OnInit, OnDestroy {
  form: FormGroup;
  loading$!: Observable<boolean | null>;
  photoLoaded: string = '';
  user$!: Observable<User | null>;
  private user: User | null = null;
  private destroy$ = new Subject<void>();
  isEditing: boolean = false;

  // Categorías disponibles
  categorias: string[] = ['INTERNO', 'EXTERNO'];

  // Estados disponibles
  estados: string[] = ['ACTIVO', 'INACTIVO'];

  // Sedes disponibles
  sedes: Sede[] = [];
  userSedeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private store: Store<fromRoot.State>,
    public dialogRef: MatDialogRef<AnuncioFormDialogComponent>,
    private dialog: MatDialog,
    private sedeService: SedeService,
    private sedeUserService: SedeUserService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // Obtener fecha actual y sumar 30 días para la fecha de fin por defecto
    const hoy = new Date();
    const fechaFin = new Date();
    fechaFin.setDate(hoy.getDate() + 30);

    // Obtener el ID de la sede del usuario
    this.userSedeId = this.sedeUserService.getSedeIdSync();

    this.form = this.fb.group({
      titulo: ['', [Validators.required]],
      descripcion: ['', [Validators.required]],
      categoria: ['INTERNO', [Validators.required]],
      imagenUrl: [''],
      fechaInicio: [hoy],
      fechaFin: [fechaFin],
      orden: [0],
      estado: ['ACTIVO'],
      sedeId: [this.userSedeId]
    });

    // Si estamos editando, inicializar con los datos del anuncio
    if (data && data.anuncio) {
      this.isEditing = true;

      // Convertir fechas de string a Date si existen
      const fechaInicioAnuncio = data.anuncio.fechaInicio ? new Date(data.anuncio.fechaInicio) : hoy;
      const fechaFinAnuncio = data.anuncio.fechaFin ? new Date(data.anuncio.fechaFin) : fechaFin;

      this.form.patchValue({
        titulo: data.anuncio.titulo,
        descripcion: data.anuncio.descripcion,
        categoria: data.anuncio.categoria,
        imagenUrl: data.anuncio.imagenUrl,
        fechaInicio: fechaInicioAnuncio,
        fechaFin: fechaFinAnuncio,
        orden: data.anuncio.orden || 0,
        estado: data.anuncio.estado || 'ACTIVO',
        sedeId: data.anuncio.sedeId || this.userSedeId
      });
      this.photoLoaded = data.anuncio.imagenUrl;
    }
  }

  ngOnInit(): void {
    this.loading$ = this.store.pipe(select(fromList.getLoading));
    this.user$ = this.store.pipe(select(fromUser.getUser));

    this.user$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => this.user = user);

    // Cargar las sedes disponibles
    this.loadSedes();
  }

  /**
   * Carga las sedes disponibles desde el servicio
   */
  loadSedes(): void {
    this.sedeService.getSedesActivas().subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data) {
          this.sedes = response.data;
        }
      },
      error: (error) => {
        console.error('Error al cargar sedes:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.form.valid) {
      if (!this.user?.id) {
        return;
      }

      // Formatear fechas para enviar al backend
      const fechaInicio = this.form.get('fechaInicio')?.value;
      const fechaFin = this.form.get('fechaFin')?.value;

      // Obtener el valor de sedeId del formulario
      const sedeIdValue = this.form.get('sedeId')?.value;

      const anuncioData = {
        titulo: this.form.get('titulo')?.value,
        descripcion: this.form.get('descripcion')?.value,
        categoria: this.form.get('categoria')?.value,
        imagenUrl: this.photoLoaded,
        fechaInicio: fechaInicio ? fechaInicio.toISOString() : null,
        fechaFin: fechaFin ? fechaFin.toISOString() : null,
        orden: this.form.get('orden')?.value,
        estado: this.form.get('estado')?.value,
        usuarioId: this.user.id,
        sedeId: sedeIdValue // Puede ser null, un número, o undefined
      };

      // Imprimir para depuración
      console.log('Enviando anuncio con sedeId:', sedeIdValue);



      // Suscribirse al estado de carga para cerrar el diálogo solo después de completar la operación
      const loadingSub = this.loading$.subscribe(loading => {
        if (loading === false) { // Explícitamente verificar false para evitar problemas con null
          console.log('AnuncioFormDialogComponent: Operación completada, cerrando diálogo');

          // Verificar si estamos creando un nuevo anuncio
          if (!this.isEditing) {
            console.log('AnuncioFormDialogComponent: Nuevo anuncio creado, esperando notificación WebSocket');

            // Esperar un poco más para asegurarnos de que el backend haya procesado la creación
            // y enviado la notificación WebSocket
            setTimeout(() => {
              this.dialogRef.close(true);
              loadingSub.unsubscribe();
            }, 2000); // Aumentar retraso para dar tiempo a que se procese la notificación WebSocket
          } else {
            // Para actualizaciones, cerrar más rápido
            setTimeout(() => {
              this.dialogRef.close(true);
              loadingSub.unsubscribe();
            }, 1000);
          }
        }
      });

      if (this.isEditing) {
        console.log('Actualizando anuncio con ID:', this.data.anuncio.id);
        console.log('Datos enviados:', anuncioData);
        // Actualizar anuncio existente
        this.store.dispatch(new fromList.Update(this.data.anuncio.id, anuncioData));
      } else {
        console.log('Creando nuevo anuncio');
        console.log('Datos enviados:', anuncioData);
        // Crear nuevo anuncio
        this.store.dispatch(new fromList.Create(anuncioData));
      }
    }
  }

  openFilesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe(url => {
      if (url) {
        this.photoLoaded = url;
        this.form.patchValue({
          imagenUrl: url
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
