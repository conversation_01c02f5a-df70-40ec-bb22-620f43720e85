/* Estilos para los modales en tema oscuro */
body.dark-theme {
  /* Aplicar estilos de tema oscuro a los modales */
  .tw-modal-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .tw-modal-content {
    background-color: #1a2035;
    color: white;
  }

  .tw-modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .tw-modal-title {
      color: white;

      .tw-icon {
        color: #60a5fa;
      }
    }

    .tw-close-button {
      color: #9ca3af;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }
  }

  .tw-coordinator-info {
    background-color: #111827;
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .tw-badge {
      background-color: rgba(255, 255, 255, 0.1);
      color: #d1d5db;

      &.highlight {
        background-color: rgba(59, 130, 246, 0.2);
        color: #93c5fd;
      }

      .tw-icon {
        color: #d1d5db;
      }

      &.highlight .tw-icon {
        color: #93c5fd;
      }
    }
  }

  .tw-search-input {
    background-color: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);
    color: white;

    &:focus {
      border-color: #60a5fa;
      box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    }
  }

  .tw-asesor-card {
    background-color: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }

    .tw-card-header {
      background-color: #111827;
      border-bottom-color: rgba(255, 255, 255, 0.1);

      .tw-avatar {
        background-color: rgba(59, 130, 246, 0.2);
        color: #93c5fd;
      }

      .tw-name {
        color: white;
      }

      .tw-dni {
        color: #9ca3af;
      }
    }

    .tw-card-body {
      .tw-icon {
        color: #9ca3af;
      }

      .tw-info-text {
        color: #e5e7eb;
      }
    }

    .tw-card-footer {
      background-color: #111827;
      border-top-color: rgba(255, 255, 255, 0.1);
    }
  }

  .tw-modal-footer {
    background-color: #111827;
    border-top-color: rgba(255, 255, 255, 0.1);

    .tw-cancel-button {
      background-color: #1f2937;
      border-color: rgba(255, 255, 255, 0.1);
      color: #9ca3af;

      &:hover {
        background-color: #374151;
        color: white;
      }
    }

    .tw-confirm-button {
      background-color: #2563eb;

      &:hover {
        background-color: #1d4ed8;
      }
    }
  }

  .tw-paginator-range {
    color: #9ca3af;
  }

  .tw-page-button {
    background-color: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);
    color: #9ca3af;

    &:hover:not(:disabled) {
      background-color: #374151;
      color: white;
    }
  }

  .tw-page-size {
    background-color: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);
    color: #9ca3af;

    &:focus {
      border-color: #60a5fa;
      box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    }
  }

  .tw-no-results {
    background-color: #111827;
    border-color: rgba(255, 255, 255, 0.1);

    .tw-icon {
      color: #6b7280;
    }

    .tw-message {
      color: #9ca3af;
    }

    .tw-reset-button {
      background-color: #1f2937;
      border-color: rgba(255, 255, 255, 0.1);
      color: #9ca3af;

      &:hover {
        background-color: #374151;
        color: white;
      }
    }
  }

  .tw-info-footer {
    color: #9ca3af;
  }

  .tw-spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: #60a5fa;
  }

  .tw-loading-text {
    color: #9ca3af;
  }
}
