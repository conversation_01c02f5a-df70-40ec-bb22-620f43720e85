import { createReducer, on } from '@ngrx/store';
import * as FaqActions from './faq.actions';
import { Faq } from '@app/models/backend/faq/faq.model';

export interface FaqState {
  faqs: Faq[];
  loading: boolean;
  error?: any;
}
export const initialState: FaqState = { faqs: [], loading: false };

export const faqReducer = createReducer(
  initialState,
  on(FaqActions.loadFaqs,    (s) => ({ ...s, loading: true })),
  on(FaqActions.loadFaqsSuccess,  (s, { faqs }) => ({ ...s, faqs, loading: false })),
  on(FaqActions.loadFaqsFailure,  (s, { error }) => ({ ...s, loading: false, error })),

  on(FaqActions.createFaqSuccess, (s, { faq }) => ({ ...s, faqs: [...s.faqs, faq] })),
  on(FaqActions.updateFaqSuccess, (s, { faq }) => ({
    ...s,
    faqs: s.faqs.map((f) => (f.id === faq.id ? faq : f)),
  })),
  on(FaqActions.deleteFaqSuccess, (s, { id }) => ({
    ...s,
    faqs: s.faqs.filter((f) => f.id !== id),
  })),

  // Lista recibida por WebSocket reemplaza todo
  on(FaqActions.faqsUpdatedWs, (s, { faqs }) => ({ ...s, faqs }))
);
