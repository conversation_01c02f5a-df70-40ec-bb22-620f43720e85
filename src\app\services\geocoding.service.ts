import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from '@src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GeocodingService {
  private mapboxUrl = 'https://api.mapbox.com/search/geocode/v6/forward';
  private mapboxToken = environment.maps.mapboxToken;

  constructor(private http: HttpClient) { }

  /**
   * Limpia el texto eliminando paréntesis y su contenido, y otros caracteres problemáticos
   * @param texto Texto a limpiar
   * @returns Texto limpio sin paréntesis y otros caracteres problemáticos
   */
  private limpiarTexto(texto: string | undefined): string {
    if (!texto) return '';

    // Eliminar paréntesis y su contenido
    let textoLimpio = texto.replace(/\s*\([^)]*\)/g, '');

    // Eliminar caracteres especiales y normalizar espacios
    textoLimpio = textoLimpio
      .replace(/[^\w\s\-áéíóúÁÉÍÓÚüÜñÑ]/g, ' ') // Mantener letras, números, espacios, guiones y acentos
      .replace(/\s+/g, ' ')                     // Normalizar espacios múltiples
      .trim();

    return textoLimpio;
  }

  /**
   * Geocodifica una dirección para obtener sus coordenadas
   * @param direccion Objeto con los datos de la dirección
   * @returns Observable con las coordenadas [latitud, longitud]
   */
  geocodificarDireccion(direccion: {
    provincia?: string,
    municipio?: string,
    via?: string,
    numero?: string,
    codigoPostal?: string,
    bloque?: string,
    escalera?: string,
    planta?: string,
    puerta?: string
  }): Observable<[number, number] | null> {
    // Usar el servicio de Mapbox para la geocodificación

    // Convertir el objeto de dirección al formato esperado por geocodificarDireccionMapbox
    const direccionMapbox = {
      provincia: direccion.provincia,
      municipio: direccion.municipio,
      via: direccion.via,
      numero: direccion.numero,
      codigoPostal: direccion.codigoPostal,
      bloque: direccion.bloque,
      escalera: direccion.escalera,
      planta: direccion.planta,
      puerta: direccion.puerta
    };

    // Llamar al método de geocodificación de Mapbox
    return this.geocodificarDireccionMapbox(direccionMapbox);
  }



  /**
   * Geocodifica una dirección utilizando la API de Mapbox
   * @param direccion Objeto con los datos de la dirección
   * @returns Observable con las coordenadas [latitud, longitud]
   */
  geocodificarDireccionMapbox(direccion: {
    provincia?: string,
    municipio?: string,
    via?: string,
    numero?: string,
    bloque?: string,
    escalera?: string,
    planta?: string,
    puerta?: string,
    codigoPostal?: string
  }): Observable<[number, number] | null> {


    // Extraer el número de la vía si está en formato "N°X" o similar
    let numeroExtraido = direccion.numero;

    // Verificar si el número está en la dirección completa
    if (direccion.via && direccion.via.includes('N°')) {
      const partes = direccion.via.split('N°');
      if (partes.length > 1) {
        // Extraer el número después de "N°"
        let numPart = partes[1].trim();
        if (numPart.includes(' ') || numPart.includes(',')) {
          numPart = numPart.split(/[ ,]/)[0].trim();
        }
        // Limpiar cualquier carácter no numérico
        numeroExtraido = numPart.replace(/[^\d]/g, '');

        // Actualizar la vía para quitar el número
        direccion.via = partes[0].trim();
        console.log('Número extraído de la vía:', numeroExtraido);
      }
    }
    // Si el número está en formato "N°X"
    else if (numeroExtraido && numeroExtraido.includes('N°')) {
      numeroExtraido = numeroExtraido.split('N°')[1].trim();
      // Si hay paréntesis, extraer solo el número antes del paréntesis
      if (numeroExtraido.includes('(')) {
        numeroExtraido = numeroExtraido.split('(')[0].trim();
      }
      // Limpiar cualquier carácter no numérico
      numeroExtraido = numeroExtraido.replace(/[^\d]/g, '');
      console.log('Número extraído del campo número:', numeroExtraido);
    }
    // Si el número es simplemente un número
    else if (numeroExtraido) {
      // Limpiar cualquier carácter no numérico
      numeroExtraido = numeroExtraido.replace(/[^\d]/g, '');
      console.log('Número ya en formato numérico:', numeroExtraido);
    }

    // Si no se ha extraído un número pero hay un número en la dirección completa
    if (!numeroExtraido && direccion.via) {
      // Buscar patrones como "Nº4", "N.4", "N4", etc.
      const patronesNumero = [
        /[Nn][°º\.]\s*(\d+)/,  // N°4, Nº4, N.4
        /[Nn](?:umero|úmero)?\s*(\d+)/i,  // Numero 4, Número 4, N4
        /,\s*(\d+)/,  // , 4
        /\s(\d+)(?:\s|,|$)/  // Espacio seguido de número y espacio, coma o fin de cadena
      ];

      for (const patron of patronesNumero) {
        const match = direccion.via.match(patron);
        if (match && match[1]) {
          numeroExtraido = match[1];
          console.log('Número extraído con patrón:', patron, numeroExtraido);
          break;
        }
      }
    }

    console.log('Número extraído final:', numeroExtraido);

    // Limpiar los textos de paréntesis y otros caracteres problemáticos
    let viaLimpia = this.limpiarTexto(direccion.via);
    const municipioLimpio = this.limpiarTexto(direccion.municipio);
    const provinciaLimpia = this.limpiarTexto(direccion.provincia);

    // Detectar si la vía es una avenida, calle, etc.
    let esAvenidaOCalle = false;
    if (viaLimpia) {
      // Verificar si la vía contiene "AVENIDA" o "CALLE" entre paréntesis
      if (direccion.via && (
          direccion.via.includes('(AVENIDA)') ||
          direccion.via.includes('(AVDA)') ||
          direccion.via.includes('(AV.)') ||
          direccion.via.includes('(CALLE)')
      )) {
        esAvenidaOCalle = true;

        // Extraer el tipo de vía
        let tipoVia = '';
        if (direccion.via.includes('(AVENIDA)')) tipoVia = 'AVENIDA';
        else if (direccion.via.includes('(AVDA)')) tipoVia = 'AVDA';
        else if (direccion.via.includes('(AV.)')) tipoVia = 'AV.';
        else if (direccion.via.includes('(CALLE)')) tipoVia = 'CALLE';

        // Reconstruir la vía con el tipo al principio
        viaLimpia = `${tipoVia} ${viaLimpia}`;
      }
      // Verificar si la vía ya contiene las palabras "AVENIDA", "CALLE", etc.
      else if (
        viaLimpia.toLowerCase().includes('avenida') ||
        viaLimpia.toLowerCase().includes('avda') ||
        viaLimpia.toLowerCase().includes('av.') ||
        viaLimpia.toLowerCase().includes('calle')
      ) {
        esAvenidaOCalle = true;
      }
    }



    // Intentar primero con la API de geocodificación estructurada de Mapbox
    if (viaLimpia && municipioLimpio && provinciaLimpia) {
      // Construir los parámetros para la API estructurada
      const structuredParams: any = {
        country: 'es',
        language: 'es',
        limit: '1',
        access_token: this.mapboxToken
      };

      // Añadir los parámetros de dirección estructurada
      if (viaLimpia) {
        // Si tenemos número, incluirlo en address_line1
        if (numeroExtraido) {
          // Para calles y avenidas, el formato puede variar
          if (esAvenidaOCalle) {
            structuredParams.address_line1 = `${viaLimpia} ${numeroExtraido}`;
            console.log(`Usando formato de calle/avenida: ${viaLimpia} ${numeroExtraido}`);
          } else {
            // Para otras vías, intentar con el número primero
            structuredParams.address_line1 = `${numeroExtraido} ${viaLimpia}`;
            console.log(`Usando formato estándar: ${numeroExtraido} ${viaLimpia}`);
          }

          // Añadir también el número como parámetro separado para mayor precisión
          structuredParams.housenumber = numeroExtraido;
        } else {
          structuredParams.street = viaLimpia;
          console.log(`Sin número, usando solo vía: ${viaLimpia}`);
        }
      }

      if (municipioLimpio) {
        structuredParams.place = municipioLimpio;
      }

      if (provinciaLimpia) {
        structuredParams.region = provinciaLimpia;
      }

      if (direccion.codigoPostal) {
        structuredParams.postcode = direccion.codigoPostal;
      }

      console.log('Parámetros estructurados para Mapbox:', structuredParams);

      return this.http.get<any>(this.mapboxUrl, { params: structuredParams }).pipe(
        switchMap(response => {
          if (response && response.features && response.features.length > 0) {
            // Mapbox devuelve las coordenadas en formato [longitud, latitud]
            const coordinates = response.features[0].geometry.coordinates;
            const lon = coordinates[0];
            const lat = coordinates[1];
            return of([lat, lon] as [number, number]);
          }

          // Si no hay resultados con la búsqueda estructurada, intentar con la búsqueda de texto libre
          return this.geocodificarDireccionMapboxTextoLibre(direccion);
        }),
        catchError(() => {
          // En caso de error, intentar con la búsqueda de texto libre
          return this.geocodificarDireccionMapboxTextoLibre(direccion);
        })
      );
    } else {
      // Si no tenemos suficiente información para una búsqueda estructurada, usar texto libre
      return this.geocodificarDireccionMapboxTextoLibre(direccion);
    }
  }

  /**
   * Geocodifica una dirección utilizando la API de Mapbox con texto libre
   * @param direccion Objeto con los datos de la dirección
   * @returns Observable con las coordenadas [latitud, longitud]
   */
  private geocodificarDireccionMapboxTextoLibre(direccion: {
    provincia?: string,
    municipio?: string,
    via?: string,
    numero?: string,
    bloque?: string,
    escalera?: string,
    planta?: string,
    puerta?: string,
    codigoPostal?: string
  }): Observable<[number, number] | null> {
    // Extraer el número de la vía si está en formato "N°X" o similar
    let numeroExtraido = direccion.numero;

    // Verificar si el número está en la vía (a veces la vía incluye "N°X")
    if (direccion.via && direccion.via.includes('N°')) {
      const partes = direccion.via.split('N°');
      if (partes.length > 1) {
        // Extraer el número después de "N°"
        let numPart = partes[1].trim();
        if (numPart.includes(' ') || numPart.includes(',')) {
          numPart = numPart.split(/[ ,]/)[0].trim();
        }
        // Limpiar cualquier carácter no numérico
        numeroExtraido = numPart.replace(/[^\d]/g, '');

        // Actualizar la vía para quitar el número
        direccion.via = partes[0].trim();
      }
    }
    // Si el número está en formato "N°X"
    else if (numeroExtraido && numeroExtraido.includes('N°')) {
      numeroExtraido = numeroExtraido.split('N°')[1].trim();
      // Si hay paréntesis, extraer solo el número antes del paréntesis
      if (numeroExtraido.includes('(')) {
        numeroExtraido = numeroExtraido.split('(')[0].trim();
      }
      // Limpiar cualquier carácter no numérico
      numeroExtraido = numeroExtraido.replace(/[^\d]/g, '');
    }

    console.log('Número extraído (texto libre):', numeroExtraido);

    // Construir la consulta de búsqueda
    let query = '';

    // Limpiar los textos de paréntesis y otros caracteres problemáticos
    let viaLimpia = this.limpiarTexto(direccion.via);
    const municipioLimpio = this.limpiarTexto(direccion.municipio);
    const provinciaLimpia = this.limpiarTexto(direccion.provincia);

    // Detectar si la vía es una avenida, calle, etc.
    let esAvenidaOCalle = false;
    if (viaLimpia) {
      // Verificar si la vía contiene "AVENIDA" o "CALLE" entre paréntesis
      if (direccion.via && (
          direccion.via.includes('(AVENIDA)') ||
          direccion.via.includes('(AVDA)') ||
          direccion.via.includes('(AV.)') ||
          direccion.via.includes('(CALLE)')
      )) {
        esAvenidaOCalle = true;

        // Extraer el tipo de vía
        let tipoVia = '';
        if (direccion.via.includes('(AVENIDA)')) tipoVia = 'AVENIDA';
        else if (direccion.via.includes('(AVDA)')) tipoVia = 'AVDA';
        else if (direccion.via.includes('(AV.)')) tipoVia = 'AV.';
        else if (direccion.via.includes('(CALLE)')) tipoVia = 'CALLE';

        // Reconstruir la vía con el tipo al principio
        viaLimpia = `${tipoVia} ${viaLimpia}`;
      }
      // Verificar si la vía ya contiene las palabras "AVENIDA", "CALLE", etc.
      else if (
        viaLimpia.toLowerCase().includes('avenida') ||
        viaLimpia.toLowerCase().includes('avda') ||
        viaLimpia.toLowerCase().includes('av.') ||
        viaLimpia.toLowerCase().includes('calle')
      ) {
        esAvenidaOCalle = true;
      }
    }

    // Construir la consulta para Mapbox
    if (viaLimpia) {
      // Incluir el número en la vía si existe
      if (numeroExtraido) {
        // Para calles y avenidas, poner el número después del nombre
        if (esAvenidaOCalle) {
          query += `${viaLimpia} ${numeroExtraido}, `;
          console.log(`Usando formato de calle/avenida (texto libre): ${viaLimpia} ${numeroExtraido}`);
        } else {
          // Para otras vías, intentar con el número primero
          query += `${numeroExtraido} ${viaLimpia}, `;
          console.log(`Usando formato estándar (texto libre): ${numeroExtraido} ${viaLimpia}`);
        }
      } else {
        query += `${viaLimpia}, `;
        console.log(`Sin número, usando solo vía (texto libre): ${viaLimpia}`);
      }
    }

    // Intentar también con una consulta alternativa que incluya explícitamente "número"
    if (numeroExtraido && viaLimpia) {
      // Añadir un parámetro adicional para intentar con una consulta alternativa
      console.log(`Añadiendo consulta alternativa con número explícito: ${viaLimpia} número ${numeroExtraido}`);
    }

    if (municipioLimpio) {
      query += `${municipioLimpio}, `;
    }

    if (provinciaLimpia) {
      query += `${provinciaLimpia}, `;
    }

    if (direccion.codigoPostal) {
      query += `${direccion.codigoPostal}, `;
    }

    query += 'España';

    // Si no hay suficiente información, devolver null
    if (!provinciaLimpia && !municipioLimpio) {
      return of(null);
    }



    // Parámetros para la API de Mapbox
    const params = {
      q: query,
      limit: '1',
      country: 'es',
      language: 'es',
      access_token: this.mapboxToken
    };

    return this.http.get<any>(this.mapboxUrl, { params }).pipe(
      map(response => {
        if (response && response.features && response.features.length > 0) {
          // Mapbox devuelve las coordenadas en formato [longitud, latitud]
          const coordinates = response.features[0].geometry.coordinates;
          const lon = coordinates[0];
          const lat = coordinates[1];
          return [lat, lon] as [number, number];
        }
        return null;
      }),
      catchError(() => {
        return of(null);
      })
    );
  }

  /**
   * Método simplificado de geocodificación con Mapbox que solo usa municipio y provincia
   * @param municipio Nombre del municipio
   * @param provincia Nombre de la provincia
   * @returns Observable con las coordenadas [latitud, longitud]
   */
  geocodificarSimpleMapbox(municipio: string, provincia: string): Observable<[number, number] | null> {
    return this.geocodificarDireccionMapbox({
      municipio: municipio,
      provincia: provincia
    });
  }

  /**
   * Método para geocodificar solo con provincia usando Mapbox
   * @param provincia Nombre de la provincia
   * @returns Observable con las coordenadas [latitud, longitud]
   */
  geocodificarProvinciaMapbox(provincia: string): Observable<[number, number] | null> {
    return this.geocodificarDireccionMapbox({
      provincia: provincia
    });
  }
}
