<div class="calendar-modal">
  <div class="modal-header">
    <h5 class="modal-title">{{ isNewData ? 'Crear agenda' : 'Editar agenda' }}</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="onCancel()"></button>
  </div>

  <form [formGroup]="calendarForm" (ngSubmit)="onSubmit()" autocomplete="off">
    <div class="modal-body">
      <input type="hidden" formControlName="id" />

      <!-- Título -->
      <div class="form-group">
        <label for="titulo" class="form-label">Título *</label>
        <input
          type="text"
          id="titulo"
          class="form-control"
          placeholder="Ingrese el título..."
          formControlName="titulo"
          [ngClass]="{ 'is-invalid': f['titulo'].errors && (f['titulo'].dirty || f['titulo'].touched || submitted) }"
        />
      </div>

      <!-- Descripción -->
      <div class="form-group">
        <label for="descripcion" class="form-label">Descripción</label>
        <textarea
          id="descripcion"
          class="form-control"
          rows="3"
          placeholder="Ingrese la descripción..."
          formControlName="descripcion"
        ></textarea>
      </div>

      <!-- Color -->
      <div class="form-group">
        <label for="color" class="form-label">Color</label>
        <select class="form-select" id="color" formControlName="color">
          <option value="">Seleccione el color...</option>
          <option *ngFor="let category of categories" [value]="category.value" [ngClass]="category.value">{{category.name}}</option>
        </select>
      </div>

      <!-- Vista previa del color seleccionado -->
      <div class="form-group mt-2" *ngIf="calendarForm.get('color')?.value">
        <label class="form-label">Vista previa del color</label>
        <div class="color-preview" [ngClass]="calendarForm.get('color')?.value">
          <span>{{getColorName(calendarForm.get('color')?.value)}}</span>
        </div>
      </div>

      <!-- Fecha y Hora Inicio -->
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="fecha_inicio" class="form-label">Fecha inicio *</label>
            <div class="input-group date-picker-group">
              <input
                type="date"
                id="fecha_inicio"
                class="form-control"
                formControlName="fecha_inicio"
              />
              <span class="input-group-text"><i class="bi bi-calendar"></i></span>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="hora_inicio" class="form-label">Hora inicio *</label>
            <div class="input-group time-picker-group">
              <input
                type="time"
                id="hora_inicio"
                class="form-control"
                formControlName="hora_inicio"
              />
              <span class="input-group-text"><i class="bi bi-clock"></i></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Fecha y Hora Final -->
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="fecha_final" class="form-label">Fecha final *</label>
            <div class="input-group date-picker-group">
              <input
                type="date"
                id="fecha_final"
                class="form-control"
                formControlName="fecha_final"
              />
              <span class="input-group-text"><i class="bi bi-calendar"></i></span>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="hora_final" class="form-label">Hora final *</label>
            <div class="input-group time-picker-group">
              <input
                type="time"
                id="hora_final"
                class="form-control"
                formControlName="hora_final"
              />
              <span class="input-group-text"><i class="bi bi-clock"></i></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-light" (click)="onCancel()">Cancelar</button>
      <button type="submit" class="btn btn-success" *ngIf="isNewData">Registrar</button>
      <button type="submit" class="btn btn-primary" *ngIf="!isNewData">Actualizar</button>
      <button type="button" class="btn btn-danger" *ngIf="!isNewData" (click)="onDelete()">Eliminar</button>
    </div>
  </form>
</div>
