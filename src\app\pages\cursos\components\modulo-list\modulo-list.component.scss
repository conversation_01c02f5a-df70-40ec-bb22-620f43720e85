.modulo-list-container {
  padding: 20px;
  
  &.dark-theme {
    background-color: #1e1e1e;
    color: #ffffff;
  }
}

.modulo-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #3f51b5;
    
    .dark-theme & {
      color: #7986cb;
    }
  }
}

.modulo-list-content {
  position: relative;
  min-height: 100px;
}

.modulo-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modulo-item {
  position: relative;
  cursor: move;
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  .dark-theme & {
    background-color: #2d2d2d;
    color: #ffffff;
  }
}

.modulo-drag-handle {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: move;
  color: #757575;
  
  .dark-theme & {
    color: #bdbdbd;
  }
}

.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.modulo-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin: 8px;
  
  &.active {
    background-color: #4caf50;
    color: white;
  }
  
  &.inactive {
    background-color: #f44336;
    color: white;
  }
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin: 15px 0;
  
  mat-icon {
    margin-right: 8px;
  }
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #3f51b5;
    
    .dark-theme & {
      color: #7986cb;
    }
  }
  
  p {
    margin-bottom: 20px;
    color: #757575;
    
    .dark-theme & {
      color: #bdbdbd;
    }
  }
}
