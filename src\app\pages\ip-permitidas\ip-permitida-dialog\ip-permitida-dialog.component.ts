import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IpPermitida } from '@app/services/ip-permitidas.service';

@Component({
  selector: 'app-ip-permitida-dialog',
  templateUrl: './ip-permitida-dialog.component.html',
  styleUrls: ['./ip-permitida-dialog.component.scss']
})
export class IpPermitidaDialogComponent {
  dialogTitle: string;
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<IpPermitidaDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IpPermitida
  ) {
    this.dialogTitle = data?.id ? 'Editar IP Permitida' : 'Nueva IP Permitida';
    
    let fechaExpiracion = null;
    if (data?.fecha_expiracion) {
      fechaExpiracion = new Date(data.fecha_expiracion);
    }
    
    const userFromLocalStorage = localStorage.getItem('user') || '';
    const user = JSON.parse(userFromLocalStorage);
    
    this.form = this.fb.group({
      id: [data?.id || null],  // Agregamos el campo id
      ip: [data?.ip || '', [
        Validators.required,
        Validators.pattern('^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
      ]],
      descripcion: [data?.descripcion || '', [Validators.maxLength(255)]],
      fecha_expiracion: [fechaExpiracion, Validators.required],
      user_create_id: [user.id],
      is_active: [data?.is_active !== undefined ? data.is_active : true]
    });
  }

  onSubmit(): void {
    if (this.form.valid) {
      const formData = this.form.value;
      // Asegurarse de que la fecha esté en el formato correcto
      if (formData.fecha_expiracion instanceof Date) {
        formData.fecha_expiracion = formData.fecha_expiracion.toISOString().split('T')[0];
      }
      this.dialogRef.close(formData);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  get f() { return this.form.controls; }
}


