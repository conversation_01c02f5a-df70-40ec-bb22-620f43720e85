# Google Drive Explorer Component

Un componente profesional para explorar, navegar y gestionar archivos en Google Drive, similar a la interfaz nativa de Google Drive.

## Características

- 🗂️ **Navegación de carpetas** con breadcrumbs
- 📁 **Creación de carpetas** dinámicamente
- ⬆️ **Subida de archivos** con drag & drop
- 🔍 **Búsqueda** en tiempo real
- 📋 **Vista de cuadrícula y lista**
- 📄 **Paginación completa** con navegación entre páginas
- 🎯 **Filtros por tipo de archivo**
- 📱 **Diseño responsivo**
- 🎨 **Iconos específicos** por tipo de archivo

## Paginación

El componente incluye un sistema de paginación completo:

### Características de Paginación

- **Navegación por páginas**: Botones para página anterior, siguiente y primera
- **Información de página**: Muestra página actual, total de páginas y elementos
- **Tokens de página**: Soporte para tokens de Google Drive API
- **Reset automático**: La paginación se resetea al navegar o buscar
- **Responsive**: Controles adaptables a dispositivos móviles

### Controles de Paginación

- 🏠 **Primera página**: Vuelve al inicio
- ⬅️ **Página anterior**: Navega a la página previa
- ➡️ **Página siguiente**: Navega a la siguiente página
- 📊 **Información**: Muestra página actual y total de elementos

### Backend Requerido

Para que la paginación funcione correctamente, el backend debe retornar:

```json
{
  "rpta": 1,
  "msg": "Success",
  "data": [...],
  "nextPageToken": "token_for_next_page",
  "totalItems": 150
}
```

## Uso

### Importar el módulo

```typescript
import { GoogleDriveExplorerModule } from './shared/components/google-drive-explorer/google-drive-explorer.module';

@NgModule({
  imports: [
    GoogleDriveExplorerModule,
    // otros módulos...
  ]
})
export class YourModule { }
```

### Abrir el explorador

```typescript
import { GoogleDriveExplorerComponent } from './shared/components/google-drive-explorer/google-drive-explorer.component';

// En tu componente
openGoogleDriveExplorer() {
  const dialogRef = this.dialog.open(GoogleDriveExplorerComponent, {
    width: '90vw',
    height: '80vh',
    maxWidth: '1200px',
    maxHeight: '800px',
    data: {
      mode: 'upload', // 'browse' | 'upload' | 'select'
      allowedTypes: ['audio/*'], // Tipos de archivo permitidos
      title: 'Subir Audio para Transcripción',
      multiSelect: false, // Para modo 'select'
      cliente: clienteData, // Datos del cliente (opcional)
      numeroMovil: '123456789' // Número móvil (opcional)
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result && result.uploaded) {
      console.log('Archivo subido:', result.fileName);
    }
  });
}
```

## Modos de operación

### 1. Modo Browse (`mode: 'browse'`)
- Navegación libre por Google Drive
- Visualización de archivos y carpetas
- Creación de carpetas
- Descarga de archivos

### 2. Modo Upload (`mode: 'upload'`)
- Subida de archivos con drag & drop
- Filtros por tipo de archivo
- **Modal permanece abierto** después de subir (nuevo comportamiento)
- Contador visual de archivos subidos
- Botón "Finalizar" para cerrar manualmente
- Permite subir múltiples archivos en sesiones

### 3. Modo Select (`mode: 'select'`)
- Selección de archivos existentes
- Soporte para selección múltiple
- Retorna archivos seleccionados

## Configuración de datos

```typescript
interface GoogleDriveExplorerData {
  cliente?: any;              // Información del cliente
  numeroMovil?: string;       // Número móvil del cliente
  mode: 'browse' | 'upload' | 'select'; // Modo de operación
  allowedTypes?: string[];    // Tipos de archivo permitidos
  title?: string;             // Título personalizado
  multiSelect?: boolean;      // Permitir selección múltiple
}
```

## Tipos de archivo soportados

El componente reconoce automáticamente los siguientes tipos:

- 📁 **Carpetas**: `application/vnd.google-apps.folder`
- 🖼️ **Imágenes**: `image/*`
- 🎥 **Videos**: `video/*`
- 🎵 **Audio**: `audio/*`
- 📄 **PDF**: `application/pdf`
- 📝 **Documentos**: Word, Google Docs
- 📊 **Hojas de cálculo**: Excel, Google Sheets
- 📈 **Presentaciones**: PowerPoint, Google Slides
- 🗜️ **Archivos comprimidos**: ZIP, RAR

## Eventos y respuestas

### Respuesta de subida exitosa
```typescript
{
  uploaded: true,
  fileName: 'archivo.mp3',
  fileCount: 1
}
```

### Respuesta de selección
```typescript
{
  selected: true,
  files: [
    {
      id: 'file-id',
      name: 'archivo.pdf',
      mimeType: 'application/pdf',
      size: 1024000,
      // ... otros campos
    }
  ]
}
```

## Personalización

### Estilos CSS
El componente utiliza clases CSS que pueden ser personalizadas:

```scss
.google-drive-explorer {
  // Contenedor principal
  
  .files-grid {
    // Vista de cuadrícula
  }
  
  .files-list {
    // Vista de lista
  }
  
  .file-card {
    // Tarjeta de archivo en vista de cuadrícula
  }
}
```

### Iconos por tipo de archivo
Los iconos se asignan automáticamente basándose en el tipo MIME:

```typescript
getFileIcon(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'videocam';
  if (mimeType.startsWith('audio/')) return 'audiotrack';
  // ... más tipos
}
```

## Dependencias

- Angular Material
- Google Drive Service (backend)
- RxJS

## Backend requerido

El componente requiere un backend con los siguientes endpoints:

- `GET /api/google-drive/files` - Listar archivos
- `GET /api/google-drive/folders` - Listar carpetas
- `POST /api/google-drive/upload` - Subir archivo
- `POST /api/google-drive/folder` - Crear carpeta
- `DELETE /api/google-drive/{fileId}` - Eliminar archivo
- `GET /api/google-drive/download/{fileId}` - Descargar archivo

## Ejemplo completo

```typescript
// En tu componente
openAudioUpload(cliente: any): void {
  const numeroMovil = cliente.numeroMovil || cliente.movilContacto;
  
  if (!numeroMovil) {
    this.showError('No se pudo identificar el número móvil del cliente');
    return;
  }

  const dialogRef = this.dialog.open(GoogleDriveExplorerComponent, {
    width: '90vw',
    height: '80vh',
    maxWidth: '1200px',
    maxHeight: '800px',
    data: {
      cliente: cliente,
      numeroMovil: numeroMovil,
      mode: 'upload',
      allowedTypes: ['audio/*'],
      title: 'Subir Audio para Transcripción'
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result && result.uploaded) {
      this.showSuccess(`Audio ${result.fileName} subido exitosamente`);
      // Procesar transcripción...
    }
  });
}
```
