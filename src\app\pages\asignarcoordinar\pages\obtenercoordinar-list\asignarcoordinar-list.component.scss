/* Estilos para el mini-snackbar */
::ng-deep .mini-snackbar {
  min-width: auto !important;
  max-width: 200px !important;
  padding: 8px 12px !important;

  .mat-simple-snackbar {
    font-size: 13px !important;
  }

  .mat-simple-snackbar-action {
    display: none !important;
  }
}

/* Estilos para el host component */
:host {
  display: block;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
