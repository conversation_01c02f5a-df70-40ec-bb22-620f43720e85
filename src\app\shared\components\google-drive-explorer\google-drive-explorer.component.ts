import { Component, OnInit, Inject, ViewChild, ElementRef } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { GoogleDriveService, GoogleDriveFile, CreateFolderRequest, UploadFileRequest } from '../../../services/google-drive.service';
import { TranscriptionService } from '../../../services/transcription.service';
import { TranscriptionDialogComponent } from '../transcription-dialog/transcription-dialog.component';
import { Subject, lastValueFrom } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

export interface GoogleDriveExplorerData {
  cliente?: any;
  numeroMovil?: string;
  mode: 'browse' | 'upload' | 'select';
  allowedTypes?: string[];
  title?: string;
  multiSelect?: boolean;
}

export interface BreadcrumbItem {
  id: string;
  name: string;
}

@Component({
  selector: 'app-google-drive-explorer',
  templateUrl: './google-drive-explorer.component.html',
  styles: [`
    @keyframes pulse-transcribe {
      0%, 100% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.8;
      }
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  `]
})
export class GoogleDriveExplorerComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('searchInput') searchInput!: ElementRef<HTMLInputElement>;

  // Datos del diálogo
  data: GoogleDriveExplorerData;

  // Estado del componente
  loading = false;
  uploadLoading = false;
  files: GoogleDriveFile[] = [];
  selectedFiles: GoogleDriveFile[] = [];
  currentFolderId: string | null = null;
  breadcrumbs: BreadcrumbItem[] = [{ id: '', name: 'Mi Drive' }];
  
  // Vista y configuración
  viewMode: 'grid' | 'list' = 'grid';
  pageSize = 20;
  currentPage = 0;
  totalItems = 0;
  totalPages = 0;
  hasNextPage = false;
  hasPreviousPage = false;
  nextPageToken = '';
  searchQuery = '';
  private searchSubject = new Subject<string>();

  // Control de vista multi-usuario
  viewAllUsers = false;

  // Drag & Drop
  isDragOver = false;

  // Estado de subida
  uploadedFilesCount = 0;
  hasUploadedFiles = false;

  constructor(
    public dialogRef: MatDialogRef<GoogleDriveExplorerComponent>,
    @Inject(MAT_DIALOG_DATA) data: GoogleDriveExplorerData,
    private googleDriveService: GoogleDriveService,
    private transcriptionService: TranscriptionService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.data = data;
  }

  ngOnInit(): void {
    this.setupSearch();
    this.loadInitialContent();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      this.searchQuery = query;
      this.resetPagination(); // Resetear paginación al buscar
      // Si hay búsqueda, siempre usar loadFiles para buscar en la carpeta actual
      // Si no hay búsqueda, usar la lógica inicial
      if (query.trim()) {
        this.loadFiles();
      } else {
        this.loadInitialContent();
      }
    });
  }

  onSearchChange(event: any): void {
    this.searchSubject.next(event.target.value);
  }

  loadInitialContent(): void {
    // Mantener el flujo normal: siempre empezar con carpetas
    if (!this.currentFolderId) {
      // Si no hay carpeta seleccionada, cargar solo las carpetas disponibles
      console.log('📁 Cargando carpetas disponibles');
      this.loadFolders();
    } else {
      // Si hay una carpeta seleccionada, cargar archivos de esa carpeta
      console.log('📂 Cargando archivos de la carpeta:', this.currentFolderId);
      this.loadFiles();
    }
  }

  loadFolders(pageToken?: string): void {
    this.loading = true;
    this.googleDriveService.listFolders(this.pageSize, pageToken)
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.files = response.data.map(folder => ({
              ...folder,
              mimeType: folder.mimeType || 'application/vnd.google-apps.folder',
              isFolder: true
            }));
            this.updatePaginationInfo(response);
          } else {
            this.showError('Error al cargar carpetas: ' + response.msg);
          }
          this.loading = false;
        },
        error: () => {
          this.showError('Error al cargar carpetas');
          this.loading = false;
        }
      });
  }

  loadFiles(pageToken?: string): void {
    this.loading = true;

    // En modo select con tipos específicos, no usar folderId para obtener archivos de todos los usuarios
    const useAllUsersMode = this.data.mode === 'select' &&
                           this.data.allowedTypes &&
                           this.data.allowedTypes.length > 0 &&
                           this.currentFolderId; // Solo cuando estemos dentro de una carpeta

    if (useAllUsersMode) {
      // Usar endpoint normal pero SIN folderId para ver archivos de todos los usuarios
      console.log('🌐 Modo todos los usuarios - Usando endpoint normal SIN folderId');
      console.log('📁 Carpeta ignorada:', this.currentFolderId);
      console.log('🔍 Query de búsqueda:', this.searchQuery);
      console.log('📄 Token de página:', pageToken);

      this.googleDriveService.listFiles(undefined, this.pageSize, this.searchQuery, pageToken) // Sin folderId
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.files = response.data.map(file => ({
                ...file,
                mimeType: file.mimeType || 'application/octet-stream',
                isFolder: this.googleDriveService.isFolder(file)
              }));
              this.updatePaginationInfo(response);
            } else {
              this.showError('Error al cargar archivos de todos los usuarios: ' + response.msg);
            }
            this.loading = false;
          },
          error: () => {
            this.showError('Error al cargar archivos de todos los usuarios');
            this.loading = false;
          }
        });
    } else {
      // Usar endpoint normal con folderId para archivos de la carpeta específica
      console.log('👤 Usando endpoint normal CON folderId:', `/api/google-drive/files`);
      console.log('📁 Carpeta actual:', this.currentFolderId || 'raíz');
      console.log('🔍 Query de búsqueda:', this.searchQuery);
      console.log('📄 Token de página:', pageToken);

      this.googleDriveService.listFiles(this.currentFolderId || undefined, this.pageSize, this.searchQuery, pageToken)
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.files = response.data.map(file => ({
                ...file,
                mimeType: file.mimeType || 'application/octet-stream',
                isFolder: this.googleDriveService.isFolder(file)
              }));
              this.updatePaginationInfo(response);
            } else {
              this.showError('Error al cargar archivos: ' + response.msg);
            }
            this.loading = false;
          },
          error: () => {
            this.showError('Error al cargar archivos');
            this.loading = false;
          }
        });
    }
  }

  private updatePaginationInfo(response: any): void {
    this.nextPageToken = response.nextPageToken || '';
    this.totalItems = response.totalItems || this.files.length;
    this.hasNextPage = !!this.nextPageToken;
    this.hasPreviousPage = this.currentPage > 0;

    // Calcular páginas aproximadas si tenemos el total
    if (this.totalItems > 0) {
      this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    }
  }

  onFileClick(file: GoogleDriveFile): void {
    if (file.isFolder) {
      this.navigateToFolder(file);
    } else {
      if (this.data.mode === 'select') {
        this.toggleFileSelection(file);
      } else {
        // Previsualizar o descargar archivo
        this.previewFile(file);
      }
    }
  }

  onFileDoubleClick(file: GoogleDriveFile): void {
    if (!file.isFolder && this.data.mode === 'select') {
      this.selectAndClose([file]);
    }
  }

  navigateToFolder(folder: GoogleDriveFile): void {
    this.currentFolderId = folder.id;
    this.breadcrumbs.push({ id: folder.id, name: folder.name });
    this.selectedFiles = [];
    this.searchQuery = ''; // Limpiar búsqueda al navegar
    this.resetPagination(); // Resetear paginación
    this.audioFileCache.clear(); // Limpiar cache de archivos de audio
    this.loadFiles(); // Ahora cargar archivos de la carpeta
  }

  navigateToBreadcrumb(breadcrumb: BreadcrumbItem): void {
    const index = this.breadcrumbs.findIndex(b => b.id === breadcrumb.id);
    if (index !== -1) {
      this.breadcrumbs = this.breadcrumbs.slice(0, index + 1);
      this.currentFolderId = breadcrumb.id || null;
      this.selectedFiles = [];
      this.searchQuery = ''; // Limpiar búsqueda al navegar
      this.resetPagination(); // Resetear paginación
      this.audioFileCache.clear(); // Limpiar cache de archivos de audio
      this.loadInitialContent(); // Usar la nueva lógica
    }
  }

  // Métodos de paginación
  resetPagination(): void {
    this.currentPage = 0;
    this.nextPageToken = '';
    this.hasNextPage = false;
    this.hasPreviousPage = false;
    this.totalItems = 0;
    this.totalPages = 0;
  }

  nextPage(): void {
    if (this.hasNextPage) {
      this.currentPage++;
      if (!this.currentFolderId) {
        this.loadFolders(this.nextPageToken);
      } else {
        this.loadFiles(this.nextPageToken);
      }
    }
  }

  previousPage(): void {
    if (this.hasPreviousPage && this.currentPage > 0) {
      this.currentPage--;
      // Para página anterior, necesitaríamos implementar un sistema de tokens previos
      // Por simplicidad, recargamos desde el inicio
      this.resetPagination();
      this.loadInitialContent();
    }
  }

  goToFirstPage(): void {
    this.resetPagination();
    this.loadInitialContent();
  }

  toggleFileSelection(file: GoogleDriveFile): void {
    const index = this.selectedFiles.findIndex(f => f.id === file.id);
    if (index > -1) {
      this.selectedFiles.splice(index, 1);
    } else {
      if (this.data.multiSelect) {
        this.selectedFiles.push(file);
      } else {
        this.selectedFiles = [file];
      }
    }
  }

  isFileSelected(file: GoogleDriveFile): boolean {
    return this.selectedFiles.some(f => f.id === file.id);
  }

  // Funciones de archivo
  createFolder(): void {
    const folderName = prompt('Nombre de la nueva carpeta:');
    if (folderName && folderName.trim()) {
      const request: CreateFolderRequest = {
        folderName: folderName.trim(),
        parentFolderId: this.currentFolderId || undefined
      };

      this.googleDriveService.createFolder(request).subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.showSuccess('Carpeta creada exitosamente');
            this.loadInitialContent(); // Recargar contenido apropiado
          } else {
            this.showError('Error al crear carpeta: ' + response.msg);
          }
        },
        error: () => {
          this.showError('Error al crear carpeta');
        }
      });
    }
  }

  openFileUpload(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    this.uploadFiles(files);
  }

  uploadFiles(files: File[]): void {
    if (!files || files.length === 0) return;

    // Verificar tipos de archivo permitidos
    if (this.data.allowedTypes) {
      const invalidFiles = files.filter(file => 
        !this.googleDriveService.isFileTypeAllowed(file, this.data.allowedTypes!)
      );
      
      if (invalidFiles.length > 0) {
        this.showError(`Tipos de archivo no permitidos: ${invalidFiles.map(f => f.name).join(', ')}`);
        return;
      }
    }

    this.uploadLoading = true;
    const uploadPromises = files.map(file => {
      const request: UploadFileRequest = {
        file,
        folderId: this.currentFolderId || undefined
      };
      return lastValueFrom(this.googleDriveService.uploadFile(request));
    });

    Promise.all(uploadPromises).then(responses => {
      const successCount = responses.filter(r => r?.rpta === 1).length;
      if (successCount === files.length) {
        // Actualizar contador de archivos subidos
        this.uploadedFilesCount += successCount;
        this.hasUploadedFiles = true;

        this.showSuccess(`${successCount} archivo(s) subido(s) exitosamente`);
        this.loadInitialContent(); // Recargar contenido apropiado

        // No cerrar automáticamente el diálogo, mantenerlo abierto
        // El usuario puede seguir navegando, subiendo más archivos o cerrar manualmente

        // Opcional: Mostrar notificación adicional si se desea
        if (files.length === 1) {
          this.showSuccess(`Archivo "${files[0].name}" subido correctamente a Google Drive`);
        }
      } else {
        this.showError(`Solo se subieron ${successCount} de ${files.length} archivos`);
        // Actualizar contador solo con los exitosos
        if (successCount > 0) {
          this.uploadedFilesCount += successCount;
          this.hasUploadedFiles = true;
        }
      }
      this.uploadLoading = false;
    }).catch(() => {
      this.showError('Error al subir archivos');
      this.uploadLoading = false;
    });
  }

  // Drag & Drop
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
    
    const files = Array.from(event.dataTransfer?.files || []);
    if (files.length > 0) {
      this.uploadFiles(files);
    }
  }

  // Utilidades
  getFileIcon(file: GoogleDriveFile): string {
    if (!file || !file.mimeType) {
      return 'insert_drive_file';
    }
    return this.googleDriveService.getFileIcon(file.mimeType);
  }

  // Cache para evitar re-evaluaciones innecesarias
  private audioFileCache = new Map<string, boolean>();

  /**
   * Verifica si un archivo es de audio válido para transcripción
   */
  isAudioFile(file: GoogleDriveFile): boolean {
    // Si es una carpeta, no es un archivo de audio
    if (!file || file.isFolder || !file.mimeType) {
      return false;
    }

    // Verificar si es una carpeta de Google Drive
    if (file.mimeType === 'application/vnd.google-apps.folder') {
      return false;
    }

    // Usar cache para evitar re-evaluaciones
    const cacheKey = `${file.id}_${file.mimeType}`;
    if (this.audioFileCache.has(cacheKey)) {
      return this.audioFileCache.get(cacheKey)!;
    }

    const audioTypes = [
      'audio/mpeg',      // MP3
      'audio/wav',       // WAV
      'audio/ogg',       // OGG
      'audio/flac',      // FLAC
      'audio/mp4',       // M4A
      'audio/aac',       // AAC
      'audio/x-ms-wma',  // WMA
      'audio/opus',      // OPUS
      'video/webm',      // WebM
      'audio/amr',       // AMR (WhatsApp)
      'video/3gpp'       // 3GP
    ];

    const isAudio = audioTypes.includes(file.mimeType) || this.isValidAudioExtension(file.name);

    // Guardar en cache
    this.audioFileCache.set(cacheKey, isAudio);

    return isAudio;
  }

  /**
   * Verifica la extensión del archivo si el tipo MIME no es reconocido
   */
  private isValidAudioExtension(fileName: string): boolean {
    const validExtensions = [
      '.mp3', '.wav', '.ogg', '.flac', '.m4a',
      '.aac', '.wma', '.opus', '.webm', '.amr', '.3gp'
    ];

    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return validExtensions.includes(extension);
  }

  /**
   * Abre el diálogo de transcripción para un archivo de audio
   */
  openTranscriptionDialog(file: GoogleDriveFile): void {
    if (!this.isAudioFile(file)) {
      this.showError('El archivo seleccionado no es un formato de audio válido para transcripción');
      return;
    }

    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '90vw',
      maxWidth: '800px',
      height: '90vh',
      maxHeight: '700px',
      disableClose: false,
      data: {
        file: file,
        cliente: this.data.cliente,
        numeroMovil: this.data.numeroMovil
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        this.showSuccess('Transcripción completada exitosamente');
        console.log('Resultado de transcripción:', result);
      }
    });
  }

  /**
   * Verifica si estamos viendo archivos de todos los usuarios
   */
  isViewingAllUsersFiles(): boolean {
    const result = this.data.mode === 'select' &&
                   !!this.data.allowedTypes &&
                   this.data.allowedTypes.length > 0 &&
                   !!this.currentFolderId; // Solo cuando estemos dentro de una carpeta

    console.log('🔍 isViewingAllUsersFiles():', {
      mode: this.data.mode,
      allowedTypes: this.data.allowedTypes,
      currentFolderId: this.currentFolderId,
      result: result
    });

    return result;
  }

  /**
   * Alterna entre vista normal y vista multi-usuario
   */
  toggleViewMode(): void {
    this.viewAllUsers = !this.viewAllUsers;
    console.log('🔄 Alternando vista:', this.viewAllUsers ? 'Todos los usuarios' : 'Mis archivos');

    // Resetear paginación y recargar contenido
    this.resetPagination();
    this.loadInitialContent();
  }

  formatFileSize(size: number): string {
    return this.googleDriveService.formatFileSize(size || 0);
  }

  formatDate(dateString: string): string {
    if (!dateString) {
      return 'Fecha no disponible';
    }
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Fecha inválida';
    }
  }

  previewFile(file: GoogleDriveFile): void {
    // Implementar previsualización según el tipo de archivo
    if (file.webViewLink) {
      window.open(file.webViewLink, '_blank');
    }
  }

  downloadFile(file: GoogleDriveFile): void {
    this.googleDriveService.downloadFile(file.id).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.name;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: () => {
        this.showError('Error al descargar archivo');
      }
    });
  }

  deleteFile(file: GoogleDriveFile): void {
    if (confirm(`¿Estás seguro de que quieres eliminar "${file.name}"?`)) {
      this.googleDriveService.deleteFile(file.id).subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.showSuccess('Archivo eliminado exitosamente');
            this.loadInitialContent(); // Recargar contenido apropiado
          } else {
            this.showError('Error al eliminar archivo: ' + response.msg);
          }
        },
        error: () => {
          this.showError('Error al eliminar archivo');
        }
      });
    }
  }

  // Acciones del diálogo
  selectAndClose(files?: GoogleDriveFile[]): void {
    const selectedFiles = files || this.selectedFiles;
    this.dialogRef.close({
      selected: true,
      files: selectedFiles
    });
  }

  closeAfterUpload(): void {
    // Cerrar el diálogo indicando que se completó la subida
    this.dialogRef.close({
      uploaded: true,
      completed: true,
      uploadedCount: this.uploadedFilesCount,
      message: `Subida completada: ${this.uploadedFilesCount} archivo(s) subido(s) exitosamente`
    });
  }

  cancel(): void {
    this.dialogRef.close();
  }

  // Utilidades de UI
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  getDisplayedColumns(): string[] {
    const baseColumns = ['name', 'size', 'modified', 'actions'];
    if (this.data.mode === 'select') {
      return ['select', ...baseColumns];
    }
    return baseColumns;
  }
}
