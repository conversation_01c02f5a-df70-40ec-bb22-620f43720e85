import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SedeUserService {
  private sedeIdSubject = new BehaviorSubject<number | null>(null);

  constructor() {
    // Inicializar con el valor de localStorage si existe
    this.loadSedeIdFromLocalStorage();
  }

  /**
   * Carga el ID de la sede del usuario desde localStorage
   */
  private loadSedeIdFromLocalStorage(): void {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);

        // Intentar obtener el ID de la sede del usuario en este orden:
        // 1. sede_id (campo para enviar al backend)
        // 2. sedeId (campo que devuelve el backend)
        // 3. sede.id (objeto sede con id)
        let sedeId: number | null = null;

        if (user.sede_id) {
          sedeId = user.sede_id;
        } else if (user.sedeId) {
          sedeId = user.sedeId;
        } else if (user.sede && user.sede.id) {
          sedeId = user.sede.id;
        }

        // Actualizar el subject con el ID de la sede
        this.sedeIdSubject.next(sedeId);

        // Si tenemos un ID de sede pero no un nombre, usar "tu sede" como valor predeterminado
        if (sedeId !== null && (!user.sede || (typeof user.sede === 'object' && !user.sede.nombre))) {
          user.sede = 'tu sede';
          localStorage.setItem('user', JSON.stringify(user));
        }
      }
    } catch (error) {
      console.error('Error al cargar el ID de la sede del usuario:', error);
    }
  }

  /**
   * Obtiene el ID de la sede del usuario
   * @returns Observable con el ID de la sede o null si no existe
   */
  getSedeId(): Observable<number | null> {
    return this.sedeIdSubject.asObservable();
  }

  /**
   * Obtiene el ID de la sede del usuario de forma síncrona
   * @returns ID de la sede o null si no existe
   */
  getSedeIdSync(): number | null {
    // Primero intentar obtener del subject
    const currentValue = this.sedeIdSubject.getValue();
    if (currentValue !== null) {
      return currentValue;
    }

    // Si no hay valor en el subject, intentar obtener del objeto user en localStorage
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);

        if (user.sede_id) {
          return user.sede_id;
        } else if (user.sedeId) {
          return user.sedeId;
        } else if (user.sede && typeof user.sede === 'object' && user.sede.id) {
          return user.sede.id;
        }
      }

      return null;
    } catch (error) {
      console.error('Error al obtener el ID de la sede del usuario:', error);
      return null;
    }
  }

  /**
   * Actualiza el ID de la sede del usuario
   * @param sedeId ID de la sede
   * @param sedeName Nombre de la sede (opcional)
   */
  setSedeId(sedeId: number | null, sedeName?: string): void {
    this.sedeIdSubject.next(sedeId);

    // Actualizar el objeto user en localStorage
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);

        // Actualizar el ID de la sede
        if (sedeId !== null) {
          user.sede_id = sedeId;

          // Si se proporciona el nombre de la sede, actualizarlo también
          if (sedeName) {
            user.sede = sedeName;
          }
        } else {
          // Si sedeId es null, eliminar las propiedades relacionadas con la sede
          delete user.sede_id;
          delete user.sede;
        }

        // Guardar el objeto user actualizado en localStorage
        localStorage.setItem('user', JSON.stringify(user));
      }
    } catch (error) {
      console.error('Error al actualizar la sede del usuario en localStorage:', error);
    }
  }

  /**
   * Obtiene el nombre de la sede del usuario de forma síncrona
   * @returns Nombre de la sede o "tu sede" si no existe
   */
  getSedeNameSync(): string {
    try {
      // Obtener del objeto user en localStorage
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);

        // Según la estructura proporcionada, el nombre de la sede está directamente en user.sede
        if (user.sede) {
          // Si sede es un string, es el nombre de la sede
          if (typeof user.sede === 'string') {
            return user.sede;
          }
          // Si sede es un objeto, intentar obtener el nombre
          else if (typeof user.sede === 'object' && user.sede.nombre) {
            return user.sede.nombre;
          }
        }

        // Intentar otras propiedades posibles
        if (user.sedeName) {
          return user.sedeName;
        } else if (user.sede_nombre) {
          return user.sede_nombre;
        }

        // Si tenemos un ID de sede pero no un nombre, usar "tu sede" como valor predeterminado
        if (user.sede_id) {
          // Actualizar el objeto user con el valor predeterminado
          user.sede = 'tu sede';
          localStorage.setItem('user', JSON.stringify(user));
          return 'tu sede';
        }
      }

      return '';
    } catch (error) {
      console.error('Error al obtener el nombre de la sede del usuario:', error);

      // En caso de error, intentar usar un valor predeterminado si hay un ID de sede
      try {
        const sedeId = this.getSedeIdSync();
        if (sedeId) {
          // Intentar actualizar el objeto user con el valor predeterminado
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            user.sede = 'tu sede';
            localStorage.setItem('user', JSON.stringify(user));
          }
          return 'tu sede';
        }
      } catch (e) {
        // Ignorar errores en el manejo de errores
      }

      return '';
    }
  }
}
