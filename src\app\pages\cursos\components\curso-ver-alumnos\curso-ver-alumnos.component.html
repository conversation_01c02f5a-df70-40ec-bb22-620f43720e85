<div
  class="w-full max-h-[80vh] flex flex-col bg-white dark:bg-gray-900 text-gray-900 dark:text-white rounded-lg shadow-xl overflow-hidden"
>
  <div
    class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700"
  >
    <div class="flex items-center justify-between">
      <h2
        mat-dialog-title
        class="text-xl font-semibold text-gray-900 dark:text-white flex items-center"
      >
        <mat-icon class="mr-3 text-blue-600 dark:text-blue-400">group</mat-icon>
        Alumnos del Curso
      </h2>
      <div class="text-sm text-gray-600 dark:text-gray-300">
        <span class="font-medium">{{ curso.nombre }}</span>
      </div>
    </div>
  </div>

  <mat-dialog-content
    class="flex-1 overflow-y-auto px-6 py-4 bg-gray-50 dark:bg-gray-900"
  >
    <div
      *ngIf="loading"
      class="flex flex-col items-center justify-center p-8 text-center bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      <mat-spinner diameter="40"></mat-spinner>
      <p class="mt-4 text-sm text-gray-600 dark:text-gray-400 font-medium">
        {{ loadingMessage || "Cargando alumnos..." }}
      </p>
    </div>

    <div
      *ngIf="error && !loading"
      class="flex items-center p-4 bg-red-50 dark:bg-red-900/30 rounded-xl border border-red-200 dark:border-red-700 my-4 shadow-sm"
    >
      <mat-icon class="mr-3 text-red-600 dark:text-red-400">error</mat-icon>
      <span class="text-sm text-red-800 dark:text-red-200 font-medium">{{
        error
      }}</span>
    </div>

    <div
      *ngIf="!loading && !error && usuariosAsignados.length > 0"
      class="space-y-4"
    >
      <div
        *ngFor="let asignacion of usuariosAsignados; let i = index"
        class="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600 transition-all duration-200 shadow-sm hover:shadow-md"
      >
        <div class="flex items-start">
          <!-- Avatar -->
          <div class="mr-4 flex-shrink-0">
            <div
              class="w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg ring-2 ring-white dark:ring-gray-600"
              [ngStyle]="{
                'background-color': getAvatarColor(asignacion.usuario.username)
              }"
            >
              {{
                getInitials(
                  asignacion.usuario.nombre,
                  asignacion.usuario.apellido
                )
              }}
            </div>
          </div>

          <!-- Información del alumno -->
          <div class="flex-1 min-w-0 overflow-hidden">
            <!-- Nombre y username -->
            <div class="flex items-center flex-wrap mb-3">
              <span
                class="font-semibold text-lg mr-2 text-gray-900 dark:text-white"
                >{{ asignacion.usuario.nombre || "" }}
                {{ asignacion.usuario.apellido || "" }}</span
              >
              <span class="text-sm text-gray-500 dark:text-gray-400"
                >({{ asignacion.usuario.username }})</span
              >
            </div>

            <!-- Detalles -->
            <div
              class="text-sm text-gray-700 dark:text-gray-300 space-y-3 mb-4"
            >
              <!-- Email -->
              <div class="flex items-center">
                <mat-icon
                  class="text-gray-500 dark:text-gray-400 mr-2 text-base"
                  >email</mat-icon
                >
                <span class="truncate">{{ asignacion.usuario.email }}</span>
              </div>

              <!-- DNI -->
              <div class="flex items-center">
                <mat-icon
                  class="text-gray-500 dark:text-gray-400 mr-2 text-base"
                  >badge</mat-icon
                >
                <span>{{ asignacion.usuario.dni }}</span>
              </div>

              <!-- Sede (si existe) -->
              <div *ngIf="asignacion.usuario.sede" class="flex items-center">
                <mat-icon
                  class="text-gray-500 dark:text-gray-400 mr-2 text-base"
                  >location_on</mat-icon
                >
                <span>{{ asignacion.usuario.sede }}</span>
              </div>

              <!-- Progreso -->
              <div class="flex items-center">
                <mat-icon
                  class="text-gray-500 dark:text-gray-400 mr-2 text-base"
                  >school</mat-icon
                >
                <div class="flex-1">
                  <div class="flex justify-between text-sm mb-2">
                    <span class="font-medium text-gray-700 dark:text-gray-300"
                      >Progreso:</span
                    >
                    <span class="font-semibold text-blue-600 dark:text-blue-400"
                      >{{ getPorcentajeProgresoAlumno(asignacion) }}%</span
                    >
                  </div>
                  <div
                    class="h-3 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden"
                  >
                    <div
                      class="h-full rounded-full transition-all duration-300"
                      [style.width.%]="getPorcentajeProgresoAlumno(asignacion)"
                      [style.background-color]="
                        getColorProgreso(
                          getPorcentajeProgresoAlumno(asignacion)
                        )
                      "
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Rol y estado de completado -->
            <div class="flex items-center flex-wrap gap-3 mt-4">
              <span
                class="px-3 py-1 text-xs font-semibold rounded-full shadow-sm"
                [ngClass]="{
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200':
                    asignacion.usuario.role?.toLowerCase() === 'admin',
                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200':
                    asignacion.usuario.role?.toLowerCase() === 'coordinador',
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                    asignacion.usuario.role?.toLowerCase() === 'asesor',
                  'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200':
                    asignacion.usuario.role?.toLowerCase() === 'auditor',
                  'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200':
                    asignacion.usuario.role?.toLowerCase() === 'programador',
                  'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200':
                    [
                      'backoffice',
                      'backofficeseguimiento',
                      'backofficetramitador'
                    ].includes(asignacion.usuario.role?.toLowerCase() || ''),
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200':
                    !asignacion.usuario.role
                }"
              >
                {{ asignacion.usuario.role || "Sin rol" }}
              </span>

              <!-- Estado de completado -->
              <span
                *ngIf="
                  getPorcentajeProgresoAlumno(asignacion) === 100 ||
                  haCompletadoCurso(asignacion)
                "
                class="flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 shadow-sm"
              >
                <mat-icon class="text-sm mr-1">check_circle</mat-icon>
                Completado
              </span>
            </div>
          </div>

          <!-- Acciones -->
          <div class="ml-4 flex-shrink-0">
            <button
              mat-icon-button
              (click)="
                onRemoveAlumno(asignacion.usuario); $event.stopPropagation()
              "
              matTooltip="Eliminar alumno del curso"
              class="w-10 h-10 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 rounded-lg"
            >
              <mat-icon class="text-lg">person_remove</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div
      *ngIf="!loading && !error && usuariosAsignados.length === 0"
      class="flex flex-col items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      <mat-icon class="text-gray-400 mb-3 text-4xl">people_outline</mat-icon>
      <span class="text-gray-600 dark:text-gray-400 text-center">
        No hay alumnos asignados a este curso
      </span>
    </div>
  </mat-dialog-content>

  <div
    class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex justify-between items-center"
  >
    <!-- Summary -->
    <div class="text-sm text-gray-600 dark:text-gray-400">
      <span class="font-medium">{{ usuariosAsignados.length }}</span> alumnos
      asignados
    </div>

    <!-- Close Button -->
    <button mat-raised-button color="primary" (click)="onClose()">
      Cerrar
    </button>
  </div>
</div>
