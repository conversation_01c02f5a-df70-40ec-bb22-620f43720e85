/* Estilos específicos para componentes Angular Material */
:host ::ng-deep {
  /* Ajustes para los campos de formulario */
  .mat-form-field-appearance-outline .mat-form-field-outline {
    @apply dark:opacity-50;
  }

  .mat-form-field-label {
    @apply dark:text-gray-300;
  }

  /* Ajustes para los botones de acción */
  .mat-mini-fab {
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
  }

  /* Ajustes para los checkboxes */
  .mat-checkbox-frame {
    @apply dark:border-gray-500;
  }

  /* Ajustes para los spinners */
  .mat-progress-spinner circle {
    @apply stroke-blue-500;
  }

  /* Ajustes para los select */
  .mat-select-panel {
    @apply dark:bg-gray-800 dark:text-white;
  }

  .mat-option {
    @apply dark:hover:bg-gray-700;
  }
  
  /* Ajustes para las tarjetas */
  .mat-card {
    @apply transition-all duration-300 hover:shadow-lg;
  }
  
  /* Ajustes para las pestañas */
  .mat-tab-label {
    @apply dark:text-gray-300;
  }
  
  .mat-tab-label-active {
    @apply dark:text-white;
  }
  
  .mat-tab-header {
    @apply dark:border-gray-700;
  }
  
  /* Ajustes para la tabla */
  .mat-table {
    @apply dark:bg-gray-800;
  }
  
  .mat-header-cell {
    @apply dark:text-gray-300 font-medium;
  }
  
  .mat-cell {
    @apply dark:text-gray-200;
  }
  
  .mat-row {
    @apply dark:border-gray-700 dark:hover:bg-gray-700;
  }
  
  /* Ajustes para el paginador */
  .mat-paginator {
    @apply dark:bg-gray-800 dark:text-gray-300;
  }
}
