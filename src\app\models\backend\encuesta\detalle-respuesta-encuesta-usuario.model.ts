/**
 * Modelo que representa el detalle de una respuesta de un usuario a una pregunta específica de una encuesta
 */
export interface DetalleRespuestaEncuestaUsuario {
  id: number;
  respuestaEncuestaUsuarioId: number;
  preguntaId: number;
  preguntaEnunciado?: string;
  preguntaTipo?: string;
  opcionId?: number;
  opcionTexto?: string;
  respuestaTexto?: string;
  respuestaNumero?: number;
  respuestaFecha?: string;
  fechaCreacion: string;
  fechaActualizacion: string;
}

/**
 * Modelo para crear un nuevo detalle de respuesta de usuario a una pregunta de encuesta
 */
export interface DetalleRespuestaEncuestaUsuarioCreateRequest {
  respuestaEncuestaUsuarioId: number;
  preguntaId: number;
  opcionId?: number; // Puede ser null para preguntas de texto libre, fecha o número
  respuestaTexto?: string; // Para preguntas de texto libre
  respuestaNumero?: number; // Para preguntas numéricas
  respuestaFecha?: string; // Para preguntas de fecha
}
