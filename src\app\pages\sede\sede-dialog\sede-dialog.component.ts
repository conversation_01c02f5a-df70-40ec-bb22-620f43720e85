import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Sede } from '@app/models/backend/sede/sede.model';
import { SedeService } from '@app/services/sede.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-sede-dialog',
  templateUrl: './sede-dialog.component.html'
})
export class SedeDialogComponent implements OnInit {
  sedeForm!: FormGroup;
  loading = false;
  mode: 'create' | 'edit' = 'create';
  title = 'Crear Sede';

  constructor(
    private fb: FormBuilder,
    private sedeService: SedeService,
    public dialogRef: MatDialogRef<SedeDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'create' | 'edit', sede: Sede }
  ) {
    this.mode = data.mode;
    this.title = this.mode === 'create' ? 'Crear Sede' : 'Editar Sede';
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.sedeForm = this.fb.group({
      nombre: [this.data.sede.nombre, [Validators.required]],
      direccion: [this.data.sede.direccion, [Validators.required]],
      ciudad: [this.data.sede.ciudad, [Validators.required]],
      provincia: [this.data.sede.provincia],
      codigoPostal: [this.data.sede.codigoPostal],
      telefono: [this.data.sede.telefono, [Validators.pattern('^[0-9]*$')]],
      email: [this.data.sede.email, [Validators.email]],
      activo: [this.data.sede.activo !== false]
    });
  }

  onSubmit(): void {
    if (this.sedeForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.mode === 'create') {
      // Para crear, no incluimos el ID para que el backend genere uno nuevo
      const sedeData: Sede = {
        ...new Sede(), // Crear un nuevo objeto Sede con valores por defecto
        ...this.sedeForm.value,
        id: undefined // Asegurarnos de que el ID sea undefined para crear un nuevo registro
      };
      this.createSede(sedeData);
    } else {
      // Para actualizar, incluimos el ID existente
      const sedeData: Sede = {
        ...this.data.sede,
        ...this.sedeForm.value
      };
      this.updateSede(sedeData);
    }
  }

  createSede(sede: Sede): void {
    this.sedeService.createSede(sede).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.rpta === 1) {
          Swal.fire({
            icon: 'success',
            title: 'Éxito',
            text: 'Sede creada correctamente'
          });
          this.dialogRef.close(true);
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: response.msg || 'No se pudo crear la sede'
          });
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error al crear sede:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudo crear la sede. Por favor, intente nuevamente.'
        });
      }
    });
  }

  updateSede(sede: Sede): void {
    if (!sede.id) {
      this.loading = false;
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'No se puede actualizar una sede sin ID.'
      });
      return;
    }

    this.sedeService.updateSede(sede.id, sede).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.rpta === 1) {
          Swal.fire({
            icon: 'success',
            title: 'Éxito',
            text: 'Sede actualizada correctamente'
          });
          this.dialogRef.close(true);
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: response.msg || 'No se pudo actualizar la sede'
          });
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error al actualizar sede:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudo actualizar la sede. Por favor, intente nuevamente.'
        });
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
