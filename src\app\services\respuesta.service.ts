import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend';
import { Respuesta, RespuestaCreateRequest, RespuestaUpdateRequest } from '@app/models/backend/curso/respuesta.model';

@Injectable({
  providedIn: 'root'
})
export class RespuestaService {
  private baseUrl = environment.url + 'api/respuestas';
  private respuestasSubject = new BehaviorSubject<Respuesta[]>([]);
  public respuestas$ = this.respuestasSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Obtiene todas las respuestas
   */
  getAllRespuestas(): Observable<GenericResponse<Respuesta[]>> {
    return this.http.get<GenericResponse<Respuesta[]>>(this.baseUrl).pipe(
      map(response => {
        if (response.data) {
          this.respuestasSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene las respuestas de una pregunta
   */
  getRespuestasByPreguntaId(preguntaId: number): Observable<GenericResponse<Respuesta[]>> {
    return this.http.get<GenericResponse<Respuesta[]>>(`${this.baseUrl}/pregunta/${preguntaId}`).pipe(
      map(response => {
        if (response.data) {
          this.respuestasSubject.next(response.data);
        }
        return response;
      })
    );
  }

  /**
   * Obtiene una respuesta por su ID
   */
  getRespuestaById(id: number): Observable<GenericResponse<Respuesta>> {
    return this.http.get<GenericResponse<Respuesta>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Crea una nueva respuesta
   */
  createRespuesta(respuesta: RespuestaCreateRequest): Observable<GenericResponse<Respuesta>> {
    return this.http.post<GenericResponse<Respuesta>>(this.baseUrl, respuesta);
  }

  /**
   * Actualiza una respuesta existente
   */
  updateRespuesta(id: number, respuesta: RespuestaUpdateRequest): Observable<GenericResponse<Respuesta>> {
    return this.http.put<GenericResponse<Respuesta>>(`${this.baseUrl}/${id}`, respuesta);
  }

  /**
   * Elimina una respuesta
   */
  deleteRespuesta(id: number): Observable<GenericResponse<any>> {
    return this.http.delete<GenericResponse<any>>(`${this.baseUrl}/${id}`);
  }
}
