<div class="modulo-form-container" [ngClass]="{'dark-theme': isDarkTheme}">
  <h3>{{ isEditMode ? 'Editar Módulo' : 'Nuevo Módulo' }}</h3>

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="mb-4">
      <label for="titulo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">T<PERSON><PERSON><PERSON> del Módulo *</label>
      <input type="text" id="titulo" formControlName="titulo"
             class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
             placeholder="Ingrese el título del módulo">
      <div *ngIf="form.get('titulo')?.hasError('required') && form.get('titulo')?.touched"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título del módulo es obligatorio
      </div>
      <div *ngIf="form.get('titulo')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        El título no debe exceder los 100 caracteres
      </div>
    </div>

    <div class="mb-4">
      <label for="descripcion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Descripción</label>
      <textarea id="descripcion" formControlName="descripcion" rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                placeholder="Ingrese una descripción del módulo"></textarea>
      <div *ngIf="form.get('descripcion')?.hasError('maxlength')"
           class="mt-1 text-sm text-red-600 dark:text-red-400">
        La descripción no debe exceder los 500 caracteres
      </div>
    </div>

    <div class="form-row">
      <div>
        <label for="orden" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Orden</label>
        <input type="number" id="orden" formControlName="orden" min="0"
               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
        <div *ngIf="form.get('orden')?.hasError('min')"
             class="mt-1 text-sm text-red-600 dark:text-red-400">
          El orden debe ser un número positivo
        </div>
      </div>

      <div>
        <label for="estado" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estado</label>
        <select id="estado" formControlName="estado"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
          <option value="A">Activo</option>
          <option value="I">Inactivo</option>
        </select>
      </div>
    </div>

    <div *ngIf="error" class="error-message">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>{{ error }}</span>
    </div>

    <div class="form-actions">
      <button type="button" (click)="onCancel()"
              class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm">
        Cancelar
      </button>
      <button type="submit" [disabled]="form.invalid || loading"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 border border-transparent rounded-md shadow-sm focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed">
        <svg *ngIf="!loading" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 10-2 0v5.586l-1.293-1.293z" />
          <path d="M5 18a1 1 0 01-.707-1.707l7-7a1 1 0 111.414 1.414l-7 7A1 1 0 015 18z" />
        </svg>
        <svg *ngIf="loading" class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>{{ isEditMode ? 'Actualizar' : 'Guardar' }}</span>
      </button>
    </div>
  </form>
</div>
