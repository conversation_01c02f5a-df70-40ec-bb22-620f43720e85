<!-- src/app/pages/faq/components/card-v1/card-v1.component.html -->
<div class="faq-card bg-white dark:bg-slate-900 rounded-lg shadow-sm hover:shadow-md p-6 relative border border-gray-200 dark:border-slate-700 transition-all duration-200">


  <!-- menú de opciones con diseño mejorado usando Tailwind y mejor responsive -->
  <div class="sm:absolute sm:top-4 sm:right-4 z-10 mb-4 sm:mb-0" *ngIf="showOption">
    <div class="flex flex-wrap gap-2 justify-end">
      <!-- Bo<PERSON><PERSON> de responder (solo para ADMIN o PROGRAMADOR o el usuario que creó la pregunta, y solo si está abierta) -->
      <button class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded border border-green-500 text-green-600 bg-white hover:bg-green-50 dark:bg-slate-800 dark:text-green-400 dark:border-green-600 dark:hover:bg-slate-700"
              *ngIf="(canRespond || isCreator || isResponder) && (data.estado !== 'CERRADA')"
              (click)="changeOption('responder')">
        <i class="mdi mdi-message-reply-text mr-1"></i> Responder
      </button>

      <!-- Botón de editar -->
      <button class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded border border-blue-500 text-blue-600 bg-white hover:bg-blue-50 dark:bg-slate-800 dark:text-blue-400 dark:border-blue-600 dark:hover:bg-slate-700"
              (click)="changeOption('editar')">
        <i class="mdi mdi-pencil mr-1"></i> Editar
      </button>

      <!-- Botón de eliminar (solo para ADMIN) -->
      <button class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded border border-red-500 text-red-600 bg-white hover:bg-red-50 dark:bg-slate-800 dark:text-red-400 dark:border-red-600 dark:hover:bg-slate-700"
              *ngIf="currentUserRole === 'ADMIN'"
              (click)="changeOption('eliminar')">
        <i class="mdi mdi-delete mr-1"></i> Eliminar
      </button>

      <!-- Botones para cambiar estado (solo para ADMIN, PROGRAMADOR, o los usuarios involucrados) -->
      <button class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded border border-amber-500 text-amber-600 bg-white hover:bg-amber-50 dark:bg-slate-800 dark:text-amber-400 dark:border-amber-600 dark:hover:bg-slate-700"
              *ngIf="(canRespond || isCreator || isResponder) && data.estado === 'ABIERTA'"
              (click)="changeOption('cerrar')">
        <i class="mdi mdi-lock mr-1"></i> Cerrar
      </button>

      <button class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded border border-blue-500 text-blue-600 bg-white hover:bg-blue-50 dark:bg-slate-800 dark:text-blue-400 dark:border-blue-600 dark:hover:bg-slate-700"
              *ngIf="(canRespond || isCreator || isResponder) && data.estado === 'CERRADA'"
              (click)="changeOption('abrir')">
        <i class="mdi mdi-lock-open mr-1"></i> Abrir
      </button>
    </div>
  </div>

  <!-- pregunta / respuesta con diseño mejorado usando Tailwind y mejor responsive -->
  <div class="sm:pr-8 mt-2 sm:mt-0">
    <h5 class="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3" [innerHTML]="data.pregunta | highlight:searchText"></h5>
    <p class="text-base text-gray-700 dark:text-gray-300 mb-4 leading-relaxed" [innerHTML]="data.respuesta | highlight:searchText"></p>

    <!-- Información adicional con diseño mejorado usando Tailwind -->
    <div class="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <!-- Estado de la pregunta (abierta/cerrada) -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium"
            [ngClass]="{'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300': data.estado === 'ABIERTA',
                       'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300': data.estado === 'CERRADA'}">
        <i class="mdi mr-1" [ngClass]="{'mdi-lock-open-variant': data.estado === 'ABIERTA', 'mdi-lock': data.estado === 'CERRADA'}"></i>
        {{ data.estado === 'CERRADA' ? 'Cerrada' : 'Abierta' }}
      </span>

      <!-- Estado de respondida -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium"
            [ngClass]="{'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300': data.respondida,
                       'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300': !data.respondida}">
        <i class="mdi mr-1" [ngClass]="{'mdi-check-circle': data.respondida, 'mdi-clock-outline': !data.respondida}"></i>
        {{ data.respondida ? 'Respondida' : 'Pendiente' }}
      </span>

      <!-- Categoría -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300" *ngIf="data.categoria">
        <i class="mdi mdi-tag mr-1"></i> {{ data.categoria || 'Sin categoría' }}
      </span>

      <!-- Tipo de usuario -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300" *ngIf="data.tipoUsuario">
        <i class="mdi mdi-account mr-1"></i> {{ data.tipoUsuario }}
      </span>

      <!-- Información de creación -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
            *ngIf="data.usuarioPreguntaNombre || (data.usuarioPregunta && isUserObject(data.usuarioPregunta))">
        <i class="mdi mdi-account-edit mr-1"></i>
        Creado por: {{ getUserCreatorName() }}
        <span *ngIf="getUserCreatorRole()" class="ml-1">({{ getUserCreatorRole() }})</span>
      </span>

      <!-- Información de respuesta -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
            *ngIf="data.usuarioRespuestaNombre || (data.usuarioRespuesta && isUserObject(data.usuarioRespuesta))">
        <i class="mdi mdi-account-check mr-1"></i>
        Respondido por: {{ getUserResponderName() }}
        <span *ngIf="getUserResponderRole()" class="ml-1">({{ getUserResponderRole() }})</span>
      </span>

      <!-- Fecha de creación -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
            *ngIf="data.createdAt">
        <i class="mdi mdi-calendar mr-1"></i> {{ getFormattedDate(data.createdAt) }}
      </span>

      <!-- Contador de respuestas adicionales -->
      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
            *ngIf="data.respuestas && data.respuestas.length > 0">
        <i class="mdi mdi-message-text-outline mr-1"></i> {{ data.respuestas.length }} respuesta{{ data.respuestas.length !== 1 ? 's' : '' }}
      </span>
    </div>
  </div>

  <!-- Mensaje de archivo adjuntado para usuarios sin permisos (clickeable) -->
  <div class="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md flex items-center gap-2 cursor-pointer hover:bg-blue-100 transition-colors dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30"
       *ngIf="hasAttachedFiles && !canViewFiles"
       (click)="openFirstAttachment()">
    <i class="mdi mdi-attachment text-lg"></i> <span>Archivo adjuntado - Click para ver/descargar</span>
  </div>

  <!-- Mensaje de archivo adjuntado para usuarios con permisos (clickeable) -->
  <div class="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md flex items-center gap-2 cursor-pointer hover:bg-blue-100 transition-colors dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30"
       *ngIf="hasAttachedFiles && canViewFiles && !activeFilter"
       (click)="openFirstAttachment()">
    <i class="mdi mdi-attachment text-lg"></i> <span>Archivo adjuntado - Click para ver/descargar</span>
  </div>

  <!-- Sección de archivos solo visible para ADMIN o PROGRAMADOR -->
  <ng-container *ngIf="hasAttachedFiles && canViewFiles">
    <!-- Filtros por tipo de archivo -->
    <div class="flex flex-wrap gap-2 mt-4 mb-3" *ngIf="countedFileByType | keyvalue as kv">
      <button
        *ngFor="let kvp of kv"
        type="button"
        class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full transition-colors"
        [ngClass]="{
          'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50': kvp.key==='video' && activeFilter!==kvp.key,
          'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50': kvp.key==='image' && activeFilter!==kvp.key,
          'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:hover:bg-yellow-900/50': kvp.key==='audio' && activeFilter!==kvp.key,
          'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50': kvp.key==='document' && activeFilter!==kvp.key,
          'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600': kvp.key==='other' && activeFilter!==kvp.key,
          'bg-blue-600 text-white': kvp.key==='video' && activeFilter===kvp.key,
          'bg-green-600 text-white': kvp.key==='image' && activeFilter===kvp.key,
          'bg-yellow-600 text-white': kvp.key==='audio' && activeFilter===kvp.key,
          'bg-red-600 text-white': kvp.key==='document' && activeFilter===kvp.key,
          'bg-gray-600 text-white': kvp.key==='other' && activeFilter===kvp.key
        }"
        (click)="filterByType(kvp.key)">
        {{ kvp.key | titlecase }}
        <span class="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-white/20 text-white">{{ kvp.value }}</span>
      </button>
    </div>

    <!-- Grid de archivos filtrados -->
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mt-4" *ngIf="activeFilter">
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 cursor-pointer hover:shadow-md transition-all hover:-translate-y-1 dark:bg-gray-800 dark:border-gray-700"
           *ngFor="let f of filesFilter; index as i"
           (click)="showFileLightBox(i)">
        <div class="flex flex-col items-center p-3">
          <!-- Icono según tipo de archivo -->
          <i class="text-3xl mb-2"
             [ngClass]="{
               'mdi mdi-file-document text-red-500 dark:text-red-400': f.type && (f.type.includes('document') || f.type.includes('pdf')),
               'mdi mdi-file-image text-green-500 dark:text-green-400': f.type && f.type.includes('image'),
               'mdi mdi-file-video text-blue-500 dark:text-blue-400': f.type && f.type.includes('video'),
               'mdi mdi-file-music text-yellow-500 dark:text-yellow-400': f.type && f.type.includes('audio'),
               'mdi mdi-file text-gray-500 dark:text-gray-400': !f.type || (f.type && !f.type.includes('document') && !f.type.includes('image') && !f.type.includes('video') && !f.type.includes('audio'))
             }"></i>

          <!-- Nombre del archivo -->
          <div class="text-sm font-medium text-center text-gray-800 dark:text-gray-200 line-clamp-2">{{ f.name }}</div>

          <!-- Tamaño del archivo -->
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1" *ngIf="f.size">{{ formatFileSize(f.size) }}</div>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Componente de respuestas múltiples -->
  <app-faq-respuestas
    *ngIf="data.respondida && (canRespond || isCreator || isResponder)"
    [faq]="data"
    (respuestaAgregada)="onRespuestaAgregada($event)"
  ></app-faq-respuestas>

</div>
