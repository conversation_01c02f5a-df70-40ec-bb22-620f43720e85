import { Pipe, PipeTransform } from '@angular/core';
import { DateUtils } from '@app/utils/date-utils';

@Pipe({
  name: 'dateArray',
})
export class DateArrayPipe implements PipeTransform {
  transform(value: string | number[]): Date | null {
    if (!value) return null;

    if (typeof value === 'string') {
      try {
        // Si es un string, intentar parsearlo como array
        const dateArray = value.split(',').map((num) => parseInt(num, 10));
        if (dateArray.length >= 7) {
          // Usar DateUtils para convertir el array a fecha
          return DateUtils.dateTimeArrayToDate(
            [dateArray[0], dateArray[1], dateArray[2]], // fecha
            [dateArray[3], dateArray[4], dateArray[5]] // hora, minuto, segundo
          );
        }
      } catch (e) {
        console.error('Error al convertir fecha:', e);
        return null;
      }
    } else if (Array.isArray(value) && value.length >= 7) {
      // Usar DateUtils para convertir el array a fecha
      return DateUtils.dateTimeArrayToDate(
        [value[0], value[1], value[2]], // fecha
        [value[3], value[4], value[5]] // hora, minuto, segundo
      );
    }

    return null;
  }
}
