import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LeccionService } from '@app/services/leccion.service';
import { Leccion, LeccionCreateRequest, LeccionUpdateRequest, TipoLeccion } from '@app/models/backend/curso/leccion.model';
import { Modulo } from '@app/models/backend/curso/modulo.model';
import { Seccion } from '@app/models/backend/curso/seccion.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { MatDialog } from '@angular/material/dialog';
import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { CuestionarioService } from '@app/services/cuestionario.service';
import { CuestionarioFormComponent } from '../cuestionario-form/cuestionario-form.component';

@Component({
  selector: 'app-leccion-form',
  templateUrl: './leccion-form.component.html'
})
export class LeccionFormComponent implements OnInit, OnDestroy {
  @Input() modulo!: Modulo;
  @Input() seccion!: Seccion;
  @Input() leccion: Leccion | null = null;
  @Input() isDarkTheme: boolean = false;
  @Output() saved = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();

  form!: FormGroup;
  loading: boolean = false;
  error: string | null = null;
  isEditMode: boolean = false;
  videoFile: File | null = null;
  videoPreviewUrl: string | null = null;
  subtitlesFile: File | null = null;
  subtitlesUrl: string | null = null;
  pdfFiles: File[] = [];
  pdfUrls: string[] = [];

  // Enum para acceder desde el template
  TipoLeccion = TipoLeccion;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private leccionService: LeccionService,
    private notification: NotificationService,
    private dialog: MatDialog,
    private cuestionarioService: CuestionarioService
  ) { }

  ngOnInit(): void {
    this.isEditMode = !!this.leccion;
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.group({
      nombre: [this.leccion?.titulo || this.leccion?.nombre || '', [Validators.required, Validators.maxLength(100)]],
      descripcion: [this.leccion?.descripcion || '', [Validators.maxLength(500)]],
      tipoLeccion: [this.leccion?.tipoLeccion || TipoLeccion.VIDEO],
      videoUrl: [this.leccion?.videoUrl || ''],
      subtitlesUrl: [this.leccion?.subtitlesUrl || ''],
      pdfUrl: [this.leccion?.pdfUrl || ''],
      duracion: [this.leccion?.duracion || 0, [Validators.min(0)]],
      orden: [this.leccion?.orden || 0, [Validators.min(0)]],
      estado: [this.leccion?.estado || 'A']
    });

    if (this.leccion?.videoUrl) {
      this.videoPreviewUrl = this.leccion.videoUrl;
    }

    if (this.leccion?.subtitlesUrl) {
      this.subtitlesUrl = this.leccion.subtitlesUrl;
    }

    if (this.leccion?.pdfUrl) {
      // Si hay múltiples URLs separadas por comas, las dividimos
      this.pdfUrls = this.leccion.pdfUrl.split(',').filter(url => url.trim() !== '');
    }
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;

    if (this.isEditMode) {
      this.updateLeccion();
    } else {
      this.createLeccion();
    }
  }

  private createLeccion(): void {
    const leccionData: LeccionCreateRequest = {
      titulo: this.form.value.nombre,  // Cambiado de nombre a titulo para coincidir con el backend
      descripcion: this.form.value.descripcion,
      tipoLeccion: this.form.value.tipoLeccion,
      videoUrl: this.form.value.videoUrl,
      subtitlesUrl: this.form.value.subtitlesUrl,
      pdfUrl: this.form.value.pdfUrl,
      duracion: this.form.value.duracion,
      orden: this.form.value.orden,
      seccionId: this.seccion.id,
      estado: this.form.value.estado
    };

    this.leccionService.createLeccion(leccionData, this.videoFile || undefined, this.subtitlesFile || undefined, this.pdfFiles.length > 0 ? this.pdfFiles : undefined)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Lección creada exitosamente');
            this.saved.emit();
          } else {
            this.error = response.msg || 'Error al crear la lección';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al crear la lección. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al crear lección:', error);
        }
      });
  }

  private updateLeccion(): void {
    if (!this.leccion) {
      return;
    }

    const leccionData: LeccionUpdateRequest = {
      titulo: this.form.value.nombre,  // Cambiado de nombre a titulo para coincidir con el backend
      descripcion: this.form.value.descripcion,
      tipoLeccion: this.form.value.tipoLeccion,
      videoUrl: this.form.value.videoUrl,
      subtitlesUrl: this.form.value.subtitlesUrl,
      pdfUrl: this.form.value.pdfUrl,
      duracion: this.form.value.duracion,
      orden: this.form.value.orden,
      estado: this.form.value.estado
    };

    // Asegurarnos de que el campo tipoLeccion se establezca correctamente
    console.log('Tipo de lección seleccionado:', this.form.value.tipoLeccion);

    this.leccionService.updateLeccion(this.leccion.id, leccionData, this.videoFile || undefined, this.subtitlesFile || undefined, this.pdfFiles.length > 0 ? this.pdfFiles : undefined)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Lección actualizada exitosamente');
            this.saved.emit();
          } else {
            this.error = response.msg || 'Error al actualizar la lección';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al actualizar la lección. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al actualizar lección:', error);
        }
      });
  }

  openFilesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe({
      next: (url) => {
        if (url) {
          this.form.patchValue({
            videoUrl: url
          });
          this.videoPreviewUrl = url;
          this.videoFile = null;
        }
      }
    });
  }

  openSubtitlesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false,
        acceptTypes: '.vtt,.srt,.txt'
      }
    });

    dialogRef.afterClosed().subscribe({
      next: (url) => {
        if (url) {
          this.form.patchValue({
            subtitlesUrl: url
          });
          this.subtitlesUrl = url;
          this.subtitlesFile = null;
          this.notification.success('Subtítulos cargados correctamente desde Firebase.');
        }
      }
    });
  }

  /**
   * Método para seleccionar un archivo de video local (comentado porque ahora solo usamos Firebase)
   */
  /*
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Verificar que sea un archivo de video
      if (!file.type.startsWith('video/')) {
        this.notification.error('Por favor, seleccione un archivo de video válido');
        return;
      }

      this.videoFile = file;

      // Crear una URL para previsualizar el video
      this.videoPreviewUrl = URL.createObjectURL(file);

      // Limpiar la URL del video existente
      this.form.patchValue({
        videoUrl: ''
      });
    }
  }
  */

  /**
   * Método para seleccionar un archivo de subtítulos local (comentado porque ahora solo usamos Firebase)
   */
  /*
  onSubtitlesFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Verificar que sea un archivo de subtítulos (txt o vtt)
      if (!file.name.endsWith('.txt') && !file.name.endsWith('.vtt') && !file.name.endsWith('.srt')) {
        this.notification.error('Por favor, seleccione un archivo de subtítulos válido (.txt, .vtt o .srt)');
        return;
      }

      this.subtitlesFile = file;

      // Crear una URL para previsualizar los subtítulos (solo para la interfaz de usuario)
      this.subtitlesUrl = URL.createObjectURL(file);

      // No guardamos la URL de tipo blob en el formulario, ya que será reemplazada por la URL de Firebase
      // Limpiamos cualquier URL anterior para evitar confusiones
      this.form.patchValue({
        subtitlesUrl: ''
      });

      this.notification.success('Archivo de subtítulos cargado correctamente. Se subirá a Firebase al guardar.');
    }
  }
  */

  removeSelectedVideo(): void {
    this.videoFile = null;
    this.videoPreviewUrl = null;
    this.form.patchValue({
      videoUrl: ''
    });
  }

  removeSelectedSubtitles(): void {
    this.subtitlesFile = null;
    this.subtitlesUrl = null;
    this.form.patchValue({
      subtitlesUrl: ''
    });
  }

  openPdfUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: true, // Permitir selección múltiple
        crop: false,
        acceptTypes: '.pdf'
      }
    });

    dialogRef.afterClosed().subscribe({
      next: (urls) => {
        if (urls && urls.length) {
          // Si es un array de URLs (múltiples PDFs)
          if (Array.isArray(urls)) {
            // Añadir las nuevas URLs a las existentes
            this.pdfUrls = [...this.pdfUrls, ...urls];
            // Actualizar el campo del formulario con todas las URLs separadas por comas
            this.form.patchValue({
              pdfUrl: this.pdfUrls.join(',')
            });
            this.notification.success(`${urls.length} PDFs cargados correctamente desde Firebase.`);
          } else {
            // Si es una sola URL (para compatibilidad)
            this.pdfUrls.push(urls);
            this.form.patchValue({
              pdfUrl: this.pdfUrls.join(',')
            });
            this.notification.success('PDF cargado correctamente desde Firebase.');
          }
          // Limpiar los archivos locales
          this.pdfFiles = [];
        }
      }
    });
  }

  removeSelectedPdf(index: number): void {
    // Eliminar la URL específica
    if (index >= 0 && index < this.pdfUrls.length) {
      this.pdfUrls.splice(index, 1);
      // Actualizar el campo del formulario
      this.form.patchValue({
        pdfUrl: this.pdfUrls.join(',')
      });
    }
  }

  removeAllPdfs(): void {
    this.pdfFiles = [];
    this.pdfUrls = [];
    this.form.patchValue({
      pdfUrl: ''
    });
  }

  onCancel(): void {
    this.cancelled.emit();
  }

  /**
   * Abre el diálogo para gestionar el cuestionario asociado a la lección
   */
  gestionarCuestionario(): void {
    if (!this.leccion || !this.leccion.id) {
      this.notification.error('Primero debe guardar la lección para poder gestionar el cuestionario');
      return;
    }

    // Verificar si ya existe un cuestionario para esta lección
    this.cuestionarioService.getCuestionarioByLeccionId(this.leccion.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          let cuestionarioId: number | undefined;

          if (response.rpta === 1 && response.data) {
            cuestionarioId = response.data.id;
          }

          // Abrir el diálogo para crear o editar el cuestionario
          const dialogRef = this.dialog.open(CuestionarioFormComponent, {
            width: '900px',
            data: {
              leccionId: this.leccion!.id,
              cuestionarioId: cuestionarioId
            },
            disableClose: true
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              this.notification.success('Cuestionario guardado exitosamente');
            }
          });
        },
        error: (error) => {
          this.notification.error('Error al verificar el cuestionario existente');
          console.error('Error al verificar cuestionario:', error);
        }
      });
  }
}
