import { User } from '../user';
import { PreguntaEncuesta } from './pregunta-encuesta.model';

/**
 * Enum que define los tipos de asignación de encuestas
 */
export enum TipoAsignacion {
  TODOS = 'TODOS',          // Encuesta para todos los usuarios
  SEDE = 'SEDE',           // Encuesta para usuarios de una sede específica
  COORDINACION = 'COORDINACION',   // Encuesta para usuarios de un coordinador específico
  PERSONAL = 'PERSONAL'        // Encuesta para un usuario específico
}

/**
 * Modelo que representa una encuesta
 */
export interface Encuesta {
  id: number;
  titulo: string;
  descripcion: string;
  fechaInicio: string;
  fechaFin: string;
  tiempoLimite: number; // Tiempo límite en minutos (null = sin límite)
  esAnonima: boolean; // Indica si la encuesta es anónima
  mostrarResultados: boolean; // Indica si se muestran los resultados al finalizar
  tipoAsignacion: TipoAsignacion; // Tipo de asignación de la encuesta
  
  // Información de sede (si aplica)
  sedeId?: number;
  sedeNombre?: string;
  
  // Información de coordinador (si aplica)
  coordinadorId?: number;
  coordinadorNombre?: string;
  
  // Información de usuario específico (si aplica)
  usuarioId?: number;
  usuarioNombre?: string;
  
  // Información del creador
  creador?: User;
  
  // Lista de preguntas
  preguntas?: PreguntaEncuesta[];
  
  // Estadísticas
  totalRespuestas?: number;
  respuestasCompletadas?: number;
  
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
}

/**
 * Modelo para crear una nueva encuesta
 */
export interface EncuestaCreateRequest {
  titulo: string;
  descripcion?: string;
  fechaInicio?: string;
  fechaFin?: string;
  tiempoLimite?: number;
  esAnonima?: boolean;
  mostrarResultados?: boolean;
  tipoAsignacion: TipoAsignacion;
  sedeId?: number;
  coordinadorId?: number;
  usuarioId?: number;
  preguntas?: PreguntaEncuestaCreateRequest[];
}

/**
 * Modelo para actualizar una encuesta existente
 */
export interface EncuestaUpdateRequest {
  titulo?: string;
  descripcion?: string;
  fechaInicio?: string;
  fechaFin?: string;
  tiempoLimite?: number;
  esAnonima?: boolean;
  mostrarResultados?: boolean;
  tipoAsignacion?: TipoAsignacion;
  sedeId?: number;
  coordinadorId?: number;
  usuarioId?: number;
  estado?: string;
}

/**
 * Modelo para crear una nueva pregunta de encuesta
 */
export interface PreguntaEncuestaCreateRequest {
  enunciado: string;
  descripcion?: string;
  orden?: number;
  tipo: string;
  esObligatoria?: boolean;
  encuestaId?: number;
  opciones?: OpcionRespuestaEncuestaCreateRequest[];
}

/**
 * Modelo para crear una nueva opción de respuesta
 */
export interface OpcionRespuestaEncuestaCreateRequest {
  texto: string;
  orden?: number;
  valor?: number;
  preguntaId?: number;
}
