import {
  NgModule,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { FlexLayoutModule } from '@angular/flex-layout';

// Material
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatNativeDateModule } from '@angular/material/core';

// Componentes
import { EncuestasRoutingModule } from './encuestas-routing.module';
import { EncuestasPageComponent } from './pages/encuestas-page/encuestas-page.component';
import { EncuestaFormComponent } from './components/encuesta-form/encuesta-form.component';
import { EncuestaDetalleComponent } from './components/encuesta-detalle/encuesta-detalle.component';
import { ResponderEncuestaComponent } from './components/responder-encuesta/responder-encuesta.component';
import { ResultadosEncuestaComponent } from './components/resultados-encuesta/resultados-encuesta.component';
import { PreguntaFormComponent } from './components/pregunta-form/pregunta-form.component';

// Shared Modules
import { IndicatorsModule } from '@app/shared/indicators';
import { PopupsModule } from '@app/shared/popups';
import { NotificationModule } from '@app/services';

@NgModule({
  declarations: [
    EncuestasPageComponent,
    EncuestaFormComponent,
    EncuestaDetalleComponent,
    ResponderEncuestaComponent,
    ResultadosEncuestaComponent,
    PreguntaFormComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    FlexLayoutModule,
    EncuestasRoutingModule,

    // Shared Modules
    IndicatorsModule,
    PopupsModule,
    NotificationModule.forRoot(),

    // Material
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatListModule,
    MatMenuModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatRadioModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatSortModule,
    MatTableModule,
    MatTabsModule,
    MatTooltipModule,
    MatNativeDateModule,
  ],
  exports: [EncuestasPageComponent],
  schemas: [
    // Esto permite usar elementos personalizados y atributos desconocidos
    // Útil durante el desarrollo para evitar errores de compilación
    CUSTOM_ELEMENTS_SCHEMA,
    NO_ERRORS_SCHEMA,
  ],
})
export class EncuestasModule {}
