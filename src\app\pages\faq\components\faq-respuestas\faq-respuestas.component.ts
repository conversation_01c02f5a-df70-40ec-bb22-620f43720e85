import { Component, Input, OnInit, OnChanges, SimpleChanges, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Faq } from '@app/models/backend/faq/faq.model';
import { FaqRespuesta } from '@app/models/backend/faq/faq-respuesta.model';
import { FaqService } from '@app/services/faq/faq.service';
import { Subject } from 'rxjs';
import { takeUntil, filter } from 'rxjs/operators';

@Component({
  selector: 'app-faq-respuestas',
  templateUrl: './faq-respuestas.component.html',
  styleUrls: ['./faq-respuestas.component.scss']
})
export class FaqRespuestasComponent implements OnInit, OnChanges, OnDestroy {
  @Input() faq!: Faq;
  @Output() respuestaAgregada = new EventEmitter<FaqRespuesta>();

  respuestas: FaqRespuesta[] = [];
  respuestaForm: FormGroup;

  // Variables para controlar permisos
  currentUserRole: string = '';
  currentUserId: number | null = null;
  isCreator: boolean = false;
  isResponder: boolean = false;
  canRespond: boolean = false;

  loading: boolean = false;
  error: string | null = null;

  // Para manejar la limpieza de suscripciones
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private faqService: FaqService
  ) {
    this.respuestaForm = this.fb.group({
      contenido: ['', [Validators.required, Validators.minLength(3)]]
    });
  }

  ngOnInit(): void {
    this.getCurrentUserInfo();
    this.cargarRespuestasRealtime();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['faq'] && !changes['faq'].firstChange) {
      this.cargarRespuestasRealtime();
      this.checkUserPermissions();
    }
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones al destruir el componente
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Obtiene la información del usuario actual desde localStorage
   */
  getCurrentUserInfo(): void {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        this.currentUserRole = user.role || '';
        this.currentUserId = user.id || null;

        // Verificar permisos
        this.checkUserPermissions();
      }
    } catch (error) {
      console.error('Error al obtener información del usuario:', error);
    }
  }

  /**
   * Verifica los permisos del usuario actual
   */
  checkUserPermissions(): void {
    if (!this.currentUserId || !this.faq) return;

    // Verificar si el usuario puede responder (ADMIN o PROGRAMADOR)
    this.canRespond = this.currentUserRole === 'ADMIN' || this.currentUserRole === 'PROGRAMADOR';

    // Verificar si el usuario es el creador de la pregunta
    if (this.faq.usuarioPreguntaId === this.currentUserId) {
      this.isCreator = true;
    } else if (this.faq.usuarioPregunta && this.faq.usuarioPregunta.id === this.currentUserId) {
      this.isCreator = true;
    } else {
      this.isCreator = false;
    }

    // Verificar si el usuario es quien respondió la pregunta
    if (this.faq.usuarioRespuestaId === this.currentUserId) {
      this.isResponder = true;
    } else if (this.faq.usuarioRespuesta && this.faq.usuarioRespuesta.id === this.currentUserId) {
      this.isResponder = true;
    } else {
      this.isResponder = false;
    }
  }

  /**
   * Carga las respuestas de la pregunta (método original)
   */
  cargarRespuestas(): void {
    if (!this.faq || !this.faq.id) return;

    this.loading = true;
    this.error = null;

    this.faqService.getRespuestasByFaqId(this.faq.id).subscribe({
      next: (respuestas) => {
        this.respuestas = respuestas;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar respuestas:', error);
        this.error = 'Error al cargar las respuestas. Por favor, intente nuevamente.';
        this.loading = false;
      }
    });
  }

  /**
   * Carga las respuestas de la pregunta con actualizaciones en tiempo real
   */
  cargarRespuestasRealtime(): void {
    if (!this.faq || !this.faq.id) return;

    this.loading = true;
    this.error = null;

    // Cancelar suscripciones anteriores
    this.destroy$.next();



    // Primero cargar las respuestas iniciales
    this.faqService.getRespuestasByFaqId(this.faq.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (respuestasIniciales) => {
          this.respuestas = respuestasIniciales;
          this.loading = false;

          // Luego suscribirse a las actualizaciones en tiempo real
          this.suscribirseActualizacionesRespuestas();
        },
        error: (error) => {
          console.error('Error al cargar respuestas iniciales:', error);
          this.error = 'Error al cargar las respuestas. Por favor, intente nuevamente.';
          this.loading = false;
        }
      });
  }

  /**
   * Suscribe a las actualizaciones en tiempo real de las respuestas
   */
  private suscribirseActualizacionesRespuestas(): void {
    if (!this.faq || !this.faq.id) return;



    // Suscribirse a las actualizaciones de respuestas
    this.faqService.getRespuestasUpdates()
      .pipe(
        takeUntil(this.destroy$),
        // Filtrar solo las actualizaciones para esta FAQ
        filter((update: {faqId: number, respuestas: FaqRespuesta[]}) => update.faqId === this.faq.id)
      )
      .subscribe({
        next: (update: {faqId: number, respuestas: FaqRespuesta[]}) => {
          // Actualizar las respuestas
          this.respuestas = update.respuestas;
        },
        error: (error) => {
          console.error(`FaqRespuestasComponent - Error al recibir actualizaciones para FAQ ${this.faq.id}:`, error);
        }
      });
  }

  /**
   * Agrega una nueva respuesta
   */
  agregarRespuesta(): void {
    if (this.respuestaForm.invalid) {
      this.respuestaForm.markAllAsTouched();
      return;
    }

    if (!this.faq || !this.faq.id) return;

    // Verificar si el usuario puede agregar respuestas
    if (!this.canRespond && !this.isCreator && !this.isResponder) {
      this.error = 'No tiene permisos para agregar respuestas.';
      return;
    }

    // Verificar si la pregunta está cerrada
    if (this.faq.estado === 'CERRADA') {
      this.error = 'No se pueden agregar respuestas a una pregunta cerrada.';
      return;
    }

    const nuevaRespuesta: FaqRespuesta = {
      contenido: this.respuestaForm.value.contenido,
      faqId: this.faq.id,
      archivos: []
    };

    this.loading = true;
    this.error = null;

    this.faqService.agregarRespuesta(this.faq.id, nuevaRespuesta).subscribe({
      next: (respuesta) => {
        // Limpiar el formulario
        this.respuestaForm.reset();
        this.loading = false;

        // Emitir evento para notificar al componente padre
        this.respuestaAgregada.emit(respuesta);

        // Ya no necesitamos recargar manualmente las respuestas
        // porque se actualizarán automáticamente a través de WebSocket
      },
      error: (error) => {
        console.error('Error al agregar respuesta:', error);
        this.error = 'Error al agregar la respuesta. Por favor, intente nuevamente.';
        this.loading = false;
      }
    });
  }

  /**
   * Formatea la fecha para mostrarla
   */
  formatDate(date: string | any[] | undefined): string {
    if (!date) return '';

    try {
      // Si la fecha es un array (formato del backend [año, mes, día, hora, minuto, segundo, nanosegundo])
      if (Array.isArray(date) && date.length >= 6) {
        const [year, month, day, hour, minute] = date;
        // Crear fecha con formato: YYYY-MM-DDTHH:MM:SS
        const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`;
        const dateObj = new Date(dateStr);
        return dateObj.toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      // Si la fecha es un string
      if (typeof date === 'string') {
        const dateObj = new Date(date);
        return dateObj.toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      return 'Fecha no válida';
    } catch (error) {
      console.error('Error al formatear fecha:', error, date);
      return 'Fecha no disponible';
    }
  }
}
