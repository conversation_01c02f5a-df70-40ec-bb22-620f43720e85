<div class="bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-2xl mx-auto">
  <!-- Título compacto -->
  <div class="bg-blue-100 dark:bg-blue-900 text-gray-800 dark:text-white px-6 py-3 rounded-t-xl border-b border-gray-200 dark:border-gray-700">
    <h2 class="text-lg font-semibold">{{ title }}</h2>
  </div>

  <div class="px-6 pt-4 pb-6 sm:px-8">
    <form [formGroup]="sedeForm" (ngSubmit)="onSubmit()" class="space-y-4">
      <!-- Nombre -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nombre</label>
        <input
          type="text"
          formControlName="nombre"
          placeholder="Nombre de la sede"
          class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
        />
        <p *ngIf="sedeForm.get('nombre')?.hasError('required') && sedeForm.get('nombre')?.touched" class="text-sm text-red-600 dark:text-red-400 mt-1">
          El nombre es obligatorio
        </p>
      </div>

      <!-- Dirección -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dirección</label>
        <input
          type="text"
          formControlName="direccion"
          placeholder="Dirección de la sede"
          class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
        />
        <p *ngIf="sedeForm.get('direccion')?.hasError('required') && sedeForm.get('direccion')?.touched" class="text-sm text-red-600 dark:text-red-400 mt-1">
          La dirección es obligatoria
        </p>
      </div>

      <!-- Ciudad -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ciudad</label>
        <input
          type="text"
          formControlName="ciudad"
          placeholder="Ciudad"
          class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
        />
        <p *ngIf="sedeForm.get('ciudad')?.hasError('required') && sedeForm.get('ciudad')?.touched" class="text-sm text-red-600 dark:text-red-400 mt-1">
          La ciudad es obligatoria
        </p>
      </div>

      <!-- Provincia y Código Postal -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Provincia</label>
          <input
            type="text"
            formControlName="provincia"
            placeholder="Provincia"
            class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Código Postal</label>
          <input
            type="text"
            formControlName="codigoPostal"
            placeholder="Código Postal"
            class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          />
        </div>
      </div>

      <!-- Teléfono y Email -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Teléfono</label>
          <input
            type="text"
            formControlName="telefono"
            placeholder="Teléfono"
            class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          />
          <p *ngIf="sedeForm.get('telefono')?.hasError('pattern') && sedeForm.get('telefono')?.touched" class="text-sm text-red-600 dark:text-red-400 mt-1">
            El teléfono debe contener solo números
          </p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
          <input
            type="email"
            formControlName="email"
            placeholder="Email"
            class="w-full h-11 px-4 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          />
          <p *ngIf="sedeForm.get('email')?.hasError('email') && sedeForm.get('email')?.touched" class="text-sm text-red-600 dark:text-red-400 mt-1">
            Formato de email inválido
          </p>
        </div>
      </div>

      <!-- Estado -->
      <div class="flex items-center mt-2">
        <input
          type="checkbox"
          formControlName="activo"
          id="activo"
          class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
        />
        <label for="activo" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Sede activa</label>
      </div>

      <!-- Botones de acción -->
      <div class="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
        <button
          type="button"
          (click)="onCancel()"
          [disabled]="loading"
          class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          Cancelar
        </button>
        <button
          type="submit"
          [disabled]="sedeForm.invalid || loading"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 border border-transparent rounded-md shadow-sm hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ loading ? 'Guardando...' : 'Guardar' }}
        </button>
      </div>
    </form>
  </div>
</div>
