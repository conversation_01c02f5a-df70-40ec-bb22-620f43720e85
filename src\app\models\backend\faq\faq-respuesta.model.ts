// app/models/backend/faq/faq-respuesta.model.ts

import { FileFaqRespuesta } from "./file-faq-respuesta.model";
import { User } from "../user";

/**
 * Modelo que representa una respuesta a una pregunta FAQ
 */
export interface FaqRespuesta {
  id?: number;
  contenido: string;
  
  // FAQ al que pertenece esta respuesta
  faqId?: number;
  
  // Usuario que crea la respuesta
  usuario?: User | null;  // Objeto User completo del backend
  usuarioId?: number;     // ID del usuario para enviar al backend
  usuarioNombre?: string; // Nombre del usuario para mostrar en la UI
  
  // Archivos adjuntos
  archivos?: FileFaqRespuesta[];
  
  // Fechas
  createdAt?: string;   // ISO‑8601 recibido del backend
  updatedAt?: string;
}
