import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NotificationReadUser } from '@app/models/notification-read-user.model';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';

export interface NotificationReadersModalData {
  notificationId: number;
  notificationTitle: string;
}

@Component({
  selector: 'app-notification-readers-modal',
  templateUrl: './notification-readers-modal.component.html',
  styleUrls: ['./notification-readers-modal.component.css']
})
export class NotificationReadersModalComponent implements OnInit {
  readers: NotificationReadUser[] = [];
  loading = true;
  error = false;

  constructor(
    public dialogRef: MatDialogRef<NotificationReadersModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NotificationReadersModalData,
    private notificacionesService: NotificacionesWsService
  ) {}

  ngOnInit(): void {
    this.loadReaders();
  }

  private loadReaders(): void {
    this.loading = true;
    this.error = false;

    this.notificacionesService
      .obtenerUsuariosQueHanLeido(this.data.notificationId)
      .subscribe({
        next: (readers) => {
          this.readers = readers;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error al cargar usuarios que han leído:', error);
          this.error = true;
          this.loading = false;
        },
      });
  }

  /**
   * Obtiene el nombre completo del usuario
   */
  getNombreCompleto(reader: NotificationReadUser): string {
    if (reader.nombre && reader.apellido) {
      return `${reader.nombre} ${reader.apellido}`;
    } else if (reader.nombre) {
      return reader.nombre;
    } else if (reader.apellido) {
      return reader.apellido;
    } else {
      return reader.userName;
    }
  }

  /**
   * Obtiene las iniciales del usuario para el avatar
   */
  getInitials(reader: NotificationReadUser): string {
    const nombreCompleto = this.getNombreCompleto(reader);
    const words = nombreCompleto.trim().split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return nombreCompleto.substring(0, 2).toUpperCase();
  }

  /**
   * Genera URL del avatar
   */
  getAvatarUrl(reader: NotificationReadUser): string {
    const initials = this.getInitials(reader);
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      initials
    )}&background=random&color=fff&size=40`;
  }

  /**
   * Formatea la fecha de lectura
   */
  formatReadTime(readAt: string): string {
    const date = new Date(readAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) {
      return 'Ahora mismo';
    } else if (diffMinutes < 60) {
      return `Hace ${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''}`;
    } else if (diffMinutes < 1440) {
      const hours = Math.floor(diffMinutes / 60);
      return `Hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMinutes / 1440);
      return `Hace ${days} día${days > 1 ? 's' : ''}`;
    }
  }

  /**
   * Cierra el modal
   */
  onClose(): void {
    this.dialogRef.close();
  }

  /**
   * Reintenta cargar los datos
   */
  retry(): void {
    this.loadReaders();
  }
}
