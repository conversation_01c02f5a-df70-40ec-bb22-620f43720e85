<div
  class="certificados-admin-container p-6 bg-gray-50 dark:bg-gray-900 min-h-screen"
>
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
      Gestión de Certificados
    </h1>
    <p class="text-gray-600 dark:text-gray-400">
      Administra los certificados de cursos completados
    </p>
  </div>

  <!-- Sección de creación de certificados -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
      Crear Nuevos Certificados
    </h2>

    <!-- Selector de curso -->
    <div class="mb-4">
      <label
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        Seleccionar Curso
      </label>
      <select
        [(ngModel)]="selectedCurso"
        (ngModelChange)="onCursoSeleccionado($event)"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        [disabled]="loadingCursos"
      >
        <option [ngValue]="null">Seleccione un curso...</option>
        <option *ngFor="let curso of cursos" [ngValue]="curso">
          {{ curso.nombre }}
        </option>
      </select>
    </div>

    <!-- Lista de usuarios elegibles -->
    <div *ngIf="selectedCurso && usuariosElegibles.length > 0" class="mt-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Usuarios Elegibles para Certificado
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          *ngFor="let usuario of usuariosElegibles"
          class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
        >
          <div class="flex items-center justify-between mb-3">
            <h4 class="font-medium text-gray-900 dark:text-white">
              {{ usuario.nombre }} {{ usuario.apellido }}
            </h4>
            <span
              class="px-2 py-1 text-xs rounded-full"
              [ngClass]="{
                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                  usuario.puedeRecibirCertificado && !usuario.tieneCertificado,
                'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200':
                  usuario.tieneCertificado,
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200':
                  !usuario.puedeRecibirCertificado
              }"
            >
              <span *ngIf="usuario.tieneCertificado">Ya tiene certificado</span>
              <span
                *ngIf="
                  !usuario.tieneCertificado && usuario.puedeRecibirCertificado
                "
                >Elegible</span
              >
              <span *ngIf="!usuario.puedeRecibirCertificado">Incompleto</span>
            </span>
          </div>

          <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
            <p>DNI: {{ usuario.dni }}</p>
            <p>Email: {{ usuario.email }}</p>
            <p>
              Progreso: {{ usuario.porcentajeCompletado }}% ({{
                usuario.leccionesCompletadas
              }}/{{ usuario.totalLecciones }})
            </p>
          </div>

          <button
            *ngIf="usuario.puedeRecibirCertificado && !usuario.tieneCertificado"
            (click)="crearCertificado(usuario)"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
          >
            Crear Certificado
          </button>

          <button
            *ngIf="!usuario.puedeRecibirCertificado"
            disabled
            class="w-full bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed"
          >
            Curso Incompleto
          </button>

          <button
            *ngIf="usuario.tieneCertificado"
            disabled
            class="w-full bg-green-300 dark:bg-green-600 text-green-700 dark:text-green-200 font-medium py-2 px-4 rounded-md cursor-not-allowed"
          >
            Certificado Emitido
          </button>
        </div>
      </div>
    </div>

    <div *ngIf="selectedCurso && usuariosElegibles.length === 0" class="mt-6">
      <p class="text-gray-600 dark:text-gray-400 text-center py-8">
        No hay usuarios elegibles para certificado en este curso.
      </p>
    </div>
  </div>

  <!-- Sección de certificados existentes -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <div
      class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6"
    >
      <h2
        class="text-xl font-semibold text-gray-900 dark:text-white mb-4 sm:mb-0"
      >
        Certificados Emitidos
      </h2>

      <!-- Filtros -->
      <div class="flex flex-col sm:flex-row gap-4">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (keyup.enter)="aplicarFiltros()"
          placeholder="Buscar por nombre, curso o código..."
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />

        <select
          [(ngModel)]="selectedEstado"
          (ngModelChange)="aplicarFiltros()"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        >
          <option *ngFor="let estado of estados" [value]="estado.value">
            {{ estado.label }}
          </option>
        </select>

        <button
          (click)="limpiarFiltros()"
          class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200"
        >
          Limpiar
        </button>
      </div>
    </div>

    <!-- Tabla de certificados -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Código
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Usuario
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Curso
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Fecha Emisión
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Horas
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Emitido Por
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Estado
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Acciones
            </th>
          </tr>
        </thead>
        <tbody
          class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
        >
          <tr
            *ngFor="let certificado of dataSource.data"
            class="hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <td
              class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ certificado.codigoCertificado }}
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
            >
              {{ certificado.usuarioNombre }} {{ certificado.usuarioApellido }}
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
            >
              {{ certificado.cursoNombre }}
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
            >
              {{
                getFechaEmision(certificado.fechaEmision) | date : "dd/MM/yyyy"
              }}
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
            >
              {{ certificado.horasCurso }}h
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
            >
              {{ certificado.emitidoPorNombre }}
              {{ certificado.emitidoPorApellido }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="px-2 py-1 text-xs rounded-full"
                [ngClass]="{
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                    certificado.estado === 'A',
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200':
                    certificado.estado === 'I'
                }"
              >
                {{ certificado.estado === "A" ? "Activo" : "Inactivo" }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  (click)="verCertificado(certificado)"
                  class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  title="Ver certificado"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                <button
                  (click)="descargarCertificado(certificado)"
                  class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                  title="Descargar certificado"
                >
                  <mat-icon>download</mat-icon>
                </button>
                <button
                  (click)="eliminarCertificado(certificado)"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  title="Eliminar certificado"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div class="mt-6">
      <mat-paginator
        [length]="totalElements"
        [pageSize]="pageSize"
        [pageIndex]="pageIndex"
        [pageSizeOptions]="pageSizeOptions"
        [showFirstLastButtons]="true"
        (page)="onPageChange($event)"
        class="border-t border-gray-200 dark:border-gray-700"
      >
      </mat-paginator>
    </div>

    <!-- Loading state -->
    <div *ngIf="loading" class="flex justify-center items-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
      ></div>
      <span class="ml-2 text-gray-600 dark:text-gray-400"
        >Cargando certificados...</span
      >
    </div>

    <!-- Empty state -->
    <div
      *ngIf="!loading && dataSource.data.length === 0"
      class="text-center py-8"
    >
      <mat-icon class="text-gray-400 text-6xl mb-4">description</mat-icon>
      <p class="text-gray-600 dark:text-gray-400 text-lg">
        No se encontraron certificados
      </p>
      <p class="text-gray-500 dark:text-gray-500 text-sm">
        Los certificados aparecerán aquí una vez que sean creados
      </p>
    </div>
  </div>
</div>
