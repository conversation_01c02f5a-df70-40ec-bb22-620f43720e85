<div class="flex flex-col w-screen h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-white overflow-hidden" @fadeIn id="map-dialog-container">
  <!-- Cabecera del diálogo -->
  <div class="flex justify-between items-center px-6 py-4 bg-blue-600 dark:bg-blue-900 border-b border-blue-400/20 dark:border-blue-700/30 z-10">
    <h2 class="text-xl font-semibold text-white flex items-center">
      <mat-icon class="mr-2">map</mat-icon>
      {{ mapType === 'leaflet' ? 'Mapa Leaflet' : 'Mapa Mapbox' }}
    </h2>
    <button
      mat-icon-button
      (click)="onClose()"
      class="text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/30 rounded-full transition-colors duration-200"
    >
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Contenedor del mapa -->
  <div class="flex-1 relative overflow-hidden flex flex-col">
    <!-- Spinner de carga principal del diálogo -->
    <div *ngIf="loading" class="absolute inset-0 flex flex-col justify-center items-center bg-black/70 z-10">
      <mat-spinner diameter="48" color="accent"></mat-spinner>
      <p class="mt-3 text-sm font-medium text-white">Inicializando mapa...</p>
    </div>

    <!-- Contenedor para el mapa Leaflet -->
    <div
      *ngIf="mapType === 'leaflet'"
      class="w-full h-[calc(100vh-64px)] relative overflow-hidden flex flex-col"
      id="dialog-map-container-leaflet"
    >
      <!-- El ID del contenedor debe coincidir con el mapContainerId -->
      <div id="dialog-map-leaflet" class="w-full h-full absolute inset-0 z-[1] bg-gray-200 dark:bg-gray-800 rounded overflow-hidden"></div>
      <app-mapa-tipificacion [queryParams]="queryParams" [inDialog]="true" [mapContainerId]="'dialog-map-leaflet'"></app-mapa-tipificacion>
    </div>

    <!-- Contenedor para el mapa Mapbox -->
    <div
      *ngIf="mapType === 'mapbox'"
      class="w-full h-[calc(100vh-64px)] relative overflow-hidden flex flex-col"
      id="dialog-map-container-mapbox"
    >
      <!-- El ID del contenedor debe coincidir con el mapContainerId -->
      <div id="dialog-map-mapbox" class="w-full h-full absolute inset-0 z-[1] bg-gray-200 dark:bg-gray-800 rounded overflow-hidden"></div>
      <app-mapa-tipificacion-mapbox [queryParams]="queryParams" [inDialog]="true" [mapContainerId]="'dialog-map-mapbox'"></app-mapa-tipificacion-mapbox>
    </div>
  </div>

  <!-- Panel flotante de edición - Más visible y con borde destacado -->
  <div class="fixed top-20 right-4 z-[1000] w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border-2 border-blue-500 dark:border-blue-400 overflow-hidden transition-all duration-300"
       [ngClass]="{'translate-x-0': showEditPanel, 'translate-x-full': !showEditPanel}"
       (click)="onEditPanelClick($event)">
    <div class="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 border-b border-blue-400 dark:border-blue-600 flex justify-between items-center panel-header">
      <h3 class="text-base font-medium text-white flex items-center">
        <mat-icon class="mr-2 text-white">edit_location</mat-icon>
        Editar dirección
      </h3>
      <button mat-icon-button (click)="toggleEditPanel()" class="text-white hover:text-gray-200 focus:outline-none">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="p-4 max-h-[70vh] overflow-y-auto">
      <!-- Campos de dirección -->
      <div class="space-y-3">
        <!-- Provincia -->
        <div class="flex flex-col w-full relative">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Provincia</label>
          <div class="relative">
            <input
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              [(ngModel)]="editAddress.provincia"
              placeholder="Provincia"
              (focus)="onProvinciaFocus()"
              (input)="onProvinciaInput($event)"
            />
            <div *ngIf="cargandoProvincias" class="absolute right-2 top-2">
              <mat-spinner diameter="20" color="accent"></mat-spinner>
            </div>
          </div>

          <!-- Dropdown de provincias -->
          <div *ngIf="mostrarProvincias" class="absolute top-full left-0 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
            <div class="sticky top-0 flex justify-between items-center p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Provincias</span>
              <button
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
                (click)="mostrarProvincias = false"
              >
                <mat-icon style="font-size: 18px; height: 18px; width: 18px;">close</mat-icon>
              </button>
            </div>
            <div *ngIf="provinciasCatastro.length === 0 && !cargandoProvincias" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
              No se encontraron provincias
            </div>
            <div *ngFor="let provincia of provinciasCatastro"
                 class="p-2 text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-0"
                 (click)="seleccionarProvincia(provincia)">
              {{ provincia.Nombre || provincia.Denominacion }}
            </div>
          </div>
        </div>

        <!-- Municipio -->
        <div class="flex flex-col w-full relative">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Municipio</label>
          <div class="relative">
            <input
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              [(ngModel)]="editAddress.municipio"
              placeholder="Municipio"
              (focus)="onMunicipioFocus()"
              (input)="onMunicipioInput($event)"
              [disabled]="!selectedProvinciaCatastro"
            />
            <div *ngIf="cargandoMunicipios" class="absolute right-2 top-2">
              <mat-spinner diameter="20" color="accent"></mat-spinner>
            </div>
          </div>

          <!-- Dropdown de municipios -->
          <div *ngIf="mostrarMunicipios" class="absolute top-full left-0 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
            <div class="sticky top-0 flex justify-between items-center p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Municipios</span>
              <button
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
                (click)="mostrarMunicipios = false"
              >
                <mat-icon style="font-size: 18px; height: 18px; width: 18px;">close</mat-icon>
              </button>
            </div>
            <div *ngIf="municipiosCatastro.length === 0 && !cargandoMunicipios" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
              No se encontraron municipios
            </div>
            <div *ngFor="let municipio of municipiosCatastro"
                 class="p-2 text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-0"
                 (click)="seleccionarMunicipio(municipio)">
              {{ municipio.Nombre || municipio.Denominacion }}
            </div>
          </div>
        </div>

        <!-- Vía -->
        <div class="flex flex-col w-full relative">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Vía</label>
          <div class="relative via-input">
            <input
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              [(ngModel)]="editAddress.via"
              placeholder="Vía"
              (focus)="onViaFocus()"
              (input)="onViaInput($event)"
              [disabled]="!selectedMunicipioCatastro"
            />
            <div *ngIf="cargandoVias" class="absolute right-2 top-2">
              <mat-spinner diameter="20" color="accent"></mat-spinner>
            </div>
          </div>

          <!-- Dropdown de vías -->
          <div *ngIf="mostrarVias" class="absolute top-full left-0 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
            <div class="sticky top-0 flex justify-between items-center p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Vías</span>
              <button
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
                (click)="mostrarVias = false"
              >
                <mat-icon style="font-size: 18px; height: 18px; width: 18px;">close</mat-icon>
              </button>
            </div>
            <div *ngIf="viasCatastro.length === 0 && !cargandoVias" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
              No se encontraron vías
            </div>
            <div *ngFor="let via of viasCatastro"
                 class="p-2 text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-0"
                 (click)="seleccionarVia(via)">
              {{ via.Nombre || via.DenominacionCompleta }}
            </div>
          </div>
        </div>

        <!-- Número -->
        <div class="flex flex-col w-full">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Número</label>
          <input
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
            [(ngModel)]="editAddress.numero"
            placeholder="Número"
          />
        </div>

        <!-- Código Postal -->
        <div class="flex flex-col w-full">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Código Postal</label>
          <input
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
            [(ngModel)]="editAddress.codigoPostal"
            placeholder="Código Postal"
          />
        </div>

        <!-- Detalles adicionales -->
        <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Detalles adicionales</h4>

          <div class="grid grid-cols-2 gap-3">
            <!-- Bloque -->
            <div class="flex flex-col w-full">
              <label class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Bloque</label>
              <input
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="editAddress.bloque"
                placeholder="Bloque"
              />
            </div>

            <!-- Escalera -->
            <div class="flex flex-col w-full">
              <label class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Escalera</label>
              <input
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="editAddress.escalera"
                placeholder="Escalera"
              />
            </div>

            <!-- Planta -->
            <div class="flex flex-col w-full">
              <label class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Planta</label>
              <input
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="editAddress.planta"
                placeholder="Planta"
              />
            </div>

            <!-- Puerta -->
            <div class="flex flex-col w-full">
              <label class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Puerta</label>
              <input
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                [(ngModel)]="editAddress.puerta"
                placeholder="Puerta"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Botones de acción -->
      <div class="mt-4 flex justify-between">
        <button
          mat-stroked-button
          color="warn"
          (click)="cancelEdit()"
          class="px-3 py-1 text-sm border border-red-300 dark:border-red-700 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
        >
          Cancelar
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="applyEdit()"
          class="px-4 py-2 text-sm bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-md shadow-md transform hover:scale-105 transition-all duration-200"
        >
          <mat-icon class="mr-1">search</mat-icon>
          Buscar dirección
        </button>
      </div>
    </div>
  </div>

  <!-- Panel lateral para editar dirección - Mejorado y más visible -->
  <div
    *ngIf="!showEditPanel"
    class="fixed top-1/2 right-0 transform -translate-y-1/2 z-[999] cursor-pointer"
  >
    <!-- Área sensible ampliada para mostrar el panel -->
    <div
      class="absolute top-0 right-0 h-full w-16 hover-area"
      (mouseenter)="showHoverPanel = true"
      (mouseleave)="showHoverPanel = false"
      (click)="showEditPanel = true"
    ></div>

    <!-- Panel de control mejorado -->
    <div
      class="w-16 h-auto py-6 bg-gradient-to-b from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 rounded-l-lg shadow-xl overflow-hidden transition-all duration-200 flex flex-col items-center justify-center gap-6 border-t-2 border-l-2 border-b-2 border-blue-400 dark:border-blue-500"
      [class.translate-x-0]="showHoverPanel"
      [class.translate-x-10]="!showHoverPanel"
      style="transition: transform 0.3s ease-in-out;"
      (click)="showEditPanel = true"
    >
      <!-- Botón para editar dirección - Más grande y llamativo -->
      <button
        class="w-12 h-12 flex items-center justify-center rounded-full bg-white/30 text-white hover:bg-white/40 focus:outline-none transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
        matTooltip="Editar dirección"
        matTooltipPosition="left"
      >
        <mat-icon style="font-size: 24px; width: 24px; height: 24px;">edit_location</mat-icon>
      </button>

      <!-- Indicador de acción mejorado -->
      <div class="flex flex-col items-center">
        <mat-icon class="text-white animate-bounce">keyboard_arrow_left</mat-icon>
        <span class="text-sm text-white font-medium mt-1 rotate-90">EDITAR</span>
      </div>
    </div>
  </div>

  <!-- Botón flotante para volver -->
  <button
    class="fixed bottom-6 right-6 z-[1000] w-14 h-14 flex items-center justify-center rounded-full bg-blue-600 dark:bg-blue-800 text-white shadow-lg hover:bg-blue-700 dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors duration-200"
    (click)="onClose()"
    matTooltip="Volver al formulario"
  >
    <mat-icon>arrow_back</mat-icon>
  </button>
</div>

<!-- Estilos globales para asegurar que los mapas se muestren correctamente -->
<style>
  /* Estilos para el área de hover y el panel */
  .hover-area {
    background-color: transparent;
    transition: all 0.3s ease;
  }

  /* Animación de rebote para el icono de flecha */
  @keyframes bounce {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-3px); }
  }

  .animate-bounce {
    animation: bounce 1.5s ease-in-out infinite;
  }

  /* Asegurarse de que los mapas ocupen todo el espacio disponible */
  #dialog-map-leaflet,
  #dialog-map-mapbox,
  #mapa-tipificacion-leaflet,
  #mapa-tipificacion-mapbox {
    height: 100% !important;
    width: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1 !important;
  }

  /* Hacer que las tarjetas de material sean transparentes */
  .mat-card {
    background-color: transparent !important;
    box-shadow: none !important;
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .mat-card-content {
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .mat-card-header {
    display: none !important;
  }

  .mat-card-actions {
    display: none !important;
  }

  /* Asegurarse de que los contenedores de mapbox-gl sean visibles */
  .mapboxgl-map {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1 !important;
  }

  /* Asegurarse de que los contenedores de leaflet sean visibles */
  .leaflet-container {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1 !important;
  }

  /* Asegurarse de que los controles de los mapas sean visibles */
  .mapboxgl-ctrl-top-right,
  .mapboxgl-ctrl-top-left,
  .leaflet-control-container {
    z-index: 10 !important;
  }

  /* Asegurarse de que los popups sean visibles */
  .mapboxgl-popup,
  .leaflet-popup {
    z-index: 20 !important;
  }

  /* Estilos para popups en modo oscuro */
  .leaflet-popup-dark-theme .leaflet-popup-content-wrapper {
    background-color: #1f2937 !important;
    color: #f0f0f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.6) !important;
  }

  .leaflet-popup-dark-theme .leaflet-popup-tip {
    background-color: #1f2937 !important;
  }

  .leaflet-popup-dark-theme a {
    color: #60a5fa !important;
  }

  /* Estilos para popups de Mapbox en modo oscuro */
  .mapbox-popup-dark-mode .mapboxgl-popup-content {
    background-color: #1f2937 !important;
    color: #f0f0f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.6) !important;
  }

  .mapbox-popup-dark-mode .mapboxgl-popup-tip {
    border-top-color: #1f2937 !important;
    border-bottom-color: #1f2937 !important;
  }

  .mapbox-popup-dark-mode a {
    color: #60a5fa !important;
  }

  /* Estilos para controles de Leaflet en modo oscuro */
  .dark-theme .leaflet-control-zoom a {
    background-color: #1f2937 !important;
    color: #f0f0f0 !important;
    border-color: #374151 !important;
  }

  .dark-theme .leaflet-control-zoom a:hover {
    background-color: #374151 !important;
  }

  /* Estilos para controles de Mapbox en modo oscuro */
  .dark-theme .mapboxgl-ctrl button {
    background-color: #1f2937 !important;
    color: #f0f0f0 !important;
  }

  .dark-theme .mapboxgl-ctrl button:hover {
    background-color: #374151 !important;
  }

  .dark-theme .mapboxgl-ctrl button .mapboxgl-ctrl-icon {
    filter: invert(1) !important;
  }
</style>
