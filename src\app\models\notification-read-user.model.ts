/**
 * Modelo para representar información de usuarios que han leído una notificación
 */
export interface NotificationReadUser {
  userId: number;
  userName: string;
  userEmail: string;
  nombre: string;
  apellido: string;
  sedeNombre: string;
  sedeId: number;
  readAt: string; // Fecha ISO
}

/**
 * Respuesta del backend para usuarios que han leído una notificación
 */
export interface NotificationReadersResponse {
  readers: NotificationReadUser[];
  readCount: number;
  notificationId: number;
}
