import { Component, OnInit, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { interval, Subscription } from 'rxjs';
import { environment } from '@src/environments/environment';
import { NotificationService } from '@app/services';

@Component({
  selector: 'app-database-status',
  templateUrl: './database-status.component.html',
  styleUrls: ['./database-status.component.scss']
})
export class DatabaseStatusComponent implements OnInit, OnDestroy {
  status: 'online' | 'offline' | 'checking' = 'checking';
  lastChecked: Date | null = null;
  private checkInterval: Subscription | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(
    private http: HttpClient,
    private notification: NotificationService
  ) {}

  ngOnInit(): void {
    // Comprobar el estado de la base de datos cada 30 segundos
    this.checkInterval = interval(30000).subscribe(() => {
      this.checkDatabaseStatus();
    });

    // Comprobar inmediatamente al iniciar
    this.checkDatabaseStatus();
  }

  ngOnDestroy(): void {
    if (this.checkInterval) {
      this.checkInterval.unsubscribe();
    }
  }

  /**
   * Comprueba el estado de la conexión a la base de datos
   */
  private checkDatabaseStatus(): void {
    this.status = 'checking';
    
    // Realizar una solicitud simple para verificar la conexión
    this.http.get(`${environment.url}api/health/database`, { responseType: 'text' })
      .subscribe({
        next: (response) => {
          if (response === 'OK') {
            this.status = 'online';
            this.reconnectAttempts = 0;
          } else {
            this.status = 'offline';
            this.handleConnectionError('La base de datos no está respondiendo correctamente');
          }
          this.lastChecked = new Date();
        },
        error: (error) => {
          this.status = 'offline';
          this.handleConnectionError('Error de conexión a la base de datos');
          this.lastChecked = new Date();
        }
      });
  }

  /**
   * Maneja los errores de conexión a la base de datos
   */
  private handleConnectionError(message: string): void {
    this.reconnectAttempts++;
    
    if (this.reconnectAttempts === 1) {
      // Solo mostrar la notificación en el primer intento
      this.notification.error(`${message}. Intentando reconectar...`);
    }
    
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      // Si se alcanza el número máximo de intentos, mostrar un mensaje más grave
      this.notification.error('No se ha podido conectar a la base de datos después de varios intentos. Por favor, recargue la página o contacte con soporte técnico.');
      
      // Detener los intentos de reconexión
      if (this.checkInterval) {
        this.checkInterval.unsubscribe();
        this.checkInterval = null;
      }
    }
  }

  /**
   * Fuerza una comprobación manual del estado de la base de datos
   */
  forceCheck(): void {
    this.checkDatabaseStatus();
  }
}
