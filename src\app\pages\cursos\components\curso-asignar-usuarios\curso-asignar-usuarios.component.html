<div
  class="w-full h-[80vh] flex flex-col bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-hidden"
>
  <!-- Header -->
  <div
    class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700"
  >
    <div class="flex items-center justify-between">
      <h2
        mat-dialog-title
        class="text-xl font-semibold text-gray-900 dark:text-white flex items-center"
      >
        <mat-icon class="mr-3 text-blue-600 dark:text-blue-400"
          >group_add</mat-icon
        >
        Asignar Usuarios al Curso
      </h2>
      <div class="text-sm text-gray-600 dark:text-gray-300">
        <span class="font-medium">{{ curso.nombre }}</span>
      </div>
    </div>
  </div>

  <!-- Search and Stats -->
  <div
    class="px-6 py-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
  >
    <div
      class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between"
    >
      <!-- Search -->
      <div class="flex-1 max-w-md">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Buscar usuarios</mat-label>
          <input
            matInput
            [formControl]="searchControl"
            placeholder="Buscar por nombre, username, DNI, email o rol"
          />
          <mat-icon
            *ngIf="!searchLoading && !searchControl.value"
            matSuffix
            class="text-gray-500 dark:text-gray-400"
            >search</mat-icon
          >
          <mat-spinner
            *ngIf="searchLoading"
            matSuffix
            diameter="20"
          ></mat-spinner>
          <button
            *ngIf="searchControl.value && !searchLoading"
            matSuffix
            mat-icon-button
            aria-label="Limpiar"
            (click)="clearSearch()"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <!-- Stats -->
      <div class="flex gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {{ usuariosSeleccionados.length }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            Seleccionados
          </div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">
            {{ totalItems }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Total</div>
        </div>
      </div>
    </div>
  </div>

  <mat-dialog-content class="flex-1 overflow-hidden p-0">
    <!-- Loading State -->
    <div
      *ngIf="loading && !searchLoading"
      class="flex flex-col items-center justify-center h-64 bg-white dark:bg-gray-900"
    >
      <mat-spinner diameter="40"></mat-spinner>
      <p class="mt-4 text-sm text-gray-600 dark:text-gray-400 font-medium">
        Cargando usuarios...
      </p>
    </div>

    <!-- Error State -->
    <div
      *ngIf="error"
      class="flex items-center justify-center p-8 bg-red-50 dark:bg-red-900/30 m-4 rounded-lg border border-red-200 dark:border-red-700"
    >
      <mat-icon class="text-red-600 dark:text-red-400 mr-3">error</mat-icon>
      <span class="text-sm text-red-800 dark:text-red-200 font-medium">{{
        error
      }}</span>
    </div>

    <!-- Search Results Info -->
    <div
      *ngIf="searchTerm && filteredUsuarios.length > 0"
      class="px-4 py-2 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-700"
    >
      <div class="flex items-center">
        <mat-icon class="text-blue-600 dark:text-blue-400 mr-2 text-sm"
          >info</mat-icon
        >
        <span class="text-sm text-blue-800 dark:text-blue-200">
          Mostrando {{ filteredUsuarios.length }} resultados para "{{
            searchTerm
          }}"
        </span>
      </div>
    </div>

    <!-- No Results -->
    <div
      *ngIf="
        searchTerm &&
        filteredUsuarios.length === 0 &&
        !searchLoading &&
        !loading
      "
      class="flex flex-col items-center justify-center h-64 bg-white dark:bg-gray-900"
    >
      <mat-icon class="text-gray-400 mb-3 text-4xl">search_off</mat-icon>
      <span class="text-gray-600 dark:text-gray-400 mb-4 text-center">
        No se encontraron usuarios para "{{ searchTerm }}"
      </span>
      <button mat-raised-button color="primary" (click)="clearSearch()">
        Limpiar búsqueda
      </button>
    </div>

    <!-- Empty State -->
    <div
      *ngIf="filteredUsuarios.length === 0 && !searchTerm && !loading"
      class="flex flex-col items-center justify-center h-64 bg-white dark:bg-gray-900"
    >
      <mat-icon class="text-gray-400 mb-3 text-4xl">people_outline</mat-icon>
      <span class="text-gray-600 dark:text-gray-400 mb-4 text-center">
        No hay usuarios disponibles
      </span>
      <button mat-raised-button color="primary" (click)="loadUsuarios()">
        Cargar usuarios
      </button>
    </div>

    <!-- Users Table -->
    <div *ngIf="filteredUsuarios.length > 0 && !loading" class="overflow-auto">
      <table
        mat-table
        [dataSource]="filteredUsuarios"
        class="w-full bg-white dark:bg-gray-900"
      >
        <!-- Selection Column -->
        <ng-container matColumnDef="select">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="w-12 px-4 py-3 bg-gray-50 dark:bg-gray-800"
          >
            <mat-checkbox
              (change)="$event ? masterToggle() : null"
              [checked]="selection.hasValue() && isAllSelected()"
              [indeterminate]="selection.hasValue() && !isAllSelected()"
              class="text-blue-600"
            >
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let usuario" class="w-12 px-4 py-3">
            <mat-checkbox
              (click)="$event.stopPropagation()"
              (change)="$event ? selection.toggle(usuario.id) : null"
              [checked]="selection.isSelected(usuario.id)"
              class="text-blue-600"
            >
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Avatar and Name Column -->
        <ng-container matColumnDef="usuario">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium"
          >
            Usuario
          </th>
          <td mat-cell *matCellDef="let usuario" class="px-4 py-3">
            <div class="flex items-center">
              <div class="mr-3 flex-shrink-0">
                <div
                  class="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-sm"
                  [ngStyle]="{
                    'background-color': getAvatarColor(usuario.username)
                  }"
                >
                  {{ getInitials(usuario.nombre, usuario.apellido) }}
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <div class="font-medium text-gray-900 dark:text-white truncate">
                  {{ usuario.nombre || "" }} {{ usuario.apellido || "" }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ usuario.username }}
                </div>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Contact Info Column -->
        <ng-container matColumnDef="contacto">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium"
          >
            Contacto
          </th>
          <td mat-cell *matCellDef="let usuario" class="px-4 py-3">
            <div class="space-y-1">
              <div class="text-sm text-gray-900 dark:text-white truncate">
                {{ usuario.email }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                DNI: {{ usuario.dni }}
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Role Column -->
        <ng-container matColumnDef="rol">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium"
          >
            Rol
          </th>
          <td mat-cell *matCellDef="let usuario" class="px-4 py-3">
            <span
              class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
              [ngClass]="{
                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200':
                  usuario.role?.toLowerCase() === 'admin',
                'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200':
                  usuario.role?.toLowerCase() === 'coordinador',
                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                  usuario.role?.toLowerCase() === 'asesor',
                'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200':
                  usuario.role?.toLowerCase() === 'auditor',
                'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200':
                  usuario.role?.toLowerCase() === 'programador',
                'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200':
                  [
                    'backoffice',
                    'backofficeseguimiento',
                    'backofficetramitador'
                  ].includes(usuario.role?.toLowerCase() || ''),
                'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200':
                  !usuario.role
              }"
            >
              {{ usuario.role || "Sin rol" }}
            </span>
          </td>
        </ng-container>

        <!-- Sede Column -->
        <ng-container matColumnDef="sede">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium"
          >
            Sede
          </th>
          <td mat-cell *matCellDef="let usuario" class="px-4 py-3">
            <span class="text-sm text-gray-900 dark:text-white">
              {{ usuario.sede || "-" }}
            </span>
          </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="estado">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium"
          >
            Estado
          </th>
          <td mat-cell *matCellDef="let usuario" class="px-4 py-3">
            <div class="flex items-center">
              <mat-icon
                *ngIf="isUsuarioAsignado(usuario.id)"
                class="text-green-500 dark:text-green-400 mr-1 text-sm"
              >
                check_circle
              </mat-icon>
              <span
                class="text-sm"
                [ngClass]="{
                  'text-green-600 dark:text-green-400 font-medium':
                    isUsuarioAsignado(usuario.id),
                  'text-gray-500 dark:text-gray-400': !isUsuarioAsignado(
                    usuario.id
                  )
                }"
              >
                {{ isUsuarioAsignado(usuario.id) ? "Asignado" : "Disponible" }}
              </span>
            </div>
          </td>
        </ng-container>

        <!-- Table Header and Rows -->
        <tr
          mat-header-row
          *matHeaderRowDef="displayedColumns"
          class="border-b border-gray-200 dark:border-gray-700"
        ></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
          (click)="toggleUsuarioSelection(row.id)"
        ></tr>
      </table>
    </div>

    <!-- Paginador -->
    <div
      *ngIf="!loading && filteredUsuarios.length > 0"
      class="flex justify-center p-4 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700"
    >
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        [pageIndex]="currentPage"
        (page)="onPageChange($event)"
        showFirstLastButtons
        class="bg-transparent"
      >
      </mat-paginator>
    </div>
  </mat-dialog-content>

  <div
    class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex justify-between items-center"
  >
    <!-- Selection Summary -->
    <div class="text-sm text-gray-600 dark:text-gray-400">
      <span class="font-medium">{{ usuariosSeleccionados.length }}</span>
      usuarios seleccionados
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-3">
      <button mat-button (click)="onCancel()" [disabled]="loading">
        Cancelar
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="onSubmit()"
        [disabled]="loading || usuariosSeleccionados.length === 0"
        class="flex items-center"
      >
        <mat-icon class="mr-2" *ngIf="!loading">save</mat-icon>
        <mat-spinner diameter="16" class="mr-2" *ngIf="loading"></mat-spinner>
        <span *ngIf="!loading">Asignar Usuarios</span>
        <span *ngIf="loading">Asignando...</span>
      </button>
    </div>
  </div>
</div>
