.container {
  padding: 20px;
  width: 100%;
  max-width: 100%;

  mat-card {
    margin-bottom: 20px;
     box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    width: 100%;

    mat-card-header {
      padding: 1.5rem;
      
      mat-card-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-buttons {
    display: flex;
    gap: 16px;
  }
}

.mat-elevation-z8 {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
}

.mat-table {
  width: 100%;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
  margin-top: 1.5rem;
  .mat-header-row {
    background-color: #1E3A8A;
    color: white;
    height: 60px;
  }

  .mat-header-cell {
    color: #fff;
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    padding: 0 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .mat-cell {
    text-align: center;
    padding: 1rem;
    font-size: 0.9rem;
    color: #4a5568;
  }

  .mat-row {
    height: 60px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #f8fafc;
    }
  }
}

// Estilos para los chips de estado
.mat-chip-list {
  .mat-chip {
    min-width: 100px;
    justify-content: center;
    font-size: 0.8rem;
    height: 28px;
    
    &.mat-primary {
      background-color: #4CAF50 !important;
    }
    
    &.mat-warn {
      background-color: #f44336 !important;
    }
  }
}

// Estilos para los botones de acción
.mat-cell button.mat-icon-button {
  width: 36px;
  height: 36px;
  line-height: 36px;
  margin: 0 4px;
  
  .mat-icon {
    font-size: 20px;
    line-height: 20px;
    height: 20px;
    width: 20px;
  }
}

// Estilos para el paginador
.mat-paginator {
  border-top: 1px solid #e2e8f0;
  background-color: white;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .mat-header-cell,
  .mat-cell {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .actions-container button {
    padding: 0.5rem 1rem;
  }
}

// Animación suave para el loading
.loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  transition: opacity 0.3s ease;
}

.description-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.mat-column-descripcion {
  flex: 0 0 25%;
  min-width: 200px;
}

