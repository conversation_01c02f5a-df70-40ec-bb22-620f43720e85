<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <h2 mat-dialog-title>
    <mat-icon>edit</mat-icon>
    <span>Actualizar Anuncio</span>
  </h2>

  <mat-dialog-content>
    <div class="form-layout">
      <!-- Columna izquierda: Imagen y metadatos -->
      <div class="left-column">
        <div class="image-section">
          <h3 class="section-title">Imagen del anuncio</h3>

          <div class="image-field">
            <div class="image-preview" *ngIf="form.get('imagenUrl')?.value" (click)="openFilesUpload()">
              <img [src]="form.get('imagenUrl')?.value" alt="Vista previa">
              <div class="overlay">
                <mat-icon>edit</mat-icon>
                <span>Cambiar imagen</span>
              </div>
            </div>
            <button *ngIf="!form.get('imagenUrl')?.value" mat-stroked-button type="button" (click)="openFilesUpload()">
              <mat-icon>add_photo_alternate</mat-icon>
              Subir imagen
            </button>
          </div>
        </div>

        <div class="metadata-section">
          <h3 class="section-title">Configuración</h3>

          <mat-form-field appearance="outline">
            <mat-label>Categoría</mat-label>
            <mat-select formControlName="categoria">
              <mat-option *ngFor="let cat of categorias" [value]="cat">{{cat}}</mat-option>
            </mat-select>
            <mat-error *ngIf="form.get('categoria')?.errors?.['required']">
              La categoría es requerida
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Estado</mat-label>
            <mat-select formControlName="estado">
              <mat-option *ngFor="let estado of estados" [value]="estado">{{estado}}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Orden</mat-label>
            <input matInput type="number" formControlName="orden" min="0">
            <mat-hint>Menor número = mayor prioridad</mat-hint>
          </mat-form-field>
        </div>
      </div>

      <!-- Columna derecha: Contenido y fechas -->
      <div class="right-column">
        <div class="content-section">
          <h3 class="section-title">Contenido del anuncio</h3>

          <mat-form-field appearance="outline">
            <mat-label>Título</mat-label>
            <input matInput formControlName="titulo" placeholder="Ingrese el título">
            <mat-error *ngIf="form.get('titulo')?.errors?.['required']">
              El título es requerido
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="description-field">
            <mat-label>Descripción</mat-label>
            <textarea matInput formControlName="descripcion" rows="6" placeholder="Ingrese la descripción"></textarea>
            <mat-error *ngIf="form.get('descripcion')?.errors?.['required']">
              La descripción es requerida
            </mat-error>
          </mat-form-field>
        </div>

        <div class="dates-section">
          <h3 class="section-title">Periodo de vigencia</h3>

          <div class="date-fields">
            <mat-form-field appearance="outline">
              <mat-label>Fecha de inicio</mat-label>
              <input matInput [matDatepicker]="pickerInicio" formControlName="fechaInicio">
              <mat-datepicker-toggle matSuffix [for]="pickerInicio"></mat-datepicker-toggle>
              <mat-datepicker #pickerInicio></mat-datepicker>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Fecha de fin</mat-label>
              <input matInput [matDatepicker]="pickerFin" formControlName="fechaFin">
              <mat-datepicker-toggle matSuffix [for]="pickerFin"></mat-datepicker-toggle>
              <mat-datepicker #pickerFin></mat-datepicker>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
      Cancelar
    </button>
    <button mat-raised-button color="primary" type="submit" [disabled]="!form.valid">
      <mat-icon>save</mat-icon>
      Actualizar
    </button>
  </mat-dialog-actions>
</form>
