import { Component, OnInit, OnDestroy } from '@angular/core';
import { User } from '@app/models/backend/user/index';
import Swal from 'sweetalert2';
import { Store, select } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { Observable, take, Subscription, filter, timeout } from 'rxjs';
import { Router } from '@angular/router';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { AnuncioWsService } from '@app/services/anuncio-ws/anuncio-ws.service';
import { ThemeService } from '@app/services/theme.service';
import { CoachingListadoService } from '../../services/coaching.listado.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html'
})
export class HomeComponent implements OnInit, OnDestroy {
  user$: Observable<fromUser.UserResponse | null>;
  isAuthorized$: Observable<boolean>;
  isModalOpen = false;
  isDarkTheme = false;
  mostrarImagenFlotante = false;
  imagenFlotanteUrl: string = '';

  private subscription = new Subscription();

  constructor(
    private store: Store<fromRoot.State>,
    private router: Router,
    private webSocketService: WebSocketService,
    private anuncioWsService: AnuncioWsService,
    private themeService: ThemeService,
    private listadoService: CoachingListadoService
  ) {
    this.user$ = this.store.pipe(select(fromUser.getUser));
    this.isAuthorized$ = this.store.pipe(select(fromUser.getIsAuthorized));
  }

  ngOnInit(): void {
    // Verificar si el WebSocket está conectado y si no estamos en la página de login
    if (!this.webSocketService.isConnected() && !this.router.url.includes('/auth/login')) {
      this.webSocketService.connect();
    }

    const token = localStorage.getItem('token');
    const claveUnica = `imagenMostrada_${token}`;

    const yaMostrado = localStorage.getItem(claveUnica);
    if (!yaMostrado) {
      const hoyLima = new Date().toLocaleDateString('en-CA', { timeZone: 'America/Lima' });
      this.cargarImagenDelDia(hoyLima);

      // Marcar que ya se mostró
      localStorage.setItem(claveUnica, 'true');
    }

    // Suscribirse al servicio de tema
    this.subscription.add(
      this.themeService.darkMode$.subscribe((isDark: boolean) => {
        this.isDarkTheme = isDark;
      })
    );

    // Inicializar el servicio de anuncios
    this.initializeAnunciosService();
  }
  cargandoImagen: boolean = false;

cargarImagenDelDia(fecha: string) {
  this.cargandoImagen = true;

  Swal.fire({
    title: 'Cargando...',
    text: 'Estamos preparndo algo bonito para ti',
    allowOutsideClick: false,
    didOpen: () => Swal.showLoading()
  });

  this.listadoService.obtenerFrasesPorFecha(fecha).subscribe({
    next: (res) => {
      

      if (res.length > 0 && res[0].imagen) {
        this.imagenFlotanteUrl = res[0].imagen;
        this.mostrarImagenFlotante = true;
      } else {
        this.mostrarImagenFlotante = false;
        this.imagenFlotanteUrl = '';
       
      }
      Swal.close(); // Cierra el SweetAlert

      this.cargandoImagen = false;
    },
    error: (err) => {
      console.error('Error al obtener la imagen:', err);
      this.mostrarImagenFlotante = false;
      this.imagenFlotanteUrl = '';
      this.cargandoImagen = false;

      Swal.close();
      Swal.fire('Error', 'No se pudo cargar la imagen del día.', 'error');
    }
  });
}


  onImagenCargada() {
    this.cargandoImagen = false;
  }

  cerrarImagenFlotante() {
    this.mostrarImagenFlotante = false;
  }



  /**
   * Inicializa el servicio de anuncios y configura las suscripciones
   */


  private initializeAnunciosService(): void {
    try {
      // Verificar si el WebSocket está conectado
      if (!this.webSocketService.isConnected()) {
        // Si no está conectado, intentar conectar primero
        this.webSocketService.connect();
      }

      // Intentar configurar las suscripciones de forma segura
      try {
        this.anuncioWsService.setupSubscriptions();
      } catch (error) {
        // Error silencioso - se intentará nuevamente cuando el WebSocket esté conectado
      }

      // Solicitar anuncios recientes EXCLUSIVAMENTE por WebSocket
      // No se realiza ninguna solicitud HTTP
      this.anuncioWsService.requestAnunciosRecientes(0, 6, true);

      // Verificar si el WebSocket está conectado o esperar a que se conecte
      if (this.webSocketService.isConnected()) {
        // Si ya está conectado, ejecutar diagnóstico después de un breve retraso
        setTimeout(() => {
          try {
            this.anuncioWsService.diagnosticarConexion();
          } catch (error) {
            // Error silencioso durante el diagnóstico
          }
        }, 2000);
      } else {
        // Esperar a que se conecte el WebSocket y luego solicitar anuncios
        this.subscription.add(
          this.webSocketService.getConnectionStatus().pipe(
            filter(connected => connected === true),
            take(1),
            timeout(10000) // Timeout de 10 segundos para evitar esperas infinitas
          ).subscribe({
            next: () => {
              // Intentar configurar las suscripciones nuevamente cuando el WebSocket esté conectado
              try {
                this.anuncioWsService.setupSubscriptions();
              } catch (error) {
                // Error silencioso - ya se habrán configurado en el WebSocketService
              }

              // Solicitar anuncios recientes por WebSocket
              this.anuncioWsService.requestAnunciosRecientes(0, 6);

              // Ejecutar diagnóstico después de un breve retraso
              setTimeout(() => {
                try {
                  this.anuncioWsService.diagnosticarConexion();
                } catch (error) {
                  // Error silencioso durante el diagnóstico
                }
              }, 2000);
            },
            error: () => {
              // Si hay un timeout, intentar nuevamente con WebSocket forzado
              this.anuncioWsService.requestAnunciosRecientes(0, 6, true);
            }
          })
        );
      }
    } catch (error) {
      // Error silencioso - intentar nuevamente con WebSocket forzado
      this.anuncioWsService.requestAnunciosRecientes(0, 6, true);
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  isAdmin(): boolean {
    let isAdmin = false;
    this.user$.subscribe((user) => {
      isAdmin =
        !!user && user.role === 'ADMIN' && !!localStorage.getItem('token');
    });
    return isAdmin;
  }

  isBackOffice(): boolean {
    let isBackOffice = false;
    this.user$.subscribe((user) => {
      isBackOffice =
        !!user && user.role === 'BACKOFFICE' && !!localStorage.getItem('token');
    });
    return isBackOffice;
  }

  isAsesor(): boolean {
    let isAsesor = false;
    this.user$.subscribe((user) => {
      isAsesor =
        !!user && user.role === 'ASESOR' && !!localStorage.getItem('token');
    });
    return isAsesor;
  }

  isCoordinador(): boolean {
    let isCoordinador = false;
    this.user$.subscribe((user) => {
      isCoordinador =
        !!user &&
        user.role === 'COORDINADOR' &&
        !!localStorage.getItem('token');
    });
    return isCoordinador;
  }

  isProgramador(): boolean {
    let isProgramador = false;
    this.user$.subscribe((user) => {
      isProgramador =
        !!user &&
        user.role === 'PROGRAMADOR' &&
        !!localStorage.getItem('token');
    });
    return isProgramador;
  }

  checkPermissionAndNavigate(route: string, type: 'LISTADO' | 'SMS' | 'REGISTRAR_VENTA') {
    this.user$.pipe(take(1)).subscribe((user) => {
      if (!user || !localStorage.getItem('token')) {
        return;
      }

      // REGISTRAR_VENTA está habilitado para todos
      if (type === 'REGISTRAR_VENTA') {
        this.router.navigate([route]);
        return;
      }

      // Bloqueo para asesores si intentan acceder a LISTADO o SMS
      // Permitir acceso a PROGRAMADOR
      if (user.role === 'ASESOR') {
        this.showAccessDeniedMessage(type);
        return;
      }

      if (type === 'LISTADO') {
        if (user.role === 'COORDINADOR') {
          this.router.navigate(['/coordinador/obtenerclientesdeasesor']);
        } else if (user.role === 'ADMIN' || user.role === 'BACKOFFICE' || user.role === 'PROGRAMADOR') {
          this.router.navigate([route]);
        } else if (user.role === 'AUDITOR') {
          this.router.navigate(['/leads/listar']);
        }
      }
    });
  }


  checkPermissionAndOpenModal() {
    this.user$.pipe(take(1)).subscribe((user) => {
      // Solo bloquear a ASESOR, permitir a PROGRAMADOR
      if (user?.role === 'ASESOR') {
        this.showAccessDeniedMessage('SMS');
        return;
      }
      this.isModalOpen = true;
    });
  }
  showAccessDeniedMessage(type: 'LISTADO' | 'SMS' | 'REGISTRAR_VENTA') {
    const messages = {
      LISTADO:
        'No tienes permisos para ver el listado de Leads. Solo administradores, backoffice, programadores y coordinadores tienen acceso.',
      SMS: 'No tienes permisos para enviar SMS. Esta función está restringida para asesores.',
      REGISTRAR_VENTA: 'No tienes permisos para registrar una venta.',
    };

    Swal.fire({
      icon: 'warning',
      title: 'Acceso Denegado',
      text: messages[type],
      confirmButtonText: 'Entendido',
      confirmButtonColor: '#3085d6',
    });
  }



  closeModal() {
    this.isModalOpen = false;
  }
}
