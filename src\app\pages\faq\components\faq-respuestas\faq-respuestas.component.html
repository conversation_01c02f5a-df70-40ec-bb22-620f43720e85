<div class="faq-respuestas-container">
  <h3 class="respuestas-title">Respuestas adicionales</h3>
  
  <!-- Men<PERSON>je de cargando -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner"></div>
    <p>Cargando respuestas...</p>
  </div>
  
  <!-- Mensaje de error -->
  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
  </div>
  
  <!-- Lista de respuestas -->
  <div class="respuestas-list" *ngIf="!loading && respuestas.length > 0">
    <div class="respuesta-item" *ngFor="let respuesta of respuestas">
      <div class="respuesta-header">
        <div class="respuesta-user">
          <span class="user-name">{{ respuesta.usuarioNombre || 'Usuario' }}</span>
          <span class="user-role" *ngIf="respuesta.usuario && respuesta.usuario.role">({{ respuesta.usuario.role }})</span>
        </div>
        <div class="respuesta-date">
          {{ formatDate(respuesta.createdAt) }}
        </div>
      </div>
      <div class="respuesta-content">
        {{ respuesta.contenido }}
      </div>
      
      <!-- Archivos adjuntos -->
      <div class="respuesta-archivos" *ngIf="respuesta.archivos && respuesta.archivos.length > 0">
        <h4 class="archivos-title">Archivos adjuntos:</h4>
        <div class="archivos-list">
          <div class="archivo-item" *ngFor="let archivo of respuesta.archivos">
            <a [href]="archivo.url" target="_blank" class="archivo-link">
              <i class="archivo-icon" [ngClass]="{
                'mdi mdi-file-document': archivo.type && (archivo.type.includes('document') || archivo.type.includes('pdf')),
                'mdi mdi-file-image': archivo.type && archivo.type.includes('image'),
                'mdi mdi-file-video': archivo.type && archivo.type.includes('video'),
                'mdi mdi-file-music': archivo.type && archivo.type.includes('audio'),
                'mdi mdi-file': !archivo.type || (archivo.type && !archivo.type.includes('document') && !archivo.type.includes('image') && !archivo.type.includes('video') && !archivo.type.includes('audio'))
              }"></i>
              <span class="archivo-name">{{ archivo.name }}</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Mensaje cuando no hay respuestas -->
  <div class="no-respuestas" *ngIf="!loading && respuestas.length === 0">
    <p>No hay respuestas adicionales.</p>
  </div>
  
  <!-- Formulario para agregar respuesta -->
  <div class="respuesta-form-container" *ngIf="(canRespond || isCreator || isResponder) && faq.estado !== 'CERRADA'">
    <h4 class="form-title">Agregar respuesta</h4>
    <form [formGroup]="respuestaForm" (ngSubmit)="agregarRespuesta()">
      <div class="form-group">
        <textarea 
          class="form-control" 
          formControlName="contenido" 
          rows="4" 
          placeholder="Escriba su respuesta aquí..."
        ></textarea>
        <div class="form-error" *ngIf="respuestaForm.get('contenido')?.invalid && respuestaForm.get('contenido')?.touched">
          <span *ngIf="respuestaForm.get('contenido')?.errors?.['required']">La respuesta es obligatoria.</span>
          <span *ngIf="respuestaForm.get('contenido')?.errors?.['minlength']">La respuesta debe tener al menos 3 caracteres.</span>
        </div>
      </div>
      <div class="form-actions">
        <button type="submit" class="btn btn-primary" [disabled]="respuestaForm.invalid || loading">
          <span *ngIf="loading">Enviando...</span>
          <span *ngIf="!loading">Enviar respuesta</span>
        </button>
      </div>
    </form>
  </div>
  
  <!-- Mensaje cuando la pregunta está cerrada -->
  <div class="pregunta-cerrada" *ngIf="faq.estado === 'CERRADA'">
    <p>Esta pregunta está cerrada y no acepta más respuestas.</p>
  </div>
</div>
