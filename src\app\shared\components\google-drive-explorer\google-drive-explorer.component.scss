.google-drive-explorer {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 80vh;
  overflow: hidden;

  .explorer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    background: #fafafa;

    .header-title {
      h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 500;
        color: #333;
      }

      .subtitle {
        margin: 4px 0 0 0;
        font-size: 0.875rem;
        color: #666;
      }

      .upload-status {
        display: flex;
        align-items: center;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 4px;
        color: #2e7d32;
        font-size: 0.875rem;
        font-weight: 500;

        .success-icon {
          margin-right: 8px;
          color: #4caf50;
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .view-toggle {
        margin-left: 16px;
      }
    }
  }

  .breadcrumbs {
    padding: 12px 24px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    .breadcrumb {
      display: flex;
      align-items: center;
      margin: 0;
      padding: 0;
      list-style: none;

      .breadcrumb-item {
        display: flex;
        align-items: center;

        .breadcrumb-link {
          display: flex;
          align-items: center;
          color: #1976d2;
          text-decoration: none;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 4px;
          transition: background-color 0.2s;

          &:hover {
            background-color: rgba(25, 118, 210, 0.1);
          }

          .breadcrumb-icon {
            margin-right: 4px;
            font-size: 18px;
          }
        }

        .current-folder {
          display: flex;
          align-items: center;
          color: #333;
          font-weight: 500;

          .breadcrumb-icon {
            margin-right: 4px;
            font-size: 18px;
          }
        }

        .separator {
          margin: 0 8px;
          color: #666;
          font-size: 18px;
        }
      }
    }
  }

  .search-bar {
    padding: 16px 24px;
    background: white;

    .search-field {
      width: 100%;
    }
  }

  .explorer-content {
    flex: 1;
    overflow: auto;
    position: relative;
    padding: 16px 24px;

    &.drag-over {
      background-color: rgba(25, 118, 210, 0.05);
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #666;

      p {
        margin-top: 16px;
        font-size: 0.875rem;
      }
    }

    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      .upload-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #666;

        p {
          margin-top: 16px;
          font-size: 0.875rem;
        }
      }
    }

    .files-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .file-card {
        display: flex;
        flex-direction: column;
        padding: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        background: white;
        position: relative;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-color: #1976d2;
        }

        &.selected {
          border-color: #1976d2;
          background-color: rgba(25, 118, 210, 0.05);
        }

        &.folder {
          .file-icon mat-icon {
            color: #ff9800;
          }
        }

        .file-icon {
          display: flex;
          justify-content: center;
          margin-bottom: 12px;

          mat-icon {
            font-size: 48px;
            width: 48px;
            height: 48px;
            color: #666;

            &.file-icon-image { color: #4caf50; }
            &.file-icon-videocam { color: #f44336; }
            &.file-icon-audiotrack { color: #9c27b0; }
            &.file-icon-picture_as_pdf { color: #f44336; }
            &.file-icon-description { color: #2196f3; }
            &.file-icon-table_chart { color: #4caf50; }
            &.file-icon-slideshow { color: #ff9800; }
            &.file-icon-archive { color: #795548; }
          }
        }

        .file-info {
          text-align: center;

          .file-name {
            font-weight: 500;
            margin-bottom: 8px;
            word-break: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .file-details {
            font-size: 0.75rem;
            color: #666;

            .file-size {
              display: block;
            }

            .file-date {
              display: block;
              margin-top: 2px;
            }
          }
        }

        .file-actions {
          position: absolute;
          top: 8px;
          right: 8px;
          opacity: 0;
          transition: opacity 0.2s;
          display: flex;
          gap: 4px;

          .transcribe-button {
            color: #667eea;

            &:hover {
              background-color: rgba(102, 126, 234, 0.1);
              color: #5a67d8;
            }

            mat-icon {
              animation: pulse-transcribe 2s infinite;
            }
          }
        }

        &:hover .file-actions {
          opacity: 1;
        }
      }
    }

    .files-list {
      .files-table {
        width: 100%;

        .file-name-cell {
          cursor: pointer;

          .file-name-container {
            display: flex;
            align-items: center;

            .file-type-icon {
              margin-right: 12px;
              color: #666;

              &.file-icon-folder { color: #ff9800; }
              &.file-icon-image { color: #4caf50; }
              &.file-icon-videocam { color: #f44336; }
              &.file-icon-audiotrack { color: #9c27b0; }
              &.file-icon-picture_as_pdf { color: #f44336; }
              &.file-icon-description { color: #2196f3; }
              &.file-icon-table_chart { color: #4caf50; }
              &.file-icon-slideshow { color: #ff9800; }
              &.file-icon-archive { color: #795548; }
            }

            .file-name {
              font-weight: 500;
            }
          }
        }

        tr.selected {
          background-color: rgba(25, 118, 210, 0.05);
        }

        .transcribe-button {
          color: #667eea;
          margin-right: 8px;

          &:hover {
            background-color: rgba(102, 126, 234, 0.1);
            color: #5a67d8;
          }

          mat-icon {
            animation: pulse-transcribe 2s infinite;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #666;
      text-align: center;

      .empty-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        color: #ccc;
      }

      h3 {
        margin: 0 0 8px 0;
        font-weight: 500;
      }

      p {
        margin: 4px 0;
        font-size: 0.875rem;
      }
    }

    .drag-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(25, 118, 210, 0.1);
      border: 2px dashed #1976d2;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 5;

      .drag-content {
        text-align: center;
        color: #1976d2;

        .drag-icon {
          font-size: 64px;
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
        }

        h3 {
          margin: 0 0 8px 0;
          font-weight: 500;
        }

        p {
          margin: 0;
          font-size: 0.875rem;
        }
      }
    }
  }

  .pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f9f9f9;

    .pagination-info {
      font-size: 0.875rem;
      color: #666;
      font-weight: 500;
    }

    .pagination-buttons {
      display: flex;
      gap: 4px;

      button {
        &:disabled {
          opacity: 0.4;
        }
      }
    }
  }

  .explorer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    background: #fafafa;

    .footer-info {
      font-size: 0.875rem;
      color: #666;

      span {
        margin-right: 16px;
      }

      .content-type {
        font-style: italic;
        color: #999;
        margin-left: 4px;
      }
    }

    .footer-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// Animaciones
@keyframes pulse-transcribe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// Responsive design
@media (max-width: 768px) {
  .google-drive-explorer {
    .explorer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .explorer-content {
      .files-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
      }
    }

    .pagination-controls {
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .pagination-info {
        text-align: center;
      }
    }

    .explorer-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .footer-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}
