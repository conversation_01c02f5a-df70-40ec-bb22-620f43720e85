
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { environment } from '@src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError, switchMap, tap, map, filter, take } from 'rxjs/operators';
import * as fromActions from './user.actions';
import { UserResponse } from './user.models';
import { UserPageResponse } from './user.models';
import { GeneralService } from '@app/services/general.service';
import { Store } from '@ngrx/store';
import { GenericResponse, UserResponse2 } from '@app/models/backend';
import * as WebSocketActions from '@app/services/websocket/websocket.actions';
// Eliminamos la importación no utilizada
import { UserStatusLoggerService } from '@app/services/user-status-logger.service';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { NotificationService } from '@app/services/notification/notification.service';

// Función de utilidad para asegurar que la URL base termine con una barra diagonal
function getBaseUrl(): string {
  return environment.url.endsWith('/') ? environment.url : environment.url + '/';
}


type Action = fromActions.All;

@Injectable()
export class UserEffects {
  constructor(
    private actions: Actions,
    private router: Router,
    private httpClient: HttpClient,
    public GeneralService: GeneralService,
    private store: Store,
    private userStatusLogger: UserStatusLoggerService,
    private webSocketService: WebSocketService,
    private notification: NotificationService
  ) {
  }

  /**
   * Maneja la expiración del token JWT
   * @param err Error HTTP
   * @returns true si el token expiró y se manejó, false en caso contrario
   */
  private handleTokenExpiration(err: any): boolean {
    // Verificar si es un error de token expirado o no autorizado
    if (
      err.error?.error === 'TOKEN_EXPIRADO' ||
      err.error?.message?.includes('JWT expired') ||
      err.status === 401 ||
      err.status === 403
    ) {
      // Preservar información importante antes de limpiar localStorage
      let preservedData = {};
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          // Preservar solo la información de sede
          if (user.sede_id) {
            preservedData = {
              sede_id: user.sede_id,
              sede: user.sede
            };
          }
        }
      } catch (error) {
        console.error('Error al preservar datos de usuario:', error);
      }

      // Limpiar datos de sesión
      localStorage.clear();
      sessionStorage.clear();

      // Mostrar mensaje al usuario
      this.notification.error(
        'Su sesión ha expirado. Por favor, inicie sesión nuevamente.'
      );

      // Guardar la información preservada en sessionStorage para recuperarla después del login
      if (Object.keys(preservedData).length > 0) {
        sessionStorage.setItem('preservedUserData', JSON.stringify(preservedData));
      }

      // Redirigir al login
      this.router.navigate(['/auth/login']);

      // Desconectar WebSocket si está conectado
      if (this.webSocketService.isConnected()) {
        this.store.dispatch(WebSocketActions.disconnectWebSocket());
      }

      return true;
    }
    return false;
  }

  // Efecto para actualizar usuario
  updateUser$: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.UPDATE_USER),
      switchMap((action: fromActions.UpdateUser) => {
        const token = localStorage.getItem('token');
        if (!token) {
          this.router.navigate(['/auth/login']);
          return of(new fromActions.UpdateUserError('No hay sesión activa'));
        }

        // Preparar los datos para el backend
        const userData: any = { ...action.user };

        // Si tenemos sede_id, usarlo directamente sin crear un objeto sede
        if (userData.sede_id) {
          // Mantener sede_id como está, no crear un objeto sede
          delete userData.sedeId; // Eliminar campo redundante
        } else if (userData.sedeId) {
          // Si no hay sede_id pero hay sedeId, usar ese
          userData.sede_id = userData.sedeId;
          delete userData.sedeId;
        }

        // Eliminar el campo sede si existe como objeto para evitar conflictos
        if (userData.sede && typeof userData.sede === 'object') {
          delete userData.sede;
        }

        // Eliminar el campo sedeNombre si existe
        if (userData.sedeNombre) {
          delete userData.sedeNombre;
        }

        console.log('Enviando datos de usuario al backend para actualizar:', JSON.stringify(userData, null, 2));
        console.log('Objeto sede:', userData.sede);

        const url = `${environment.url}api/user/${action.id}`;
        return this.httpClient
          .put<GenericResponse<UserResponse>>(url, userData)
          .pipe(
            tap((response) => {
              if (response.rpta === 1) {
                this.notification.success(
                  response.msg || 'Usuario actualizado exitosamente'
                );
              } else {
                this.notification.error(
                  response.msg || 'Error al actualizar usuario'
                );
              }
            }),
            map((response) => {
              if (response.rpta === 1) {
                return new fromActions.UpdateUserSuccess(response.data);
              } else {
                return new fromActions.UpdateUserError(response.msg);
              }
            }),
            catchError((err) => {
              if (this.handleTokenExpiration(err)) {
                return of(new fromActions.InitUnauthorized());
              }
              this.notification.error('Error al actualizar usuario');
              return of(new fromActions.UpdateUserError(err.message));
            })
          );
      })
    )
  );

  // Efecto para eliminar usuario (soft delete)
  deleteUser$: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.DELETE_USER),
      switchMap((action: fromActions.DeleteUser) => {
        const token = localStorage.getItem('token');
        if (!token) {
          this.router.navigate(['/auth/login']);
          return of(new fromActions.DeleteUserError('No hay sesión activa'));
        }

        const url = `${environment.url}api/user/soft/${action.id}`;
        return this.httpClient.delete<GenericResponse<string>>(url).pipe(
          tap((response) => {
            if (response.rpta === 1) {
              this.notification.success(
                response.msg || 'Usuario eliminado exitosamente'
              );
            } else {
              this.notification.error(
                response.msg || 'Error al eliminar usuario'
              );
            }
          }),
          map((response) => {
            if (response.rpta === 1) {
              return new fromActions.DeleteUserSuccess(action.id);
            } else {
              return new fromActions.DeleteUserError(response.msg);
            }
          }),
          catchError((err) => {
            if (this.handleTokenExpiration(err)) {
              return of(new fromActions.InitUnauthorized());
            }
            this.notification.error('Error al eliminar usuario');
            return of(new fromActions.DeleteUserError(err.message));
          })
        );
      })
    )
  );

// Efecto para listar usuarios paginados
loadUsersPage: Observable<Action> = createEffect(() =>
  this.actions.pipe(
    ofType(fromActions.Types.LOAD_USERS_PAGE),
    switchMap((action: fromActions.LoadUsersPage) => {
      const url = `${environment.url}api/user/listar?page=${action.page}&size=${action.size}`;

      return this.httpClient.get(url, { responseType: 'text' }).pipe(
        // ...
        map((responseText: string) => {
          let response: GenericResponse<UserPageResponse>;
          response = JSON.parse(responseText); // Recuerda que aquí parseas el string

          if (response.rpta === 1) {
            return new fromActions.LoadUsersPageSuccess(response.data);
          } else {
            this.notification.error(response.msg || 'Error al cargar usuarios');
            return new fromActions.LoadUsersPageError(response.msg);
          }
        }),
          catchError((err) => {
            if (this.handleTokenExpiration(err)) {
              return of(new fromActions.InitUnauthorized());
            }
            this.notification.error('Error al cargar usuarios');
            return of(new fromActions.LoadUsersPageError(err.message));
          })
        );
      })
    )
  );

  // Efecto para buscar usuarios (búsqueda genérica)
  searchUsers$: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.SEARCH_USERS),
      switchMap((action: fromActions.SearchUsers) => {
        const token = localStorage.getItem('token');
        if (!token) {
          this.router.navigate(['/auth/login']);
          return of(new fromActions.SearchUsersError('No hay sesión activa'));
        }

        // Construir la URL con los parámetros de búsqueda
        let url = `${environment.url}api/user/buscar?page=${action.page}&size=${action.size}`;

        // Añadir el parámetro de búsqueda si existe
        if (action.query && action.query.trim() !== '') {
          url += `&query=${encodeURIComponent(action.query)}`;
        }

        // Añadir el parámetro de sede_id si existe
        if (action.sedeId) {
          url += `&sede_id=${action.sedeId}`;
        }

        return this.httpClient.get<GenericResponse<UserPageResponse>>(url).pipe(
          map((response) => {
            if (response.rpta === 1) {
              return new fromActions.SearchUsersSuccess(response.data);
            } else {
              this.notification.error(
                response.msg || 'Error al buscar usuarios'
              );
              return new fromActions.SearchUsersError(response.msg);
            }
          }),
          catchError((err) => {
            if (this.handleTokenExpiration(err)) {
              return of(new fromActions.InitUnauthorized());
            }
            this.notification.error('Error al buscar usuarios');
            return of(new fromActions.SearchUsersError(err.message));
          })
        );
      })
    )
  );

  signInEmail: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.SIGIN_IN_EMAIL),
      map((action: fromActions.SignInEmail) => action.credentials),
      switchMap((credentials) =>
        this.httpClient
          .post<GenericResponse<any>>(
            `${getBaseUrl()}api/authentication/sign-in`,
            credentials
          )
          .pipe(
            tap((response) => {
              if (response.rpta === 1) {
                // En el backend SOLID, la respuesta viene directamente en data, no en data.user
                const responseData = response.data;

                // Crear un objeto user con la estructura que espera el frontend
                const user = {
                  id: responseData.userId,
                  username: responseData.username,
                  nombre: responseData.nombre,
                  apellido: responseData.apellido,
                  role: responseData.role,
                  token: responseData.token,
                  // Otros campos que puedan venir en la respuesta
                  sede: responseData.sede,
                  sede_id: responseData.sede_id
                };

                // Verificar si hay datos preservados de una sesión anterior
                try {
                  const preservedDataStr = sessionStorage.getItem('preservedUserData');
                  if (preservedDataStr) {
                    const preservedData = JSON.parse(preservedDataStr);

                    // Si el usuario tiene el mismo sede_id, restaurar el nombre de la sede
                    if (preservedData.sede_id === user.sede_id) {
                      user.sede = preservedData.sede;
                      console.log('🔁 Restaurado el nombre de sede:', preservedData.sede);
                    }

                    // Limpiar los datos preservados
                    sessionStorage.removeItem('preservedUserData');
                  }
                } catch (error) {
                  console.error('Error al restaurar datos preservados:', error);
                }

                // Guardar token y user
                localStorage.setItem('token', responseData.token);
                localStorage.setItem('user', JSON.stringify(user));

                // Guardar coordinador si existe
                if (responseData.coordinador) {
                  localStorage.setItem('coordinador', JSON.stringify(responseData.coordinador));
                } else {
                  localStorage.removeItem('coordinador');
                }

                // Guardar asesores si existe
                if (responseData.asesores) {
                  localStorage.setItem('asesores', JSON.stringify(responseData.asesores));
                } else {
                  localStorage.removeItem('asesores');
                }

                // Guardar user globalmente
                this.GeneralService.usuario$ = user;

                // Guardar estado del usuario
                const userStatus = responseData.status || 'OFFLINE';
                localStorage.setItem('userStatus', userStatus);

                // Verificar si hay una URL guardada para restaurar
                const redirectUrl = sessionStorage.getItem('redirectUrl');

                if (redirectUrl) {
                  // Si hay una URL guardada, redirigir a ella y limpiar
                  sessionStorage.removeItem('redirectUrl');
                  this.router.navigateByUrl(redirectUrl);
                } else {
                  // Si no hay URL guardada, redirigir según el rol
                  const role = (responseData.role || '').toString().trim().toUpperCase();
                  switch (role) {
                    case 'ADMIN':
                    case 'COORDINADOR':
                    case 'ASESOR':
                    case 'BACKOFFICE':
                    case 'BACKOFFICETRAMITADOR':
                    case 'BACKOFFICESEGUIMIENTO':
                    case 'PROGRAMADOR':
                    case 'AUDITOR':
                      this.router.navigate(['/home']);
                      break;
                    default:
                      this.router.navigate(['/']);
                      break;
                  }
                }
              } else {
                this.notification.error(response.msg || 'Credenciales incorrectas');
              }
            }),
            map((response) => {
              if (response.rpta === 1) {
                const responseData = response.data;
                const userStatus = responseData.status || 'OFFLINE';

                // Crear un objeto user con la estructura que espera el frontend
                const user = {
                  id: responseData.userId,
                  username: responseData.username,
                  nombre: responseData.nombre,
                  apellido: responseData.apellido,
                  role: responseData.role,
                  token: responseData.token,
                  // Otros campos que puedan venir en la respuesta
                  sede: responseData.sede,
                  sede_id: responseData.sede_id,
                  sede_nombre: responseData.sede_nombre
                };

                // Conectar WebSocket después de inicio de sesión exitoso
                // Esta es la única vez que se debe establecer la conexión WebSocket
                console.log('UserEffects: Conectando WebSocket después del inicio de sesión exitoso');
                this.store.dispatch(WebSocketActions.connectWebSocket());

                // Inicializar el servicio de logs de estado de usuarios inmediatamente
                this.userStatusLogger.initialize();

                // Esperar a que el WebSocket se conecte antes de inicializar los servicios
                this.webSocketService.getConnectionStatus()
                  .pipe(
                    filter(connected => connected === true),
                    take(1)
                  )
                  .subscribe(() => {
                    // Solicitar explícitamente la lista completa de usuarios conectados
                    this.webSocketService.sendMessage('/app/users.status.all');

                    // Inicializar anuncios inmediatamente
                    import('@app/services/anuncio-ws/anuncio-ws.service')
                      .then(anuncioModule => {
                        // Importar el servicio de sede del usuario
                        import('@app/services/sede-user.service')
                          .then(sedeUserModule => {
                            // Crear una instancia del servicio de sede
                            const sedeUserService = new sedeUserModule.SedeUserService();

                            // Crear el servicio de anuncios con todos los parámetros necesarios
                            const anuncioService = new anuncioModule.AnuncioWsService(
                              this.webSocketService,
                              this.store,
                              null as any,
                              sedeUserService
                            );

                            anuncioService.initializeAfterLogin();
                          })
                          .catch(error => {
                            console.error('Error al cargar el servicio de sede del usuario:', error);
                          });
                      })
                      .catch(error => {
                        // Error silencioso
                        console.error('Error al inicializar servicio de anuncios:', error);
                      });
                  });
                return new fromActions.SignInEmailSuccess(
                  responseData.username,
                  user,
                  userStatus
                );
              } else {
                return new fromActions.SignInEmailError(response.msg);
              }
            }),
            catchError((err) => {
              // Detectar errores específicos de conexión a la base de datos
              if (err.status === 500 && (
                  err.error?.message?.includes('HikariPool') ||
                  err.error?.message?.includes('Connection is not available') ||
                  err.error?.message?.includes('Communications link failure') ||
                  err.error?.message?.includes('Unable to acquire JDBC Connection')
              )) {
                this.notification.error('Error de conexión a la base de datos. Por favor, intente nuevamente en unos momentos.');
                console.error('Error de conexión a la base de datos:', err);
                return of(new fromActions.SignInEmailError('Error de conexión a la base de datos'));
              } else if (err.status === 0) {
                // Intentar conectar WebSocket sin mostrar error inmediatamente
                // Esto evita mostrar el mensaje de error cuando el servidor está disponible pero hay un problema temporal
                try {
                  // Intentar iniciar la conexión WebSocket
                  this.webSocketService.connect();

                  // Esperar más tiempo (3 segundos) para verificar si se conectó
                  // Esto da más tiempo para establecer la conexión antes de mostrar un error
                  setTimeout(() => {
                    if (this.webSocketService.isConnected()) {
                      // Si se conectó, no mostrar error
                      console.log('WebSocket conectado correctamente a pesar del error HTTP');

                      // Intentar iniciar sesión nuevamente de forma silenciosa
                      // Obtener las credenciales del localStorage si están disponibles
                      const savedUsername = localStorage.getItem('lastUsername');
                      const savedPassword = localStorage.getItem('lastPassword');

                      if (savedUsername && savedPassword) {
                        const credentials = {
                          username: savedUsername,
                          password: savedPassword
                        };

                        this.httpClient.post<GenericResponse<any>>(
                          `${environment.url}api/authentication/sign-in`,
                          credentials
                        ).subscribe({
                          next: (response) => {
                            if (response.rpta === 1 && response.data) {
                              // Procesar la respuesta exitosa silenciosamente
                              const responseData = response.data;

                              // Crear objeto de usuario
                              const user = {
                                id: responseData.userId || responseData.id,
                                username: responseData.username,
                                nombre: responseData.nombre,
                                apellido: responseData.apellido,
                                role: responseData.role,
                                token: responseData.token,
                                sede: responseData.sede,
                                sede_id: responseData.sede_id
                              };

                              const userStatus = responseData.status || 'ONLINE';

                              // Guardar token y usuario
                              localStorage.setItem('token', responseData.token);
                              localStorage.setItem('user', JSON.stringify(user));
                              localStorage.setItem('userStatus', userStatus);

                              // Actualizar el store
                              this.store.dispatch(new fromActions.SignInEmailSuccess(
                                responseData.username,
                                user,
                                userStatus
                              ));

                              // Verificar si hay una URL guardada para restaurar
                              const redirectUrl = sessionStorage.getItem('redirectUrl');

                              if (redirectUrl) {
                                // Si hay una URL guardada, redirigir a ella y limpiar
                                sessionStorage.removeItem('redirectUrl');
                                this.router.navigateByUrl(redirectUrl);
                              } else {
                                // Si no hay URL guardada, redirigir según el rol
                                const role = (responseData.role || '').toString().trim().toUpperCase();
                                switch (role) {
                                  case 'ADMIN':
                                  case 'COORDINADOR':
                                  case 'ASESOR':
                                  case 'BACKOFFICE':
                                  case 'BACKOFFICETRAMITADOR':
                                  case 'BACKOFFICESEGUIMIENTO':
                                  case 'PROGRAMADOR':
                                  case 'GERENCIA':
                                    this.router.navigate(['/home']);
                                    break;
                                  case 'AUDITOR':
                                    this.router.navigate(['/leads/listar']);
                                    break;
                                  default:
                                    this.router.navigate(['/']);
                                    break;
                                }
                              }
                          }
                        },
                        error: () => {
                          // Ignorar errores en el reintento silencioso
                        }
                      });
                    } else {
                      // Si no se conectó después de 3 segundos, mostrar error
                      this.notification.error('No se pudo conectar con el servidor. Verifique su conexión a internet.');
                      console.error('Error de conexión al servidor:', err);
                    }
                  }}
                  , 3000);
                } catch (e) {
                  // Si hay un error al intentar conectar WebSocket, esperar antes de mostrar error
                  setTimeout(() => {
                    this.notification.error('No se pudo conectar con el servidor. Verifique su conexión a internet.');
                    console.error('Error de conexión al servidor:', err);
                  }, 1000);
                }

                return of(new fromActions.SignInEmailError('Error de conexión al servidor'));
              } else {
                this.notification.error('Credenciales incorrectas');
                return of(new fromActions.SignInEmailError(err.message));
              }
            })
          )
      )
    )
  );



  // -----------------------------------------------------------------------------
//  E F E C T O   ·  R E G I S T E R   U S E R
// -----------------------------------------------------------------------------
registerUser$ = createEffect(() =>
  this.actions.pipe(
    ofType(fromActions.Types.REGISTER_USER),
    switchMap((action: fromActions.RegisterUser) => {
      /* ----------------------------------------------------------------------
         1.  Verificar sesión
      ---------------------------------------------------------------------- */
      const token = localStorage.getItem('token');
      if (!token) {
        this.router.navigate(['/auth/login']);
        return of(new fromActions.RegisterUserError('No hay sesión activa'));
      }

      /* ----------------------------------------------------------------------
         2.  Normalizar payload
      ---------------------------------------------------------------------- */
      const userData: any = { ...action.user };

      // ─── Sede ────────────────────────────────────────────────────────────
      // Si llega { sede:{ id: 2 } } ==> enviamos sede_id: 2
      if (userData.sede && userData.sede.id) {
        userData.sede_id = userData.sede.id;
      }
      delete userData.sede;  // el backend NO espera el objeto sede

      // ─── Teléfono ────────────────────────────────────────────────────────
      userData.telefono = userData.telefono ? String(userData.telefono) : null;

      // ─── Email ───────────────────────────────────────────────────────────
      if (!userData.email || userData.email === '') {
        userData.email = null; // el backend generará <EMAIL>
      }

      // ─── Estado, DNI (asegurar formatos) ─────────────────────────────────
      userData.estado = 'A';
      if (userData.dni) userData.dni = String(userData.dni);

      console.log('🛰️  Enviando a /api/user/registrar 👉', userData);

      /* ----------------------------------------------------------------------
         3.  POST al backend
      ---------------------------------------------------------------------- */
      return this.httpClient
        .post<GenericResponse<UserResponse>>(
          `${getBaseUrl()}api/user/registrar`,
          userData
        )
        .pipe(
          /* -------------------- Éxito -------------------- */
          tap((resp) => {
            if (resp.rpta === 1) {
              this.notification.success(
                resp.msg || 'Usuario registrado exitosamente.'
              );
              this.store.dispatch(new fromActions.Init()); // refrescar lista
            } else {
              this.notification.error(resp.msg || 'Error al registrar usuario');
            }
          }),
          map((resp) =>
            resp.rpta === 1
              ? new fromActions.RegisterUserSuccess(resp.data)
              : new fromActions.RegisterUserError(resp.msg)
          ),

          /* -------------------- Error -------------------- */
          catchError((err) => {
            if (this.handleTokenExpiration(err)) {
              return of(new fromActions.InitUnauthorized());
            }

            // Obtener mensaje legible
            let msg = 'Error al registrar el usuario.';
            if (err.error?.msg)          msg = err.error.msg;
            else if (typeof err.error === 'string') {
              try { msg = JSON.parse(err.error).msg || msg; } catch {}
            } else if (err.message)      msg = err.message;

            this.notification.error(msg);
            return of(new fromActions.RegisterUserError(msg));
          })
        );
    })
  )
);

   // Efecto para inicializar usuario (Init)
 init: Observable<Action> = createEffect(() =>
  this.actions.pipe(
    ofType(fromActions.Types.INIT),
    switchMap(() => {
      const token = localStorage.getItem('token');
      if (!token) return of(new fromActions.InitUnauthorized());

      return this.httpClient
        .get<GenericResponse<UserResponse2>>(`${getBaseUrl()}api/user`)
        .pipe(
          tap((response) => {
            if (response.rpta === 1) {
              // 👉 Guardar nuevo token si viene en la respuesta
              if (response.data.user.token) {
                localStorage.setItem('token', response.data.user.token);
                console.log('🔁 Token renovado y guardado en localStorage');
              }

              // Normalizar la estructura del objeto user para que coincida con la estructura del login
              const normalizedUser: any = {
                id: response.data.user.id,
                username: response.data.user.username,
                nombre: response.data.user.nombre,
                apellido: response.data.user.apellido,
                role: response.data.user.role,
                token: response.data.user.token || localStorage.getItem('token') || '',
                // Manejar la sede correctamente
                sede_id: response.data.user.sede_id,
                // Usar sedeNombre si existe, o sede si es un string
                sede: response.data.user.sedeNombre ||
                      (response.data.user.sede && typeof response.data.user.sede === 'string' ?
                       response.data.user.sede : null)
              };

              // Preservar el nombre de la sede si ya existe en localStorage
              try {
                const existingUserStr = localStorage.getItem('user');
                if (existingUserStr) {
                  const existingUser = JSON.parse(existingUserStr);

                  // Si el usuario existente tiene un nombre de sede pero la respuesta no lo tiene,
                  // preservar el nombre existente
                  if (existingUser.sede && typeof existingUser.sede === 'string' && !normalizedUser.sede) {
                    normalizedUser.sede = existingUser.sede;
                  }

                  // Si el usuario existente tiene un sede_id pero la respuesta no lo tiene,
                  // preservar el sede_id existente
                  if (existingUser.sede_id && !normalizedUser.sede_id) {
                    normalizedUser.sede_id = existingUser.sede_id;
                  }
                }
              } catch (error) {
                console.error('Error al preservar el nombre de la sede:', error);
              }

              // Si tenemos un ID de sede pero no un nombre, usar el nombre de la sede del backend o "tu sede" como valor predeterminado
              if (normalizedUser.sede_id && !normalizedUser.sede) {
                normalizedUser.sede = response.data.user.sedeNombre || "tu sede";
                console.log('🔁 Usando nombre de sede predeterminado:', normalizedUser.sede);
              }

              // Guardar el objeto user normalizado en localStorage
              localStorage.setItem('user', JSON.stringify(normalizedUser));

              if (response.data.coordinador) {
                localStorage.setItem('coordinador', JSON.stringify(response.data.coordinador));
              } else {
                localStorage.removeItem('coordinador');
              }

              // Actualizar el servicio general con el usuario normalizado
              this.GeneralService.usuario$ = normalizedUser;
            }
          }),

          map((response) => {
            if (response.rpta === 1) {
              // No conectamos el WebSocket aquí, solo en el login exitoso
              // Obtener el usuario normalizado de localStorage para asegurar consistencia
              try {
                const userStr = localStorage.getItem('user');
                if (userStr) {
                  const user = JSON.parse(userStr);
                  return new fromActions.InitAuthorized(
                    user.username,
                    user
                  );
                }
              } catch (error) {
                console.error('Error al obtener usuario de localStorage:', error);
              }

              // Si hay algún error, usar el objeto original como respaldo
              return new fromActions.InitAuthorized(
                response.data.user.username,
                response.data.user
              );
            } else {
              return new fromActions.InitUnauthorized();
            }
          }),
          catchError((err) => {
            console.error('Error en la inicialización:', err);

            // Intentar una vez recargar la página si hay un error
            const refreshed = sessionStorage.getItem('refreshed');
            if (!refreshed) {
              localStorage.clear();
              sessionStorage.setItem('refreshed', 'true');
              this.router.navigate(['/auth/login']);
              this.notification.error('Su sesión ha expirado. Por favor, inicie sesión nuevamente.');
            } else {
              sessionStorage.removeItem('refreshed');
            }

            return of(new fromActions.InitUnauthorized());
          })
        );
    })
  )
);




  // Efecto para carga masiva de ASESOR
  cargaMasivaAsesor$: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.CARGA_MASIVA_ASESOR),
      switchMap((action: fromActions.CargaMasivaAsesor) => {
        const token = localStorage.getItem('token');
        if (!token) {
          this.router.navigate(['/auth/login']);
          return of(
            new fromActions.CargaMasivaAsesorError('No hay sesión activa')
          );
        }

        const formData = new FormData();
        formData.append('file', action.file);

        // Mostrar mensaje de carga
        this.notification.success('Procesando archivo, por favor espere...');

        return this.httpClient
          .post<GenericResponse<string>>(
            `${getBaseUrl()}api/user/crear-masivo`,
            formData
          )
          .pipe(
            tap((response) => {
              if (response.rpta === 1) {
                this.notification.success(
                  response.msg || 'Usuarios ASESOR creados exitosamente'
                );
                // Actualizar la lista de usuarios después de una carga exitosa
                this.store.dispatch(new fromActions.Init());
              } else {
                this.notification.error(
                  response.msg || 'Error al subir archivo para usuarios ASESOR'
                );
              }
            }),
            map((response) => {
              if (response.rpta === 1) {
                return new fromActions.CargaMasivaAsesorSuccess(response.data);
              } else {
                return new fromActions.CargaMasivaAsesorError(response.msg);
              }
            }),
            catchError((err) => {
              if (this.handleTokenExpiration(err)) {
                return of(new fromActions.InitUnauthorized());
              }

              // Intentar extraer un mensaje de error más específico
              let errorMessage = 'Error al subir archivo para usuarios ASESOR';

              if (err.error && err.error.msg) {
                errorMessage = err.error.msg;
              } else if (err.error && typeof err.error === 'string') {
                try {
                  const errorObj = JSON.parse(err.error);
                  if (errorObj.msg) {
                    errorMessage = errorObj.msg;
                  }
                } catch (e) {
                  // Si no se puede parsear, usar el error como está
                  if (err.error) {
                    errorMessage = err.error;
                  }
                }
              } else if (err.message) {
                errorMessage = err.message;
              }

              // Mostrar mensaje de error
              console.error('Error al cargar usuarios ASESOR:', err);
              this.notification.error(errorMessage);

              return of(new fromActions.CargaMasivaAsesorError(errorMessage));
            })
          );
      })
    )
  );

  // Efecto para carga masiva de BACKOFFICE
  cargaMasivaBackoffice$: Observable<Action> = createEffect(() =>
    this.actions.pipe(
      ofType(fromActions.Types.CARGA_MASIVA_BACKOFFICE),
      switchMap((action: fromActions.CargaMasivaBackOffice) => {
        const token = localStorage.getItem('token');
        if (!token) {
          this.router.navigate(['/auth/login']);
          return of(
            new fromActions.CargaMasivaBackOfficeError('No hay sesión activa')
          );
        }

        const formData = new FormData();
        formData.append('file', action.file);

        // Mostrar mensaje de carga
        this.notification.success('Procesando archivo, por favor espere...');

        return this.httpClient
          .post<GenericResponse<string>>(
            `${getBaseUrl()}api/user/crear-masivo-backoffice`,
            formData
          )
          .pipe(
            tap((response) => {
              if (response.rpta === 1) {
                this.notification.success(
                  response.msg || 'Usuarios BACKOFFICE creados exitosamente'
                );
                // Actualizar la lista de usuarios después de una carga exitosa
                this.store.dispatch(new fromActions.Init());
              } else {
                this.notification.error(
                  response.msg ||
                    'Error al subir archivo para usuarios BACKOFFICE'
                );
              }
            }),
            map((response) => {
              if (response.rpta === 1) {
                return new fromActions.CargaMasivaBackOfficeSuccess(
                  response.data
                );
              } else {
                return new fromActions.CargaMasivaBackOfficeError(response.msg);
              }
            }),
            catchError((err) => {
              if (this.handleTokenExpiration(err)) {
                return of(new fromActions.InitUnauthorized());
              }

              // Intentar extraer un mensaje de error más específico
              let errorMessage = 'Error al subir archivo para usuarios BACKOFFICE';

              if (err.error && err.error.msg) {
                errorMessage = err.error.msg;
              } else if (err.error && typeof err.error === 'string') {
                try {
                  const errorObj = JSON.parse(err.error);
                  if (errorObj.msg) {
                    errorMessage = errorObj.msg;
                  }
                } catch (e) {
                  // Si no se puede parsear, usar el error como está
                  if (err.error) {
                    errorMessage = err.error;
                  }
                }
              } else if (err.message) {
                errorMessage = err.message;
              }

              // Mostrar mensaje de error
              console.error('Error al cargar usuarios BACKOFFICE:', err);
              this.notification.error(errorMessage);

              return of(
                new fromActions.CargaMasivaBackOfficeError(errorMessage)
              );
            })
          );
      })
    )
  );

  // El efecto para cambiar el rol de un usuario ha sido eliminado
  // Ahora el cambio de rol se maneja directamente en la actualización del usuario
}
