import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SeccionService } from '@app/services/seccion.service';
import { Seccion, SeccionCreateRequest, SeccionUpdateRequest } from '@app/models/backend/curso/seccion.model';
import { NotificationService } from '@app/services/notification/notification.service';
import { Modulo } from '@app/models/backend/curso/modulo.model';

@Component({
  selector: 'app-seccion-form-inline',
  templateUrl: './seccion-form-inline.component.html'
})
export class SeccionFormInlineComponent implements OnInit, OnDestroy {
  @Input() modulo!: Modulo;
  @Input() seccion: Seccion | null = null;
  @Input() isDarkTheme: boolean = false;
  @Output() saved = new EventEmitter<Seccion>();
  @Output() cancelled = new EventEmitter<void>();

  seccionForm: FormGroup;
  loading: boolean = false;
  isEditMode: boolean = false;
  title: string = 'Crear nueva sección';

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private seccionService: SeccionService,
    private notification: NotificationService
  ) {
    this.seccionForm = this.fb.group({
      titulo: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', Validators.maxLength(500)],
      orden: [null]
    });
  }

  ngOnInit(): void {
    this.isEditMode = !!this.seccion;

    if (this.isEditMode) {
      this.title = 'Editar sección';
      this.seccionForm.patchValue({
        titulo: this.seccion?.titulo,
        descripcion: this.seccion?.descripcion,
        orden: this.seccion?.orden
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit(): void {
    if (this.seccionForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.isEditMode && this.seccion) {
      this.updateSeccion();
    } else {
      this.createSeccion();
    }
  }

  private createSeccion(): void {
    const seccionData: SeccionCreateRequest = {
      titulo: this.seccionForm.value.titulo,
      descripcion: this.seccionForm.value.descripcion,
      orden: this.seccionForm.value.orden,
      moduloId: this.modulo.id
    };

    this.seccionService.createSeccion(seccionData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.saved.emit(response.data);
          } else {
            this.notification.error(response.msg || 'Error al crear la sección');
          }
        },
        error: (error) => {
          this.loading = false;
          this.notification.error('Error al crear la sección. Por favor, inténtelo de nuevo.');
          console.error('Error al crear sección:', error);
        }
      });
  }

  private updateSeccion(): void {
    if (!this.seccion) {
      return;
    }

    const seccionData: SeccionUpdateRequest = {
      titulo: this.seccionForm.value.titulo,
      descripcion: this.seccionForm.value.descripcion,
      orden: this.seccionForm.value.orden
    };

    this.seccionService.updateSeccion(this.seccion.id, seccionData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.saved.emit(response.data);
          } else {
            this.notification.error(response.msg || 'Error al actualizar la sección');
          }
        },
        error: (error) => {
          this.loading = false;
          this.notification.error('Error al actualizar la sección. Por favor, inténtelo de nuevo.');
          console.error('Error al actualizar sección:', error);
        }
      });
  }

  onCancel(): void {
    this.cancelled.emit();
  }
}
