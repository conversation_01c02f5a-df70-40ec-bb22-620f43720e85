import { Component, OnInit, OnD<PERSON>roy, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Curso } from '@app/models/backend/curso/curso.model';
import { CursoUsuario } from '@app/models/backend/curso/curso-usuario.model';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';

import { NotificationService } from '@app/services/notification/notification.service';
import { User } from '@app/models/backend/user';

import Swal from 'sweetalert2';

@Component({
  selector: 'app-curso-ver-alumnos',
  templateUrl: './curso-ver-alumnos.component.html',
})
export class CursoVerAlumnosComponent implements OnInit, OnDestroy {
  curso: Curso;
  usuariosAsignados: any[] = []; // Cambiado a any[] para incluir la información de progreso
  usuarios: User[] = [];
  loading = false;
  loadingMessage: string = '';
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private cursoUsuarioService: CursoUsuarioService,
    private notification: NotificationService,
    private dialogRef: MatDialogRef<CursoVerAlumnosComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { curso: Curso }
  ) {
    this.curso = data.curso;
  }

  ngOnInit(): void {
    this.loadUsuariosAsignados();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadUsuariosAsignados(): void {
    this.loading = true;
    this.loadingMessage = 'Cargando alumnos...';
    this.error = null;

    this.cursoUsuarioService
      .getUsuariosByCursoId(this.curso.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.usuariosAsignados = response.data || [];

            // Extraer los usuarios de las asignaciones
            if (this.usuariosAsignados.length > 0) {
              this.usuarios = this.usuariosAsignados
                .filter((cu) => cu.usuario) // Filtrar solo los que tienen usuario
                .map((cu) => cu.usuario as User);

              if (this.usuarios.length === 0) {
                this.error = 'No hay alumnos asignados a este curso';
              }
            } else {
              this.error = 'No hay alumnos asignados a este curso';
            }
          } else {
            this.error = response.msg || 'Error al cargar alumnos';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error =
            'Error al cargar alumnos. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar alumnos:', error);
        },
      });
  }

  /**
   * Obtiene el progreso de un alumno específico
   * @param asignacion Objeto de asignación que contiene la información de progreso
   * @returns Objeto con el progreso del alumno o undefined si no existe
   */
  getProgresoAlumno(asignacion: any): any {
    return asignacion && asignacion.progreso ? asignacion.progreso : null;
  }

  /**
   * Obtiene el porcentaje de progreso de un alumno
   * @param asignacion Objeto de asignación que contiene la información de progreso
   * @returns Porcentaje de progreso (0-100)
   */
  getPorcentajeProgresoAlumno(asignacion: any): number {
    const progreso = this.getProgresoAlumno(asignacion);
    return progreso ? progreso.porcentajeProgreso : 0;
  }

  /**
   * Determina si un alumno ha completado el curso
   * @param asignacion Objeto de asignación que contiene la información de progreso
   * @returns true si el alumno ha completado el curso, false en caso contrario
   */
  haCompletadoCurso(asignacion: any): boolean {
    return asignacion && asignacion.completado === true;
  }

  /**
   * Determina el color del progreso según el porcentaje
   * @param porcentaje Porcentaje de progreso
   * @returns Color CSS para el progreso
   */
  getColorProgreso(porcentaje: number): string {
    if (porcentaje < 30) return '#f44336'; // Rojo
    if (porcentaje < 70) return '#ff9800'; // Naranja
    return '#4caf50'; // Verde
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getInitials(nombre?: string, apellido?: string): string {
    let initials = '';
    if (nombre && nombre.length > 0) {
      initials += nombre.charAt(0).toUpperCase();
    }
    if (apellido && apellido.length > 0) {
      initials += apellido.charAt(0).toUpperCase();
    }
    return initials || '?';
  }

  getAvatarColor(username: string): string {
    const colors = [
      '#1abc9c',
      '#2ecc71',
      '#3498db',
      '#9b59b6',
      '#34495e',
      '#16a085',
      '#27ae60',
      '#2980b9',
      '#8e44ad',
      '#2c3e50',
      '#f1c40f',
      '#e67e22',
      '#e74c3c',
      '#95a5a6',
      '#f39c12',
      '#d35400',
      '#c0392b',
      '#7f8c8d',
    ];

    // Generar un índice basado en el username
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
      hash = username.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Usar el hash para seleccionar un color
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  }

  /**
   * Elimina un alumno del curso
   * @param usuario Usuario a eliminar del curso
   */
  onRemoveAlumno(usuario: User): void {
    // Buscar la asignación correspondiente a este usuario
    const asignacion = this.usuariosAsignados.find(
      (a) => a.usuario && a.usuario.id === usuario.id
    );

    if (!asignacion) {
      this.notification.error('No se encontró la asignación para este usuario');
      return;
    }

    // Mostrar confirmación antes de eliminar
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas eliminar a ${usuario.nombre} ${usuario.apellido} del curso?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar',
    }).then((result) => {
      if (result.isConfirmed) {
        this.loading = true;
        this.loadingMessage = 'Eliminando alumno del curso...';

        // Llamar al servicio para eliminar la asignación
        this.cursoUsuarioService
          .deleteCursoUsuario(asignacion.id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response) => {
              this.loading = false;
              if (response.rpta === 1) {
                // Eliminar el usuario de la lista local
                this.usuarios = this.usuarios.filter(
                  (u) => u.id !== usuario.id
                );
                this.usuariosAsignados = this.usuariosAsignados.filter(
                  (a) => a.id !== asignacion.id
                );

                // Mostrar mensaje de éxito
                this.notification.success(
                  'Alumno eliminado del curso exitosamente'
                );

                // Si no quedan usuarios, mostrar mensaje
                if (this.usuarios.length === 0) {
                  this.error = 'No hay alumnos asignados a este curso';
                }
              } else {
                this.notification.error(
                  response.msg || 'Error al eliminar alumno del curso'
                );
              }
            },
            error: (error) => {
              this.loading = false;
              this.notification.error(
                'Error al eliminar alumno del curso. Por favor, inténtelo de nuevo.'
              );
              console.error('Error al eliminar alumno del curso:', error);
            },
          });
      }
    });
  }
}
