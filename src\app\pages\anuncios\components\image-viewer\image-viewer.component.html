<!-- Mat Dialog Style Image Viewer -->
<div
  class=" rounded-lg shadow-xl max-w-4xl max-h-[90vh] relative"
  @zoomInOut
>
  <!-- Header -->
  <div class="flex justify-between items-center p-4 border-b">
    <h2 class="text-lg font-medium text-gray-900 truncate">
      {{ data.title }}
    </h2>
    <button
      mat-icon-button
      (click)="onClose()"
      class="text-gray-500 hover:text-gray-700"
    >
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Image Content -->
  <div class="p-4 flex justify-center items-center">
    <div class="relative">
      <app-spinner *ngIf="loading"></app-spinner>
      <img
        [src]="data.imageUrl"
        [alt]="data.title"
        (load)="onImageLoad()"
        class="max-w-full max-h-[70vh] object-contain rounded"
        [class.opacity-0]="loading"
        [class.opacity-100]="!loading"
      />
    </div>
  </div>
</div>
