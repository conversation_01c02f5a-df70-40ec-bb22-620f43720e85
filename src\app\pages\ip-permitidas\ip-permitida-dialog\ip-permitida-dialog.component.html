<h2 mat-dialog-title>{{ dialogTitle }}</h2>
<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Dirección IP</mat-label>
      <input matInput formControlName="ip" placeholder="Ej: ***********">
      <mat-error *ngIf="f['ip'].errors?.['required']">La IP es requerida</mat-error>
      <mat-error *ngIf="f['ip'].errors?.['pattern']">Ingrese una IP válida</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Descripción</mat-label>
      <textarea matInput formControlName="descripcion" 
                placeholder="Ingrese una descripción para esta IP"
                rows="3"></textarea>
      <mat-error *ngIf="f['descripcion'].errors?.['required']">
        La descripción es requerida
      </mat-error>
      <mat-error *ngIf="f['descripcion'].errors?.['maxlength']">
        La descripción no debe exceder 255 caracteres
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Fecha de Expiración</mat-label>
      <input matInput formControlName="fecha_expiracion" type="date">
      <mat-error *ngIf="f['fecha_expiracion'].errors?.['required']">
        La fecha de expiración es requerida
      </mat-error>
    </mat-form-field>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()">Cancelar</button>
    <button mat-raised-button color="primary" type="submit" 
            [disabled]="!form.valid">Guardar</button>
  </mat-dialog-actions>
</form>
