import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { filter, take, debounceTime } from 'rxjs/operators';

import { WebSocketService } from '../websocket/WebSocketService';
import { AnuncioResponse } from '@app/pages/anuncios/store/save/save.models';
import { Store } from '@ngrx/store';
import * as fromAnuncioActions from '@app/pages/anuncios/store/save/save.actions';
import { NotificationService } from '../notification/notification.service';
import { SedeUserService } from '../sede-user.service';

/**
 * Servicio WebSocket para anuncios
 * - Gestiona la comunicación en tiempo real con el backend para anuncios
 * - Proporciona streams para anuncios y anuncios recientes
 * - Maneja eventos de creación, actualización y eliminación de anuncios
 */
@Injectable({ providedIn: 'root' })
export class AnuncioWsService implements OnDestroy {
  // Control de singleton para evitar múltiples instancias
  private static instance: AnuncioWsService;

  // Variables estáticas para control global de solicitudes
  static anunciosRecientesSolicitados: boolean = false;
  static ultimaSolicitudGlobal: number = 0;
  static TIEMPO_MINIMO_ENTRE_SOLICITUDES_GLOBAL: number = 2000; // 2 segundos
  static servicioInicializado: boolean = false;

  // Subjects para los diferentes tipos de anuncios
  private anunciosSubject = new BehaviorSubject<AnuncioResponse[]>([]);
  private anunciosRecientesSubject = new BehaviorSubject<AnuncioResponse[]>([]);

  // Estado de conexión
  private connectionStatus = new BehaviorSubject<boolean>(false);

  // Suscripciones
  private subscriptions: Subscription[] = [];

  // Parámetros de paginación
  private currentPage = 0;
  private pageSize = 8;
  private recentPage = 0;
  private recentPageSize = 6;

  // Indicadores de inicialización
  private initialized = false;
  private initializingAfterLogin = false;

  constructor(
    private webSocketService: WebSocketService,
    private store: Store,
    private notification: NotificationService,
    private sedeUserService: SedeUserService
  ) {
    // Implementación de singleton
    if (AnuncioWsService.instance) {
      return AnuncioWsService.instance;
    }

    AnuncioWsService.instance = this;

    // Inicializar servicio

    // Suscribirse al estado de conexión del WebSocket
    this.addSubscription(
      'connection',
      this.webSocketService.getConnectionStatus().subscribe((connected) => {
        this.connectionStatus.next(connected);

        if (connected && !this.initialized) {
          // WebSocket conectado, inicializar suscripciones
          this.setupSubscriptions();
        } else if (connected && this.initialized) {
          // Si ya está inicializado y se reconecta, solicitar la lista completa de anuncios
          // IMPORTANTE: En el listado de anuncios, SOLO usar el endpoint /app/anuncios.todos
          this.webSocketService.sendMessage('/app/anuncios.todos', {
            page: this.currentPage,
            size: this.pageSize,
          });
        }
      })
    );
  }

  /**
   * Configura las suscripciones a los canales WebSocket
   * Método público para permitir inicialización desde componentes
   */
  setupSubscriptions(): void {
    if (this.initialized) {
      return;
    }

    // Verificar si el WebSocket está conectado
    if (!this.webSocketService.isConnected()) {
      // Si no está conectado, intentar conectar primero
      this.webSocketService.connect();

      // Suscribirse al estado de conexión para inicializar cuando se conecte
      const sub = this.webSocketService.getConnectionStatus()
        .pipe(
          filter(connected => connected),
          take(1)
        )
        .subscribe(() => {
          // Cuando se conecte, intentar inicializar nuevamente
          this.setupSubscriptions();
        });

      this.subscriptions.push(sub);

      // No marcar como inicializado aún, se intentará nuevamente cuando se conecte
      return;
    }

    try {
      // Limpiar suscripciones existentes para evitar duplicados
      this.subscriptions.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
      this.subscriptions = [];

      // Asegurarse de que estamos suscritos a los tópicos de anuncios
      // Esto es necesario porque el WebSocketService podría no haberse suscrito automáticamente
      // IMPORTANTE: En el listado de anuncios, SOLO usar el tópico /topic/anuncios/todos
      const anuncioTopics = [
        '/topic/anuncios/new',
        '/topic/anuncios/update',
        '/topic/anuncios/delete',
        '/topic/anuncios/todos', // Tópico principal para todos los anuncios (activos e inactivos)
      ];

      // Intentar suscribirse a cada tópico de forma segura
      anuncioTopics.forEach((topic) => {
        try {
          // Verificar si ya estamos suscritos a este tópico
          if (!this.webSocketService['activeSubscriptions'].has(topic)) {
            this.webSocketService.subscribeToDynamicTopic(topic, 'TOPIC');
          }
        } catch (error) {
          // Error silencioso - el WebSocketService manejará la suscripción cuando se conecte
          console.error('Error al suscribirse a tópico:', topic, error);
        }
      });
    } catch (error) {
      console.error('Error en setupSubscriptions:', error);
      // Error silencioso - se intentará nuevamente más tarde
      return;
    }

    // Obtener el ID de la sede del usuario
    const sedeId = this.sedeUserService.getSedeIdSync();

    // Determinar los tópicos específicos según la sede
    // Para anuncios recientes, SIEMPRE usar el tópico específico de la sede
    const recientesTopic = sedeId
      ? `/topic/anuncios/recientes/sede/${sedeId}`
      : '/topic/anuncios/recientes';

    // Para anuncios activos, SIEMPRE usar el tópico específico de la sede
    const activosTopic = sedeId
      ? `/topic/anuncios/activos/sede/${sedeId}`
      : '/topic/anuncios/activos';

    // Para todos los anuncios, usar el tópico que devuelve todos los anuncios sin filtrar por estado
    const todosTopic = '/topic/anuncios/todos';

    // Asegurarse de que estamos suscritos a los tópicos específicos de la sede
    if (sedeId) {
      try {
        // Suscribirse a todos los tópicos específicos de la sede
        this.webSocketService.subscribeToDynamicTopic(recientesTopic, 'TOPIC');
        this.webSocketService.subscribeToDynamicTopic(activosTopic, 'TOPIC');
        this.webSocketService.subscribeToDynamicTopic(todosTopic, 'TOPIC');
      } catch (error) {
        // Error silencioso - el WebSocketService manejará la suscripción cuando se conecte
      }
    }

    // Suscribirse a la lista de anuncios recientes (prioridad alta)
    // Esta es la suscripción más importante para mostrar anuncios recientes después del login
    this.addSubscription(
      'recientes',
      this.webSocketService
        .getMessagesByDestination(recientesTopic)
        .pipe(
          // Usar debounceTime para reducir la frecuencia de actualizaciones
          debounceTime(300)
        )
        .subscribe((response: any) => {
          let anunciosRecientes: AnuncioResponse[] = [];
          let totalElements = 0;
          let totalPages = 1;

          // Manejar diferentes formatos de respuesta
          if (response) {
            // Caso 1: Respuesta es un objeto Page con content (formato Spring Data)
            if (response.content && Array.isArray(response.content)) {
              anunciosRecientes = response.content;
              totalElements =
                response.totalElements || anunciosRecientes.length;
              totalPages =
                response.totalPages ||
                Math.ceil(totalElements / this.recentPageSize);
            }
            // Caso 2: Respuesta es directamente un array
            else if (Array.isArray(response)) {
              anunciosRecientes = response;
              totalElements = anunciosRecientes.length;
              totalPages = Math.ceil(totalElements / this.recentPageSize);
            }
            // Caso 3: Respuesta es un objeto GenericResponse con data que contiene Page
            else if (
              response.data &&
              response.data.content &&
              Array.isArray(response.data.content)
            ) {
              anunciosRecientes = response.data.content;
              totalElements =
                response.data.totalElements || anunciosRecientes.length;
              totalPages =
                response.data.totalPages ||
                Math.ceil(totalElements / this.recentPageSize);
            }
            // Caso 4: Respuesta es un objeto con propiedades pero sin content (formato personalizado)
            else if (typeof response === 'object' && !Array.isArray(response)) {
              // Intentar extraer anuncios de cualquier propiedad que sea un array
              for (const key in response) {
                if (Array.isArray(response[key])) {
                  anunciosRecientes = response[key];
                  break;
                }
              }
              totalElements = anunciosRecientes.length;
              totalPages = Math.ceil(totalElements / this.recentPageSize);
            }
          }

          if (anunciosRecientes.length > 0) {
            this.anunciosRecientesSubject.next(anunciosRecientes);

            // Actualizar el store para mantener sincronización
            this.store.dispatch(
              new fromAnuncioActions.ReadRecentSuccess(
                anunciosRecientes,
                totalElements,
                totalPages,
                this.recentPage
              )
            );
          }
        })
    );

    // Asegurarse de que estamos suscritos al tópico de nuevos anuncios
    try {
      this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/new', 'TOPIC');
      //console.log('AnuncioWsService: Suscrito explícitamente al tópico /topic/anuncios/new');
    } catch (error) {
      console.error('Error al suscribirse a tópico /topic/anuncios/new:', error);
    }

    // Suscribirse a eventos de nuevo anuncio
    this.addSubscription(
      'new',
      this.webSocketService
        .getMessagesByDestination('/topic/anuncios/new')
        .subscribe((anuncio: AnuncioResponse) => {
          //console.log('AnuncioWsService: Recibido nuevo anuncio en /topic/anuncios/new:', anuncio);
          if (anuncio) {
            // Mostrar notificación
            if (this.notification) {
              this.notification.success(`Nuevo anuncio: ${anuncio.titulo}`);
            }

            // 1. Actualizar inmediatamente la lista de anuncios recientes
            const recentList = this.anunciosRecientesSubject.value;

            // Verificar si el anuncio ya está en la lista
            const exists = recentList.some((a) => a.id === anuncio.id);

            // Siempre agregar el nuevo anuncio al principio de la lista (es el más reciente)
            // incluso si ya existe, para asegurar que tenga los datos más actualizados
            const updatedRecentList = exists
              ? [anuncio, ...recentList.filter((a) => a.id !== anuncio.id)]
              : [anuncio, ...recentList];

            // Si la lista excede el tamaño máximo, recortarla
            const finalRecentList = updatedRecentList.slice(
              0,
              this.recentPageSize
            );

            // Actualizar el subject
            this.anunciosRecientesSubject.next(finalRecentList);

            // También actualizar el store para mantener sincronización
            this.store.dispatch(
              new fromAnuncioActions.ReadRecentSuccess(
                finalRecentList,
                finalRecentList.length,
                Math.ceil(finalRecentList.length / this.recentPageSize),
                this.recentPage
              )
            );

            // 2. Actualizar inmediatamente la lista principal de anuncios
            const mainList = this.anunciosSubject.value;

            // Verificar si el anuncio ya está en la lista principal
            const existsInMain = mainList.some((a) => a.id === anuncio.id);

            // Si estamos en la primera página, el nuevo anuncio debe aparecer al principio
            if (this.currentPage === 0) {
              // Agregar el nuevo anuncio al principio de la lista
              const updatedMainList = existsInMain
                ? [anuncio, ...mainList.filter((a) => a.id !== anuncio.id)]
                : [anuncio, ...mainList];

              // Si la lista excede el tamaño máximo, recortarla
              const finalMainList = updatedMainList.slice(0, this.pageSize);

              // Actualizar el subject
              this.anunciosSubject.next(finalMainList);

              // También actualizar el store para mantener sincronización
              this.store.dispatch(
                new fromAnuncioActions.ReadSuccess(
                  finalMainList,
                  mainList.length + (existsInMain ? 0 : 1), // Incrementar el total solo si es nuevo
                  Math.ceil((mainList.length + (existsInMain ? 0 : 1)) / this.pageSize),
                  this.currentPage
                )
              );
            } else if (!existsInMain) {
              // Si no estamos en la primera página, solo actualizar el contador total
              // pero no modificar la lista actual para evitar confusión
              this.store.dispatch(
                new fromAnuncioActions.ReadSuccess(
                  mainList,
                  mainList.length + 1, // Incrementar el total
                  Math.ceil((mainList.length + 1) / this.pageSize),
                  this.currentPage
                )
              );
            }

            // Guardar el ID del anuncio recién creado en una variable de clase
            AnuncioWsService.ultimoAnuncioCreado = anuncio.id;

           // console.log('AnuncioWsService: Nuevo anuncio recibido y guardado:', anuncio.id);

            // Agregar inmediatamente el anuncio a las listas

            // 1. Agregar a la lista principal si estamos en la primera página
            if (this.currentPage === 0) {
              const mainList = this.anunciosSubject.value;
              // Verificar si el anuncio ya está en la lista principal
              const existsInMain = mainList.some(a => a.id === anuncio.id);

              if (!existsInMain) {
                //console.log('AnuncioWsService: Agregando nuevo anuncio a la lista principal');
                const updatedMainList = [anuncio, ...mainList];

                // Si la lista excede el tamaño máximo, recortarla
                const finalMainList = updatedMainList.slice(0, this.pageSize);

                // Actualizar el subject
                this.anunciosSubject.next(finalMainList);

                // Actualizar el store
                this.store.dispatch(
                  new fromAnuncioActions.ReadSuccess(
                    finalMainList,
                    mainList.length + 1, // Incrementar el total
                    Math.ceil((mainList.length + 1) / this.pageSize),
                    this.currentPage
                  )
                );
              }
            }

            // 2. Agregar a la lista de recientes
            const recentListCurrent = this.anunciosRecientesSubject.value;
            // Verificar si el anuncio ya está en la lista de recientes
            const existsInRecent = recentListCurrent.some(a => a.id === anuncio.id);

            if (!existsInRecent) {
              //console.log('AnuncioWsService: Agregando nuevo anuncio a la lista de recientes');
              const updatedRecentList = [anuncio, ...recentListCurrent];

              // Si la lista excede el tamaño máximo, recortarla
              const finalRecentList = updatedRecentList.slice(0, this.recentPageSize);

              // Actualizar el subject
              this.anunciosRecientesSubject.next(finalRecentList);

              // Actualizar el store
              this.store.dispatch(
                new fromAnuncioActions.ReadRecentSuccess(
                  finalRecentList,
                  recentListCurrent.length + 1, // Incrementar el total
                  Math.ceil((recentListCurrent.length + 1) / this.recentPageSize),
                  this.recentPage
                )
              );
            }

            // No solicitar una actualización completa inmediatamente para evitar que se sobrescriban nuestros cambios
            // En su lugar, programar una verificación después de un tiempo para asegurarnos de que el anuncio sigue en las listas
            setTimeout(() => {
              // Verificar si el anuncio sigue en la lista principal
              const mainList = this.anunciosSubject.value;
              const existsInMain = mainList.some(a => a.id === anuncio.id);

              // Verificar si el anuncio sigue en la lista de recientes
              const recentListCheck = this.anunciosRecientesSubject.value;
              const existsInRecent = recentListCheck.some(a => a.id === anuncio.id);

              //console.log(`AnuncioWsService: Verificando persistencia del anuncio ${anuncio.id}:`,
                //          `En lista principal: ${existsInMain}, En lista recientes: ${existsInRecent}`);

              // Si el anuncio no está en alguna de las listas, volver a agregarlo
              if (!existsInMain || !existsInRecent) {
                //console.log('AnuncioWsService: Anuncio no persistido, volviendo a agregarlo');

                // Volver a agregar a la lista principal si no está y estamos en la primera página
                if (!existsInMain && this.currentPage === 0) {
                  const updatedMainList = [anuncio, ...mainList];

                  // Si la lista excede el tamaño máximo, recortarla
                  const finalMainList = updatedMainList.slice(0, this.pageSize);

                  // Actualizar el subject
                  this.anunciosSubject.next(finalMainList);

                  // Actualizar el store
                  this.store.dispatch(
                    new fromAnuncioActions.ReadSuccess(
                      finalMainList,
                      mainList.length + 1, // Incrementar el total
                      Math.ceil((mainList.length + 1) / this.pageSize),
                      this.currentPage
                    )
                  );
                }

                // Volver a agregar a la lista de recientes si no está
                if (!existsInRecent) {
                  const updatedRecentList = [anuncio, ...recentListCheck];

                  // Si la lista excede el tamaño máximo, recortarla
                  const finalRecentList = updatedRecentList.slice(0, this.recentPageSize);

                  // Actualizar el subject
                  this.anunciosRecientesSubject.next(finalRecentList);

                  // Actualizar el store
                  this.store.dispatch(
                    new fromAnuncioActions.ReadRecentSuccess(
                      finalRecentList,
                      recentListCheck.length + 1, // Incrementar el total
                      Math.ceil((recentListCheck.length + 1) / this.recentPageSize),
                      this.recentPage
                    )
                  );
                }
              }
            }, 2000);
          }
        })
    );

    // Suscribirse a eventos de actualización y eliminación
    // Estas suscripciones también son importantes, las configuramos inmediatamente

    // Suscribirse a la lista completa de anuncios
    this.addSubscription(
      'anuncios',
      this.webSocketService
        .getMessagesByDestination(todosTopic)
        .pipe(
          // Usar debounceTime para reducir la frecuencia de actualizaciones
          debounceTime(500)
        )
        .subscribe((response: any) => {
          let anuncios: AnuncioResponse[] = [];
          let totalElements = 0;
          let totalPages = 1;

          // Manejar diferentes formatos de respuesta
          if (response) {
            // Caso 1: Respuesta es un objeto Page con content (formato Spring Data)
            if (response.content && Array.isArray(response.content)) {
              anuncios = response.content;
              totalElements = response.totalElements || anuncios.length;
              totalPages =
                response.totalPages || Math.ceil(totalElements / this.pageSize);
            }
            // Caso 2: Respuesta es directamente un array
            else if (Array.isArray(response)) {
              anuncios = response;
              totalElements = anuncios.length;
              totalPages = Math.ceil(totalElements / this.pageSize);
            }
            // Caso 3: Respuesta es un objeto GenericResponse con data que contiene Page
            else if (
              response.data &&
              response.data.content &&
              Array.isArray(response.data.content)
            ) {
              anuncios = response.data.content;
              totalElements = response.data.totalElements || anuncios.length;
              totalPages =
                response.data.totalPages ||
                Math.ceil(totalElements / this.pageSize);
            }
            // Caso 4: Respuesta es un objeto con propiedades pero sin content (formato personalizado)
            else if (typeof response === 'object' && !Array.isArray(response)) {
              // Intentar extraer anuncios de cualquier propiedad que sea un array
              for (const key in response) {
                if (Array.isArray(response[key])) {
                  anuncios = response[key];
                  break;
                }
              }
              totalElements = anuncios.length;
              totalPages = Math.ceil(totalElements / this.pageSize);
            }
          }

          // Verificar si hay un anuncio recién creado que debemos preservar
          if (AnuncioWsService.ultimoAnuncioCreado !== null && this.currentPage === 0) {
            // Buscar si el anuncio recién creado ya está en la lista recibida
            const nuevoAnuncioYaIncluido = anuncios.some(a => a.id === AnuncioWsService.ultimoAnuncioCreado);

            if (!nuevoAnuncioYaIncluido) {
             // console.log(`AnuncioWsService: Preservando anuncio recién creado ${AnuncioWsService.ultimoAnuncioCreado} que no está en la respuesta`);

              // Buscar el anuncio recién creado en la lista actual
              const currentList = this.anunciosSubject.value;
              const nuevoAnuncio = currentList.find(a => a.id === AnuncioWsService.ultimoAnuncioCreado);

              if (nuevoAnuncio) {
                // Agregar el anuncio recién creado al principio de la lista
                anuncios = [nuevoAnuncio, ...anuncios];

                // Si la lista excede el tamaño máximo, recortarla
                if (anuncios.length > this.pageSize) {
                  anuncios = anuncios.slice(0, this.pageSize);
                }

                // Incrementar el total de elementos para reflejar el nuevo anuncio
                totalElements += 1;

                // Recalcular el número total de páginas
                totalPages = Math.ceil(totalElements / this.pageSize);

                //console.log(`AnuncioWsService: Anuncio ${AnuncioWsService.ultimoAnuncioCreado} preservado en la lista. Total: ${totalElements}, Páginas: ${totalPages}`);
              }
            } else {
              //console.log(`AnuncioWsService: Anuncio recién creado ${AnuncioWsService.ultimoAnuncioCreado} ya incluido en la respuesta`);
            }
          }

          // Siempre actualizar la lista, incluso si está vacía
          // Esto es importante para manejar correctamente la paginación
          this.anunciosSubject.next(anuncios);

          // Actualizar el store para mantener sincronización
          this.store.dispatch(
            new fromAnuncioActions.ReadSuccess(
              anuncios,
              totalElements,
              totalPages,
              this.currentPage
            )
          );

          // Registrar información de depuración sobre la paginación
          //console.log(`AnuncioWsService: Recibidos ${anuncios.length} anuncios para página ${this.currentPage}, tamaño ${this.pageSize}, total ${totalElements}, páginas ${totalPages}`);
        })
    );

    // Suscribirse a eventos de actualización de anuncio
    this.addSubscription(
      'update',
      this.webSocketService
        .getMessagesByDestination('/topic/anuncios/update')
        .subscribe((anuncio: AnuncioResponse) => {
          if (anuncio) {
            // Mostrar notificación
            if (this.notification) {
              this.notification.success(
                `Anuncio actualizado: ${anuncio.titulo}`
              );
            }

            // 1. Actualizar inmediatamente la lista de anuncios recientes
            const recentList = this.anunciosRecientesSubject.value;
            const recentIndex = recentList.findIndex(
              (a) => a.id === anuncio.id
            );

            if (recentIndex !== -1) {
              // Verificar si el anuncio tiene sedeId null pero el anuncio en la lista tiene un valor
              const existingAnuncio = recentList[recentIndex];

              // Si el backend devuelve sedeId null pero el anuncio existente tiene un valor, mantener el valor existente
              if (anuncio.sedeId === null && existingAnuncio.sedeId) {
                console.log('WebSocket: Corrigiendo sedeId null en anuncio reciente actualizado:', anuncio.id);
                console.log('WebSocket: Usando sedeId existente:', existingAnuncio.sedeId);
                anuncio = {
                  ...anuncio,
                  sedeId: existingAnuncio.sedeId
                };
              }

              // Crear una copia de la lista actual
              const updatedRecentList = [...recentList];
              // Actualizar el anuncio en la lista
              updatedRecentList[recentIndex] = anuncio;
              // Actualizar el subject
              this.anunciosRecientesSubject.next(updatedRecentList);

              // También actualizar el store para mantener sincronización
              this.store.dispatch(
                new fromAnuncioActions.ReadRecentSuccess(
                  updatedRecentList,
                  updatedRecentList.length,
                  Math.ceil(updatedRecentList.length / this.recentPageSize),
                  this.recentPage
                )
              );
            }

            // 2. Actualizar inmediatamente la lista principal de anuncios
            const mainList = this.anunciosSubject.value;
            const mainIndex = mainList.findIndex((a) => a.id === anuncio.id);

            if (mainIndex !== -1) {
              // Verificar si el anuncio tiene sedeId null pero el anuncio en la lista tiene un valor
              const existingAnuncio = mainList[mainIndex];

              // Si el backend devuelve sedeId null pero el anuncio existente tiene un valor, mantener el valor existente
              if (anuncio.sedeId === null && existingAnuncio.sedeId) {
                console.log('WebSocket: Corrigiendo sedeId null en anuncio actualizado:', anuncio.id);
                console.log('WebSocket: Usando sedeId existente:', existingAnuncio.sedeId);
                anuncio = {
                  ...anuncio,
                  sedeId: existingAnuncio.sedeId
                };
              }

              // Crear una copia de la lista actual
              const updatedMainList = [...mainList];
              // Actualizar el anuncio en la lista
              updatedMainList[mainIndex] = anuncio;
              // Actualizar el subject
              this.anunciosSubject.next(updatedMainList);

              // También actualizar el store para mantener sincronización
              this.store.dispatch(
                new fromAnuncioActions.ReadSuccess(
                  updatedMainList,
                  updatedMainList.length,
                  Math.ceil(updatedMainList.length / this.pageSize),
                  this.currentPage
                )
              );
            }
          }
        })
    );

    // Suscribirse a eventos de eliminación de anuncio
    this.addSubscription(
      'delete',
      this.webSocketService
        .getMessagesByDestination('/topic/anuncios/delete')
        .subscribe((anuncioId: number) => {
          if (anuncioId) {
            // 1. Actualizar inmediatamente la lista de anuncios recientes
            const recentList = this.anunciosRecientesSubject.value;

            // Buscar el anuncio en la lista para mostrar su título en la notificación
            const deletedAnuncio = recentList.find((a) => a.id === anuncioId);

            // Mostrar notificación con el título si está disponible
            if (this.notification) {
              if (deletedAnuncio) {
                this.notification.success(
                  `Anuncio eliminado: ${deletedAnuncio.titulo}`
                );
              } else {
                this.notification.success(`Anuncio eliminado`);
              }
            }

            // Filtrar el anuncio eliminado de la lista
            const updatedRecentList = recentList.filter(
              (a) => a.id !== anuncioId
            );

            // Si la lista ha cambiado, actualizar el subject
            if (updatedRecentList.length !== recentList.length) {
              this.anunciosRecientesSubject.next(updatedRecentList);

              // También actualizar el store para mantener sincronización
              this.store.dispatch(
                new fromAnuncioActions.ReadRecentSuccess(
                  updatedRecentList,
                  updatedRecentList.length,
                  Math.ceil(updatedRecentList.length / this.recentPageSize),
                  this.recentPage
                )
              );
            }

            // 2. Actualizar inmediatamente la lista principal de anuncios
            const mainList = this.anunciosSubject.value;

            // Filtrar el anuncio eliminado de la lista principal
            const updatedMainList = mainList.filter((a) => a.id !== anuncioId);

            // Si la lista ha cambiado, actualizar el subject
            if (updatedMainList.length !== mainList.length) {
              this.anunciosSubject.next(updatedMainList);

              // También actualizar el store para mantener sincronización
              this.store.dispatch(
                new fromAnuncioActions.ReadSuccess(
                  updatedMainList,
                  updatedMainList.length,
                  Math.ceil(updatedMainList.length / this.pageSize),
                  this.currentPage
                )
              );
            }
          }
        })
    );

    this.initialized = true;

    // Verificar si el usuario es ADMIN
    const userStr = localStorage.getItem('user');
    const isAdmin = userStr ? JSON.parse(userStr).role === 'ADMIN' : false;

    // No solicitar anuncios recientes automáticamente en el listado de anuncios
    // Esto evita inicializar tópicos innecesarios
    //console.log('AnuncioWsService: Evitando solicitud automática de anuncios recientes en el listado');

    // Solicitar la lista completa de anuncios usando SIEMPRE el endpoint todos
    // Para ADMIN, no incluir sedeId en la solicitud y añadir el rol
    if (isAdmin) {
      this.webSocketService.sendMessage('/app/anuncios.todos', {
        page: this.currentPage,
        size: this.pageSize,
        role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
      });
    } else {
      this.webSocketService.sendMessage('/app/anuncios.todos', {
        page: this.currentPage,
        size: this.pageSize
        // No enviamos sedeId para obtener anuncios de todas las sedes
      });
    }
  }

  /**
   * Añade una suscripción al registro para limpieza posterior
   * Evita suscripciones duplicadas usando un nombre único
   */
  private addSubscription(name: string, subscription: Subscription): void {
    // Buscar si ya existe una suscripción con este nombre
    const existingIndex = this.subscriptions.findIndex(
      (sub) => (sub as any)._name === name
    );

    // Si existe, eliminarla primero
    if (existingIndex !== -1) {
      this.subscriptions[existingIndex].unsubscribe();
      this.subscriptions.splice(existingIndex, 1);
    }

    // Añadir un nombre a la suscripción para identificarla
    (subscription as any)._name = name;

    // Añadir suscripción al registro
    this.subscriptions.push(subscription);
  }

  // Variable para controlar el tiempo de la última solicitud de anuncios
  private ultimaSolicitudAnuncios = 0;
  // Tiempo mínimo entre solicitudes de anuncios (500ms)
  private tiempoMinimoEntreSolicitudesAnuncios = 500;
  // Variable estática para control global de solicitudes de anuncios
  static anunciosSolicitados: boolean = false;

  // Variable estática para almacenar el ID del último anuncio creado
  static ultimoAnuncioCreado: number | null = null;

  /**
   * Solicita la lista de anuncios al servidor
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket, sin solicitudes HTTP
   * Evita solicitudes duplicadas con un límite de tiempo
   */
  requestAnuncios(
    page: number = 0,
    size: number = 8, // Cambiado a 8 para coincidir con el backend
    forzar: boolean = false
  ): void {
    // Guardar los valores de paginación
    this.currentPage = page;
    this.pageSize = size;

    // Verificar si el usuario es ADMIN
    const userStr = localStorage.getItem('user');
    const isAdmin = userStr ? JSON.parse(userStr).role === 'ADMIN' : false;

    // Si es una solicitud forzada, ignorar todas las restricciones
    if (forzar) {
      // Actualizar los tiempos de la última solicitud
      const ahora = Date.now();
      this.ultimaSolicitudAnuncios = ahora;
      AnuncioWsService.ultimaSolicitudGlobal = ahora;

      // Verificar si WebSocket está conectado
      if (this.webSocketService.isConnected()) {
        // Asegurarse de que estamos suscritos al tópico correcto
        try {
          this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/todos', 'TOPIC');
        } catch (error) {
          console.error('Error al suscribirse a tópico /topic/anuncios/todos:', error);
        }

        // Asegurarse de que los parámetros de paginación son números válidos
        const pageNum = Number(page);
        const sizeNum = Number(size);

        //console.log(`AnuncioWsService: Solicitando anuncios para página ${pageNum}, tamaño ${sizeNum}`);

        // Usar exclusivamente WebSocket para obtener todos los anuncios (sin filtrar por estado)
        // Para ADMIN, asegurarse de que se envía el rol en la solicitud
        if (isAdmin) {
          this.webSocketService.sendMessage('/app/anuncios.todos', {
            page: pageNum,
            size: sizeNum,
            role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
          });
        } else {
          this.webSocketService.sendMessage('/app/anuncios.todos', { page: pageNum, size: sizeNum });
        }

        // Marcar que ya se han solicitado anuncios en esta sesión, pero permitir solicitudes
        // para diferentes páginas
        AnuncioWsService.anunciosSolicitados = true;
        return;
      } else {
        // Intentar conectar WebSocket si no está conectado
        this.webSocketService.connect();

        // Esperar a que se conecte el WebSocket y luego solicitar anuncios
        const sub = this.webSocketService
          .getConnectionStatus()
          .pipe(
            filter((connected) => connected === true),
            take(1)
          )
          .subscribe(() => {
            // Asegurarse de que estamos suscritos al tópico correcto
            try {
              this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/todos', 'TOPIC');
            } catch (error) {
              console.error('Error al suscribirse a tópico /topic/anuncios/todos:', error);
            }

            // Enviar la solicitud inmediatamente cuando se conecte (sin filtrar por estado)
            // Para ADMIN, asegurarse de que se envía el rol en la solicitud
            if (isAdmin) {
              this.webSocketService.sendMessage('/app/anuncios.todos', {
                page,
                size,
                role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
              });
            } else {
              this.webSocketService.sendMessage('/app/anuncios.todos', { page, size });
            }

            AnuncioWsService.anunciosSolicitados = true;
          });

        this.subscriptions.push(sub);
        return;
      }
    }

    // Para solicitudes no forzadas, aplicar restricciones

    // Permitir siempre solicitudes de cambio de página, incluso si ya se han solicitado anuncios
    // Esto es necesario para que la paginación funcione correctamente
    // Solo verificamos si es la misma página que ya tenemos cargada
    if (AnuncioWsService.anunciosSolicitados && this.currentPage === page && this.pageSize === size) {
      //console.log('AnuncioWsService: Evitando solicitud duplicada para la misma página:', page);
      return;
    }

    // Verificar si ha pasado suficiente tiempo desde la última solicitud (control local)
    const ahora = Date.now();
    if (
      ahora - this.ultimaSolicitudAnuncios <
      this.tiempoMinimoEntreSolicitudesAnuncios
    ) {
      return;
    }

    // Verificar si ha pasado suficiente tiempo desde la última solicitud (control global)
    if (
      ahora - AnuncioWsService.ultimaSolicitudGlobal <
      AnuncioWsService.TIEMPO_MINIMO_ENTRE_SOLICITUDES_GLOBAL
    ) {
      return;
    }

    // Actualizar los tiempos de la última solicitud
    this.ultimaSolicitudAnuncios = ahora;
    AnuncioWsService.ultimaSolicitudGlobal = ahora;

    // Reutilizar la variable isAdmin ya declarada anteriormente

    // Verificar si WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      // Asegurarse de que estamos suscritos al tópico correcto
      try {
        this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/todos', 'TOPIC');
      } catch (error) {
        console.error('Error al suscribirse a tópico /topic/anuncios/todos:', error);
      }

      // Usar exclusivamente WebSocket para obtener todos los anuncios (sin filtrar por estado)
      // Para ADMIN, asegurarse de que se envía el rol en la solicitud
      if (isAdmin) {
        this.webSocketService.sendMessage('/app/anuncios.todos', {
          page,
          size,
          role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
        });
      } else {
        this.webSocketService.sendMessage('/app/anuncios.todos', { page, size });
      }

      // Marcar que ya se han solicitado anuncios en esta sesión
      AnuncioWsService.anunciosSolicitados = true;
    } else {
      // Intentar conectar WebSocket si no está conectado
      this.webSocketService.connect();

      // Esperar a que se conecte el WebSocket y luego solicitar anuncios
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter((connected) => connected === true),
          take(1)
        )
        .subscribe(() => {
          // Asegurarse de que estamos suscritos al tópico correcto
          try {
            this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/todos', 'TOPIC');
          } catch (error) {
            console.error('Error al suscribirse a tópico /topic/anuncios/todos:', error);
          }

          // Enviar la solicitud inmediatamente cuando se conecte (sin filtrar por estado)
          // Para ADMIN, asegurarse de que se envía el rol en la solicitud
          if (isAdmin) {
            // Asegurarse de que los parámetros de paginación son números válidos
            const pageNum = Number(page);
            const sizeNum = Number(size);

            this.webSocketService.sendMessage('/app/anuncios.todos', {
              page: pageNum,
              size: sizeNum,
              role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
            });
          } else {
            // Asegurarse de que los parámetros de paginación son números válidos
            const pageNum = Number(page);
            const sizeNum = Number(size);

            this.webSocketService.sendMessage('/app/anuncios.todos', { page: pageNum, size: sizeNum });
          }

          AnuncioWsService.anunciosSolicitados = true;
        });

      this.subscriptions.push(sub);
    }
    // No se realiza ninguna solicitud HTTP como respaldo
  }

  // Variable para controlar el tiempo de la última solicitud
  private ultimaSolicitudAnunciosRecientes = 0;
  // Tiempo mínimo entre solicitudes (500ms)
  private tiempoMinimoEntreSolicitudes = 500;

  /**
   * Solicita la lista de anuncios recientes al servidor
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket, sin solicitudes HTTP
   * Evita solicitudes duplicadas con un límite de tiempo
   * GARANTIZA: Solo una solicitud de anuncios recientes en toda la sesión durante el login
   *
   * @param page Número de página a solicitar (por defecto 0)
   * @param size Tamaño de página (por defecto 6, coincide con el backend)
   * @param forzar Si es true, ignora las restricciones de tiempo y solicita los anuncios de todos modos
   */
  requestAnunciosRecientes(
    page: number = 0,
    size: number = 6, // Mantener en 6 para coincidir con el backend
    forzar: boolean = false
  ): void {

    this.recentPage = page;
    this.recentPageSize = size;

    // Verificar si el usuario es ADMIN
    const userStr = localStorage.getItem('user');
    const isAdmin = userStr ? JSON.parse(userStr).role === 'ADMIN' : false;

    // Obtener el ID de la sede del usuario (solo si no es ADMIN)
    const sedeId = !isAdmin ? this.sedeUserService.getSedeIdSync() : null;

    // Si es una solicitud forzada, ignorar todas las restricciones
    if (forzar) {

      // Actualizar los tiempos de la última solicitud
      const ahora = Date.now();
      this.ultimaSolicitudAnunciosRecientes = ahora;
      AnuncioWsService.ultimaSolicitudGlobal = ahora;

      // Verificar si WebSocket está conectado
      if (this.webSocketService.isConnected()) {

        // Asegurarse de que estamos suscritos al tópico correcto
        try {
          if (isAdmin) {
            this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/recientes', 'TOPIC');
          } else if (sedeId) {
            this.webSocketService.subscribeToDynamicTopic(`/topic/anuncios/recientes/sede/${sedeId}`, 'TOPIC');
          }
        } catch (error) {
          console.error('Error al suscribirse a tópico de anuncios recientes:', error);
        }

        // Usar exclusivamente WebSocket con el tópico específico por sede
        // Para ADMIN, no incluir sedeId en la solicitud
        if (isAdmin) {
          // Asegurarse de que los parámetros de paginación son números válidos
          const pageNum = Number(page);
          const sizeNum = Number(size);

          this.webSocketService.sendMessage('/app/anuncios.recientes', {
            page: pageNum,
            size: sizeNum,
            role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
          });
        } else {
          // Asegurarse de que los parámetros de paginación son números válidos
          const pageNum = Number(page);
          const sizeNum = Number(size);

          this.webSocketService.sendMessage('/app/anuncios.recientes', {
            page: pageNum,
            size: sizeNum,
            sedeId
          });
        }

        // Marcar que ya se han solicitado anuncios recientes en esta sesión
        AnuncioWsService.anunciosRecientesSolicitados = true;

        // Inicializar el servicio si aún no se ha hecho
        if (!AnuncioWsService.servicioInicializado) {
          AnuncioWsService.servicioInicializado = true;
        }
        return;
      } else {

        // Intentar conectar WebSocket si no está conectado
        this.webSocketService.connect();

        // Esperar a que se conecte el WebSocket y luego solicitar anuncios
        const sub = this.webSocketService
          .getConnectionStatus()
          .pipe(
            filter((connected) => connected === true),
            take(1)
          )
          .subscribe(() => {

            // Asegurarse de que estamos suscritos al tópico correcto
            try {
              if (isAdmin) {
                this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/recientes', 'TOPIC');
              } else if (sedeId) {
                this.webSocketService.subscribeToDynamicTopic(`/topic/anuncios/recientes/sede/${sedeId}`, 'TOPIC');
              }
            } catch (error) {
              console.error('Error al suscribirse a tópico de anuncios recientes:', error);
            }

            // Enviar la solicitud inmediatamente cuando se conecte
            // Para ADMIN, no incluir sedeId en la solicitud
            if (isAdmin) {
              this.webSocketService.sendMessage('/app/anuncios.recientes', {
                page,
                size,
                role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
              });
            } else {
              this.webSocketService.sendMessage('/app/anuncios.recientes', { page, size, sedeId });
            }

            AnuncioWsService.anunciosRecientesSolicitados = true;

            // Inicializar el servicio si aún no se ha hecho
            if (!AnuncioWsService.servicioInicializado) {
              AnuncioWsService.servicioInicializado = true;
            }
          });

        this.subscriptions.push(sub);
        return;
      }
    }

    // Para solicitudes no forzadas, aplicar restricciones

    // Si ya se han solicitado anuncios recientes en esta sesión, no hacer nada
    if (AnuncioWsService.anunciosRecientesSolicitados) {
      return;
    }

    // Verificar si ha pasado suficiente tiempo desde la última solicitud (control local)
    const ahora = Date.now();
    if (
      ahora - this.ultimaSolicitudAnunciosRecientes <
      this.tiempoMinimoEntreSolicitudes
    ) {
      return;
    }

    // Verificar si ha pasado suficiente tiempo desde la última solicitud (control global)
    if (
      ahora - AnuncioWsService.ultimaSolicitudGlobal <
      AnuncioWsService.TIEMPO_MINIMO_ENTRE_SOLICITUDES_GLOBAL
    ) {
      return;
    }

    // Actualizar los tiempos de la última solicitud
    this.ultimaSolicitudAnunciosRecientes = ahora;
    AnuncioWsService.ultimaSolicitudGlobal = ahora;

    // Reutilizar la variable isAdmin ya declarada anteriormente

    // Verificar si WebSocket está conectado
    if (this.webSocketService.isConnected()) {

      // Asegurarse de que estamos suscritos al tópico correcto
      try {
        if (isAdmin) {
          this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/recientes', 'TOPIC');
        } else if (sedeId) {
          this.webSocketService.subscribeToDynamicTopic(`/topic/anuncios/recientes/sede/${sedeId}`, 'TOPIC');
        }
      } catch (error) {
        console.error('Error al suscribirse a tópico de anuncios recientes:', error);
      }

      // Usar exclusivamente WebSocket
      // Para ADMIN, no incluir sedeId en la solicitud
      if (isAdmin) {
        this.webSocketService.sendMessage('/app/anuncios.recientes', {
          page,
          size,
          role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
        });
      } else {
        this.webSocketService.sendMessage('/app/anuncios.recientes', { page, size, sedeId });
      }

      // Marcar que ya se han solicitado anuncios recientes en esta sesión
      AnuncioWsService.anunciosRecientesSolicitados = true;

      // Inicializar el servicio si aún no se ha hecho
      if (!AnuncioWsService.servicioInicializado) {
        AnuncioWsService.servicioInicializado = true;
      }
    } else {

      // Intentar conectar WebSocket si no está conectado
      this.webSocketService.connect();

      // Esperar a que se conecte el WebSocket y luego solicitar anuncios
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter((connected) => connected === true),
          take(1)
        )
        .subscribe(() => {

          // Asegurarse de que estamos suscritos al tópico correcto
          try {
            if (isAdmin) {
              this.webSocketService.subscribeToDynamicTopic('/topic/anuncios/recientes', 'TOPIC');
            } else if (sedeId) {
              this.webSocketService.subscribeToDynamicTopic(`/topic/anuncios/recientes/sede/${sedeId}`, 'TOPIC');
            }
          } catch (error) {
            console.error('Error al suscribirse a tópico de anuncios recientes:', error);
          }

          // Enviar la solicitud inmediatamente cuando se conecte
          // Para ADMIN, no incluir sedeId en la solicitud
          if (isAdmin) {
            // Asegurarse de que los parámetros de paginación son números válidos
            const pageNum = Number(page);
            const sizeNum = Number(size);

            this.webSocketService.sendMessage('/app/anuncios.recientes', {
              page: pageNum,
              size: sizeNum,
              role: 'ADMIN' // Añadir el rol para que el backend pueda identificar que es un admin
            });
          } else {
            // Asegurarse de que los parámetros de paginación son números válidos
            const pageNum = Number(page);
            const sizeNum = Number(size);

            this.webSocketService.sendMessage('/app/anuncios.recientes', {
              page: pageNum,
              size: sizeNum,
              sedeId
            });
          }

          AnuncioWsService.anunciosRecientesSolicitados = true;

          // Inicializar el servicio si aún no se ha hecho
          if (!AnuncioWsService.servicioInicializado) {
            AnuncioWsService.servicioInicializado = true;
          }
        });

      this.subscriptions.push(sub);
    }
    // No se realiza ninguna solicitud HTTP como respaldo
  }

  /**
   * Actualiza todas las listas de anuncios
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket para todos los anuncios
   */
  refreshAnuncios(): void {
    //console.log(`AnuncioWsService: Actualizando todas las listas de anuncios. Página actual: ${this.currentPage}, tamaño: ${this.pageSize}`);

    // Resetear los flags para forzar nuevas solicitudes
    AnuncioWsService.anunciosSolicitados = false;
    AnuncioWsService.anunciosRecientesSolicitados = false;

    // Verificar si estamos en la página de listado de anuncios
    const isAnunciosList = window.location.href.includes('anuncios/list');

    // Para anuncios normales, usar EXCLUSIVAMENTE WebSocket
    this.requestAnuncios(this.currentPage, this.pageSize, true);

    // Para anuncios recientes, usar EXCLUSIVAMENTE WebSocket
    // Solo si NO estamos en la página de listado de anuncios
    if (!isAnunciosList) {
      this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);
    }
  }

  // Variable estática para controlar inicialización global
  private static servicioInicializadoGlobal = false;

  /**
   * Inicializa el servicio después del login
   * Configura las suscripciones WebSocket inmediatamente para recibir actualizaciones en tiempo real
   */
  initializeAfterLogin(): void {
    // Evitar inicializaciones duplicadas a nivel de instancia
    if (this.initializingAfterLogin) {
      return;
    }

    // Evitar inicializaciones duplicadas a nivel global
    if (AnuncioWsService.servicioInicializadoGlobal) {
      return;
    }

    this.initializingAfterLogin = true;
    AnuncioWsService.servicioInicializadoGlobal = true;

    // Configurar suscripciones WebSocket inmediatamente
    // Esto asegura que estemos escuchando los tópicos desde el principio
    if (!this.initialized) {
      this.setupSubscriptions();
    }

    // Para anuncios recientes, usar EXCLUSIVAMENTE WebSocket
    // No se realiza ninguna solicitud HTTP

    // Usar el método optimizado para solicitar anuncios recientes
    // Este método ya maneja la conexión WebSocket y los reintentos
    this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);

    // Marcar como no inicializando después de un breve retraso
    setTimeout(() => {
      this.initializingAfterLogin = false;
    }, 500);
  }

  /**
   * Obtiene el observable de anuncios
   */
  getAnuncios(): Observable<AnuncioResponse[]> {
    return this.anunciosSubject.asObservable();
  }

  /**
   * Obtiene el observable de anuncios recientes
   */
  getAnunciosRecientes(): Observable<AnuncioResponse[]> {
    return this.anunciosRecientesSubject.asObservable();
  }

  /**
   * Obtiene el estado de conexión
   */
  getConnectionStatus(): Observable<boolean> {
    return this.connectionStatus.asObservable();
  }

  /**
   * Verifica si el servicio está inicializado
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Método de depuración para forzar la solicitud de anuncios recientes
   * Útil para diagnosticar problemas con la recepción de anuncios
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket, sin solicitudes HTTP
   */
  forceRequestAnunciosRecientes(): void {
    // Incluso si el WebSocket no está conectado, intentar conectar y solicitar
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();

      // Esperar a que se conecte y luego solicitar
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter(connected => connected),
          take(1)
        )
        .subscribe(() => {
          // Resetear los flags para forzar una nueva solicitud
          AnuncioWsService.anunciosRecientesSolicitados = false;

          // Llamar al método requestAnunciosRecientes con el parámetro forzar=true
          // El método ya incluirá el sedeId automáticamente
          this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);
        });

      this.subscriptions.push(sub);
      return;
    }

    // Si ya está conectado, solicitar directamente

    // Resetear los flags para forzar una nueva solicitud
    AnuncioWsService.anunciosRecientesSolicitados = false;

    // Llamar al método requestAnunciosRecientes con el parámetro forzar=true
    this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);
  }

  /**
   * Fuerza la actualización de todas las listas de anuncios
   * Útil después de operaciones de creación, actualización o eliminación
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket para todos los anuncios
   */
  forceUpdateAllLists(): void {
    // Incluso si el WebSocket no está conectado, intentar conectar y solicitar
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();

      // Esperar a que se conecte y luego solicitar
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter(connected => connected),
          take(1)
        )
        .subscribe(() => {
          // Resetear los flags para forzar nuevas solicitudes
          AnuncioWsService.anunciosSolicitados = false;
          AnuncioWsService.anunciosRecientesSolicitados = false;

          // Verificar si estamos en la página de listado de anuncios
          const isAnunciosList = window.location.href.includes('anuncios/list');

          // Para anuncios normales, usar EXCLUSIVAMENTE WebSocket
          // Usar el tamaño de página actual
          this.requestAnuncios(this.currentPage, this.pageSize, true);

          // También solicitar anuncios recientes, pero solo si NO estamos en la página de listado
          if (!isAnunciosList) {
            this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);
          }
        });

      this.subscriptions.push(sub);
      return;
    }

    // Si ya está conectado, solicitar directamente
    // Resetear los flags para forzar nuevas solicitudes
    AnuncioWsService.anunciosSolicitados = false;
    AnuncioWsService.anunciosRecientesSolicitados = false;

    // Verificar si estamos en la página de listado de anuncios
    const isAnunciosList = window.location.href.includes('anuncios/list');

    // Para anuncios normales, usar EXCLUSIVAMENTE WebSocket
    this.requestAnuncios(this.currentPage, this.pageSize, true);

    // También solicitar anuncios recientes, pero solo si NO estamos en la página de listado
    if (!isAnunciosList) {
      this.requestAnunciosRecientes(this.recentPage, this.recentPageSize, true);
    }
  }

  /**
   * Método de depuración para diagnosticar problemas con WebSocket
   * Fuerza la actualización de todas las listas de anuncios
   * OPTIMIZADO: Usa EXCLUSIVAMENTE WebSocket para todos los anuncios
   */
  diagnosticarConexion(): void {
    // Solicitar explícitamente todos los anuncios para forzar una actualización
    if (this.webSocketService.isConnected()) {
      // Para anuncios normales, usar EXCLUSIVAMENTE WebSocket
      this.requestAnuncios(this.currentPage, this.pageSize, true);
    }
  }

  /**
   * Limpia las suscripciones al destruir el servicio
   */
  ngOnDestroy(): void {
    // Limpiar recursos
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];
  }
}
