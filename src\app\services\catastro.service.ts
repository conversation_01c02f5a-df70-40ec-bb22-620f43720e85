import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';

/**
 * Servicio para interactuar con la API del Catastro a través del proxy del backend
 */
@Injectable({
  providedIn: 'root'
})
export class CatastroService {
  private baseUrl = `${environment.url}api/catastro`;
  private baseVentas = `${environment.urlVentas}`;

  constructor(private http: HttpClient) { }

  /**
   * Obtiene todas las provincias o filtra por nombre
   * @param filtro Filtro opcional para buscar provincias por nombre
   * @returns Observable con la respuesta de la API
   */
  getProvincias(filtro: string = ''): Observable<any> {
    return this.http.get<any>(`${this.baseVentas}provincias/` ).pipe(
      catchError(error => {
        console.error('Error al obtener provincias:', error);
        return of([]);
      })
    );
  }

  /**
   * Obtiene los municipios de una provincia
   * @param codigoProvincia Código de la provincia
   * @param filtro Filtro opcional para buscar municipios por nombre
   * @returns Observable con la respuesta de la API
   */
  getMunicipios(codigoProvincia: string | number, filtro: string = '' ): Observable<any> {
    return this.http.get<any>(`${this.baseVentas}/municipios/provincia/${codigoProvincia}/` );
  }

  /**
   * Obtiene las vías de un municipio
   * @param codigoProvincia Código de la provincia
   * @param codigoMunicipio Código del municipio
   * @param filtro Filtro opcional para buscar vías por nombre
   * @returns Observable con la respuesta de la API
   */
  getVias(codigoProvincia: string | number, codigoMunicipio: string | number, filtro: string = ''): Observable<any> {
    return this.http.get<any>(`${this.baseVentas}/vias/municipio/${codigoMunicipio}/provincia/${codigoProvincia}/` );
  }

  /**
   * Obtiene los municipios por código postal
   * @param codigoPostal Código postal a buscar
   */
  getMunicipioByCP(codigoPostal: string): Observable<any[]> {
    return this.http.post<any[]>(`${this.baseUrl}/municipios-cp`, { codigoPostal }).pipe(
      catchError(error => {
        console.error('Error al obtener municipios por CP:', error);
        return of([]);
      })
    );
  }

  /**
   * Obtiene las vías de un municipio por su código
   * @param codigoMunicipio Código del municipio
   */
  getViasByMunicipio(codigoMunicipio: string): Observable<any[]> {
    return this.http.post<any[]>(`${this.baseUrl}/vias-municipio`, { codigoMunicipio }).pipe(
      catchError(error => {
        console.error('Error al obtener vías del municipio:', error);
        return of([]);
      })
    );
  }

  // El método getProvincias ya está definido arriba

  /**
   * Obtiene los municipios de una provincia
   * @param codigoProvincia Código de la provincia
   */
  getMunicipiosByProvincia(codigoProvincia: string): Observable<any[]> {
    return this.http.post<any[]>(`${this.baseUrl}/municipios-provincia`, { codigoProvincia }).pipe(
      catchError(error => {
        console.error('Error al obtener municipios de la provincia:', error);
        return of([]);
      })
    );
  }
}
