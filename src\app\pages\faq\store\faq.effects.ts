import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { FaqService } from '@app/services/faq/faq.service';
import * as FaqActions from './faq.actions';
import * as WebSocketActions from '@app/services/websocket/websocket.actions';
import { map, mergeMap, catchError, filter } from 'rxjs/operators';
import { of } from 'rxjs';

@Injectable()
export class FaqEffects {
  constructor(private actions$: Actions, private faqSrv: FaqService) {}

  loadFaqs$ = createEffect(() =>
    this.actions$.pipe(
      ofType(FaqActions.loadFaqs),
      mergeMap(() =>
        this.faqSrv.getAll().pipe(
          map((faqs) => FaqActions.loadFaqsSuccess({ faqs })),
          catchError((error) => of(FaqActions.loadFaqsFailure({ error })))
        )
      )
    )
  );

  createFaq$ = createEffect(() =>
    this.actions$.pipe(
      ofType(FaqActions.createFaq),
      mergeMap(({ faq }) => {
        console.log('Effect - createFaq - Recibido:', faq);
        return this.faqSrv.create(faq).pipe(
          map((f) => {
            console.log('Effect - createFaq - Éxito:', f);
            return FaqActions.createFaqSuccess({ faq: f });
          }),
          catchError((error) => {
            console.error('Effect - createFaq - Error:', error);
            return of(FaqActions.createFaqFailure({ error }));
          })
        );
      })
    )
  );

  updateFaq$ = createEffect(() =>
    this.actions$.pipe(
      ofType(FaqActions.updateFaq),
      mergeMap(({ id, faq }) => {
        console.log('Effect - updateFaq - Recibido:', { id, faq });
        return this.faqSrv.update(id, faq).pipe(
          map((f) => {
            console.log('Effect - updateFaq - Éxito:', f);
            return FaqActions.updateFaqSuccess({ faq: f });
          }),
          catchError((error) => {
            console.error('Effect - updateFaq - Error:', error);
            return of(FaqActions.updateFaqFailure({ error }));
          })
        );
      })
    )
  );

  deleteFaq$ = createEffect(() =>
    this.actions$.pipe(
      ofType(FaqActions.deleteFaq),
      mergeMap(({ id }) =>
        this.faqSrv.remove(id).pipe(
          map(() => FaqActions.deleteFaqSuccess({ id })),
          catchError((error) => of(FaqActions.deleteFaqFailure({ error })))
        )
      )
    )
  );

  /** Cuando tu WebSocketService emita `FAQS_UPDATED`, refrescamos el store */
  faqsWs$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WebSocketActions.webSocketMessageReceived),
      // Filtramos sólo si el backend mandó la lista de FAQs
      map(({ messageType, messagePayload }) =>
        messageType === 'FAQS_UPDATED' ? messagePayload : null
      ),
      filter((faqs): faqs is any => !!faqs),
      map((faqs) => FaqActions.faqsUpdatedWs({ faqs }))
    )
  );
}
