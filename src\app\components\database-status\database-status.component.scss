.database-status-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin: 8px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-right: 16px;
  
  &.online {
    color: #4caf50;
  }
  
  &.offline {
    color: #f44336;
  }
  
  &.checking {
    color: #ff9800;
  }
  
  mat-icon {
    margin-right: 8px;
  }
}

.status-text {
  font-weight: 500;
}

.last-checked {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-right: 16px;
  flex-grow: 1;
}

.rotating {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Tema oscuro
:host-context(.dark-theme) {
  .database-status-container {
    background-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .last-checked {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .status-indicator {
    &.online {
      color: #81c784;
    }
    
    &.offline {
      color: #e57373;
    }
    
    &.checking {
      color: #ffb74d;
    }
  }
}
