<div class="inline-flex items-center ml-1.5 md:ml-2.5">
  <div
    class="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full relative"
    [ngClass]="{
      'bg-green-500 shadow-[0_0_4px_#22C55E] dark:bg-green-400 dark:shadow-[0_0_4px_#4ADE80]': isOnline,
      'bg-red-500 shadow-[0_0_4px_#EF4444] dark:bg-red-400 dark:shadow-[0_0_4px_#F87171]': !isOnline
    }">
    <div
      *ngIf="isOnline"
      class="absolute inset-0 bg-green-500 dark:bg-green-400 rounded-full animate-ping opacity-50 scale-150">
    </div>
  </div>
</div>
