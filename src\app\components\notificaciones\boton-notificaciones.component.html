<!-- Estilos para la animación de bordes -->
<style>
  @keyframes rotateBorder {
    0% {
      border-top-color: #3b82f6;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: transparent;
    }
    25% {
      border-top-color: transparent;
      border-right-color: #3b82f6;
      border-bottom-color: transparent;
      border-left-color: transparent;
    }
    50% {
      border-top-color: transparent;
      border-right-color: transparent;
      border-bottom-color: #3b82f6;
      border-left-color: transparent;
    }
    75% {
      border-top-color: transparent;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: #3b82f6;
    }
    100% {
      border-top-color: #3b82f6;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: transparent;
    }
  }

  @media (prefers-color-scheme: dark) {
    @keyframes rotateBorder {
      0% {
        border-top-color: #60a5fa;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
      }
      25% {
        border-top-color: transparent;
        border-right-color: #60a5fa;
        border-bottom-color: transparent;
        border-left-color: transparent;
      }
      50% {
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: #60a5fa;
        border-left-color: transparent;
      }
      75% {
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: #60a5fa;
      }
      100% {
        border-top-color: #60a5fa;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
      }
    }
  }
</style>

<!-- Botón de notificaciones con Tailwind CSS -->
<div class="relative flex items-center">
  <div
    class="relative w-8 h-8 md:w-10 md:h-10 group cursor-pointer"
    (click)="cargarNotificaciones()"
  >
    <!-- Borde giratorio -->
    <div
      class="absolute inset-0 border-2 border-transparent rounded-none"
      style="animation: rotateBorder 2s infinite linear"
    ></div>

    <!-- Botón principal -->
    <button
      mat-icon-button
      [matMenuTriggerFor]="menuNotificaciones"
      class="relative z-10 w-full h-full flex items-center justify-center transition-colors duration-200 focus:outline-none cursor-pointer !p-0"
      matTooltip="Notificaciones"
    >
      <mat-icon
        class="text-blue-900 dark:text-blue-100 text-base md:text-[20px] !m-0"
        >notifications</mat-icon
      >
    </button>

    <!-- Badge de notificaciones no leídas -->
    <span
      *ngIf="contadorNoLeidas > 0"
      class="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 md:w-5 md:h-5 text-xs font-bold text-white bg-red-500 dark:bg-red-600 rounded-full animate-pulse z-20"
    >
      {{ contadorNoLeidas > 99 ? "99+" : contadorNoLeidas }}
    </span>
  </div>
</div>

<!-- Menú de notificaciones -->
<mat-menu
  #menuNotificaciones="matMenu"
  xPosition="before"
  yPosition="below"
  [overlapTrigger]="false"
  class="!w-[calc(100vw-20px)] !max-w-[450px] sm:!w-[450px]"
>
  <ng-template matMenuContent>
    <div
      class="w-full max-h-[80vh] overflow-hidden rounded-md shadow-lg bg-white dark:bg-[#0a1628] dark:border dark:border-blue-500/20"
      (click)="$event.stopPropagation()"
    >
      <app-notificaciones></app-notificaciones>
    </div>
  </ng-template>
</mat-menu>
