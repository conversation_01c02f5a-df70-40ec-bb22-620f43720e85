import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { IpPermitidaDialogComponent } from './ip-permitida-dialog/ip-permitida-dialog.component';
import { IpPermitidasService, IpPermitida } from '@app/services/ip-permitidas.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-ip-permitidas',
  templateUrl: './ip-permitidas.component.html',
  styleUrls: ['./ip-permitidas.component.scss']
})
export class IpPermitidasComponent implements OnInit {
  displayedColumns: string[] = ['ip', 'descripcion', 'fecha_expiracion', 'is_active', 'acciones'];
  dataSource!: MatTableDataSource<IpPermitida>;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  loading = false;

  constructor(
    private ipPermitidasService: IpPermitidasService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.cargarIpPermitidas();
  }

  cargarIpPermitidas(): void {
    this.loading = true;
    this.ipPermitidasService.getAll().subscribe({
      next: (response) => {
        this.dataSource = new MatTableDataSource(response as unknown as IpPermitida[]);
        this.dataSource.paginator = this.paginator;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar IPs permitidas:', error);
        this.loading = false;
        this.snackBar.open('Error al cargar las IPs permitidas', 'Cerrar', {
          duration: 3000
        });
      }
    });
  }

  abrirDialog(ipPermitida?: IpPermitida): void {
    const dialogRef = this.dialog.open(IpPermitidaDialogComponent, {
      width: '500px',
      data: ipPermitida || {}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.id) {  // Si tiene ID, es una actualización
          this.actualizarIpPermitida(result);
        } else {  // Si no tiene ID, es una creación
          this.crearIpPermitida(result);
        }
      }
    });
  }

  crearIpPermitida(ipPermitida: IpPermitida): void {
    this.loading = true;
    this.ipPermitidasService.create(ipPermitida).subscribe({
      next: (response) => {
        this.cargarIpPermitidas();
        this.snackBar.open('IP permitida creada exitosamente', 'Cerrar', {
          duration: 3000
        });
      },
      error: (error) => {
        console.error('Error al crear IP permitida:', error);
        this.loading = false;
        this.snackBar.open('Error al crear la IP permitida', 'Cerrar', {
          duration: 3000
        });
      }
    });
  }

  actualizarIpPermitida(ipPermitida: IpPermitida): void {
    this.loading = true;
    // Asegurarse de que el ID existe antes de actualizar
    if (!ipPermitida.id) {
      this.snackBar.open('Error: ID no encontrado', 'Cerrar', {
        duration: 3000
      });
      this.loading = false;
      return;
    }

    this.ipPermitidasService.update(ipPermitida.id, ipPermitida).subscribe({
      next: (response) => {
        this.cargarIpPermitidas();
        this.snackBar.open('IP permitida actualizada exitosamente', 'Cerrar', {
          duration: 3000
        });
      },
      error: (error) => {
        console.error('Error al actualizar IP permitida:', error);
        this.loading = false;
        this.snackBar.open('Error al actualizar la IP permitida', 'Cerrar', {
          duration: 3000
        });
      }
    });
  }

  eliminarIpPermitida(ipPermitida: IpPermitida): void {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `${ipPermitida.is_active ? 'Desea inactivar' : 'Desea activar'} esta IP ?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        const userFromLocalStorage = localStorage.getItem('user') || '';
        const user = JSON.parse(userFromLocalStorage);
        this.loading = true;
        this.ipPermitidasService.delete(ipPermitida.id!, user.id).subscribe({
          next: (response) => {
            this.cargarIpPermitidas();
            Swal.fire(
              '¡Eliminado!',
              'IP permitida eliminada exitosamente',
              'success'
            );
          },
          error: (error) => {
            console.error('Error al eliminar IP permitida:', error);
            this.loading = false;
            Swal.fire(
              'Error',
              'Error al eliminar la IP permitida',
              'error'
            );
          }
        });
      }
    });
  }
 
}






