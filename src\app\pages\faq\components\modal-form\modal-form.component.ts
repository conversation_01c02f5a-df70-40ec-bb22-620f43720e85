import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Store } from '@ngrx/store';
import * as FaqActions from '../../store/faq.actions';
import { Faq } from '@app/models/backend/faq/faq.model';
import { FileFaq } from '@app/models/backend/faq/file-faq.model';

@Component({
  selector: 'app-modal-form',
  templateUrl: './modal-form.component.html',
  styleUrls: ['./modal-form.component.scss']
})
export class ModalFormComponent implements OnInit {
  @Input() dataModal!: { title: string; data?: Faq };
  @Input() modalRef!: BsModalRef<ModalFormComponent>;
// indica si es alta o edición
get isNewData(): boolean { return !this.dataModal?.data; }

  faqForm = this.fb.group({
    tipoUsuario: ['', Validators.required],
    pregunta : ['', Validators.required],
    respuesta : [''],
    archivos  : [[] as FileFaq[]]
  });
  submitted = false;

  // Tipos de usuario disponibles
  tiposUsuario = [
    { value: 'ADMIN', viewValue: 'ADMIN' },
    { value: 'AUDITOR', viewValue: 'AUDITOR' },
    { value: 'BACKOFFICE', viewValue: 'BACKOFFICE' },
    { value: 'COORDINADOR', viewValue: 'COORDINADOR' },
    { value: 'ASESOR', viewValue: 'ASESOR' },
    { value: 'Invitado', viewValue: 'Invitado' },
    { value: 'DESARROLLADOR', viewValue: 'DESARROLLADOR' },
  ];



  constructor(private fb: FormBuilder, private store: Store) {}

  ngOnInit() {
    if (this.dataModal?.data) this.faqForm.patchValue(this.dataModal.data);
  }

  /* recibe input del sub‑componente de subida */
  getUploadedFiles(files: FileFaq[]) {
    this.faqForm.patchValue({ archivos: files });
  }

  saveData() {
    this.submitted = true;
    if (this.faqForm.invalid) {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.faqForm.controls).forEach(key => {
        const control = this.faqForm.get(key);
        control?.markAsTouched();
      });
      return;
    }
    const faq = this.faqForm.value as unknown as Faq;
    if (this.dataModal?.data) {
      this.store.dispatch(FaqActions.updateFaq({ id: this.dataModal.data.id!, faq }));
    } else {
      this.store.dispatch(FaqActions.createFaq({ faq }));
    }
    this.modalRef.hide();
  }

  /* helpers */
  filterByType(type: string) {}
  onRemove(f: FileFaq) {}
  showFileLink(url: string) { window.open(url, '_blank'); }
  showFileLightBox(ix: number) {}
  countedFileByType!: Record<string, number>; // generar si lo necesitas
  activeFilter: string | null = null;
  uploadFiles: FileFaq[] = [];
  savedFiles: FileFaq[] = [];
  savedFilesFilter: FileFaq[] = [];
  get fFaq() { return this.faqForm.controls; }
}