import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as mapboxgl from 'mapbox-gl';
import { CatastroService } from '@app/services/catastro.service';
import { GeocodingService } from '@app/services/geocoding.service';
import { environment } from '@src/environments/environment';

@Component({
  selector: 'app-mapa-tipificacion-mapbox',
  templateUrl: './mapa-tipificacion-mapbox.component.html'
})
export class MapaTipificacionMapboxComponent implements OnInit {
  private map!: mapboxgl.Map;

  // Inputs para recibir parámetros directamente desde un componente padre
  @Input() queryParams: any;
  @Input() inDialog: boolean = false;
  @Input() mapContainerId: string = 'mapa-tipificacion-mapbox';

  // Datos de dirección
  provincia: string = '';
  municipio: string = '';
  via: string = '';
  numero: string = '';
  bloque: string = '';
  escalera: string = '';
  planta: string = '';
  puerta: string = '';
  codigoPostal: string = '';

  // Códigos para API Catastro
  codigoProvincia: string = '';
  codigoMunicipio: string = '';

  // Estado de carga
  cargando: boolean = false;
  datosFaltantes: boolean = false;
  camposFaltantes: string[] = [];

  // Provincias, municipios y vías para autocompletado
  provinciasCatastro: any[] = [];
  municipiosCatastro: any[] = [];
  viasCatastro: any[] = [];

  // Estados de carga para los dropdowns
  cargandoMunicipios: boolean = false;
  cargandoVias: boolean = false;

  // Valores seleccionados para los dropdowns
  selectedMunicipio: any = null;
  selectedVia: any = null;

  // Coordenadas por defecto (Madrid)
  defaultCenter: [number, number] = [-3.703790, 40.416775]; // Orden correcto para Mapbox: [longitud, latitud]

  // Coordenadas actuales (para mostrar en la interfaz)
  currentLat: number = 40.416775;
  currentLng: number = -3.703790;
  zoomLevel: number = 6;

  // Estado de error de geocodificación
  errorGeocoding: boolean = false;
  mensajeError: string = '';

  // Versiones limpias de los datos para mostrar
  viaLimpia: string = '';
  municipioLimpio: string = '';
  provinciaLimpia: string = '';

  // Estado del panel de dirección
  mostrarDireccionPanel: boolean = true;
  editMode: boolean = false;

  // Estado de carga para los dropdowns
  cargandoProvincias: boolean = false;
  mostrarProvincias: boolean = false;
  mostrarMunicipios: boolean = false;
  mostrarVias: boolean = false;

  // Copias de seguridad para cancelar edición
  private backupDireccion: any = {};

  // Marcador actual
  currentMarker: mapboxgl.Marker | null = null;

  // Estado del tema del mapa
  isDarkTheme: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private catastroService: CatastroService,
    private geocodingService: GeocodingService
  ) { }

  ngOnInit(): void {
    // Configurar el token de Mapbox (usando la propiedad como objeto en lugar de tipo)
    (mapboxgl as any).accessToken = environment.maps.mapboxToken;

    console.log('Token de Mapbox configurado:', environment.maps.mapboxToken);
    console.log('Coordenadas por defecto:', this.defaultCenter);

    // Forzar la activación de Mapbox para este componente
    environment.maps.mapaTipificacionMapbox = 1;

    // Obtener parámetros de la URL o del Input queryParams
    if (this.queryParams) {
      // Si recibimos los parámetros como Input, usarlos directamente
      this.provincia = this.queryParams['provincia'] || '';
      this.municipio = this.queryParams['municipio'] || '';
      this.via = this.queryParams['via'] || '';
      this.numero = this.queryParams['numero'] || '';
      this.bloque = this.queryParams['bloque'] || '';
      this.escalera = this.queryParams['escalera'] || '';
      this.planta = this.queryParams['planta'] || '';
      this.puerta = this.queryParams['puerta'] || '';
      this.codigoPostal = this.queryParams['codigoPostal'] || '';
      this.codigoProvincia = this.queryParams['codigoProvincia'] || '';
      this.codigoMunicipio = this.queryParams['codigoMunicipio'] || '';

      // Procesar los datos recibidos
      this.procesarDatosRecibidos();
    } else {
      // Si no hay Input, obtener parámetros de la URL
      this.route.queryParams.subscribe(params => {
        this.provincia = params['provincia'] || '';
        this.municipio = params['municipio'] || '';
        this.via = params['via'] || '';
        this.numero = params['numero'] || '';
        this.bloque = params['bloque'] || '';
        this.escalera = params['escalera'] || '';
        this.planta = params['planta'] || '';
        this.puerta = params['puerta'] || '';
        this.codigoPostal = params['codigoPostal'] || '';
        this.codigoProvincia = params['codigoProvincia'] || '';
        this.codigoMunicipio = params['codigoMunicipio'] || '';

        // Procesar los datos recibidos
        this.procesarDatosRecibidos();
      });
    }

    // Escuchar eventos personalizados para actualizar el mapa desde el diálogo
    if (this.inDialog) {
      console.log('Configurando listener para eventos de actualización de mapa en Mapbox');

      // Añadir un listener para el evento personalizado
      document.addEventListener('update-map', (event: any) => {
        console.log('Evento update-map recibido en componente Mapbox:', event.detail);

        // Actualizar los parámetros
        if (event.detail) {
          // Actualizar los parámetros
          this.provincia = event.detail.provincia || this.provincia;
          this.municipio = event.detail.municipio || this.municipio;
          this.via = event.detail.via || this.via;
          this.numero = event.detail.numero || this.numero;
          this.codigoPostal = event.detail.codigoPostal || this.codigoPostal;
          this.bloque = event.detail.bloque || this.bloque;
          this.escalera = event.detail.escalera || this.escalera;
          this.planta = event.detail.planta || this.planta;
          this.puerta = event.detail.puerta || this.puerta;

          // Actualizar códigos si están disponibles
          if (event.detail.codigoProvincia) {
            this.codigoProvincia = event.detail.codigoProvincia;
          }

          if (event.detail.codigoMunicipio) {
            this.codigoMunicipio = event.detail.codigoMunicipio;
          }

          console.log('Valores actualizados en mapa Mapbox:');
          console.log('Provincia:', this.provincia);
          console.log('Municipio:', this.municipio);
          console.log('Vía:', this.via);
          console.log('Número:', this.numero);
          console.log('Bloque:', this.bloque);
          console.log('Escalera:', this.escalera);
          console.log('Planta:', this.planta);
          console.log('Puerta:', this.puerta);

          // Buscar en el mapa con los nuevos parámetros
          this.buscarEnMapa();
        }
      });
    }
  }

  /**
   * Procesa los datos recibidos, ya sea por URL o por Input
   */
  private procesarDatosRecibidos(): void {
    // Actualizar versiones limpias de los datos
    this.actualizarDatosLimpios();

    // Verificar datos faltantes
    this.verificarDatosFaltantes();

    // Cargar datos adicionales si es necesario
    this.cargarDatosAdicionales();

    // Inicializar el mapa después de obtener los datos
    // Aumentar el tiempo de espera para asegurar que el DOM esté listo
    setTimeout(() => {
      this.initMap();
      // No llamamos a buscarEnMapa() aquí, se llamará desde el evento 'load' del mapa
    }, 500);
  }

  /**
   * Carga datos adicionales según la información disponible
   */
  cargarDatosAdicionales(): void {
    // Si tenemos provincia pero no municipio, cargar municipios
    if (this.provincia && this.codigoProvincia && !this.municipio) {
      this.cargarMunicipios();
    }

    // Si tenemos provincia y municipio pero no vía, cargar vías
    if (this.provincia && this.municipio && this.codigoProvincia && this.codigoMunicipio && !this.via) {
      this.cargarVias();
    }
  }

  /**
   * Carga los municipios de la provincia seleccionada
   */
  cargarMunicipios(): void {
    this.cargandoMunicipios = true;
    this.catastroService.getMunicipios(this.codigoProvincia).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.municipiosCatastro = response.d;
        }
        this.cargandoMunicipios = false;
      },
      error: (error) => {
        console.error('Error al cargar municipios:', error);
        this.cargandoMunicipios = false;
      }
    });
  }

  /**
   * Carga las vías del municipio seleccionado
   */
  cargarVias(): void {
    this.cargandoVias = true;
    this.catastroService.getVias(this.codigoProvincia, this.codigoMunicipio).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.viasCatastro = response.d;
        }
        this.cargandoVias = false;
      },
      error: (error) => {
        console.error('Error al cargar vías:', error);
        this.cargandoVias = false;
      }
    });
  }

  ngAfterViewInit(): void {
    // El mapa se inicializa en ngOnInit después de obtener los parámetros
  }

  private initMap(): void {
    try {
      // Esperar un momento para asegurarse de que el contenedor esté listo
      setTimeout(() => {
        try {
          // Verificar si el elemento existe
          const mapElement = document.getElementById(this.mapContainerId);
          if (!mapElement) {
            console.error(`Elemento con ID ${this.mapContainerId} no encontrado`);

            // Si estamos en un diálogo, intentar crear el elemento
            if (this.inDialog) {
              console.log(`Intentando crear el elemento ${this.mapContainerId} para el diálogo`);
              const containerElement = document.getElementById('dialog-map-container-mapbox');
              if (containerElement) {
                // Limpiar el contenedor
                containerElement.innerHTML = '';

                // Crear el elemento del mapa
                const mapDiv = document.createElement('div');
                mapDiv.id = this.mapContainerId;
                mapDiv.className = 'map-instance';
                mapDiv.style.width = '100%';
                mapDiv.style.height = '100%';
                mapDiv.style.position = 'absolute';
                mapDiv.style.top = '0';
                mapDiv.style.left = '0';
                mapDiv.style.zIndex = '1';

                // Añadir el elemento al contenedor
                containerElement.appendChild(mapDiv);

                // Intentar inicializar el mapa de nuevo después de un breve retraso
                setTimeout(() => {
                  this.initMap();
                }, 300);
                return;
              }
            }
            return;
          }

          // Si el mapa ya está inicializado, destruirlo primero
          if (this.map) {

            this.map.remove();
            this.map = null as any;
          }

          // Verificar si el tema oscuro está activo
          this.isDarkTheme = document.body.classList.contains('dark-theme');

          // Seleccionar el estilo del mapa según el tema
          const mapStyle = this.isDarkTheme
            ? 'mapbox://styles/mapbox/dark-v10'  // Estilo oscuro
            : 'mapbox://styles/mapbox/streets-v11'; // Estilo claro (predeterminado)


          // Siempre inicializar el mapa, independientemente de la configuración
          // Esto es para evitar errores cuando se intenta usar el mapa
          this.map = new mapboxgl.Map({
            container: this.mapContainerId,
            style: mapStyle,
            center: this.defaultCenter, // Usar defaultCenter que ya tiene el orden correcto [longitud, latitud]
            zoom: 6
          });

          // Añadir controles de navegación en la esquina inferior izquierda
          this.map.addControl(new mapboxgl.NavigationControl(), 'bottom-left');

          // Crear un botón personalizado para alternar el tema del mapa
          this.addThemeToggleControl();

          // Guardar la instancia del mapa en una variable global para poder acceder a ella desde fuera
          (window as any).mapboxMapInstance = this.map;

          // Esperar a que el mapa se cargue completamente
          this.map.on('load', () => {


            // Invalidar el tamaño del mapa para forzar su redibujado
            setTimeout(() => {
              this.map.resize();

              // Forzar un segundo redimensionamiento
              setTimeout(() => {
                this.map.resize();

                // Si estamos en un diálogo, buscar inmediatamente después de que el mapa esté listo
                if (this.inDialog) {

                  this.buscarEnMapa();
                }
              }, 300);
            }, 300);
          });
        } catch (innerError) {

        }
      }, 500); // Aumentamos el tiempo de espera para asegurar que el DOM esté listo
    } catch (error) {

    }
  }

  verificarDatosFaltantes(): void {
    this.camposFaltantes = [];

    if (!this.provincia) {
      this.camposFaltantes.push('provincia');
    }

    if (!this.municipio && this.provincia) {
      this.camposFaltantes.push('municipio');
    }

    if (!this.via && this.provincia && this.municipio) {
      this.camposFaltantes.push('vía');
    }

    this.datosFaltantes = this.camposFaltantes.length > 0;
  }

  buscarEnMapa(): void {
    this.cargando = true;
    this.errorGeocoding = false;
    this.mensajeError = '';

    // Verificar si el mapa está inicializado
    if (!this.map) {
      // Si estamos en un diálogo, intentar inicializar el mapa primero
      if (this.inDialog) {
        this.initMap();
        // Volver a intentar la búsqueda después de un breve retraso
        setTimeout(() => {
          this.buscarEnMapa();
        }, 800);
      }
      this.cargando = false;
      return;
    }

    // Eliminar marcador existente si hay uno
    if (this.currentMarker) {
      this.currentMarker.remove();
      this.currentMarker = null;
    }

    // Preparar objeto de dirección para geocodificación
    const direccion = {
      provincia: this.provincia,
      municipio: this.municipio,
      via: this.via,
      numero: this.numero,
      bloque: this.bloque,
      escalera: this.escalera,
      planta: this.planta,
      puerta: this.puerta,
      codigoPostal: this.codigoPostal
    };



    // Si tenemos provincia, municipio y vía, intentamos ubicar en el mapa
    if (this.provincia && this.municipio && this.via) {
      // Usar el servicio de geocodificación de Mapbox para obtener coordenadas reales
      this.geocodingService.geocodificarDireccionMapbox(direccion).subscribe({
        next: (coordenadas) => {
          if (coordenadas) {
            // Actualizar coordenadas con las obtenidas de la geocodificación
            this.currentLat = coordenadas[0];
            this.currentLng = coordenadas[1];
            this.zoomLevel = 16;
          } else {
            // Si no se pudieron obtener coordenadas, usar valores por defecto
            this.currentLat = 40.416775; // Madrid latitud
            this.currentLng = -3.703790; // Madrid longitud
            this.zoomLevel = 14;
            this.errorGeocoding = true;
            this.mensajeError = 'No se pudieron obtener coordenadas exactas para la dirección "' + this.via + '" en ' + this.municipio + ', ' + this.provincia + '.';
          }

          try {
            // Verificar nuevamente si el mapa está inicializado
            if (this.map) {
              // Actualizar la vista del mapa

              this.map.flyTo({
                center: [this.currentLng, this.currentLat],
                zoom: this.zoomLevel,
                essential: true
              });

              // Actualizar versiones limpias de los datos
              this.actualizarDatosLimpios();

              // Construir la dirección completa para el popup
              let direccionCompleta = '';

              // Mostrar tanto la versión original como la limpia si son diferentes
              if (this.via !== this.viaLimpia && this.viaLimpia) {
                direccionCompleta = `${this.viaLimpia}`;
                direccionCompleta += `<br><small>(Original: ${this.via})</small>`;
              } else {
                direccionCompleta = `${this.via}`;
              }

              if (this.numero) direccionCompleta += `, ${this.numero}`;
              if (this.bloque) direccionCompleta += `, Bloque ${this.bloque}`;
              if (this.escalera) direccionCompleta += `, Esc. ${this.escalera}`;
              if (this.planta) direccionCompleta += `, Planta ${this.planta}`;
              if (this.puerta) direccionCompleta += `, Puerta ${this.puerta}`;

              // Municipio y provincia
              if (this.municipio !== this.municipioLimpio && this.municipioLimpio) {
                direccionCompleta += `<br>${this.municipioLimpio}, ${this.provinciaLimpia}`;
                direccionCompleta += `<br><small>(Original: ${this.municipio}, ${this.provincia})</small>`;
              } else {
                direccionCompleta += `<br>${this.municipio}, ${this.provincia}`;
              }

              if (this.codigoPostal) direccionCompleta += ` (CP: ${this.codigoPostal})`;
              direccionCompleta += `<br>Lat: ${this.currentLat.toFixed(6)}, Lng: ${this.currentLng.toFixed(6)}`;

              // Añadir un marcador en la ubicación con estilos para modo oscuro
              const popupContent = `
                <div class="mapboxgl-popup-content-inner text-gray-800">
                  ${direccionCompleta}
                </div>
              `;

              // Crear el popup con offset
              const popup = new mapboxgl.Popup({
                offset: 25,
                className: 'mapbox-popup-dark-mode'
              })
              .setHTML(popupContent);

              this.currentMarker = new mapboxgl.Marker()
                .setLngLat([this.currentLng, this.currentLat])
                .setPopup(popup)
                .addTo(this.map);

              this.currentMarker.togglePopup(); // Mostrar el popup
            } else {

            }
          } catch (error) {
          }

          this.cargando = false;
        },
        error: () => {
          this.currentLat = 40.416775; // Madrid latitud
          this.currentLng = -3.703790; // Madrid longitud
          this.zoomLevel = 14;
          this.errorGeocoding = true;
          this.mensajeError = 'Error al conectar con el servicio de geocodificación. Se utilizarán coordenadas aproximadas.';

          try {
            // Verificar nuevamente si el mapa está inicializado
            if (this.map) {
              // Actualizar la vista del mapa con valores por defecto

              this.map.flyTo({
                center: [this.currentLng, this.currentLat],
                zoom: this.zoomLevel,
                essential: true
              });
            }
          } catch (error) {
          }

          this.cargando = false;
        }
      });
    }
    // Si solo tenemos provincia y municipio
    else if (this.provincia && this.municipio) {
      // Usar el objeto de dirección completo para aprovechar el código postal
      this.geocodingService.geocodificarDireccionMapbox({
        provincia: this.provincia,
        municipio: this.municipio,
        codigoPostal: this.codigoPostal
      }).subscribe({
        next: (coordenadas) => {
          if (coordenadas) {
            this.currentLat = coordenadas[0];
            this.currentLng = coordenadas[1];
            this.zoomLevel = 13;
          } else {
            this.currentLat = 40.416775; // Madrid latitud
            this.currentLng = -3.703790; // Madrid longitud
            this.zoomLevel = 12;
            this.errorGeocoding = true;
            this.mensajeError = 'No se pudieron obtener coordenadas exactas para el municipio ' + this.municipio + ' en ' + this.provincia + '.';
          }

          try {
            // Verificar nuevamente si el mapa está inicializado
            if (this.map) {
              // Actualizar la vista del mapa

              this.map.flyTo({
                center: [this.currentLng, this.currentLat],
                zoom: this.zoomLevel,
                essential: true
              });

              // Actualizar versiones limpias de los datos
              this.actualizarDatosLimpios();

              // Construir la dirección completa para el popup
              let direccionCompleta = '';

              // Municipio y provincia
              if (this.municipio !== this.municipioLimpio && this.municipioLimpio) {
                direccionCompleta += `${this.municipioLimpio}, ${this.provinciaLimpia}`;
                direccionCompleta += `<br><small>(Original: ${this.municipio}, ${this.provincia})</small>`;
              } else {
                direccionCompleta += `${this.municipio}, ${this.provincia}`;
              }

              if (this.codigoPostal) direccionCompleta += ` (CP: ${this.codigoPostal})`;
              direccionCompleta += `<br>Lat: ${this.currentLat.toFixed(6)}, Lng: ${this.currentLng.toFixed(6)}`;

              // Añadir un marcador en la ubicación con estilos para modo oscuro
              const popupContent = `
                <div class="mapboxgl-popup-content-inner text-gray-800">
                  ${direccionCompleta}
                </div>
              `;

              // Crear el popup con offset
              const popup = new mapboxgl.Popup({
                offset: 25,
                className: 'mapbox-popup-dark-mode'
              })
              .setHTML(popupContent);

              this.currentMarker = new mapboxgl.Marker()
                .setLngLat([this.currentLng, this.currentLat])
                .setPopup(popup)
                .addTo(this.map);

              this.currentMarker.togglePopup(); // Mostrar el popup
            }
          } catch (error) {
          }

          this.cargando = false;
        },
        error: () => {
          this.currentLat = 40.416775; // Madrid latitud
          this.currentLng = -3.703790; // Madrid longitud
          this.zoomLevel = 12;
          this.errorGeocoding = true;
          this.mensajeError = 'Error al conectar con el servicio de geocodificación. Se utilizarán coordenadas aproximadas.';

          // Actualizar la vista del mapa con valores por defecto
          if (this.map) {
            this.map.flyTo({
              center: [this.currentLng, this.currentLat],
              zoom: this.zoomLevel,
              essential: true
            });
          }

          this.cargando = false;
        }
      });
    }
    // Si solo tenemos provincia
    else if (this.provincia) {
      // Usar el objeto de dirección completo para aprovechar el código postal
      this.geocodingService.geocodificarDireccionMapbox({
        provincia: this.provincia,
        codigoPostal: this.codigoPostal
      }).subscribe({
        next: (coordenadas) => {
          if (coordenadas) {
            this.currentLat = coordenadas[0];
            this.currentLng = coordenadas[1];
            this.zoomLevel = 9;
          } else {
            this.currentLat = 40.416775; // Madrid latitud
            this.currentLng = -3.703790; // Madrid longitud
            this.zoomLevel = 8;
            this.errorGeocoding = true;
            this.mensajeError = 'No se pudieron obtener coordenadas exactas para la provincia ' + this.provincia + '.';
          }

          try {
            // Verificar nuevamente si el mapa está inicializado
            if (this.map) {
              // Actualizar la vista del mapa

              this.map.flyTo({
                center: [this.currentLng, this.currentLat],
                zoom: this.zoomLevel,
                essential: true
              });

              // Actualizar versiones limpias de los datos
              this.actualizarDatosLimpios();

              // Construir la dirección completa para el popup
              let direccionCompleta = '';

              // Provincia
              if (this.provincia !== this.provinciaLimpia && this.provinciaLimpia) {
                direccionCompleta += `${this.provinciaLimpia}`;
                direccionCompleta += `<br><small>(Original: ${this.provincia})</small>`;
              } else {
                direccionCompleta += `${this.provincia}`;
              }

              if (this.codigoPostal) direccionCompleta += ` (CP: ${this.codigoPostal})`;
              direccionCompleta += `<br>Lat: ${this.currentLat.toFixed(6)}, Lng: ${this.currentLng.toFixed(6)}`;

              // Añadir un marcador en la ubicación con estilos para modo oscuro
              const popupContent = `
                <div class="mapboxgl-popup-content-inner text-gray-800">
                  ${direccionCompleta}
                </div>
              `;

              // Crear el popup con offset
              const popup = new mapboxgl.Popup({
                offset: 25,
                className: 'mapbox-popup-dark-mode'
              })
              .setHTML(popupContent);

              this.currentMarker = new mapboxgl.Marker()
                .setLngLat([this.currentLng, this.currentLat])
                .setPopup(popup)
                .addTo(this.map);

              this.currentMarker.togglePopup(); // Mostrar el popup
            }
          } catch (error) {
          }

          this.cargando = false;
        },
        error: () => {
          this.currentLat = 40.416775; // Madrid latitud
          this.currentLng = -3.703790; // Madrid longitud
          this.zoomLevel = 8;
          this.errorGeocoding = true;
          this.mensajeError = 'Error al conectar con el servicio de geocodificación. Se utilizarán coordenadas aproximadas.';

          // Actualizar la vista del mapa con valores por defecto
          if (this.map) {
            this.map.flyTo({
              center: [this.currentLng, this.currentLat],
              zoom: this.zoomLevel,
              essential: true
            });
          }

          this.cargando = false;
        }
      });
    } else {
      this.cargando = false;
    }
  }

  /**
   * Completa los datos faltantes y actualiza el mapa
   */
  completarDatos(): void {
    // Actualizar datos según las selecciones
    if (this.selectedMunicipio) {
      this.municipio = this.selectedMunicipio.Denominacion;
      this.codigoMunicipio = this.selectedMunicipio.Codigo;

      // Si seleccionamos municipio y no tenemos vía, cargar vías
      if (!this.via && this.codigoProvincia && this.codigoMunicipio) {
        this.cargarVias();
      }
    }

    if (this.selectedVia) {
      this.via = this.selectedVia.DenominacionCompleta;
    }

    // Actualizar versiones limpias de los datos
    this.actualizarDatosLimpios();

    // Verificar si aún faltan datos
    this.verificarDatosFaltantes();

    // Actualizar el mapa con los nuevos datos
    this.buscarEnMapa();
  }

  /**
   * Maneja la selección de una provincia
   */
  seleccionarProvincia(provincia: any): void {
    this.provincia = provincia.Denominacion;
    this.codigoProvincia = provincia.Codigo;

    // Limpiar municipio y vía si cambiamos de provincia
    this.municipio = '';
    this.codigoMunicipio = '';
    this.via = '';

    // Cargar municipios de la provincia seleccionada
    this.cargarMunicipios();

    // Ocultar lista de provincias
    this.mostrarProvincias = false;

    // Actualizar versiones limpias de los datos
    this.actualizarDatosLimpios();

    // Verificar datos faltantes
    this.verificarDatosFaltantes();
  }

  /**
   * Maneja la selección de un municipio
   */
  seleccionarMunicipio(municipio: any): void {
    this.selectedMunicipio = municipio;
    this.municipio = municipio.Denominacion;
    this.codigoMunicipio = municipio.Codigo;

    // Si seleccionamos municipio, cargar vías
    if (this.codigoProvincia && this.codigoMunicipio) {
      this.cargarVias();
    }

    // Actualizar versiones limpias de los datos
    this.actualizarDatosLimpios();

    // Verificar datos faltantes
    this.verificarDatosFaltantes();
  }

  /**
   * Maneja la selección de una vía
   */
  seleccionarVia(via: any): void {
    this.selectedVia = via;
    this.via = via.DenominacionCompleta;

    // Actualizar versiones limpias de los datos
    this.actualizarDatosLimpios();

    // Verificar datos faltantes
    this.verificarDatosFaltantes();
  }

  volver(): void {
    this.router.navigate(['/static/welcome']);
  }

  /**
   * Añade un botón personalizado para alternar entre tema claro y oscuro del mapa
   */
  private addThemeToggleControl(): void {
    // Crear un elemento div para el botón
    const themeToggleControl = document.createElement('div');
    themeToggleControl.className = 'mapboxgl-ctrl mapboxgl-ctrl-group theme-toggle-control';
    themeToggleControl.style.backgroundColor = '#fff';
    themeToggleControl.style.border = 'none';
    themeToggleControl.style.borderRadius = '4px';
    themeToggleControl.style.boxShadow = '0 0 0 2px rgba(0,0,0,0.1)';
    themeToggleControl.style.cursor = 'pointer';

    // Crear el botón con el icono
    const button = document.createElement('button');
    button.className = 'theme-toggle-button';
    button.style.width = '30px';
    button.style.height = '30px';
    button.style.background = 'none';
    button.style.border = 'none';
    button.style.padding = '0';
    button.style.margin = '0';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    button.style.cursor = 'pointer';
    button.title = this.isDarkTheme ? 'Cambiar a tema claro' : 'Cambiar a tema oscuro';

    // Crear el icono
    const icon = document.createElement('div');
    icon.innerHTML = this.isDarkTheme
      ? '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>' // Icono de sol
      : '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>'; // Icono de luna

    button.appendChild(icon);
    themeToggleControl.appendChild(button);

    // Añadir evento de clic para alternar el tema
    button.addEventListener('click', () => {
      this.toggleMapTheme();
    });

    // Añadir el control al mapa en la esquina inferior izquierda
    this.map.addControl({
      onAdd: () => {
        return themeToggleControl;
      },
      onRemove: () => {
        themeToggleControl.parentNode?.removeChild(themeToggleControl);
      }
    }, 'bottom-left');
  }

  /**
   * Alterna entre tema claro y oscuro del mapa
   */
  private toggleMapTheme(): void {
    // Cambiar el estado del tema
    this.isDarkTheme = !this.isDarkTheme;

    // Seleccionar el nuevo estilo del mapa
    const newStyle = this.isDarkTheme
      ? 'mapbox://styles/mapbox/dark-v10'  // Estilo oscuro
      : 'mapbox://styles/mapbox/streets-v11'; // Estilo claro

    // Guardar la posición y el zoom actuales
    const currentCenter = this.map.getCenter();
    const currentZoom = this.map.getZoom();

    // Cambiar el estilo del mapa
    this.map.setStyle(newStyle);

    // Actualizar el icono del botón después de que el estilo se haya cargado
    this.map.once('style.load', () => {
      // Restaurar la posición y el zoom
      this.map.setCenter(currentCenter);
      this.map.setZoom(currentZoom);

      // Restaurar el marcador si existe
      if (this.currentMarker) {
        this.currentMarker.addTo(this.map);
      }

      // Actualizar el icono del botón
      const themeToggleButton = document.querySelector('.theme-toggle-button');
      if (themeToggleButton) {
        const icon = themeToggleButton.querySelector('div');
        if (icon) {
          icon.innerHTML = this.isDarkTheme
            ? '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>' // Icono de sol
            : '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>'; // Icono de luna
        }
        (themeToggleButton as HTMLButtonElement).title = this.isDarkTheme ? 'Cambiar a tema claro' : 'Cambiar a tema oscuro';
      }
    });
  }

  /**
   * Alterna entre el modo de visualización y edición
   */
  toggleEditMode(event: Event): void {
    if (event) {
      event.stopPropagation(); // Evitar que se propague al toggleDireccionPanel
    }

    if (!this.editMode) {
      // Guardar copia de seguridad de los datos actuales
      this.backupDireccion = {
        provincia: this.provincia,
        municipio: this.municipio,
        via: this.via,
        numero: this.numero,
        bloque: this.bloque,
        escalera: this.escalera,
        planta: this.planta,
        puerta: this.puerta,
        codigoPostal: this.codigoPostal,
        codigoProvincia: this.codigoProvincia,
        codigoMunicipio: this.codigoMunicipio
      };

      // Activar modo edición
      this.editMode = true;
    } else {
      this.cancelarEdicion();
    }
  }

  /**
   * Cancela la edición y restaura los valores originales
   */
  cancelarEdicion(): void {
    // Restaurar valores originales
    this.provincia = this.backupDireccion.provincia;
    this.municipio = this.backupDireccion.municipio;
    this.via = this.backupDireccion.via;
    this.numero = this.backupDireccion.numero;
    this.bloque = this.backupDireccion.bloque;
    this.escalera = this.backupDireccion.escalera;
    this.planta = this.backupDireccion.planta;
    this.puerta = this.backupDireccion.puerta;
    this.codigoPostal = this.backupDireccion.codigoPostal;
    this.codigoProvincia = this.backupDireccion.codigoProvincia;
    this.codigoMunicipio = this.backupDireccion.codigoMunicipio;

    // Desactivar modo edición
    this.editMode = false;

    // Ocultar listas desplegables
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;
  }

  /**
   * Guarda los cambios y actualiza el mapa
   */
  guardarEdicion(): void {
    // Desactivar modo edición
    this.editMode = false;

    // Ocultar listas desplegables
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    // Actualizar versiones limpias
    this.actualizarDatosLimpios();

    // Guardar los valores de los campos adicionales para mostrarlos en la interfaz
    // pero no se usarán para la geocodificación
    const numeroGuardado = this.numero;
    const bloqueGuardado = this.bloque;
    const escaleraGuardada = this.escalera;
    const plantaGuardada = this.planta;
    const puertaGuardada = this.puerta;

    // Buscar en el mapa con los nuevos datos
    this.buscarEnMapa();

    // Restaurar los valores de los campos adicionales después de la geocodificación
    // para que se muestren en la interfaz
    this.numero = numeroGuardado;
    this.bloque = bloqueGuardado;
    this.escalera = escaleraGuardada;
    this.planta = plantaGuardada;
    this.puerta = puertaGuardada;
  }

  /**
   * Actualiza las versiones limpias de los datos
   */
  actualizarDatosLimpios(): void {
    // Limpiar provincia
    this.provinciaLimpia = this.limpiarTexto(this.provincia);

    // Limpiar municipio
    this.municipioLimpio = this.limpiarTexto(this.municipio);

    // Limpiar vía
    this.viaLimpia = this.limpiarTexto(this.via);
  }

  /**
   * Limpia un texto eliminando paréntesis y otros caracteres especiales
   */
  limpiarTexto(texto: string): string {
    if (!texto) return '';

    // Eliminar paréntesis y su contenido
    let textoLimpio = texto.replace(/\([^)]*\)/g, '');

    // Eliminar caracteres especiales
    textoLimpio = textoLimpio.replace(/[^\w\s,áéíóúÁÉÍÓÚñÑüÜ.-]/g, '');

    // Eliminar espacios múltiples
    textoLimpio = textoLimpio.replace(/\s+/g, ' ');

    // Eliminar espacios al inicio y final
    textoLimpio = textoLimpio.trim();

    return textoLimpio;
  }

  /**
   * Maneja el evento de focus en el campo de provincia
   */
  onProvinciaFocus(): void {
    // Cargar provincias si no están cargadas
    if (this.provinciasCatastro.length === 0) {
      this.cargandoProvincias = true;
      this.catastroService.getProvincias().subscribe({
        next: (response) => {
          if (response && response.d) {
            this.provinciasCatastro = response.d;
          }
          this.cargandoProvincias = false;
          this.mostrarProvincias = true;
        },
        error: (error) => {
          console.error('Error al cargar provincias:', error);
          this.cargandoProvincias = false;
        }
      });
    } else {
      this.mostrarProvincias = true;
    }

    // Ocultar otras listas
    this.mostrarMunicipios = false;
    this.mostrarVias = false;
  }

  /**
   * Maneja el evento de focus en el campo de municipio
   */
  onMunicipioFocus(): void {
    if (this.provincia && this.codigoProvincia) {
      // Cargar municipios si no están cargados
      this.cargandoMunicipios = true;
      this.catastroService.getMunicipios(this.codigoProvincia).subscribe({
        next: (response) => {
          if (response && response.d) {
            this.municipiosCatastro = response.d;
          }
          this.cargandoMunicipios = false;
          this.mostrarMunicipios = true;
        },
        error: (error) => {
          console.error('Error al cargar municipios:', error);
          this.cargandoMunicipios = false;
        }
      });
    }

    // Ocultar otras listas
    this.mostrarProvincias = false;
    this.mostrarVias = false;
  }

  /**
   * Maneja el evento de focus en el campo de vía
   */
  onViaFocus(): void {
    if (this.provincia && this.municipio && this.codigoProvincia && this.codigoMunicipio) {
      // Cargar vías si no están cargadas
      this.cargandoVias = true;
      this.catastroService.getVias(this.codigoProvincia, this.codigoMunicipio).subscribe({
        next: (response) => {
          if (response && response.d) {
            this.viasCatastro = response.d;
          }
          this.cargandoVias = false;
          this.mostrarVias = true;
        },
        error: (error) => {
          console.error('Error al cargar vías:', error);
          this.cargandoVias = false;
        }
      });
    }

    // Ocultar otras listas
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
  }
}
