import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { WebSocketService } from '../websocket/WebSocketService';
import { UserResponseDTO, UserWebSocketDTO } from '@app/models/backend/user/user-response.dto';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { map } from 'rxjs/operators';
import { NotificationService } from '../notification/notification.service';

/**
 * Servicio para manejar la lista de usuarios con actualizaciones en tiempo real
 */
@Injectable({
  providedIn: 'root'
})
export class UserListService implements OnDestroy {
  // Subjects para los usuarios
  private usersSubject = new BehaviorSubject<UserResponseDTO[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private webSocketService: WebSocketService,
    private http: HttpClient,
    private notificationService: NotificationService
  ) {
    // Suscribirse a los mensajes WebSocket
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.clearSubscriptions();
  }

  /**
   * Configura las suscripciones a los canales WebSocket
   */
  private setupSubscriptions(): void {
    // Limpiar suscripciones existentes
    this.clearSubscriptions();

    // Suscribirse al canal de usuarios
    this.subscriptions.push(
      this.webSocketService.getMessagesByType('USER_CREATED')
        .subscribe((message: UserWebSocketDTO) => {
          if (message && message.user) {
            this.handleUserCreated(message.user);
            this.notificationService.success('Nuevo usuario registrado: ' + message.user.nombre + ' ' + message.user.apellido);
          }
        })
    );

    // Suscribirse a las actualizaciones de usuarios
    this.subscriptions.push(
      this.webSocketService.getMessagesByType('USER_UPDATED')
        .subscribe((message: UserWebSocketDTO) => {
          if (message && message.user) {
            this.handleUserUpdated(message.user);
            this.notificationService.success('Usuario actualizado: ' + message.user.nombre + ' ' + message.user.apellido);
          }
        })
    );

    // Suscribirse a las eliminaciones de usuarios
    this.subscriptions.push(
      this.webSocketService.getMessagesByType('USER_DELETED')
        .subscribe((message: UserWebSocketDTO) => {
          if (message && message.user) {
            this.handleUserDeleted(message.user);
            this.notificationService.success('Usuario eliminado: ' + message.user.nombre + ' ' + message.user.apellido);
          }
        })
    );
  }

  /**
   * Limpia todas las suscripciones
   */
  private clearSubscriptions(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }

  /**
   * Maneja la creación de un usuario
   */
  private handleUserCreated(user: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;

    // Verificar si el usuario ya existe
    if (!currentUsers.some(u => u.id === user.id)) {
      // Agregar el nuevo usuario
      this.usersSubject.next([...currentUsers, user]);
    }
  }

  /**
   * Maneja la actualización de un usuario
   */
  private handleUserUpdated(user: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;
    const index = currentUsers.findIndex(u => u.id === user.id);

    if (index !== -1) {
      // Actualizar el usuario existente
      const updatedUsers = [...currentUsers];
      updatedUsers[index] = user;
      this.usersSubject.next(updatedUsers);
    }
  }

  /**
   * Maneja la eliminación de un usuario
   */
  private handleUserDeleted(user: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;

    // Filtrar el usuario eliminado
    const updatedUsers = currentUsers.filter(u => u.id !== user.id);

    if (updatedUsers.length !== currentUsers.length) {
      this.usersSubject.next(updatedUsers);
    }
  }

  /**
   * Carga la lista de usuarios desde el backend
   */
  loadUsers(page: number = 0, size: number = 10, sedeId?: number): void {
    this.loadingSubject.next(true);
    this.errorSubject.next(null);

    // Construir la URL con los parámetros
    let url = `${environment.url}api/user/listar?page=${page}&size=${size}`;
    if (sedeId) {
      url += `&sede_id=${sedeId}`;
    }

    this.http.get<GenericResponse<any>>(url)
      .pipe(
        map(response => {
          if (response.rpta === 1 && response.data) {
            return response.data.users || [];
          }
          throw new Error(response.msg || 'Error al cargar usuarios');
        })
      )
      .subscribe({
        next: (users) => {
          this.usersSubject.next(users);
          this.loadingSubject.next(false);
        },
        error: (error) => {
          this.errorSubject.next(error.message || 'Error al cargar usuarios');
          this.loadingSubject.next(false);
        }
      });
  }

  /**
   * Obtiene la lista de usuarios como Observable
   */
  getUsers(): Observable<UserResponseDTO[]> {
    return this.usersSubject.asObservable();
  }

  /**
   * Obtiene el estado de carga como Observable
   */
  getLoading(): Observable<boolean> {
    return this.loadingSubject.asObservable();
  }

  /**
   * Obtiene el error como Observable
   */
  getError(): Observable<string | null> {
    return this.errorSubject.asObservable();
  }
}
