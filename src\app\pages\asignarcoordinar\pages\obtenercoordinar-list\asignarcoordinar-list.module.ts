import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StoreModule } from '@ngrx/store';
import { coordinadorReducer } from '../../store/save/save.reducer';
import { CoordinadorEffects } from '../../store/save';
import { EffectsModule } from '@ngrx/effects';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
// import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { MatIconModule } from '@angular/material/icon';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SpinnerModule } from '../../../../shared/indicators/spinner/spinner.module';
import { IndicatorsModule } from '../../../../shared/indicators';
import { AsignarCoordinadorListPageRoutingModule } from './asignarcoordinar-list-routing.module';
import { AsignarcoordinarListComponent } from './asignarcoordinar-list.component';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { PageTitleModule } from '@app/shared/components/page-title/page-title.module';

@NgModule({
  declarations: [AsignarcoordinarListComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    AsignarCoordinadorListPageRoutingModule,
    StoreModule.forFeature('coordinador', coordinadorReducer),
    EffectsModule.forFeature([CoordinadorEffects]),
    FormsModule,
    FlexLayoutModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    MatDatepickerModule,
    MatNativeDateModule,
    SpinnerModule,
    IndicatorsModule,
    MatChipsModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatListModule,
    PageTitleModule,
  ],
  exports: [AsignarcoordinarListComponent],
})
export class AsignarCoordinadorListModule {}
