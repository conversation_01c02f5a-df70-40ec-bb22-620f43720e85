import { Injectable, NgZone } from '@angular/core';
import { Subject, fromEvent, merge, timer, Subscription } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class InactivityService {
private static readonly INACTIVITY_LIMIT_MS = 60 * 60 * 1000; // 60 minutos en milisegundos

  private inactivityTimer$ = new Subject<void>();
  private stop$ = new Subject<void>();
  private userEventsSub: Subscription | null = null;

  public onInactivityDetected$ = this.inactivityTimer$.asObservable();

  constructor(private ngZone: NgZone) {}

  startMonitoring(): void {
    this.stopMonitoring(); // evita múltiples listeners

    this.ngZone.runOutsideAngular(() => {
      const events$ = merge(
        fromEvent(window, 'mousemove'),
        fromEvent(window, 'keydown'),
        fromEvent(window, 'mousedown'),
        fromEvent(window, 'touchstart'),
        fromEvent(window, 'scroll')
      );

      this.userEventsSub = events$
        .pipe(debounceTime(300))
        .subscribe(() => this.resetTimer());

      this.resetTimer();
    });
  }

  stopMonitoring(): void {
    this.stop$.next();
    this.userEventsSub?.unsubscribe();
  }

  private resetTimer(): void {
    this.stop$.next();

    timer(InactivityService.INACTIVITY_LIMIT_MS)
      .pipe(takeUntil(this.stop$))
      .subscribe(() => {
        this.inactivityTimer$.next(); // 🔔 Notifica inactividad
      });
  }
}
