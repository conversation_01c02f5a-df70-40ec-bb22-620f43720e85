import { createReducer, on, Action } from '@ngrx/store';
import * as CoordinadorActions from './save.actions';
import { CoordinadorDTO } from '@app/models/backend/dto/coordinador.dto';
import { AsesorDTO } from '@app/models/backend/dto/asesor.dto';
import { ClienteResidencial } from '@app/models/backend/clienteresidencial';

/**
 * Interfaz del estado para el feature de Coordinador.
 */
export interface CoordinadorState {
  coordinadores: CoordinadorDTO[];
  selectedCoordinador: CoordinadorDTO | null;
  loading: boolean;
  error: any;
  asesoresDisponibles: AsesorDTO[];
  clientesDeAsesor: any[];
  selectedAsesorId: number | null;
  asesoresConClientes: any;
  selectedCliente: ClienteResidencial | null;
  excelBlob: Blob | null; // ✅ AÑADIR ESTA LÍNEA

  // Nuevos campos para paginación
  totalItems: number;
  totalPages: number;
  currentPage: number;
  size: number;
  hasNext: boolean;
  hasPrevious: boolean;
}



/**
 * Estado inicial del feature.
 */
export const initialState: CoordinadorState = {
  coordinadores: [],
  selectedCoordinador: null,
  loading: false,
  error: null,
  asesoresDisponibles: [],
  clientesDeAsesor: [],
  selectedAsesorId: null,
  asesoresConClientes: null,
  selectedCliente: null,
  excelBlob: null, // ✅ AÑADIR ESTA LÍNEA

  // Nuevos campos para paginación
  totalItems: 0,
  totalPages: 0,
  currentPage: 0,
  size: 10,
  hasNext: false,
  hasPrevious: false
};



/**
 * Reducer para el feature "Coordinador".
 */
const _coordinadorReducer = createReducer(
  initialState,

  // --- CARGA DE COORDINADORES ---
  on(CoordinadorActions.loadCoordinadoresPaginados, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(CoordinadorActions.loadCoordinadoresPaginadosSuccess, (state, { coordinadores, totalItems, totalPages, currentPage , size, hasNext, hasPrevious }) => ({
    ...state,
    coordinadores,
    totalItems,
    totalPages,
    currentPage,
    size,
    hasNext,
    hasPrevious,
    loading: false,
    error: null
  })),
  on(CoordinadorActions.loadCoordinadoresPaginadosFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // --- ASIGNAR ASESORES A UN COORDINADOR ---
  on(CoordinadorActions.asignarAsesores, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(CoordinadorActions.asignarAsesoresSuccess, (state, { coordinador }) => ({
    ...state,
    coordinadores: state.coordinadores.map(c =>
      c.id === coordinador.id ? coordinador : c
    ),
    selectedCoordinador: coordinador,
    loading: false,
    error: null
  })),
  on(CoordinadorActions.asignarAsesoresFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // --- CARGAR ASESORES DISPONIBLES ---
  on(CoordinadorActions.loadAsesoresDisponibles, state => ({
    ...state,
    // No activamos el loading global para evitar el indicador de carga en toda la pantalla
    error: null
  })),
  on(CoordinadorActions.loadAsesoresDisponiblesSuccess, (state, { asesores }) => ({
    ...state,
    asesoresDisponibles: asesores,
    loading: false,
    error: null
  })),
  on(CoordinadorActions.loadAsesoresDisponiblesFailure, (state, { error }) => ({
    ...state,
    asesoresDisponibles: [],
    loading: false,
    error
  })),

  // --- ASIGNAR UN ASESOR INDIVIDUAL ---
  on(CoordinadorActions.asignarAsesorIndividual, state => ({
    ...state,
    // No activamos el loading global para evitar el indicador de carga en toda la pantalla
    error: null
  })),
  on(CoordinadorActions.asignarAsesorIndividualSuccess, (state, { coordinadorId, asesor }) => ({
    ...state,
    coordinadores: state.coordinadores.map(coordinador => {
      if (coordinador.id === coordinadorId) {
        // Verificar si el asesor ya existe
        const asesorExiste = coordinador.asesores.some(a => a.id === asesor.id);
        if (asesorExiste) {
          return coordinador;
        }
        return {
          ...coordinador,
          asesores: [...coordinador.asesores, asesor]
        };
      }
      return coordinador;
    }),
    loading: false,
    error: null
  })),
  on(CoordinadorActions.asignarAsesorIndividualFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // --- CARGAR CLIENTES DE UN ASESOR ---
  on(CoordinadorActions.loadClientesDeAsesor, (state, { asesorId }) => ({
    ...state,
    loading: true,
    error: null,
    selectedAsesorId: asesorId
  })),
  on(CoordinadorActions.loadClientesDeAsesorSuccess, (state, { clientes }) => ({
    ...state,
    clientesDeAsesor: clientes,
    loading: false,
    error: null
  })),
  on(CoordinadorActions.loadClientesDeAsesorFailure, (state, { error }) => ({
    ...state,
    clientesDeAsesor: [],
    loading: false,
    error
  })),

  // --- CARGAR ASESORES CON CLIENTES ---
  on(CoordinadorActions.loadAsesoresConClientes, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(CoordinadorActions.loadAsesoresConClientesSuccess, (state, { data }) => ({
    ...state,
    asesoresConClientes: data,
    loading: false,
    error: null
  })),
  on(CoordinadorActions.loadAsesoresConClientesFailure, (state, { error }) => ({
    ...state,
    asesoresConClientes: null,
    loading: false,
    error
  })),

  // --- REMOVER ASESOR ---
  on(CoordinadorActions.removerAsesor, state => ({
    ...state,
    // No activamos el loading global para evitar el indicador de carga en toda la pantalla
    error: null
  })),
  on(CoordinadorActions.removerAsesorSuccess, (state, { coordinadorId, asesorId }) => ({
    ...state,
    coordinadores: state.coordinadores.map(coordinador => {
      if (coordinador.id === coordinadorId) {
        return {
          ...coordinador,
          asesores: coordinador.asesores.filter(asesor => asesor.id !== asesorId)
        };
      }
      return coordinador;
    }),
    loading: false,
    error: null
  })),
  on(CoordinadorActions.removerAsesorFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  on(CoordinadorActions.obtenerClientePorMovilSuccess, (state, { cliente }) => ({
    ...state,
    selectedCliente: cliente,
  })),

  on(CoordinadorActions.obtenerClientePorMovilFailure, (state, { error }) => ({
    ...state,
    errorCliente: error,
  })),
  on(CoordinadorActions.descargarExcelCliente, state => ({
    ...state,
    loading: true
  })),
  on(CoordinadorActions.descargarExcelClienteSuccess, state => ({
    ...state,
    loading: false
  })),
  on(CoordinadorActions.descargarExcelClienteFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  on(CoordinadorActions.loadClienteByDniAndMobileSuccess, (state, { cliente }) => ({
    ...state,
    selectedCliente: cliente,
  })),
  on(CoordinadorActions.loadClienteByDniAndMobileFailure, (state, { error }) => ({
    ...state,
    errorCliente: error,
  })),
  on(CoordinadorActions.exportarClientesHoyCoordinadorSuccess, (state, { excelBlob }) => ({
    ...state,
    excelBlob // <- se guarda el blob en el estado
  })),

);

export function coordinadorReducer(state: CoordinadorState | undefined, action: Action): CoordinadorState {
  return _coordinadorReducer(state, action);
}
