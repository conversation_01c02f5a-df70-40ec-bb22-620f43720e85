import { Pipe, PipeTransform } from '@angular/core';
import { AnuncioResponse } from '../store/save';

@Pipe({
  name: 'filter'
})
export class FilterPipe implements PipeTransform {
  transform(items: AnuncioResponse[] | null, searchText: string): AnuncioResponse[] {
    if (!items) return [];
    if (!searchText) return items;

    searchText = searchText.toLowerCase();

    return items.filter(item => {
      // Formatear la fecha para buscar por día-mes-año
      const fecha = new Date(item.fechaPublicacion);
      const fechaFormateada = `${fecha.getDate().toString().padStart(2, '0')}-${(fecha.getMonth() + 1).toString().padStart(2, '0')}-${fecha.getFullYear()}`;

      return item.titulo.toLowerCase().includes(searchText) ||
             item.descripcion.toLowerCase().includes(searchText) ||
             item.categoria.toLowerCase().includes(searchText) ||
             fechaFormateada.includes(searchText);
    });
  }
}
