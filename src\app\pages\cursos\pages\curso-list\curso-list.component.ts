import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CursoService } from '@app/services/curso.service';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';
import { Curso } from '@app/models/backend/curso/curso.model';
import { ThemeService } from '@app/services';
import { NotificationService } from '@app/services/notification/notification.service';
import { CursoAsignarUsuariosComponent } from '../../components/curso-asignar-usuarios/curso-asignar-usuarios.component';
import { CursoVerAlumnosComponent } from '../../components/curso-ver-alumnos/curso-ver-alumnos.component';

@Component({
  selector: 'app-curso-list',
  templateUrl: './curso-list.component.html',
})
export class CursoListComponent implements OnInit, OnDestroy {
  cursos: Curso[] = [];
  loading: boolean = false;
  error: string | null = null;
  searchTerm: string = '';
  isDarkTheme: boolean = false;

  // URL de imagen de placeholder para cursos
  cursoPlaceholderUrl: string =
    'https://img.freepik.com/free-vector/online-certification-illustration_23-2148575636.jpg';

  private destroy$ = new Subject<void>();

  constructor(
    private cursoService: CursoService,
    private cursoUsuarioService: CursoUsuarioService,
    private router: Router,
    private dialog: MatDialog,
    private themeService: ThemeService,
    private notification: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadCursos();
    this.checkDarkTheme();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadCursos(): void {
    this.loading = true;
    this.error = null;

    this.cursoService
      .getAllCursos()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.cursos = response.data;
          } else {
            this.error = response.msg || 'Error al cargar los cursos';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error =
            'Error al cargar los cursos. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar cursos:', error);
        },
      });
  }

  checkDarkTheme(): void {
    this.themeService.darkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isDark: boolean) => {
        this.isDarkTheme = isDark;
      });
  }

  onCreateCurso(): void {
    this.router.navigate(['/cursos/crear']);
  }

  onEditCurso(id: number): void {
    this.router.navigate(['/cursos/editar', id]);
  }

  onViewCurso(id: number): void {
    this.router.navigate(['/cursos/detalle', id]);
  }

  onAsignarUsuarios(curso: Curso): void {
    const dialogRef = this.dialog.open(CursoAsignarUsuariosComponent, {
      width: '800px',
      data: { curso },
      panelClass: ['curso-dialog'],
    });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.destroy$))
      .subscribe((result) => {
        if (result) {
          this.notification.success('Usuarios asignados correctamente');
        }
        // Corregir problemas de layout después de cerrar el modal
        this.forceLayoutRefresh();
      });
  }

  onVerAlumnos(curso: Curso): void {
    // Detectar el ancho de la pantalla para ajustar el tamaño del modal
    const screenWidth = window.innerWidth;
    let dialogWidth = '800px';
    let dialogMaxWidth = '95vw';

    // En pantallas pequeñas, usar un ancho más adaptativo
    if (screenWidth < 768) {
      dialogWidth = '95vw';
    } else if (screenWidth < 1024) {
      dialogWidth = '80vw';
    }

    const dialogRef = this.dialog.open(CursoVerAlumnosComponent, {
      width: dialogWidth,
      maxWidth: dialogMaxWidth,
      data: { curso },
      panelClass: [
        'curso-dialog',
        'responsive-dialog',
        this.isDarkTheme ? 'dark-theme' : '',
      ],
    });

    // Corregir problemas de layout después de cerrar el modal
    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.forceLayoutRefresh();
      });
  }

  onDeleteCurso(id: number): void {
    if (confirm('¿Está seguro de que desea eliminar este curso?')) {
      this.cursoService
        .deleteCurso(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              this.notification.success('Curso eliminado exitosamente');
              this.loadCursos();
            } else {
              this.error = response.msg || 'Error al eliminar el curso';
              this.notification.error(this.error);
            }
          },
          error: (error) => {
            console.error('Error al eliminar curso:', error);
            this.error =
              'Error al eliminar el curso. Por favor, inténtelo de nuevo.';
            this.notification.error(this.error);
          },
        });
    }
  }

  filterCursos(): Curso[] {
    if (!this.searchTerm.trim()) {
      return this.cursos;
    }

    const term = this.searchTerm.toLowerCase();
    return this.cursos.filter(
      (curso) =>
        curso.nombre.toLowerCase().includes(term) ||
        curso.descripcion.toLowerCase().includes(term)
    );
  }

  /**
   * Fuerza un refresh del layout para corregir problemas después de cerrar modales
   */
  private forceLayoutRefresh(): void {
    // Pequeño delay para asegurar que el modal se haya cerrado completamente
    setTimeout(() => {
      // Forzar un repaint del DOM
      document.body.style.display = 'none';
      document.body.offsetHeight; // Trigger reflow
      document.body.style.display = '';

      // Alternativa más suave: solo trigger reflow en el contenedor
      const container = document.querySelector('.grid');
      if (container) {
        (container as HTMLElement).style.transform = 'translateZ(0)';
        setTimeout(() => {
          (container as HTMLElement).style.transform = '';
        }, 10);
      }
    }, 100);
  }
}
