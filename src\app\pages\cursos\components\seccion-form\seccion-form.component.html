<div class="seccion-form-container">
  <h2 mat-dialog-title>{{ title }}</h2>
  
  <form [formGroup]="seccionForm" (ngSubmit)="onSubmit()">
    <mat-dialog-content>
      <div class="form-field-container">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Título</mat-label>
          <input matInput formControlName="titulo" placeholder="Ingrese el título de la sección" required>
          <mat-error *ngIf="seccionForm.get('titulo')?.hasError('required')">
            El título es obligatorio
          </mat-error>
          <mat-error *ngIf="seccionForm.get('titulo')?.hasError('maxlength')">
            El título no puede exceder los 100 caracteres
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Descripción</mat-label>
          <textarea matInput formControlName="descripcion" placeholder="Ingrese una descripción para la sección" rows="3"></textarea>
          <mat-error *ngIf="seccionForm.get('descripcion')?.hasError('maxlength')">
            La descripción no puede exceder los 500 caracteres
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Orden</mat-label>
          <input matInput type="number" formControlName="orden" placeholder="Orden de la sección">
          <mat-hint>Si no se especifica, se asignará automáticamente</mat-hint>
        </mat-form-field>

        <div class="modulo-info">
          <span class="label">Módulo:</span>
          <span class="value">{{ data.modulo.titulo }}</span>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button type="button" [disabled]="loading" (click)="onCancel()">Cancelar</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="seccionForm.invalid || loading">
        <mat-spinner *ngIf="loading" diameter="20" class="button-spinner"></mat-spinner>
        <span *ngIf="!loading">{{ isEditMode ? 'Actualizar' : 'Crear' }}</span>
      </button>
    </mat-dialog-actions>
  </form>
</div>
